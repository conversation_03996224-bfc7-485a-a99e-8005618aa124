//http://countdownjs.org/

var Timer;
var sessionTime = 0;
var sessionThresholdMinutes = 5;
var sessionThreshold = sessionThresholdMinutes * 60 * 1000;
var sessionDownDivId = "session-countdown";
var sessionProcessTimer;
var redirectProcessTimer;
var sessionDownText = null;
var redirectTimeOutPage = "#{request.contextPath}/paginas/publico/timeout.xhtml";

var timerId = null;
var timerThresholdId = null;
var threshold = false;

var incrementTime = 1000,
    currentTime = 1500;

timerComplete = function() {
	removeMouseMoveEventListener();
	startRedirectTimer();
};

function startRedirectTimer(){
	closeTimeoutDialog();
	redirectTimeout();
	
}
init = function(time) {
	var data = new Date(new Date().getTime() + time*1000);
	timerId = 	  countdown(
			data,
	    function(ts) {
		  sessionDownText = document.getElementById(sessionDownDivId);
		  sessionDownText.innerHTML = ts.toHTML("strong");
		  		
		  if(ts.value >= (-1*sessionThreshold) && !threshold){
			 openTimeoutDialog();
			 threshold = true;
			 
		  } 
		  
		  if(ts.value >= 0){
			  window.clearInterval(timerId);
			  timerComplete();
		  }
	    },
	    countdown.MINUTES|countdown.SECONDS);
	
	threshold = false;
};

this.resetCountdown = function(time) {
	window.clearInterval(timerId);
	window.clearInterval(timerThresholdId);
	
	init(time);
};


		 
function openTimeoutDialog(){
	var el = document.getElementById("overlay");
	el.style.visibility = (el.style.visibility == "visible") ? "hidden" : "visible";
	
	addMouseMoveEventListener();
}

function closeTimeoutDialog(){
	document.getElementById("overlay").style.visibility = 'hidden';
}

function removeMouseMoveEventListener(){
	if (document.removeEventListener) {
	    document.removeEventListener("mousemove", onMouseMove, false);
	} else if (document.detachEvent) {
	    document.detachEvent("onmousemove", onMouseMove);
	}
}

function onMouseMove(){
	removeMouseMoveEventListener();
	restartSession();
	closeTimeoutDialog();
}

function addMouseMoveEventListener(){
	if (document.addEventListener) {
	    document.addEventListener("mousemove", onMouseMove, false);
	} else if (document.attachEvent) {
	    document.attachEvent("onmousemove", onMouseMove);
	}
}

function pad(number, length) {
    var str = '' + number;
    while (str.length < length) {str = '0' + str;}
    return str;
}

function formatTime(){
	var segundo = currentTime % 60;
	var minutos = parseInt(currentTime / 60);
	
	if (!sessionDownText)
		sessionDownText = document.getElementById(sessionDownDivId);
	
	if(sessionDownText){
		segundo = (segundo + "").replace(/^(\d)$/, '0$1');
		minutos = (minutos + "").replace(/^(\d)$/, '0$1');
		
		sessionDownText.innerHTML = minutos + "min:" + segundo + "s";
	}
}
