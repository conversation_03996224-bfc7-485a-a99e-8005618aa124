@charset "utf-8";
/* CSS Document */
html, body {
	margin: 0;
	height: 100%;
}

body {
	color: gray;
}

.icon-plus-circled {
	margin-right: 8px;
}

.icon-liberada-envio {
	background-image: url("#{resource['imagens/seta.png']}");
}

.tamanhoImagemSinalizadora {
	height: 25px;
	width: 25px;
}

.box {
	width: 30px;
	height: 20px;
	border-radius: 10px;
}

.corVerde {
	background-color: #5cb85c;
}

.corLaranja {
	background-color: #f0ad4e;
}

.corVermelho {
	background-color: #d9534f;
}

.corTextoVerde {
	color: #5cb85c;
}

.corTextoLaranja {
	color: #f0ad4e;
}

.corTextoVermelho {
	color: #d9534f;
}

#wizard_back, #wizard_next {
	width: auto !important;
}

a:link {
	text-decoration: none;
}

a:visited {
	text-decoration: none;
}

a:hover {
	text-decoration: none;
	color: #FF0000;
}

a:active {
	text-decoration: none;
}

.cellGridFormLabel td, .cellGridFormLabel tr {
	align: center;
}

.ui-panelgrid {
	border: none !important;
}

.semBordaPanel tr, .semBordaPanel td {
	border: none !important;
}

.ui-breadcrumb .ui-icon-home {
	background-image: none !important;
}

.ui-tree .ui-icon-folder-collapsed {
	background-image: url("#{resource['imagens/icon-folder.png']}");
	background-size: 100% !important;
	width: 18px;
	height: 18px;
}

.ui-tree .ui-icon-folder-open {
	background-image: url("#{resource['imagens/icon-folder-open.png']}");
	background-size: 100% !important;
	width: 18px;
	height: 18px;
}

.ui-tree .ui-icon-document {
	background-image: url("#{resource['imagens/icon-file.png']}'");
	background-size: 100% !important;
	width: 18px;
	height: 18px;
}

.layout-header-widgets {
	top: 8px;
}

.layout-header-widgets-submenu {
	/* margin-top: 12px; */
	
}

.ui-tree, .ui-datatable .ui-datatable-tablewrapper table thead tr th,
	.ui-datatable table thead tr th, .ui-datatable table tbody tr td,
	.ui-menu .ui-menuitem .ui-menuitem-link, table tbody tr td {
	color: gray;
}

.ui-fluid .ui-button, .ui-fluid .ui-splitbutton, .ui-fluid .ui-splitbutton .ui-button
	{
	width: auto;
}

.ui-tree .ui-tree-container {
	padding: 0;
	overflow-x: hidden;
}

.ui-tree .ui-tree-toggler, .ui-tree .ui-treenode-leaf-icon {
	display: none;
}

.ui-tree .ui-treenode-icon {
	position: relative;
	left: 18px;
	top: 15px;
	margin-right: 10px;
}

.ui-tree .ui-icon-folder-open, .ui-tree .ui-icon-folder-collapsed {
	background-size: 200%
}

.ui-treenode-content {
	/* padding-left: 8px; */
	
}

.comprimentoSidebar {
	width: 260px !important;
}

@media ( max-width : 1200px) and (min-width: 641px) {
	.comprimentoSidebar {
		width: 50px !important;
	}
	.comprimentoSidebar:hover {
		width: 260px !important;
	}
}

.corpo {
	background: #FFFFFF;
}

.ui-tree .ui-treenode .ui-state-highlight {
	color: #3D3D3D;
}

.ui-tree .ui-treenode .ui-treenode-label.ui-state-hover {
	color: #3D3D3D;
}

.ui-tree .ui-treenode-label {
	width: 89%;
}

.noDecoration {
	text-decoration: none !important;
}

.layout-header-widgets li i {
	margin-top: 8px;
}

img {
	border-style: none;
}

a {
	text-decoration: none;
}

a:hover {
	/* color: darkblue !important; */
	
}

td {
	font: 11px Verdana, Arial, Helvetica, sans-serif;
	vertical-align: middle;
}

#container { /* 	width: 1024px; */
	width: auto;
	/* using 20px less than a full 800px width allows for browser chrome and avoids a horizontal scroll bar */
	background: #FFFFFF;
	margin: 0 auto;
	/* the auto margins (in conjunction with a width) center the page */
	border: 1px solid #000000;
	text-align: left;
	/* this overrides the text-align: center on the body element. */
}

#cabecalho {
	padding-bottom: 5px;
	padding-left: 20px;
	padding-right: 20px;
}

#timer {
	padding-top: 5px;
	float: right;
}

#entidade {
	padding-top: 8px;
	float: left;
}

#mainContent {
	padding: 10px 20px 20px 20px;
	/* remember that padding is the space inside the div box and margin is the space outside the div box */
}

#iconSistema {
	float: left;
	height: 30px;
	margin: 10px;
}

#iconTce {
	float: left;
	padding: 0 10px;
	margin: 7px 0;
	height: 35px;
	border-right: 1px solid #A8CAE6;
}

#iconTceMobile {
	float: left;
	padding: 0 10px;
	margin: 7px 0;
	height: 35px;
	border-right: 1px solid #A8CAE6;
}

.semBorda tbody tr td {
	border: none !important;
}

.negrito {
	font-weight: bold;
}

.headerNegrito {
	font-weight: bold;
	font-size: 11.5px;
}

.semBorda tr, .semBorda td {
	border: none !important;
}

#lblHeader {
	margin-right: 10px;
	margin-top: 12px;
	height: auto;
	color: white;
	font-size: 18px;
	display: block;
	float: left;
}

.textoRodape {
	padding: 10px;
}

.breadcrumbTopTextLabel {
	float: right;
	text-align: right;
	color: black;
	font-size: 10px;
}

.campoObrigatorio {
	font-weight: bold !important;
}

.ellipsis {
	text-overflow: ellipsis;
	white-space: nowrap;
	overflow: hidden;
	max-width: 96%;
	display: inline-block;
}

.comprimentoAuto {
	width: 100% !important;
}

.limitarWidthEllipsis>span>span.ui-treenode-label {
	width: calc(100% - 40px);
}

.panelScrollBoder {
	overflow: scroll;
	border: none;
}

.ui-menu-list {
	position: static;
	overflow: visibleauto;
	max-height: 500px;
	max-width: 100%;
}

.breadcrumb {
	height: 25px;
	padding: 1px 5px 7px;
}

.ui-datagrid-no-border>.ui-datagrid-content {
	border: none;
}

.tamanho-content .ui-panel-content {
	height: calc(100%);
}

.lido {
	font-weight: normal !important;
}

.naolido {
	font-weight: bold !important;
}

.marcadorNumeroVermelho {
	color: red
}

.fonteVermelha {
	color: red
}

.ui-datatable .ui-column-filter {
	width: 100px !important;
	max-width: calc(100% - 20px) !important;
}

.ui-menu .ui-menuitem-text {
	white-space: normal !important;
	text-align: left;
}

.alertInfo {
	color: #31708f;
	background-color: #d9edf7;
	border-color: #bce8f1;
	padding: 10px;
	border-radius: 10px;
}

.middle td {
	vertical-align: middle;
}

table.padding_zero  tbody tr td:first-child {
	padding-left: 0px;
	padding-right: 0px;
	text-align: left;
}

.ui-datatable tbody td span.colNome {
	display: block;
	text-align: left;
}

.ui-datatable tbody td span.colCodigo {
	display: block;
	text-align: center;
}

.ui-datatable tbody td span.colValor {
	display: block;
	text-align: right;
}

.alinhamentoEsquerda {
	display: block;
	text-align: left;
}

.alinhamentoCentralizado {
	display: block;
	text-align: center;
}

.alinhamentoSelectEsquerda {
	float: left !important;
	display: block !important;
}

.autocompleteSize .ui-autocomplete-multiple-container {
	width: 100%;
}

.ui-autocomplete-table {
	min-width: 450px;
}

.ui-autocomplete-panel {
	min-width: 450px;
}

.ui-growl-item-container {
	opacity: 1.0 !important;
}

.ui-fileupload-content.ui-widget-content.ui-corner-bottom {
	display: none !important;
}

ul.ui-autocomplete-multiple-container.ui-widget.ui-inputfield.ui-state-default.ui-corner-all
	{
	width: 100% !important;
}

.ui-splitbutton {
	padding-right: 1px;
}

.ui-splitbutton .ui-button.ui-splitbutton-menubutton {
	position: relative;
}

.ui-selectonemenu {
	min-width: auto !important;
	padding-right: 25px !important;
}

.ui-selectonemenu label.ui-selectonemenu-label {
	white-space: normal !important;
	width: calc(100%) !important;
}

.ui-selectonemenu-items {
	max-width: 100%;
}

.ui-selectonemenu-panel {
	max-width: 650px;
}

.ui-selectonemenu-item {
	white-space: normal !important;
}

.ui-selectcheckboxmenu {
	min-width: auto !important;
	padding-right: 25px !important;
}

.ui-selectcheckboxmenu-token-label {
	white-space: normal !important;
	width: calc(100%) !important;
}

.ui-selectcheckboxmenu-token {
	border-style: solid;
	border-width: 1px;
}

.ui-selectcheckboxmenu-items {
	max-width: 100%;
}

.ui-selectcheckboxmenu-panel {
	max-width: 650px;
}

.ui-selectcheckboxmenu-item {
	white-space: normal !important;
}

.ui-selectcheckboxmenu-multiple span.ui-icon-triangle-1-s {
	top: 0 !important;
}


.ui-inputfield {
	background: white !important;
}

.ui-accordion .ui-tabs-outline {
	-webkit-box-shadow: none;
	-moz-box-shadow: none;
	box-shadow: none;
}

.ui-layout-center .ui-icon, .ui-button .ui-icon {
	background-image:
		url("#{resource['primefaces-sentinel:images/ui-icons_ffffff_256x240.png']}");
}

.ui-accordion-header .ui-icon-triangle-1-s {
	background-image:
		url("#{resource['primefaces-sentinel:images/downarrow.svg']}");
}

.ui-accordion-header .ui-icon-triangle-1-e {
	background-image:
		url("#{resource['primefaces-sentinel:images/right-arrow.svg']}");
}

.ui-datatable .ui-paginator .ui-state-active {
	background: #F3F5F7;
}

@media ( max-width : 1200px) and (min-width: 641px) {
	/* #layout-menubar { width: 260px; }
	#layout-menubar .layout-menubar-container { width: 100%; }
	#layout-menubar .layout-menubar-container>li:hover { width: 100%; }
	#layout-menubar .layout-menubarinner-box { width: 230px; padding: 10px 15px; }
	#layout-menubar #layout-menubar-resize { float: right; }
	#buttonArea { display: block; } */
}