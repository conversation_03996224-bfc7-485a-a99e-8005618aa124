<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg">
<metadata></metadata>
<defs>
<font id="titillium_webbold" horiz-adv-x="1146" >
<font-face units-per-em="2048" ascent="1638" descent="-410" />
<missing-glyph horiz-adv-x="450" />
<glyph horiz-adv-x="0" />
<glyph unicode="&#xd;" horiz-adv-x="681" />
<glyph unicode=" "  horiz-adv-x="450" />
<glyph unicode="&#x09;" horiz-adv-x="450" />
<glyph unicode="&#xa0;" horiz-adv-x="450" />
<glyph unicode="!" horiz-adv-x="573" d="M139 0v330h295v-330h-295zM139 1393h297l-28 -881h-242z" />
<glyph unicode="&#x22;" horiz-adv-x="841" d="M113 1393h264l-12 -492h-242zM467 1393h264l-14 -492h-242z" />
<glyph unicode="#" d="M33 305v230h196v258h-196v229h196v330h238v-330h213v330h238v-330h196v-229h-196v-258h196v-230h-196v-305h-238v305h-213v-305h-238v305h-196zM467 535h213v258h-213v-258z" />
<glyph unicode="$" d="M106 975q0 203 123 302t338 99h27l37 283h151l-37 -295q127 -12 234 -33l35 -8l-23 -217q-150 16 -276 24l-41 -329q213 -68 289.5 -148.5t76.5 -244.5q0 -213 -129 -323t-338 -110h-2l-30 -241q-152 6 -152 14l29 238q-152 18 -254 43l-41 8l27 213q162 -23 299 -31 l45 356q-211 63 -299.5 153.5t-88.5 246.5zM379 993q0 -49 30.5 -79.5t118.5 -63.5l35 285q-184 -9 -184 -142zM602 217q166 18 166 172q0 49 -27.5 81t-99.5 60z" />
<glyph unicode="%" d="M29 1078q0 298 245.5 298t245.5 -298t-245.5 -298t-245.5 298zM229 1079.5q0 -73.5 9.5 -104.5t36 -31t35.5 31t9 104.5t-9 103t-35.5 29.5t-36 -29.5t-9.5 -103zM256 -16l481 1448l150 -54l-481 -1444zM627 273q0 298 245.5 298t245.5 -298t-245.5 -298t-245.5 298z M827 274.5q0 -73.5 9.5 -104.5t36 -31t36 31t9.5 104.5t-9.5 103.5t-36 30t-36 -30t-9.5 -103.5z" />
<glyph unicode="&#x26;" horiz-adv-x="1435" d="M72 401q0 166 69.5 261.5t220.5 154.5q-84 102 -109.5 166t-25.5 166q0 143 115 226t310.5 83t304 -85t108.5 -232.5t-61.5 -231.5t-219.5 -194l201 -201q12 31 24.5 119t14.5 143l266 -6q-31 -238 -104 -416l229 -200l-155 -179l-218 179q-72 -78 -183 -128.5 t-236 -50.5q-299 0 -425 107.5t-126 318.5zM350 424q0 -203 238 -203q82 0 159.5 22.5t108.5 59.5l-354 354q-152 -74 -152 -233zM506 1087q0 -82 92 -180l35 -37q84 61 118.5 109.5t34.5 116.5q0 129 -140 128.5t-140 -137.5z" />
<glyph unicode="'" horiz-adv-x="489" d="M115 1393h264l-14 -492h-242z" />
<glyph unicode="(" horiz-adv-x="630" d="M78 616.5q0 137.5 24.5 282.5t59.5 254q74 229 129 336l24 47h260q-70 -178 -132 -458.5t-62 -461t49 -398.5t98 -345l47 -129h-260q-29 45 -75.5 151.5t-78.5 203t-57.5 238.5t-25.5 279.5z" />
<glyph unicode=")" horiz-adv-x="630" d="M55 -256q70 158 132.5 425t62.5 447.5t-48 410.5t-98 370l-49 139h260q29 -51 76 -167t79 -218t57.5 -249.5t25.5 -285t-24.5 -277.5t-59.5 -243q-74 -215 -129 -309l-25 -43h-260z" />
<glyph unicode="*" horiz-adv-x="870" d="M82 938l215 160l-209 149l94 129l207 -149l80 248l154 -52l-78 -249h252v-152h-252l78 -246l-150 -45l-84 248l-211 -162z" />
<glyph unicode="+" d="M102 391v246h347v344h245v-344h350v-246h-350v-348h-245v348h-347z" />
<glyph unicode="," horiz-adv-x="538" d="M41 -252l102 533h303l-184 -533h-221z" />
<glyph unicode="-" horiz-adv-x="835" d="M115 420v250h606v-250h-606z" />
<glyph unicode="." horiz-adv-x="524" d="M115 0v340h295v-340h-295z" />
<glyph unicode="/" horiz-adv-x="954" d="M63 16l590 1454l242 -86l-590 -1454z" />
<glyph unicode="0" d="M49 668.5q0 371.5 132 539.5t392.5 168t392.5 -168t132 -539.5t-131 -532.5t-393.5 -161t-393.5 161t-131 532.5zM340 674q0 -252 55.5 -352.5t180 -100.5t178 100.5t53.5 353.5t-53.5 354t-179 101t-180 -102t-54.5 -354z" />
<glyph unicode="1" d="M152 1055l446 297h258v-1352h-283v1032l-292 -188z" />
<glyph unicode="2" d="M133 0v240l289 292q154 158 214 242t60 179.5t-50 134t-142 38.5q-137 0 -299 -24l-49 -6l-15 219q201 61 416 61q430 0 430 -387q0 -152 -65.5 -262.5t-237.5 -267.5l-235 -213h561v-246h-877z" />
<glyph unicode="3" d="M117 45l14 215q229 -39 385 -39q211 0 211 180q0 76 -54.5 121t-142.5 45h-272v234h272q68 0 120 57t52 127q0 145 -202 145q-141 0 -301 -28l-54 -8l-18 209q188 74 420.5 73.5t339 -89.5t106.5 -285.5t-157 -301.5q98 -53 140 -110.5t42 -181.5q0 -219 -109.5 -326 t-351.5 -107q-178 0 -375 54z" />
<glyph unicode="4" d="M82 223v215l334 914h311l-362 -883h290v379h283v-379h129v-246h-129v-223h-283v223h-573z" />
<glyph unicode="5" d="M102 51l31 209q248 -39 414 -39q100 0 153.5 53.5t53.5 152.5t-45 144t-127 45q-137 0 -242 -26l-31 -8l-168 39l37 731h826v-246h-605l-34 -293q127 49 241 49q438 0 438 -407q0 -229 -120.5 -354.5t-341.5 -125.5q-94 0 -214 18.5t-192 39.5z" />
<glyph unicode="6" d="M72 690q0 352 145 519t418 167q160 0 342 -45l63 -14l-24 -217q-209 31 -363 30q-291 0 -291 -315l44 12q135 39 215 39q231 0 347.5 -106.5t116.5 -330.5t-131 -339t-379.5 -115t-375.5 181.5t-127 533.5zM360 575q0 -354 230 -354q98 0 151.5 54.5t53.5 152.5 q0 193 -199 193q-96 0 -201 -35z" />
<glyph unicode="7" d="M141 1100v252h865v-320l-482 -1057l-262 74l461 985v66h-582z" />
<glyph unicode="8" d="M57 344q0 131 40 204t130 148q-86 70 -116.5 136.5t-30.5 175.5q0 174 133 271t356.5 97t360.5 -98t137 -272q0 -119 -28.5 -178.5t-118.5 -131.5q90 -76 128.5 -143.5t38.5 -185.5q0 -201 -143 -296.5t-375 -95.5q-512 0 -512 369zM360 410q0 -180 212 -180.5t212 180.5 q0 106 -120 153h-181q-123 -47 -123 -153zM383 956q0 -84 100 -153h181q100 70 100 160q-1 159 -191 159q-92 0 -141 -41t-49 -125z" />
<glyph unicode="9" d="M57 923q0 214 133.5 333.5t378 119.5t373.5 -181t129 -538.5t-143.5 -519.5t-419.5 -162q-160 0 -342 45l-64 15l25 217q209 -31 381 -31q272 0 272 332l-43 -14q-141 -47 -215 -47q-223 0 -344 108.5t-121 322.5zM348 924q0 -187 199 -187q94 0 203 37l32 10 q0 346 -229 346q-98 0 -151.5 -54t-53.5 -152z" />
<glyph unicode=":" horiz-adv-x="524" d="M115 0v340h295v-340h-295zM115 592v340h295v-340h-295z" />
<glyph unicode=";" horiz-adv-x="571" d="M55 -252l103 533h303l-185 -533h-221zM141 592v340h295v-340h-295z" />
<glyph unicode="&#x3c;" d="M135 401v226l832 374v-278l-535 -203l535 -221v-279z" />
<glyph unicode="=" d="M119 178v248h909v-248h-909zM119 604v248h909v-248h-909z" />
<glyph unicode="&#x3e;" d="M180 20v279l535 221l-535 203v278l832 -374v-226z" />
<glyph unicode="?" horiz-adv-x="894" d="M61 1346q176 72 373 71.5t296 -80.5t99 -239.5t-31.5 -232t-134 -149.5t-129 -118t-26.5 -90v-64h-219q-63 70 -64 187q0 74 130 178t160 144t30 98q0 121 -174 120q-123 0 -252 -24l-43 -8zM244 0v330h295v-330h-295z" />
<glyph unicode="@" horiz-adv-x="1992" d="M78 535q0 473 256 715.5t700.5 242.5t671.5 -222t227 -646v-17q0 -315 -99 -461.5t-296 -146.5q-145 0 -238 74q-20 16 -34 41q-164 -115 -303 -115q-193 0 -288 120t-95 387t89 392t302 125q72 0 155 -37l31 -14v26h275v-366q0 -283 11 -324t24.5 -57t27.5 -19.5 t39 -3.5q66 0 95.5 80t29.5 299v19q0 328 -150.5 476t-479.5 148t-503 -188.5t-174 -547.5t156 -530t522 -171l293 18l10 -240q-184 -20 -303 -20q-236 0 -405.5 47t-296.5 156q-250 219 -250 760zM858 514q0 -272 127 -272q78 0 184 51q-12 74 -12 217v252q-86 20 -125 20 q-106 0 -140 -59t-34 -209z" />
<glyph unicode="A" horiz-adv-x="1232" d="M35 0l313 1393h537l313 -1393h-283l-55 252h-487l-56 -252h-282zM426 498h381l-141 661h-99z" />
<glyph unicode="B" horiz-adv-x="1247" d="M152 0v1393h546q217 0 326 -87t109 -282q0 -117 -35 -185.5t-119 -121.5q92 -39 136 -113t44 -205q0 -203 -118.5 -301t-333.5 -98h-555zM434 242h258q92 0 136 36.5t44 135.5q0 168 -180 168h-258v-340zM434 819h252q160 0 160 166t-162 166h-250v-332z" />
<glyph unicode="C" horiz-adv-x="1112" d="M96 696q0 403 114 562t419 159q176 0 407 -55l-8 -225q-200 31 -333 31h-4q-134 0 -191.5 -36t-85 -136.5t-27.5 -342t56.5 -335t225.5 -93.5t359 29l6 -231q-217 -47 -391 -47q-2 0 -4 -1q-171 0 -275 43t-163.5 137.5t-82 221.5t-22.5 319z" />
<glyph unicode="D" horiz-adv-x="1306" d="M152 0v1393h469q180 0 296.5 -38t181 -125t89 -205t24.5 -306.5t-22.5 -312t-85 -221t-181 -141.5t-302.5 -44h-469zM434 246h187q154 0 223 78q60 63 74 229q4 66 4 181.5t-8.5 185t-39 127t-91 79t-162.5 21.5h-187v-901z" />
<glyph unicode="E" horiz-adv-x="1134" d="M152 0v1393h901v-246h-619v-326h496v-241h-496v-334h619v-246h-901z" />
<glyph unicode="F" horiz-adv-x="1087" d="M152 0v1393h888v-246h-606v-410h496v-245h-496v-492h-282z" />
<glyph unicode="G" horiz-adv-x="1251" d="M92 702.5q0 378.5 127 546.5t432 168q190 0 412 -43l74 -14l-9 -219q-242 27 -397 27h-5q-157 -1 -220.5 -37.5t-93 -136t-29.5 -340t59.5 -337t245.5 -96.5l170 8v271h-127v245h406v-722q-293 -47 -475 -48q-326 0 -448 174.5t-122 553z" />
<glyph unicode="H" horiz-adv-x="1386" d="M152 0v1393h282v-570h518v570h283v-1393h-283v578h-518v-578h-282z" />
<glyph unicode="I" horiz-adv-x="585" d="M152 0v1393h282v-1393h-282z" />
<glyph unicode="J" horiz-adv-x="618" d="M39 102q82 0 119 31t37 115v1145h280l2 -1157q0 -223 -99 -301t-339 -78v245z" />
<glyph unicode="K" horiz-adv-x="1214" d="M152 0v1393h282v-613l189 21l229 592h322l-304 -699l312 -694h-326l-233 555l-189 -20v-535h-282z" />
<glyph unicode="L" horiz-adv-x="966" d="M152 0v1393h282v-1143h510v-250h-792z" />
<glyph unicode="M" horiz-adv-x="1757" d="M152 0v1393h481l246 -1016l245 1016h482v-1393h-283v1077h-31l-272 -1016h-283l-272 1016h-31v-1077h-282z" />
<glyph unicode="N" horiz-adv-x="1419" d="M152 0v1393h477l336 -1147h20v1147h283v-1393h-465l-348 1147h-21v-1147h-282z" />
<glyph unicode="O" horiz-adv-x="1335" d="M90 690.5q0 368.5 130 547.5t447.5 179t447.5 -179t130 -547.5t-130 -542t-447.5 -173.5t-447.5 173.5t-130 542zM381 689q0 -253 60.5 -360.5t226.5 -107.5t226 107.5t60 360.5t-61.5 367.5t-225 114.5t-225 -114.5t-61.5 -367.5z" />
<glyph unicode="P" horiz-adv-x="1206" d="M152 0v1393h512q495 0 495 -482q0 -246 -125.5 -375.5t-369.5 -129.5h-230v-406h-282zM434 647h228q210 0 210 260q0 131 -51 187.5t-159 56.5h-228v-504z" />
<glyph unicode="Q" horiz-adv-x="1335" d="M90 689q0 370 130 549t447.5 179t447.5 -179t130 -550q0 -455 -215 -612l170 -275l-258 -120l-184 301q-20 -7 -90 -7q-319 0 -448.5 172t-129.5 542zM381 688q0 -254 60.5 -360.5t226.5 -106.5t226 106.5t60 360.5t-61.5 368.5t-225 114.5t-225 -114.5t-61.5 -368.5z " />
<glyph unicode="R" horiz-adv-x="1269" d="M152 0v1393h540q492 0 492 -459q0 -272 -205 -402l199 -532h-310l-161 467h-273v-467h-282zM434 709h262q101 0 150 61t49 161.5t-52 160t-151 59.5h-258v-442z" />
<glyph unicode="S" horiz-adv-x="1114" d="M76 1004q0 209 127 311t348 102q152 0 385 -41l74 -14l-23 -223q-288 32 -407 32q-223 0 -224 -147q0 -66 55.5 -100.5t259 -100t285.5 -148.5t82 -255q0 -219 -133 -332t-348 -113q-160 0 -391 50l-74 14l29 219q274 -37 420 -37q217 0 217 180q0 66 -50.5 103 t-199.5 80q-238 68 -335 161t-97 259z" />
<glyph unicode="T" horiz-adv-x="1077" d="M27 1143v250h1024v-250h-369v-1143h-283v1143h-372z" />
<glyph unicode="U" horiz-adv-x="1339" d="M139 471v922h283v-928q0 -244 248 -244t248 244v928h282v-922q0 -256 -132 -376t-398.5 -120t-398.5 120t-132 376z" />
<glyph unicode="V" horiz-adv-x="1234" d="M33 1393h297l241 -1147h93l241 1147h297l-323 -1393h-523z" />
<glyph unicode="W" horiz-adv-x="1904" d="M39 1393h297l172 -1151h31l249 1151h328l250 -1151h31l172 1151h297l-267 -1393h-428l-219 1059l-219 -1059h-428z" />
<glyph unicode="X" horiz-adv-x="1165" d="M18 0l402 668l-402 725h314l258 -512l264 512h293l-402 -711l402 -682h-313l-259 481l-264 -481h-293z" />
<glyph unicode="Y" horiz-adv-x="1140" d="M0 1393h313l256 -555l256 555h314l-426 -830v-563h-283v563z" />
<glyph unicode="Z" horiz-adv-x="1089" d="M84 0v244l582 866v37h-582v246h922v-246l-582 -864v-37h582v-246h-922z" />
<glyph unicode="[" horiz-adv-x="727" d="M139 -252v1786h516v-246h-235v-1294h235v-246h-516z" />
<glyph unicode="\" horiz-adv-x="1019" d="M66 1360l233 106l655 -1439l-237 -101z" />
<glyph unicode="]" horiz-adv-x="727" d="M72 -6h235v1294h-235v246h516v-1786h-516v246z" />
<glyph unicode="^" d="M43 641l397 711h234l397 -711h-287l-223 444l-231 -444h-287z" />
<glyph unicode="_" horiz-adv-x="1269" d="M197 -150h876v-233h-876v233z" />
<glyph unicode="`" horiz-adv-x="548" d="M-35 1307l82 235l516 -209l-61 -178z" />
<glyph unicode="a" horiz-adv-x="1046" d="M59 311q0 164 88.5 232.5t270.5 81.5l219 16v64q0 63 -28.5 86.5t-92.5 23.5l-385 -16l-8 190q219 59 417 59q1 0 2 1q196 0 282 -82q87 -82 87 -262v-426q4 -49 16.5 -69t51.5 -28l-8 -207q-104 0 -167 14.5t-126 57.5q-147 -72 -301 -72q-317 0 -318 336zM336 311.5 q0 -112.5 100 -112.5q78 0 170 24l31 8v220l-186 -17q-115 -10 -115 -122.5z" />
<glyph unicode="b" horiz-adv-x="1114" d="M127 4v1430h274v-441q132 56 222 56q215 0 314 -118t99 -421t-114.5 -419t-395.5 -116q-88 0 -321 21zM401 229q94 -8 125 -8q137 0 184.5 66.5t47.5 238.5q0 276 -172 277q-82 0 -156 -21l-29 -6v-547z" />
<glyph unicode="c" horiz-adv-x="913" d="M78 514q0 283 102.5 409t329.5 126q106 0 272 -33l56 -12l-9 -218q-162 16 -239 17q-141 0 -187.5 -60.5t-46.5 -228.5t46.5 -230.5t189.5 -62.5l237 17l9 -220q-219 -43 -334 -43q-227 0 -326.5 128t-99.5 411z" />
<glyph unicode="d" horiz-adv-x="1124" d="M78 513q0 280 105.5 408t318.5 128q66 0 182 -21l39 -8v414h274v-1434h-272v43q-143 -68 -248 -68q-223 0 -311 129t-88 409zM356 516q0 -160 37 -227.5t122 -67.5t179 25l29 6v539q-113 20 -201 20q-166 0 -166 -295z" />
<glyph unicode="e" horiz-adv-x="1044" d="M76 498q0 551 452 551q446 0 447 -463l-21 -191h-602q2 -94 50.5 -136t138.5 -42q190 0 340 12l57 7l4 -203q-236 -57 -426 -58q-231 0 -335.5 123t-104.5 400zM350 604h355q0 123 -39 171t-136.5 48t-137.5 -50t-42 -169z" />
<glyph unicode="f" horiz-adv-x="731" d="M66 791v233h110v45q0 221 65.5 305t233.5 84q74 0 191 -20l43 -6l-4 -224q-90 4 -151.5 4t-82 -32.5t-20.5 -112.5v-43h247v-233h-247v-791h-275v791h-110z" />
<glyph unicode="g" horiz-adv-x="1093" d="M76 -186q0 129 147 243q-76 51 -76 156q0 41 62 135l18 29q-139 100 -139 295.5t118 283.5t312 88q88 0 174 -20l33 -6l330 10v-219l-154 12q45 -70 45 -139q0 -205 -104.5 -282.5t-327.5 -77.5q-47 0 -84 8q-20 -53 -20 -87t34.5 -46.5t163.5 -14.5q258 -2 352.5 -68.5 t94.5 -241.5t-135.5 -259t-365.5 -84t-354 61.5t-124 223.5zM346 -158q0 -88 216 -88t216 103q0 55 -36.5 70.5t-155.5 17.5l-185 14q-55 -56 -55 -117zM362 680.5q0 -150.5 157 -150.5t157 150.5t-157 150.5t-157 -150.5z" />
<glyph unicode="h" horiz-adv-x="1132" d="M127 0v1434h274v-459q141 74 254 74q205 0 283 -121t78 -373v-555h-275v549q0 129 -30.5 191.5t-122.5 62.5q-80 0 -160 -25l-27 -8v-770h-274z" />
<glyph unicode="i" horiz-adv-x="528" d="M127 0v1024h274v-1024h-274zM127 1155v279h274v-279h-274z" />
<glyph unicode="j" horiz-adv-x="530" d="M-70 -268q92 61 129 96t53.5 83t16.5 138v975h274v-977q0 -209 -72.5 -308t-291.5 -210zM129 1155v279h274v-279h-274z" />
<glyph unicode="k" horiz-adv-x="1054" d="M127 0v1434h274v-816l105 19l203 387h307l-266 -485l280 -539h-309l-207 399l-113 -18v-381h-274z" />
<glyph unicode="l" horiz-adv-x="552" d="M139 0v1434h275v-1434h-275z" />
<glyph unicode="m" horiz-adv-x="1718" d="M127 0v1024h272v-57q141 82 242 82q166 0 256 -97q188 96 344 97q203 0 282 -118t79 -376v-555h-275v547q0 131 -28.5 193.5t-114.5 62.5q-66 0 -160 -29l-31 -10q8 -154 8 -234v-530h-274v526q0 152 -26.5 214.5t-116.5 62.5q-80 0 -158 -29l-25 -8v-766h-274z" />
<glyph unicode="n" horiz-adv-x="1132" d="M127 0v1024h272v-57q139 82 256 82q205 0 283 -121t78 -373v-555h-275v547q0 131 -30.5 193.5t-122.5 62.5q-84 0 -162 -29l-25 -8v-766h-274z" />
<glyph unicode="o" horiz-adv-x="1089" d="M74 514q0 254 108.5 394.5t362.5 140.5t362.5 -140.5t108.5 -394.5q0 -539 -471 -539t-471 539zM352 514q0 -158 42 -231.5t150.5 -73.5t150.5 73.5t42 231.5t-42 229.5t-150.5 71.5t-150.5 -71.5t-42 -229.5z" />
<glyph unicode="p" horiz-adv-x="1114" d="M127 -430v1454h272v-55q133 80 234 80q207 0 305 -124t98 -421t-108.5 -413t-356.5 -116q-68 0 -145 13l-25 4v-422h-274zM401 225q92 -12 150 -12q119 0 163 69.5t44 237.5q0 283 -178 283q-76 0 -154 -31l-25 -10v-537z" />
<glyph unicode="q" horiz-adv-x="1112" d="M78 515q0 304 113.5 419t394.5 115q125 0 325 -21l74 -6v-1452h-274v461q-132 -56 -222 -56q-215 0 -313 118t-98 422zM352 498q0 -276 174 -277q82 0 158 21l27 6v555q-76 8 -125 8q-137 0 -185.5 -70.5t-48.5 -242.5z" />
<glyph unicode="r" horiz-adv-x="761" d="M127 0v1024h272v-109q172 102 328 134v-277q-166 -35 -285 -72l-41 -14v-686h-274z" />
<glyph unicode="s" horiz-adv-x="966" d="M74 718q0 163 110.5 246t284.5 83q121 0 330 -39l67 -13l-4 -227q-254 33 -355 33t-131 -18.5t-30 -58.5t40 -55.5t205 -45t234.5 -97t69.5 -219.5q0 -332 -412 -332q-135 0 -327 37l-66 13l8 229q254 -33 353.5 -33t135.5 19.5t36 58.5t-38 57.5t-197 45t-236.5 90 t-77.5 226.5z" />
<glyph unicode="t" horiz-adv-x="745" d="M47 791v233h121v285h274v-285h252v-233h-252v-420q0 -61 3.5 -87t22.5 -44.5t60 -18.5l156 4l12 -219q-137 -31 -209 -31q-184 0 -251.5 83t-67.5 307v426h-121z" />
<glyph unicode="u" horiz-adv-x="1132" d="M117 487v537h274v-541q0 -152 24.5 -207t119.5 -55q90 0 170 29l26 8v766h275v-1024h-273v57q-147 -82 -256 -82q-213 0 -286.5 118t-73.5 394z" />
<glyph unicode="v" horiz-adv-x="1019" d="M31 1024h286l164 -791h58l172 791h278l-246 -1024h-467z" />
<glyph unicode="w" horiz-adv-x="1581" d="M49 1024h270l125 -791h50l155 771h283l155 -771h50l125 791h270l-195 -1024h-430l-116 627l-117 -627h-430z" />
<glyph unicode="x" horiz-adv-x="970" d="M29 0l280 514l-280 510h293l163 -317l166 317h293l-291 -502l291 -522h-293l-166 313l-163 -313h-293z" />
<glyph unicode="y" horiz-adv-x="1024" d="M33 1024h270l186 -791h48l186 791h270l-360 -1454h-268l112 430h-192z" />
<glyph unicode="z" horiz-adv-x="929" d="M84 0v246l430 532h-430v246h760v-246l-430 -532h430v-246h-760z" />
<glyph unicode="{" horiz-adv-x="739" d="M33 535v213q229 53 229 192l-14 260q0 195 93 272.5t325 86.5v-234q-88 -8 -120 -42.5t-32 -102.5l14 -262q0 -129 -42 -184.5t-187 -90.5q143 -35 187 -97.5t44 -193.5l-14 -239q0 -70 29.5 -109t116.5 -45v-233q-227 8 -320.5 88t-93.5 264l14 248q0 150 -229 209z" />
<glyph unicode="|" horiz-adv-x="548" d="M137 -430v1864h275v-1864h-275z" />
<glyph unicode="}" horiz-adv-x="739" d="M74 1325v234q231 -8 324.5 -86t93.5 -273l-15 -260q0 -139 230 -192v-213q-229 -59 -230 -209l15 -248q0 -184 -93.5 -264t-320.5 -88v233q86 6 115.5 45t29.5 109l-14 239q0 131 44 193.5t187 97.5q-145 35 -187 90t-42 185l14 262q0 68 -31.5 102.5t-119.5 42.5z" />
<glyph unicode="~" d="M125 578q129 106 252 106q57 0 212.5 -45t196.5 -45q76 0 181 57l34 21l17 -219q-39 -39 -107.5 -72t-126 -33t-219.5 45t-200.5 45t-95 -20.5t-89.5 -40.5l-35 -19z" />
<glyph unicode="&#xa1;" horiz-adv-x="540" d="M121 -369l29 881h241l27 -881h-297zM123 694v330h295v-330h-295z" />
<glyph unicode="&#xa2;" d="M180 509q0 423 373 454v206h225v-215l162 -26l-8 -205q-162 6 -265.5 6t-155.5 -50t-52 -167t53 -168t182 -51l238 8l8 -205q-80 -20 -162 -26v-224h-225v213q-373 27 -373 450z" />
<glyph unicode="&#xa3;" d="M154 0v233h151v420h-131v234h131v121q0 213 82 289.5t258 76.5q137 0 268 -37l45 -12l-8 -213q-139 16 -268 16q-106 0 -107 -133v-108h314v-234h-314v-420h254l142 33l43 -229l-166 -37h-694z" />
<glyph unicode="&#xa5;" d="M10 1352h314l251 -432l250 432h314l-293 -527h182v-229h-309v-92h309v-230h-309v-274h-283v274h-317v230h317v92h-317v229h188z" />
<glyph unicode="&#xa8;" horiz-adv-x="548" d="M-61 1221v270h262v-270h-262zM381 1221v270h262v-270h-262z" />
<glyph unicode="&#xa9;" horiz-adv-x="1316" d="M94 892q0 249 160 413.5t408.5 164.5t404.5 -168.5t156 -416.5t-156 -413t-403.5 -165t-408.5 168t-161 417zM221 888.5q0 -190.5 125 -324.5t312.5 -134t312.5 133t125 323.5t-126 326t-311.5 135.5t-311.5 -134.5t-126 -325zM414 889.5q0 191.5 62.5 263.5t207.5 72 q80 0 143 -25l21 -6l-14 -176q-72 12 -125.5 12t-68.5 -28.5t-15 -102.5q0 -154 71 -154l138 11l14 -168q-57 -37 -185 -37t-188.5 73.5t-60.5 265z" />
<glyph unicode="&#xab;" horiz-adv-x="1230" d="M82 397v197l457 336v-277l-224 -153l224 -176v-277zM662 397v197l456 336v-277l-223 -153l223 -176v-277z" />
<glyph unicode="&#xad;" horiz-adv-x="835" d="M115 420v250h606v-250h-606z" />
<glyph unicode="&#xae;" horiz-adv-x="1316" d="M94 891q0 250 160 414.5t407.5 164.5t404.5 -168.5t157 -416.5t-155 -413t-403.5 -165t-409.5 167t-161 417zM221 888.5q0 -188.5 127 -323.5t312.5 -135t310.5 134t125 323.5t-126 325t-311.5 135.5t-311.5 -135.5t-126 -324zM412 565v645h278q106 0 170 -52t64 -147.5 t-16.5 -139.5t-61.5 -80l84 -226h-205l-57 191h-60v-191h-196zM606 901h58q70 0 69.5 81t-71.5 81h-56v-162z" />
<glyph unicode="&#xb4;" horiz-adv-x="548" d="M10 1333l516 209l82 -235l-536 -152z" />
<glyph unicode="&#xb8;" horiz-adv-x="540" d="M61 -471l9 160q63 -2 98 -2q78 0 78 53q0 45 -78 45h-49v217h108v-78q131 -2 191.5 -39t60.5 -149.5t-59.5 -171t-167.5 -58.5q-90 0 -162 17z" />
<glyph unicode="&#xbb;" horiz-adv-x="1234" d="M113 47v277l223 176l-223 153v277l456 -336v-197zM692 47v277l223 176l-223 153v277l457 -336v-197z" />
<glyph unicode="&#xbf;" horiz-adv-x="886" d="M59 -73.5q0 159.5 32 232t134.5 149.5t129 118t26.5 90v64h219q63 -70 64 -187q0 -74 -130.5 -178t-160 -144t-29.5 -98q0 -121 174 -120q123 0 252 24l43 8l14 -207q-176 -72 -372.5 -71.5t-296 80.5t-99.5 239.5zM350 694v330h295v-330h-295z" />
<glyph unicode="&#xc0;" horiz-adv-x="1232" d="M35 0l313 1393h537l313 -1393h-283l-55 252h-487l-56 -252h-282zM291 1690l84 245l516 -217l-68 -190zM426 498h381l-141 661h-99z" />
<glyph unicode="&#xc1;" horiz-adv-x="1232" d="M35 0l313 1393h537l313 -1393h-283l-55 252h-487l-56 -252h-282zM332 1718l516 217l84 -245l-533 -162zM426 498h381l-141 661h-99z" />
<glyph unicode="&#xc2;" horiz-adv-x="1232" d="M35 0l313 1393h537l313 -1393h-283l-55 252h-487l-56 -252h-282zM219 1567l275 305h239l275 -305h-291l-101 114l-106 -114h-291zM426 498h381l-141 661h-99z" />
<glyph unicode="&#xc3;" horiz-adv-x="1232" d="M35 0l313 1393h537l313 -1393h-283l-55 252h-487l-56 -252h-282zM207 1757q41 49 105.5 90t125 41t185.5 -47t147 -47q47 0 135 66l31 20l59 -199q-43 -49 -106.5 -89t-116.5 -40t-183 47.5t-157 47.5q-51 0 -137 -66l-29 -20zM426 498h381l-141 661h-99z" />
<glyph unicode="&#xc4;" horiz-adv-x="1232" d="M35 0l313 1393h537l313 -1393h-283l-55 252h-487l-56 -252h-282zM264 1565v270h262v-270h-262zM426 498h381l-141 661h-99zM702 1565v270h263v-270h-263z" />
<glyph unicode="&#xc5;" horiz-adv-x="1232" d="M35 0l305 1356q-31 53 -31 121q0 121 88 189.5t219.5 68.5t219.5 -68.5t88 -189.5q0 -63 -31 -121l305 -1356h-283l-55 252h-487l-56 -252h-282zM426 498h381l-141 661h-99zM496 1478q0 -38 28.5 -60.5t79.5 -24.5h12q55 0 88 22.5t33 61.5t-32.5 61.5t-88 22.5 t-88 -22.5t-32.5 -60.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="1783" d="M25 0l376 1413h1299v-270h-619v-293h496v-268h-496v-312h619v-270h-897v238h-436l-60 -238h-282zM436 510h367l2 633h-205z" />
<glyph unicode="&#xc7;" horiz-adv-x="1112" d="M96 720q0 380 114 538.5t419 158.5q176 0 407 -55l-8 -225q-203 31 -337 30.5t-191.5 -36t-85 -136t-27.5 -342t56.5 -335t225.5 -93.5t359 29l6 -231q-193 -43 -389 -48v-51q131 -2 191.5 -39t60.5 -149.5t-59.5 -171t-167.5 -58.5q-88 0 -162 17l-29 6l8 160 q63 -2 99 -2q78 0 78 53q0 45 -78 45h-49v195q-254 25 -347.5 192.5t-93.5 547.5z" />
<glyph unicode="&#xc8;" horiz-adv-x="1134" d="M152 0v1393h901v-246h-619v-326h496v-241h-496v-334h619v-246h-901zM285 1690l84 245l516 -217l-68 -190z" />
<glyph unicode="&#xc9;" horiz-adv-x="1134" d="M152 0v1393h901v-246h-619v-326h496v-241h-496v-334h619v-246h-901zM315 1718l516 217l84 -245l-532 -162z" />
<glyph unicode="&#xca;" horiz-adv-x="1134" d="M152 0v1393h901v-246h-619v-326h496v-241h-496v-334h619v-246h-901zM215 1567l274 305h240l275 -305h-291l-101 114l-106 -114h-291z" />
<glyph unicode="&#xcb;" horiz-adv-x="1134" d="M152 0v1393h901v-246h-619v-326h496v-241h-496v-334h619v-246h-901zM252 1565v270h262v-270h-262zM690 1565v270h262v-270h-262z" />
<glyph unicode="&#xcc;" horiz-adv-x="585" d="M-49 1690l84 245l516 -217l-68 -190zM152 0v1393h282v-1393h-282z" />
<glyph unicode="&#xcd;" horiz-adv-x="585" d="M14 1718l516 217l84 -245l-532 -162zM152 0v1393h282v-1393h-282z" />
<glyph unicode="&#xce;" horiz-adv-x="585" d="M-102 1567l274 305h240l274 -305h-291l-100 114l-107 -114h-290zM152 0v1393h282v-1393h-282z" />
<glyph unicode="&#xcf;" horiz-adv-x="585" d="M-57 1565v270h262v-270h-262zM152 0v1393h282v-1393h-282zM381 1565v270h262v-270h-262z" />
<glyph unicode="&#xd1;" horiz-adv-x="1419" d="M152 0v1393h477l336 -1147h20v1147h283v-1393h-465l-348 1147h-21v-1147h-282zM305 1757q41 49 105.5 90t125 41t185.5 -47t147 -47q47 0 136 66l30 20l60 -199q-43 -49 -106.5 -89t-117 -40t-183.5 47.5t-157 47.5q-51 0 -137 -66l-28 -20z" />
<glyph unicode="&#xd2;" horiz-adv-x="1335" d="M90 690.5q0 368.5 130 547.5t447.5 179t447.5 -179t130 -547.5t-130 -542t-447.5 -173.5t-447.5 173.5t-130 542zM334 1690l84 245l516 -217l-68 -190zM381 689q0 -253 60.5 -360.5t226.5 -107.5t226 107.5t60 360.5t-61.5 367.5t-225 114.5t-225 -114.5t-61.5 -367.5z " />
<glyph unicode="&#xd3;" horiz-adv-x="1335" d="M90 690.5q0 368.5 130 547.5t447.5 179t447.5 -179t130 -547.5t-130 -542t-447.5 -173.5t-447.5 173.5t-130 542zM342 1718l516 217l84 -245l-532 -162zM381 689q0 -253 60.5 -360.5t226.5 -107.5t226 107.5t60 360.5t-61.5 367.5t-225 114.5t-225 -114.5t-61.5 -367.5z " />
<glyph unicode="&#xd4;" horiz-adv-x="1335" d="M90 690.5q0 368.5 130 547.5t447.5 179t447.5 -179t130 -547.5t-130 -542t-447.5 -173.5t-447.5 173.5t-130 542zM276 1567l275 305h240l274 -305h-291l-100 114l-107 -114h-291zM381 689q0 -253 60.5 -360.5t226.5 -107.5t226 107.5t60 360.5t-61.5 367.5t-225 114.5 t-225 -114.5t-61.5 -367.5z" />
<glyph unicode="&#xd5;" horiz-adv-x="1335" d="M90 690.5q0 368.5 130 547.5t447.5 179t447.5 -179t130 -547.5t-130 -542t-447.5 -173.5t-447.5 173.5t-130 542zM272 1757q41 49 105.5 90t125 41t185.5 -47t148 -47q47 0 135 66l30 20l60 -199q-43 -49 -106.5 -89t-117 -40t-183.5 47.5t-156 47.5q-51 0 -138 -66 l-28 -20zM381 689q0 -253 60.5 -360.5t226.5 -107.5t226 107.5t60 360.5t-61.5 367.5t-225 114.5t-225 -114.5t-61.5 -367.5z" />
<glyph unicode="&#xd6;" horiz-adv-x="1335" d="M90 690.5q0 368.5 130 547.5t447.5 179t447.5 -179t130 -547.5t-130 -542t-447.5 -173.5t-447.5 173.5t-130 542zM317 1565v270h263v-270h-263zM381 689q0 -253 60.5 -360.5t226.5 -107.5t226 107.5t60 360.5t-61.5 367.5t-225 114.5t-225 -114.5t-61.5 -367.5zM756 1565 v270h262v-270h-262z" />
<glyph unicode="&#xd8;" horiz-adv-x="1335" d="M90 688q0 371 130 550t448 179q119 0 206 -24l125 266l205 -88l-133 -287q174 -168 174 -596q0 -367 -130 -540t-447 -173q-100 0 -197 21l-119 -256l-200 100l118 254q-180 160 -180 594zM381 720q0 -222 31 -323l354 760q-49 14 -98 14q-164 0 -225.5 -114.5 t-61.5 -336.5zM580 229q35 -8 88 -8q166 0 226 107.5t60 324.5t-28 320z" />
<glyph unicode="&#xd9;" horiz-adv-x="1339" d="M139 471v922h283v-928q0 -244 248 -244t248 244v928h282v-922q0 -256 -132 -376t-398.5 -120t-398.5 120t-132 376zM358 1690l84 245l516 -217l-67 -190z" />
<glyph unicode="&#xda;" horiz-adv-x="1339" d="M139 471v922h283v-928q0 -244 248 -244t248 244v928h282v-922q0 -256 -132 -376t-398.5 -120t-398.5 120t-132 376zM358 1718l516 217l84 -245l-532 -162z" />
<glyph unicode="&#xdb;" horiz-adv-x="1339" d="M139 471v922h283v-928q0 -244 248 -244t248 244v928h282v-922q0 -256 -132 -376t-398.5 -120t-398.5 120t-132 376zM276 1567l275 305h240l274 -305h-291l-100 114l-107 -114h-291z" />
<glyph unicode="&#xdc;" horiz-adv-x="1339" d="M139 471v922h283v-928q0 -244 248 -244t248 244v928h282v-922q0 -256 -132 -376t-398.5 -120t-398.5 120t-132 376zM319 1565v270h263v-270h-263zM758 1565v270h262v-270h-262z" />
<glyph unicode="&#xdd;" horiz-adv-x="1140" d="M0 1393h313l256 -555l256 555h314l-426 -830v-563h-283v563zM272 1718l516 217l84 -245l-532 -162z" />
<glyph unicode="&#xdf;" horiz-adv-x="1251" d="M127 0v1069q0 209 109.5 299t355.5 90t362.5 -75.5t116.5 -245.5q0 -111 -44 -162t-134 -91t-112.5 -58.5t-22.5 -47t32.5 -53.5t169 -87.5t190.5 -122.5t54 -179q0 -207 -93 -284t-320 -77q-111 0 -267 37l-49 11l8 219q199 -25 275 -25q164 0 164 78q0 47 -31 71.5 t-166 82t-192.5 126t-57.5 173t37 162t127 94t119.5 59t29.5 65.5t-40.5 66t-150.5 23t-153 -36t-43 -118v-1063h-274z" />
<glyph unicode="&#xe0;" horiz-adv-x="1046" d="M59 311q0 164 88.5 232.5t270.5 81.5l219 16v64q0 63 -28.5 86.5t-92.5 23.5l-385 -16l-8 190q219 59 416.5 59.5t284.5 -81.5t87 -262v-426q4 -49 16.5 -69t51.5 -28l-8 -207q-104 0 -167 14.5t-126 57.5q-147 -72 -301 -72q-317 0 -318 336zM211 1307l82 235l516 -209 l-61 -178zM336 311.5q0 -112.5 100 -112.5q78 0 170 24l31 8v220l-186 -17q-115 -10 -115 -122.5z" />
<glyph unicode="&#xe1;" horiz-adv-x="1046" d="M59 311q0 164 88.5 232.5t270.5 81.5l219 16v64q0 63 -28.5 86.5t-92.5 23.5l-385 -16l-8 190q219 59 416.5 59.5t284.5 -81.5t87 -262v-426q4 -49 16.5 -69t51.5 -28l-8 -207q-104 0 -167 14.5t-126 57.5q-147 -72 -301 -72q-317 0 -318 336zM197 1333l516 209l82 -235 l-537 -152zM336 311.5q0 -112.5 100 -112.5q78 0 170 24l31 8v220l-186 -17q-115 -10 -115 -122.5z" />
<glyph unicode="&#xe2;" horiz-adv-x="1046" d="M59 311q0 164 88.5 232.5t270.5 81.5l219 16v64q0 63 -28.5 86.5t-92.5 23.5l-385 -16l-8 190q219 59 416.5 59.5t284.5 -81.5t87 -262v-426q4 -49 16.5 -69t51.5 -28l-8 -207q-104 0 -167 14.5t-126 57.5q-147 -72 -301 -72q-317 0 -318 336zM170 1188l258 309h172 l262 -309h-252l-94 137l-94 -137h-252zM336 311.5q0 -112.5 100 -112.5q78 0 170 24l31 8v220l-186 -17q-115 -10 -115 -122.5z" />
<glyph unicode="&#xe3;" horiz-adv-x="1046" d="M59 311q0 164 88.5 232.5t270.5 81.5l219 16v64q0 63 -28.5 86.5t-92.5 23.5l-385 -16l-8 190q219 59 416.5 59.5t284.5 -81.5t87 -262v-426q4 -49 16.5 -69t51.5 -28l-8 -207q-104 0 -167 14.5t-126 57.5q-147 -72 -301 -72q-317 0 -318 336zM111 1362q41 47 103 85 t108.5 38t141.5 -37t120 -37q49 0 135 51l29 19l59 -156q-12 -14 -32.5 -35.5t-78 -56.5t-103.5 -35t-140.5 37t-120.5 37q-59 0 -135 -47l-27 -17zM336 311.5q0 -112.5 100 -112.5q78 0 170 24l31 8v220l-186 -17q-115 -10 -115 -122.5z" />
<glyph unicode="&#xe4;" horiz-adv-x="1046" d="M59 311q0 164 88.5 232.5t270.5 81.5l219 16v64q0 63 -28.5 86.5t-92.5 23.5l-385 -16l-8 190q219 59 416.5 59.5t284.5 -81.5t87 -262v-426q4 -49 16.5 -69t51.5 -28l-8 -207q-104 0 -167 14.5t-126 57.5q-147 -72 -301 -72q-317 0 -318 336zM133 1221v270h262v-270 h-262zM336 311.5q0 -112.5 100 -112.5q78 0 170 24l31 8v220l-186 -17q-115 -10 -115 -122.5zM575 1221v270h263v-270h-263z" />
<glyph unicode="&#xe5;" horiz-adv-x="1046" d="M59 311q0 164 88.5 232.5t270.5 81.5l219 16v64q0 63 -28.5 86.5t-92.5 23.5l-385 -16l-8 190q219 59 416.5 59.5t284.5 -81.5t87 -262v-426q4 -49 16.5 -69t51.5 -28l-8 -207q-104 0 -167 14.5t-126 57.5q-147 -72 -301 -72q-317 0 -318 336zM303 1305.5 q0 101.5 68.5 171t170 69.5t171 -69.5t69.5 -171t-69.5 -170t-171 -68.5t-170 68.5t-68.5 170zM336 311.5q0 -112.5 100 -112.5q78 0 170 24l31 8v220l-186 -17q-115 -10 -115 -122.5zM451 1307q0 -41 26.5 -68t67.5 -27t66.5 27t25.5 68t-25.5 66.5t-66.5 25.5t-67.5 -25.5 t-26.5 -66.5z" />
<glyph unicode="&#xe6;" horiz-adv-x="1605" d="M59 314.5q0 160.5 89.5 231t267.5 81.5l221 14v51q0 41 -28.5 66.5t-84.5 25.5q-147 0 -321 -16l-66 -6l-8 239q272 45 435 45.5t251 -75.5q106 76 272 76q446 0 447 -461l-21 -193h-602q2 -88 49.5 -127t139.5 -39q139 0 340 15l57 4l4 -213q-236 -57 -435.5 -57.5 t-303.5 98.5l-47 -23q-172 -76 -336 -76t-242 89.5t-78 250zM336 319q0 -109 84 -108q82 0 182 22l35 9l-2 190l-184 -8q-115 -6 -115 -105zM911 604h353q0 109 -40 154t-137.5 45t-136.5 -47.5t-39 -151.5z" />
<glyph unicode="&#xe7;" horiz-adv-x="913" d="M78 529.5q0 267.5 102.5 393.5t329.5 126q106 0 272 -33l56 -12l-9 -218q-162 16 -239 17q-141 0 -187.5 -60.5t-46.5 -228.5t46.5 -230.5t189.5 -62.5l237 17l9 -220q-199 -39 -316 -43v-51q131 -2 191.5 -39t60.5 -149.5t-59.5 -171t-167.5 -58.5q-88 0 -162 17l-29 6 l9 160q63 -2 98 -2q78 0 78 53q0 45 -78 45h-49v197q-178 23 -257 151.5t-79 396z" />
<glyph unicode="&#xe8;" horiz-adv-x="1044" d="M76 498q0 551 452 551q446 0 447 -463l-21 -191h-602q2 -94 50.5 -136t138.5 -42q190 0 340 12l57 7l4 -203q-236 -57 -426 -58q-231 0 -335.5 123t-104.5 400zM225 1307l82 235l516 -209l-61 -178zM350 604h355q0 123 -39 171t-136.5 48t-137.5 -50t-42 -169z" />
<glyph unicode="&#xe9;" horiz-adv-x="1044" d="M76 498q0 551 452 551q446 0 447 -463l-21 -191h-602q2 -94 50.5 -136t138.5 -42q190 0 340 12l57 7l4 -203q-236 -57 -426 -58q-231 0 -335.5 123t-104.5 400zM215 1333l516 209l82 -235l-537 -152zM350 604h355q0 123 -39 171t-136.5 48t-137.5 -50t-42 -169z" />
<glyph unicode="&#xea;" horiz-adv-x="1044" d="M76 498q0 551 452 551q446 0 447 -463l-21 -191h-602q2 -94 50.5 -136t138.5 -42q190 0 340 12l57 7l4 -203q-236 -57 -426 -58q-231 0 -335.5 123t-104.5 400zM182 1188l258 309h172l262 -309h-251l-95 137l-94 -137h-252zM350 604h355q0 123 -39 171t-136.5 48 t-137.5 -50t-42 -169z" />
<glyph unicode="&#xeb;" horiz-adv-x="1044" d="M76 498q0 551 452 551q446 0 447 -463l-21 -191h-602q2 -94 50.5 -136t138.5 -42q190 0 340 12l57 7l4 -203q-236 -57 -426 -58q-231 0 -335.5 123t-104.5 400zM172 1221v270h262v-270h-262zM350 604h355q0 123 -39 171t-136.5 48t-137.5 -50t-42 -169zM614 1221v270h263 v-270h-263z" />
<glyph unicode="&#xec;" horiz-adv-x="528" d="M-121 1307l82 235l516 -209l-61 -178zM127 0v1024h274v-1024h-274z" />
<glyph unicode="&#xed;" horiz-adv-x="528" d="M63 1333l517 209l82 -235l-537 -152zM127 0v1024h274v-1024h-274z" />
<glyph unicode="&#xee;" horiz-adv-x="528" d="M-92 1188l258 309h172l262 -309h-252l-94 137l-94 -137h-252zM127 0v1024h274v-1024h-274z" />
<glyph unicode="&#xef;" horiz-adv-x="528" d="M-98 1221v270h262v-270h-262zM127 0v1024h274v-1024h-274zM344 1221v270h262v-270h-262z" />
<glyph unicode="&#xf1;" horiz-adv-x="1132" d="M127 0v1024h272v-57l39 20q37 20 101.5 41t115.5 21q205 0 283 -121t78 -373v-555h-275v547q0 131 -30.5 193.5t-124.5 62.5t-185 -37v-766h-274zM227 1362q41 47 103.5 85t108.5 38t141.5 -37t119.5 -37q49 0 136 51l28 19l60 -156q-12 -14 -33 -35.5t-78 -56.5 t-103 -35t-140.5 37t-123.5 37q-57 0 -133 -47l-26 -17z" />
<glyph unicode="&#xf2;" horiz-adv-x="1089" d="M74 514q0 254 108.5 394.5t362.5 140.5t362.5 -140.5t108.5 -394.5q0 -539 -471 -539t-471 539zM252 1307l82 235l516 -209l-62 -178zM352 514q0 -158 42 -231.5t150.5 -73.5t150.5 73.5t42 231.5t-42 229.5t-150.5 71.5t-150.5 -71.5t-42 -229.5z" />
<glyph unicode="&#xf3;" horiz-adv-x="1089" d="M74 514q0 254 108.5 394.5t362.5 140.5t362.5 -140.5t108.5 -394.5q0 -539 -471 -539t-471 539zM238 1333l516 209l82 -235l-537 -152zM352 514q0 -158 42 -231.5t150.5 -73.5t150.5 73.5t42 231.5t-42 229.5t-150.5 71.5t-150.5 -71.5t-42 -229.5z" />
<glyph unicode="&#xf4;" horiz-adv-x="1089" d="M74 514q0 254 108.5 394.5t362.5 140.5t362.5 -140.5t108.5 -394.5q0 -539 -471 -539t-471 539zM186 1188l258 309h172l263 -309h-252l-95 137l-94 -137h-252zM352 514q0 -158 42 -231.5t150.5 -73.5t150.5 73.5t42 231.5t-42 229.5t-150.5 71.5t-150.5 -71.5t-42 -229.5 z" />
<glyph unicode="&#xf5;" horiz-adv-x="1089" d="M74 514q0 254 108.5 394.5t362.5 140.5t362.5 -140.5t108.5 -394.5q0 -539 -471 -539t-471 539zM186 1362q41 47 103.5 85t108.5 38t141.5 -37t119.5 -37q49 0 136 51l28 19l60 -156q-12 -14 -33 -35.5t-78 -56.5t-103 -35t-140.5 37t-122.5 37q-57 0 -134 -47l-26 -17z M352 514q0 -158 42 -231.5t150.5 -73.5t150.5 73.5t42 231.5t-42 229.5t-150.5 71.5t-150.5 -71.5t-42 -229.5z" />
<glyph unicode="&#xf6;" horiz-adv-x="1089" d="M74 514q0 254 108.5 394.5t362.5 140.5t362.5 -140.5t108.5 -394.5q0 -539 -471 -539t-471 539zM190 1221v270h263v-270h-263zM352 514q0 -158 42 -231.5t150.5 -73.5t150.5 73.5t42 231.5t-42 229.5t-150.5 71.5t-150.5 -71.5t-42 -229.5zM633 1221v270h262v-270h-262z " />
<glyph unicode="&#xf8;" horiz-adv-x="1089" d="M74 514q0 254 108.5 394.5t362.5 140.5q57 0 114 -11l82 205l166 -61l-84 -201q193 -121 193 -467q0 -539 -471 -539q-70 0 -129 13l-84 -209l-164 61l88 211q-182 123 -182 463zM352 528.5q0 -143.5 25 -209.5l194 494l-26 2q-109 0 -151 -71.5t-42 -215zM504 211 q12 -2 41 -2q109 0 150.5 73.5t41.5 219t-28 215.5z" />
<glyph unicode="&#xf9;" horiz-adv-x="1132" d="M117 487v537h274v-541q0 -152 24.5 -207t119.5 -55q90 0 170 29l26 8v766h275v-1024h-273v57q-147 -82 -256 -82q-213 0 -286.5 118t-73.5 394zM201 1307l82 235l516 -209l-62 -178z" />
<glyph unicode="&#xfa;" horiz-adv-x="1132" d="M117 487v537h274v-541q0 -152 24.5 -207t123 -55t192.5 37v766h275v-1024h-273v57l-41 -20q-39 -20 -102.5 -41t-112.5 -21q-213 0 -286.5 118t-73.5 394zM266 1333l516 209l82 -235l-536 -152z" />
<glyph unicode="&#xfb;" horiz-adv-x="1132" d="M117 487v537h274v-541q0 -152 24.5 -207t123 -55t192.5 37v766h275v-1024h-273v57l-41 -20q-39 -20 -102.5 -41t-112.5 -21q-213 0 -286.5 118t-73.5 394zM168 1188l258 309h172l262 -309h-252l-94 137l-94 -137h-252z" />
<glyph unicode="&#xfc;" horiz-adv-x="1132" d="M117 487v537h274v-541q0 -152 24.5 -207t123 -55t192.5 37v766h275v-1024h-273v57l-41 -20q-39 -20 -102.5 -41t-112.5 -21q-213 0 -286.5 118t-73.5 394zM217 1221v270h262v-270h-262zM659 1221v270h263v-270h-263z" />
<glyph unicode="&#xfd;" horiz-adv-x="1024" d="M33 1024h270l186 -791h48l186 791h270l-360 -1454h-268l112 430h-192zM250 1333l516 209l82 -235l-537 -152z" />
<glyph unicode="&#xff;" horiz-adv-x="1024" d="M33 1024h270l186 -791h48l186 791h270l-360 -1454h-268l112 430h-192zM162 1221v270h262v-270h-262zM604 1221v270h262v-270h-262z" />
<glyph unicode="&#x100;" horiz-adv-x="1232" d="M35 0l313 1393h537l313 -1393h-283l-55 252h-487l-56 -252h-282zM254 1602v200h711v-200h-711zM426 498h381l-141 661h-99z" />
<glyph unicode="&#x101;" horiz-adv-x="1046" d="M59 311q0 164 88.5 232.5t270.5 81.5l219 16v64q0 63 -28.5 86.5t-92.5 23.5l-385 -16l-8 190q219 59 416.5 59.5t284.5 -81.5t87 -262v-426q4 -49 16.5 -69t51.5 -28l-8 -207q-104 0 -167 14.5t-126 57.5q-147 -72 -301 -72q-317 0 -318 336zM219 1223v204h588v-204 h-588zM336 311.5q0 -112.5 100 -112.5q78 0 170 24l31 8v220l-186 -17q-115 -10 -115 -122.5z" />
<glyph unicode="&#x102;" horiz-adv-x="1232" d="M35 0l313 1393h537l313 -1393h-283l-55 252h-487l-56 -252h-282zM227 1864h248q8 -52 43 -84.5t91.5 -32.5t92 32.5t41.5 84.5h248q-16 -139 -111.5 -224.5t-271.5 -85.5t-270 85t-111 225zM426 498h381l-141 661h-99z" />
<glyph unicode="&#x103;" horiz-adv-x="1048" d="M59 314.5q0 160.5 87 230t270 82.5l221 14v51q0 41 -28.5 66.5t-73.5 25.5q-152 0 -398 -16l-8 233l72 11q219 35 350 35q180 0 270 -81t90 -272v-379q0 -59 12.5 -78.5t53.5 -27.5l-8 -234q-123 0 -178.5 15.5t-112.5 54.5l-47 -16q-141 -53 -266 -54q-150 0 -228 89.5 t-78 250zM121 1532h285q14 -90 113 -90t112 90h284q-16 -154 -115.5 -244t-281.5 -90t-281.5 90t-115.5 244zM336 319q0 -108 84 -108t217 31v190l-186 -8q-115 -6 -115 -105z" />
<glyph unicode="&#x104;" horiz-adv-x="1232" d="M35 0l313 1393h537l313 -1393h-37q-115 -96 -114.5 -164.5t65.5 -68.5l86 8l25 -195q-121 -23 -214.5 -22.5t-155.5 53.5t-62 143q0 139 135 246h-11l-55 252h-487l-56 -252h-282zM426 498h381l-141 661h-99z" />
<glyph unicode="&#x105;" horiz-adv-x="1044" d="M59 311q0 164 88.5 232.5t270.5 81.5l219 16v64q0 63 -28.5 86.5t-92.5 23.5l-385 -16l-8 190q219 59 416.5 59.5t284.5 -81.5t87 -262v-426q4 -49 16.5 -69t51.5 -28l-8 -207l-78 2q-90 -84 -90 -143q0 -68 65 -67l86 8l25 -195q-121 -23 -214 -22.5t-155.5 53.5 t-62.5 135t42 148.5t83 97.5l41 31l6 -3l-41 27q-147 -72 -301 -72q-317 0 -318 336zM336 311.5q0 -112.5 100 -112.5q78 0 170 24l31 8v220l-186 -17q-115 -10 -115 -122.5z" />
<glyph unicode="&#x106;" horiz-adv-x="1112" d="M96 696q0 403 114 562t419 159q176 0 407 -55l-8 -225q-203 31 -337 30.5t-191.5 -36t-85 -136t-27.5 -342t56.5 -335t225.5 -93.5t359 29l6 -231q-217 -47 -391 -47.5t-278.5 42.5t-164 137.5t-82 221.5t-22.5 319zM301 1718l516 217l84 -245l-532 -162z" />
<glyph unicode="&#x107;" horiz-adv-x="913" d="M78 514q0 283 102.5 409t329.5 126q106 0 272 -33l56 -12l-9 -218q-162 16 -239 17q-141 0 -187.5 -60.5t-46.5 -228.5t46.5 -230.5t189.5 -62.5l237 17l9 -220q-219 -43 -334 -43q-227 0 -326.5 128t-99.5 411zM199 1333l516 209l82 -235l-537 -152z" />
<glyph unicode="&#x108;" horiz-adv-x="1112" d="M96 696q0 403 114 562t419 159q176 0 407 -55l-8 -225q-203 31 -337 30.5t-191.5 -36t-85 -136t-27.5 -342t56.5 -335t225.5 -93.5t359 29l6 -231q-217 -47 -391 -47.5t-278.5 42.5t-164 137.5t-82 221.5t-22.5 319zM219 1567l275 305h239l275 -305h-291l-101 114 l-106 -114h-291z" />
<glyph unicode="&#x109;" horiz-adv-x="913" d="M78 514q0 283 102.5 409t329.5 126q106 0 272 -33l56 -12l-9 -218q-162 16 -239 17q-141 0 -187.5 -60.5t-46.5 -228.5t46.5 -230.5t189.5 -62.5l237 17l9 -220q-219 -43 -334 -43q-227 0 -326.5 128t-99.5 411zM111 1188l258 309h172l262 -309h-252l-94 137l-95 -137 h-251z" />
<glyph unicode="&#x10a;" horiz-adv-x="1112" d="M96 696q0 403 114 562t419 159q176 0 407 -55l-8 -225q-203 31 -337 30.5t-191.5 -36t-85 -136t-27.5 -342t56.5 -335t225.5 -93.5t359 29l6 -231q-217 -47 -391 -47.5t-278.5 42.5t-164 137.5t-82 221.5t-22.5 319zM449 1556v277h274v-277h-274z" />
<glyph unicode="&#x10b;" horiz-adv-x="913" d="M78 514q0 283 102.5 409t329.5 126q106 0 272 -33l56 -12l-9 -218q-162 16 -239 17q-141 0 -187.5 -60.5t-46.5 -228.5t46.5 -230.5t189.5 -62.5l237 17l9 -220q-219 -43 -334 -43q-227 0 -326.5 128t-99.5 411zM387 1139v276h275v-276h-275z" />
<glyph unicode="&#x10c;" horiz-adv-x="1112" d="M96 696q0 403 114 562t419 159q176 0 407 -55l-8 -225q-203 31 -337 30.5t-191.5 -36t-85 -136t-27.5 -342t56.5 -335t225.5 -93.5t359 29l6 -231q-217 -47 -391 -47.5t-278.5 42.5t-164 137.5t-82 221.5t-22.5 319zM238 1872h290l107 -115l100 115h291l-274 -305h-240z " />
<glyph unicode="&#x10d;" horiz-adv-x="913" d="M78 514q0 283 102.5 409t329.5 126q106 0 272 -33l56 -12l-9 -218q-162 16 -239 17q-141 0 -187.5 -60.5t-46.5 -228.5t46.5 -230.5t189.5 -62.5l237 17l9 -220q-219 -43 -334 -43q-227 0 -326.5 128t-99.5 411zM150 1497h251l95 -137l94 137h252l-262 -309h-172z" />
<glyph unicode="&#x10e;" horiz-adv-x="1306" d="M152 0v1393h469q180 0 296.5 -38t181 -125t89 -205t24.5 -306.5t-22.5 -312t-85 -221t-181 -141.5t-302.5 -44h-469zM229 1872h291l107 -115l100 115h291l-275 -305h-239zM434 246h187q154 0 223 78q60 63 74 229q4 66 4 181.5t-8.5 185t-39 127t-91 79t-162.5 21.5h-187 v-901z" />
<glyph unicode="&#x10f;" horiz-adv-x="1359" d="M78 513q0 280 105.5 408t318.5 128q66 0 182 -21l39 -8v414h274v-1434h-272v43q-143 -68 -248 -68q-223 0 -311 129t-88 409zM356 516q0 -160 37 -227.5t122 -67.5t179 25l29 6v539q-113 20 -201 20q-166 0 -166 -295zM1114 909l94 480h267l-113 -480h-248z" />
<glyph unicode="&#x110;" horiz-adv-x="1314" d="M51 567v271h111v575h465q180 0 296.5 -39t181 -128t89.5 -208.5t25 -309t-22.5 -314.5t-85 -224.5t-181.5 -144.5t-303 -45h-465v567h-111zM440 268h187q154 0 225 78q76 84 76 379q0 88 -3 139t-13.5 102.5t-31 81t-53.5 54.5q-63 43 -200 43h-187v-307h267v-271h-267 v-299z" />
<glyph unicode="&#x111;" horiz-adv-x="1124" d="M78 513q0 280 105.5 408t318.5 128q66 0 182 -21l39 -8v254h-459v237h670v-77h63v-1434h-272v43q-143 -68 -248 -68q-223 0 -311 129t-88 409zM356 516q0 -160 37 -227.5t122 -67.5t179 25l29 6v539q-113 20 -201 20q-166 0 -166 -295z" />
<glyph unicode="&#x112;" horiz-adv-x="1134" d="M152 0v1393h901v-246h-619v-326h496v-241h-496v-334h619v-246h-901zM238 1602v200h710v-200h-710z" />
<glyph unicode="&#x113;" horiz-adv-x="1044" d="M76 498q0 551 452 551q446 0 447 -463l-21 -191h-602q2 -94 50.5 -136t138.5 -42q190 0 340 12l57 7l4 -203q-236 -57 -426 -58q-231 0 -335.5 123t-104.5 400zM231 1223v204h588v-204h-588zM350 604h355q0 123 -39 171t-136.5 48t-137.5 -50t-42 -169z" />
<glyph unicode="&#x114;" horiz-adv-x="1134" d="M152 0v1393h901v-246h-619v-326h496v-241h-496v-334h619v-246h-901zM233 1864h248q8 -52 43 -84.5t91.5 -32.5t92 32.5t42.5 84.5h247q-16 -139 -111 -224.5t-271.5 -85.5t-270.5 85t-111 225z" />
<glyph unicode="&#x115;" horiz-adv-x="1044" d="M76 498q0 551 452 551q446 0 447 -463l-21 -191h-602q2 -94 50.5 -136t138.5 -42q190 0 340 12l57 7l4 -203q-236 -57 -426 -58q-231 0 -335.5 123t-104.5 400zM193 1495h206q8 -63 42 -99t87 -36q115 0 129 135h207q-16 -133 -103 -227t-232.5 -94t-232.5 94t-103 227z M350 604h355q0 123 -39 171t-136.5 48t-137.5 -50t-42 -169z" />
<glyph unicode="&#x116;" horiz-adv-x="1134" d="M152 0v1393h901v-246h-619v-326h496v-241h-496v-334h619v-246h-901zM477 1571v276h275v-276h-275z" />
<glyph unicode="&#x117;" horiz-adv-x="1044" d="M76 498q0 551 452 551q446 0 447 -463l-21 -191h-602q2 -94 50.5 -136t138.5 -42q190 0 340 12l57 7l4 -203q-236 -57 -426 -58q-231 0 -335.5 123t-104.5 400zM350 604h355q0 123 -39 171t-136.5 48t-137.5 -50t-42 -169zM389 1139v276h275v-276h-275z" />
<glyph unicode="&#x118;" horiz-adv-x="1134" d="M152 0v1393h901v-246h-619v-326h496v-241h-496v-334h619v-246h-58q-115 -96 -114.5 -164.5t65.5 -68.5l86 8l25 -195q-121 -23 -214 -22.5t-155.5 53.5t-62.5 143q0 139 135 246h-608z" />
<glyph unicode="&#x119;" horiz-adv-x="1046" d="M76 498q0 551 452 551q446 0 447 -463l-21 -191h-602q2 -94 50.5 -136t138.5 -42q190 0 340 12l57 7l4 -203q-61 -14 -137 -29l29 -2q-119 -96 -119 -165.5t65 -69.5l86 8l25 -195q-121 -23 -214 -22.5t-155.5 53.5t-62.5 143q0 121 106 223q-16 -2 -49 -2 q-231 0 -335.5 123t-104.5 400zM350 604h355q0 123 -39 171t-136.5 48t-137.5 -50t-42 -169z" />
<glyph unicode="&#x11a;" horiz-adv-x="1134" d="M152 0v1393h901v-246h-619v-326h496v-241h-496v-334h619v-246h-901zM178 1872h291l106 -115l101 115h291l-275 -305h-239z" />
<glyph unicode="&#x11b;" horiz-adv-x="1044" d="M76 498q0 551 452 551q446 0 447 -463l-21 -191h-602q2 -94 50.5 -136t138.5 -42q190 0 340 12l57 7l4 -203q-236 -57 -426 -58q-231 0 -335.5 123t-104.5 400zM174 1497h252l94 -137l94 137h252l-262 -309h-172zM350 604h355q0 123 -39 171t-136.5 48t-137.5 -50 t-42 -169z" />
<glyph unicode="&#x11c;" horiz-adv-x="1251" d="M92 702.5q0 378.5 127 546.5t432 168q190 0 412 -43l74 -14l-9 -219q-246 27 -402.5 26.5t-220 -37t-93 -136t-29.5 -340t59.5 -337t245.5 -96.5l170 8v271h-127v245h406v-722q-293 -47 -475 -48q-326 0 -448 174.5t-122 553zM279 1567l274 305h240l274 -305h-291 l-100 114l-107 -114h-290z" />
<glyph unicode="&#x11d;" horiz-adv-x="1093" d="M76 -186q0 129 147 243q-76 51 -76 156q0 41 62 135l18 29q-139 100 -139 295.5t118 283.5t312 88q88 0 174 -20l33 -6l330 10v-219l-154 12q45 -70 45 -139q0 -205 -104.5 -282.5t-327.5 -77.5q-47 0 -84 8q-20 -53 -20 -87t34.5 -46.5t163.5 -14.5q258 -2 352.5 -68.5 t94.5 -241.5t-135.5 -259t-365.5 -84t-354 61.5t-124 223.5zM211 1188l258 309h172l262 -309h-252l-94 137l-94 -137h-252zM346 -158q0 -88 216 -88t216 103q0 55 -36.5 70.5t-155.5 17.5l-185 14q-55 -56 -55 -117zM362 680.5q0 -150.5 157 -150.5t157 150.5t-157 150.5 t-157 -150.5z" />
<glyph unicode="&#x11e;" horiz-adv-x="1251" d="M92 702.5q0 378.5 127 546.5t432 168q190 0 412 -43l74 -14l-9 -219q-246 27 -402.5 26.5t-220 -37t-93 -136t-29.5 -340t59.5 -337t245.5 -96.5l170 8v271h-127v245h406v-722q-293 -47 -475 -48q-326 0 -448 174.5t-122 553zM279 1864h247q9 -52 43.5 -84.5t91 -32.5 t92 32.5t42.5 84.5h247q-16 -139 -111 -224.5t-271.5 -85.5t-270.5 85t-110 225z" />
<glyph unicode="&#x11f;" horiz-adv-x="1093" d="M76 -186q0 129 147 243q-76 51 -76 156q0 41 62 135l18 29q-139 100 -139 295.5t118 283.5t312 88q88 0 174 -20l33 -6l330 10v-219l-154 12q45 -70 45 -139q0 -205 -104.5 -282.5t-327.5 -77.5q-47 0 -84 8q-20 -53 -20 -87t34.5 -46.5t163.5 -14.5q258 -2 352.5 -68.5 t94.5 -241.5t-135.5 -259t-365.5 -84t-354 61.5t-124 223.5zM219 1495h207q8 -63 42 -99t87 -36q115 0 129 135h207q-16 -133 -103.5 -227t-232.5 -94t-232 94t-104 227zM346 -158q0 -88 216 -88t216 103q0 55 -36.5 70.5t-155.5 17.5l-185 14q-55 -56 -55 -117zM362 680.5 q0 -150.5 157 -150.5t157 150.5t-157 150.5t-157 -150.5z" />
<glyph unicode="&#x120;" horiz-adv-x="1251" d="M92 702.5q0 378.5 127 546.5t432 168q190 0 412 -43l74 -14l-9 -219q-246 27 -402.5 26.5t-220 -37t-93 -136t-29.5 -340t59.5 -337t245.5 -96.5l170 8v271h-127v245h406v-722q-293 -47 -475 -48q-326 0 -448 174.5t-122 553zM502 1571v276h274v-276h-274z" />
<glyph unicode="&#x121;" horiz-adv-x="1093" d="M76 -186q0 129 147 243q-76 51 -76 156q0 41 62 135l18 29q-139 100 -139 295.5t118 283.5t312 88q88 0 174 -20l33 -6l330 10v-219l-154 12q45 -70 45 -139q0 -205 -104.5 -282.5t-327.5 -77.5q-47 0 -84 8q-20 -53 -20 -87t34.5 -46.5t163.5 -14.5q258 -2 352.5 -68.5 t94.5 -241.5t-135.5 -259t-365.5 -84t-354 61.5t-124 223.5zM346 -158q0 -88 216 -88t216 103q0 55 -36.5 70.5t-155.5 17.5l-185 14q-55 -56 -55 -117zM362 680.5q0 -150.5 157 -150.5t157 150.5t-157 150.5t-157 -150.5zM416 1139v276h274v-276h-274z" />
<glyph unicode="&#x122;" horiz-adv-x="1251" d="M92 702.5q0 378.5 127 546.5t432 168q190 0 412 -43l74 -14l-9 -219q-246 27 -402.5 26.5t-220 -37t-93 -136t-29.5 -340t59.5 -337t245.5 -96.5l170 8v271h-127v245h406v-722q-293 -47 -475 -48q-326 0 -448 174.5t-122 553zM451 -637l94 479h266l-113 -479h-247z" />
<glyph unicode="&#x123;" horiz-adv-x="1093" d="M76 -186q0 129 147 243q-76 51 -76 156q0 41 62 135l18 29q-139 100 -139 295.5t118 283.5t312 88q88 0 174 -20l33 -6l330 10v-219l-154 12q45 -70 45 -139q0 -205 -104.5 -282.5t-327.5 -77.5q-47 0 -84 8q-20 -53 -20 -87t34.5 -46.5t163.5 -14.5q258 -2 352.5 -68.5 t94.5 -241.5t-135.5 -259t-365.5 -84t-354 61.5t-124 223.5zM346 -158q0 -88 216 -88t216 103q0 55 -36.5 70.5t-155.5 17.5l-185 14q-55 -56 -55 -117zM348 1194l113 479h248l-95 -479h-266zM362 680.5q0 -150.5 157 -150.5t157 150.5t-157 150.5t-157 -150.5z" />
<glyph unicode="&#x124;" horiz-adv-x="1386" d="M152 0v1393h282v-570h518v570h283v-1393h-283v578h-518v-578h-282zM299 1567l274 305h240l274 -305h-290l-101 114l-106 -114h-291z" />
<glyph unicode="&#x125;" horiz-adv-x="1132" d="M127 0v1434h274v-459q141 74 254 74q205 0 283 -121t78 -373v-555h-275v549q0 129 -30.5 191.5t-122.5 62.5q-80 0 -160 -25l-27 -8v-770h-274zM219 1536l258 309h172l262 -309h-252l-94 137l-94 -137h-252z" />
<glyph unicode="&#x126;" horiz-adv-x="1402" d="M39 995v238h121v160h282v-160h519v160h282v-160h135v-238h-135v-995h-282v578h-519v-578h-282v995h-121zM442 823h519v172h-519v-172z" />
<glyph unicode="&#x127;" horiz-adv-x="1132" d="M25 1090v237h102v107h274v-107h293v-237h-293v-115q141 74 254 74q205 0 283 -121t78 -373v-555h-275v549q0 129 -30.5 191.5t-122.5 62.5q-80 0 -160 -25l-27 -8v-770h-274v1090h-102z" />
<glyph unicode="&#x128;" horiz-adv-x="585" d="M-104 1757q41 49 105.5 90t124.5 41t185 -47t148 -47q47 0 135 66l31 20l59 -199q-43 -49 -106.5 -89t-116.5 -40t-183.5 47.5t-156.5 47.5q-51 0 -137 -66l-29 -20zM152 0v1393h282v-1393h-282z" />
<glyph unicode="&#x129;" horiz-adv-x="528" d="M-78 1362q41 47 103.5 85t108.5 38t141.5 -37t119.5 -37q49 0 135 51l29 19l59 -156q-12 -14 -32.5 -35.5t-77.5 -56.5t-103.5 -35t-140.5 37t-121 37q-59 0 -135 -47l-26 -17zM127 0v1024h274v-1024h-274z" />
<glyph unicode="&#x12a;" horiz-adv-x="585" d="M-55 1602v200h710v-200h-710zM152 0v1393h282v-1393h-282z" />
<glyph unicode="&#x12b;" horiz-adv-x="528" d="M-29 1223v204h588v-204h-588zM127 0v1024h274v-1024h-274z" />
<glyph unicode="&#x12c;" horiz-adv-x="585" d="M-53 1864h248q8 -52 42.5 -84.5t91 -32.5t92.5 32.5t42 84.5h248q-16 -139 -111.5 -224.5t-271.5 -85.5t-270.5 85t-110.5 225zM152 0v1393h282v-1393h-282z" />
<glyph unicode="&#x12d;" horiz-adv-x="528" d="M-72 1495h207q8 -63 42 -99t87 -36q115 0 129 135h207q-16 -133 -103 -227t-232.5 -94t-232.5 94t-104 227zM127 0v1024h274v-1024h-274z" />
<glyph unicode="&#x12e;" horiz-adv-x="585" d="M41 -246q0 135 139 246h-28v1393h282v-1393q-49 -31 -93 -82t-44 -84q0 -68 65 -67l87 8l24 -195q-121 -23 -214 -22.5t-155.5 53.5t-62.5 143z" />
<glyph unicode="&#x12f;" horiz-adv-x="528" d="M8 -246q0 137 144 246h-25v1024h274v-1024q-49 -31 -93 -82t-44 -84q0 -68 66 -67l86 8l24 -195q-121 -23 -214 -22.5t-155.5 53.5t-62.5 143zM127 1155v279h274v-279h-274z" />
<glyph unicode="&#x130;" horiz-adv-x="585" d="M152 0v1393h282v-1393h-282zM156 1571v276h274v-276h-274z" />
<glyph unicode="&#x131;" horiz-adv-x="528" d="M127 0v1024h274v-1024h-274z" />
<glyph unicode="&#x134;" horiz-adv-x="618" d="M-59 1567l274 305h240l274 -305h-291l-100 114l-107 -114h-290zM39 102q82 0 119 31t37 115v1145h280l2 -1157q0 -223 -99 -301t-339 -78v245z" />
<glyph unicode="&#x135;" horiz-adv-x="530" d="M-76 1188l258 309h172l262 -309h-251l-95 137l-94 -137h-252zM-70 -268q92 61 129 96t53.5 83t16.5 138v975h274v-977q0 -209 -72.5 -308t-291.5 -210z" />
<glyph unicode="&#x136;" horiz-adv-x="1210" d="M152 0v1413h282v-606l184 20l230 586h326l-304 -702l312 -711h-326l-233 555l-189 -20v-535h-282zM408 -637l94 479h266l-113 -479h-247z" />
<glyph unicode="&#x137;" horiz-adv-x="1054" d="M-20 -637l94 479h266l-113 -479h-247zM127 0v1434h274v-816l105 19l203 387h307l-266 -485l280 -539h-309l-207 399l-113 -18v-381h-274z" />
<glyph unicode="&#x139;" horiz-adv-x="968" d="M152 0v1413h282v-1141h510v-272h-792zM225 1718l516 217l84 -245l-532 -162z" />
<glyph unicode="&#x13a;" horiz-adv-x="552" d="M84 1763l516 209l82 -235l-537 -152zM139 0v1434h275v-1434h-275z" />
<glyph unicode="&#x13b;" horiz-adv-x="966" d="M152 0v1393h282v-1143h510v-250h-792zM324 -637l94 479h266l-113 -479h-247z" />
<glyph unicode="&#x13c;" horiz-adv-x="552" d="M41 -637l94 479h266l-112 -479h-248zM139 0v1434h275v-1434h-275z" />
<glyph unicode="&#x13d;" horiz-adv-x="1019" d="M152 0v1393h282v-1143h510v-250h-792zM678 881v532h266v-532h-266z" />
<glyph unicode="&#x13e;" horiz-adv-x="772" d="M139 0v1434h275v-1434h-275zM526 909l95 480h266l-113 -480h-248z" />
<glyph unicode="&#x141;" horiz-adv-x="985" d="M-49 569l219 154v670h283v-473l202 143l133 -184l-335 -236v-393h510v-250h-793v444l-86 -59z" />
<glyph unicode="&#x142;" horiz-adv-x="780" d="M6 559l238 166v709h274v-516l129 90l133 -185l-262 -184v-639h-274v446l-105 -71z" />
<glyph unicode="&#x143;" horiz-adv-x="1419" d="M152 0v1393h477l336 -1147h20v1147h283v-1393h-465l-348 1147h-21v-1147h-282zM414 1718l516 217l84 -245l-533 -162z" />
<glyph unicode="&#x144;" horiz-adv-x="1132" d="M127 0v1024h272v-57l39 20q37 20 101.5 41t115.5 21q205 0 283 -121t78 -373v-555h-275v547q0 131 -30.5 193.5t-124.5 62.5t-185 -37v-766h-274zM299 1333l516 209l82 -235l-537 -152z" />
<glyph unicode="&#x145;" horiz-adv-x="1419" d="M152 0v1393h477l336 -1147h20v1147h283v-1393h-465l-348 1147h-21v-1147h-282zM518 -637l94 479h267l-113 -479h-248z" />
<glyph unicode="&#x146;" horiz-adv-x="1132" d="M-20 -637l94 479h266l-113 -479h-247zM127 0v1024h272v-57q139 82 256 82q205 0 283 -121t78 -373v-555h-275v547q0 131 -30.5 193.5t-122.5 62.5q-84 0 -162 -29l-25 -8v-766h-274z" />
<glyph unicode="&#x147;" horiz-adv-x="1419" d="M152 0v1393h477l336 -1147h20v1147h283v-1393h-465l-348 1147h-21v-1147h-282zM317 1872h291l107 -115l100 115h291l-275 -305h-239z" />
<glyph unicode="&#x148;" horiz-adv-x="1132" d="M127 0v1024h272v-57q139 82 256 82q205 0 283 -121t78 -373v-555h-275v547q0 131 -30.5 193.5t-122.5 62.5q-84 0 -162 -29l-25 -8v-766h-274zM215 1497h252l94 -137l94 137h252l-262 -309h-172z" />
<glyph unicode="&#x14a;" horiz-adv-x="1419" d="M152 0v1393h477l336 -1147h20v1147h283v-1448q0 -223 -99.5 -301t-339.5 -78v246q82 0 119 30.5t37 114.5v43h-182l-348 1147h-21v-1147h-282z" />
<glyph unicode="&#x14b;" horiz-adv-x="1130" d="M125 0v1022h274v-45q168 72 265 72q190 0 271 -132.5t81 -381.5v-525q0 -215 -68.5 -313t-296.5 -205l-123 227q141 78 177 124t36 157v535q0 262 -135 262q-59 0 -170 -27l-37 -10v-760h-274z" />
<glyph unicode="&#x14c;" horiz-adv-x="1335" d="M90 690.5q0 368.5 130 547.5t447.5 179t447.5 -179t130 -547.5t-130 -542t-447.5 -173.5t-447.5 173.5t-130 542zM305 1602v200h711v-200h-711zM381 689q0 -253 60.5 -360.5t226.5 -107.5t226 107.5t60 360.5t-61.5 367.5t-225 114.5t-225 -114.5t-61.5 -367.5z" />
<glyph unicode="&#x14d;" horiz-adv-x="1089" d="M74 514q0 254 108.5 394.5t362.5 140.5t362.5 -140.5t108.5 -394.5q0 -539 -471 -539t-471 539zM258 1223v204h588v-204h-588zM352 514q0 -158 42 -231.5t150.5 -73.5t150.5 73.5t42 231.5t-42 229.5t-150.5 71.5t-150.5 -71.5t-42 -229.5z" />
<glyph unicode="&#x14e;" horiz-adv-x="1335" d="M90 690.5q0 368.5 130 547.5t447.5 179t447.5 -179t130 -547.5t-130 -542t-447.5 -173.5t-447.5 173.5t-130 542zM289 1864h248q8 -52 42.5 -84.5t91 -32.5t92.5 32.5t42 84.5h248q-16 -139 -111.5 -224.5t-271.5 -85.5t-270.5 85t-110.5 225zM381 689 q0 -253 60.5 -360.5t226.5 -107.5t226 107.5t60 360.5t-61.5 367.5t-225 114.5t-225 -114.5t-61.5 -367.5z" />
<glyph unicode="&#x14f;" horiz-adv-x="1089" d="M74 514q0 254 108.5 394.5t362.5 140.5t362.5 -140.5t108.5 -394.5q0 -539 -471 -539t-471 539zM213 1495h207q8 -63 42 -99t87 -36q115 0 129 135h207q-16 -133 -103.5 -227t-233 -94t-232.5 94t-103 227zM352 514q0 -158 42 -231.5t150.5 -73.5t150.5 73.5t42 231.5 t-42 229.5t-150.5 71.5t-150.5 -71.5t-42 -229.5z" />
<glyph unicode="&#x150;" horiz-adv-x="1335" d="M90 690.5q0 368.5 130 547.5t447.5 179t447.5 -179t130 -547.5t-130 -542t-447.5 -173.5t-447.5 173.5t-130 542zM324 1589l137 404l237 -86l-159 -391zM381 689q0 -253 60.5 -360.5t226.5 -107.5t226 107.5t60 360.5t-61.5 367.5t-225 114.5t-225 -114.5t-61.5 -367.5z M750 1587l137 404l237 -86l-159 -392z" />
<glyph unicode="&#x151;" horiz-adv-x="1089" d="M74 514q0 254 108.5 394.5t362.5 140.5t362.5 -140.5t108.5 -394.5q0 -539 -471 -539t-471 539zM109 1286l239 387l223 -135l-256 -385zM352 514q0 -158 42 -231.5t150.5 -73.5t150.5 73.5t42 231.5t-42 229.5t-150.5 71.5t-150.5 -71.5t-42 -229.5zM526 1286l240 389 l223 -137l-256 -385z" />
<glyph unicode="&#x152;" horiz-adv-x="1847" d="M94 712.5q0 395.5 120 560.5t413 165q88 0 239 -25h897v-270h-618v-293h495v-268h-495v-312h618v-270h-893q-156 -25 -243 -25q-309 0 -421 171t-112 566.5zM385 665.5q0 -227.5 56.5 -319.5t234.5 -92q51 0 190 12v881q-143 12 -227 12t-142.5 -36t-85 -133 t-26.5 -324.5z" />
<glyph unicode="&#x153;" horiz-adv-x="1708" d="M74 512q0 254 108.5 394.5t362.5 140.5q119 0 198.5 -38t133.5 -128q53 92 129.5 129t183.5 37q446 0 446 -461l-20 -193h-602q2 -88 49 -127t139 -39q139 0 340 15l57 4l5 -213q-236 -57 -442 -57.5t-298 139.5q-98 -139 -335.5 -139.5t-346 141t-108.5 395.5zM352 512 q0 -150 42 -219.5t150.5 -69.5t150.5 69.5t42 219.5t-42 218.5t-150.5 68.5t-150.5 -68.5t-42 -218.5zM1012 604h354q0 109 -40 154t-136 45t-137 -48.5t-41 -150.5z" />
<glyph unicode="&#x154;" horiz-adv-x="1269" d="M152 0v1393h540q492 0 492 -459q0 -272 -205 -402l199 -532h-310l-161 467h-273v-467h-282zM348 1718l516 217l84 -245l-532 -162zM434 709h262q100 0 149.5 61t49.5 161.5t-52 160t-151 59.5h-258v-442z" />
<glyph unicode="&#x155;" horiz-adv-x="761" d="M98 1333l516 209l82 -235l-536 -152zM127 0v1024h272v-109q172 102 328 134v-277q-166 -35 -285 -72l-41 -14v-686h-274z" />
<glyph unicode="&#x156;" horiz-adv-x="1269" d="M152 0v1393h540q492 0 492 -459q0 -272 -205 -402l199 -532h-310l-161 467h-273v-467h-282zM432 -637l94 479h267l-113 -479h-248zM434 709h262q101 0 150 61t49 161.5t-52 160t-151 59.5h-258v-442z" />
<glyph unicode="&#x157;" horiz-adv-x="761" d="M33 -637l94 479h266l-112 -479h-248zM127 0v1024h272v-109q172 102 328 134v-277q-166 -35 -285 -72l-41 -14v-686h-274z" />
<glyph unicode="&#x158;" horiz-adv-x="1269" d="M152 0v1393h540q492 0 492 -459q0 -272 -205 -402l199 -532h-310l-161 467h-273v-467h-282zM219 1872h291l106 -115l101 115h291l-275 -305h-239zM434 709h262q101 0 150 61t49 161.5t-52 160t-151 59.5h-258v-442z" />
<glyph unicode="&#x159;" horiz-adv-x="761" d="M10 1497h252l94 -137l95 137h251l-262 -309h-172zM127 0v1024h272v-109q172 102 328 134v-277q-166 -35 -285 -72l-41 -14v-686h-274z" />
<glyph unicode="&#x15a;" horiz-adv-x="1114" d="M76 1004q0 209 127 311t348 102q152 0 385 -41l74 -14l-23 -223q-288 32 -407 32q-223 0 -224 -147q0 -66 55.5 -100.5t259 -100t285.5 -148.5t82 -255q0 -219 -133 -332t-348 -113q-160 0 -391 50l-74 14l29 219q274 -37 420 -37q217 0 217 180q0 66 -50.5 103 t-199.5 80q-238 68 -335 161t-97 259zM295 1718l516 217l84 -245l-533 -162z" />
<glyph unicode="&#x15b;" horiz-adv-x="966" d="M74 718q0 163 110.5 246t284.5 83q121 0 330 -39l67 -13l-4 -227q-254 33 -355 33t-131 -18.5t-30 -58.5t40 -55.5t205 -45t234.5 -97t69.5 -219.5q0 -332 -412 -332q-135 0 -327 37l-66 13l8 229q254 -33 353.5 -33t135.5 19.5t36 58.5t-38 57.5t-197 45t-236.5 90 t-77.5 226.5zM186 1333l516 209l82 -235l-536 -152z" />
<glyph unicode="&#x15c;" horiz-adv-x="1114" d="M76 1004q0 209 127 311t348 102q152 0 385 -41l74 -14l-23 -223q-288 32 -407 32q-223 0 -224 -147q0 -66 55.5 -100.5t259 -100t285.5 -148.5t82 -255q0 -219 -133 -332t-348 -113q-160 0 -391 50l-74 14l29 219q274 -37 420 -37q217 0 217 180q0 66 -50.5 103 t-199.5 80q-238 68 -335 161t-97 259zM172 1567l274 305h240l275 -305h-291l-101 114l-106 -114h-291z" />
<glyph unicode="&#x15d;" horiz-adv-x="966" d="M74 718q0 163 110.5 246t284.5 83q121 0 330 -39l67 -13l-4 -227q-254 33 -355 33t-131 -18.5t-30 -58.5t40 -55.5t205 -45t234.5 -97t69.5 -219.5q0 -332 -412 -332q-135 0 -327 37l-66 13l8 229q254 -33 353.5 -33t135.5 19.5t36 58.5t-38 57.5t-197 45t-236.5 90 t-77.5 226.5zM139 1188l258 309h172l262 -309h-251l-95 137l-94 -137h-252z" />
<glyph unicode="&#x15e;" horiz-adv-x="1114" d="M76 1004q0 209 127 311t348 102q152 0 385 -41l74 -14l-23 -223q-289 33 -407 32q-223 0 -224 -147q0 -66 55.5 -100.5t259 -100t285.5 -148.5t82 -267.5t-109.5 -297t-291.5 -130.5v-56q131 -2 191.5 -39t60.5 -149.5t-59.5 -171t-167.5 -58.5q-90 0 -162 17l-29 6 l8 160q63 -2 99 -2q78 0 77 53q0 45 -77 45h-50v190q-156 4 -368 50l-68 14l29 219q274 -37 420 -37q217 0 217 180q0 66 -50.5 103t-199.5 80q-238 68 -335 161t-97 259z" />
<glyph unicode="&#x15f;" horiz-adv-x="966" d="M74 718q0 163 110.5 246t284.5 83q121 0 330 -39l67 -13l-4 -227q-254 33 -355 33t-131 -18.5t-30 -58.5t40 -55.5t205 -45t234.5 -97t69.5 -219.5q0 -317 -371 -330v-53q131 -2 191.5 -39t60.5 -149.5t-59.5 -171t-167.5 -58.5q-88 0 -164 17l-27 6l9 160q63 -2 98 -2 q78 0 78 53q0 45 -78 45h-49v195q-129 8 -277 36l-49 9l8 229q254 -33 353.5 -33t135.5 19.5t36 58.5t-38 57.5t-197 45t-236.5 90t-77.5 226.5z" />
<glyph unicode="&#x160;" horiz-adv-x="1114" d="M76 1004q0 209 127 311t348 102q152 0 385 -41l74 -14l-23 -223q-288 32 -407 32q-223 0 -224 -147q0 -66 55.5 -100.5t259 -100t285.5 -148.5t82 -255q0 -219 -133 -332t-348 -113q-160 0 -391 50l-74 14l29 219q274 -37 420 -37q217 0 217 180q0 66 -50.5 103 t-199.5 80q-238 68 -335 161t-97 259zM207 1872h291l106 -115l101 115h290l-274 -305h-240z" />
<glyph unicode="&#x161;" horiz-adv-x="966" d="M74 718q0 163 110.5 246t284.5 83q121 0 330 -39l67 -13l-4 -227q-254 33 -355 33t-131 -18.5t-30 -58.5t40 -55.5t205 -45t234.5 -97t69.5 -219.5q0 -332 -412 -332q-135 0 -327 37l-66 13l8 229q254 -33 353.5 -33t135.5 19.5t36 58.5t-38 57.5t-197 45t-236.5 90 t-77.5 226.5zM158 1497h252l94 -137l94 137h252l-262 -309h-172z" />
<glyph unicode="&#x162;" horiz-adv-x="0" />
<glyph unicode="&#x163;" horiz-adv-x="745" d="M47 791v233h121v285h274v-285h252v-233h-252v-420q0 -61 3.5 -87t22.5 -44.5t60 -18.5l156 4l12 -219q-137 -31 -222 -31t-144 21v-72q131 -2 191.5 -39t60.5 -149.5t-59.5 -171t-168.5 -58.5q-90 0 -161 17l-29 6l8 160q63 -2 98 -2q78 0 78 53q0 45 -78 45h-49v217h92 q-82 33 -113.5 117t-31.5 246v426h-121z" />
<glyph unicode="&#x164;" horiz-adv-x="1077" d="M27 1143v250h1024v-250h-369v-1143h-283v1143h-372zM147 1872h291l107 -115l100 115h291l-274 -305h-240z" />
<glyph unicode="&#x165;" horiz-adv-x="997" d="M49 774v248h121v287h274v-287h252v-248h-252v-387q0 -61 3.5 -87t22.5 -44t60 -18l156 4l12 -236q-137 -31 -209 -31q-184 0 -251.5 83t-67.5 307v409h-121zM770 881v532h266v-532h-266z" />
<glyph unicode="&#x166;" horiz-adv-x="1081" d="M29 1143v250h1024v-250h-369v-387h285v-238h-285v-518h-283v518h-270v238h270v387h-372z" />
<glyph unicode="&#x167;" horiz-adv-x="747" d="M47 774v248h121v287h274v-287h252v-248h-252v-141h195v-205h-195v-41q0 -61 3.5 -87t22.5 -44t60 -18l156 4l12 -236q-137 -31 -209 -31q-184 0 -251.5 83t-67.5 307v63h-62v205h62v141h-121z" />
<glyph unicode="&#x168;" horiz-adv-x="1339" d="M139 471v922h283v-928q0 -244 248 -244t248 244v928h282v-922q0 -256 -132 -376t-398.5 -120t-398.5 120t-132 376zM262 1757q41 49 105.5 90t125 41t185.5 -47t147 -47q47 0 138 66l28 20l60 -199q-43 -49 -106.5 -89t-117 -40t-183.5 47.5t-157 47.5q-51 0 -137 -66 l-28 -20z" />
<glyph unicode="&#x169;" horiz-adv-x="1132" d="M117 487v537h274v-541q0 -152 24.5 -207t123 -55t192.5 37v766h275v-1024h-273v57l-41 -20q-39 -20 -102.5 -41t-112.5 -21q-213 0 -286.5 118t-73.5 394zM203 1362q41 47 103.5 85t108.5 38t141 -37t120 -37q49 0 135 51l29 19l59 -156q-12 -14 -32.5 -35.5t-78 -56.5 t-103.5 -35t-140 37t-123 37q-57 0 -133 -47l-27 -17z" />
<glyph unicode="&#x16a;" horiz-adv-x="1339" d="M139 471v922h283v-928q0 -244 248 -244t248 244v928h282v-922q0 -256 -132 -376t-398.5 -120t-398.5 120t-132 376zM322 1602v200h710v-200h-710z" />
<glyph unicode="&#x16b;" horiz-adv-x="1132" d="M117 487v537h274v-541q0 -152 24.5 -207t123 -55t192.5 37v766h275v-1024h-273v57l-41 -20q-39 -20 -102.5 -41t-112.5 -21q-213 0 -286.5 118t-73.5 394zM272 1223v204h588v-204h-588z" />
<glyph unicode="&#x16c;" horiz-adv-x="1339" d="M139 471v922h283v-928q0 -244 248 -244t248 244v928h282v-922q0 -256 -132 -376t-398.5 -120t-398.5 120t-132 376zM297 1864h248q8 -52 43 -84.5t91 -32.5t92 32.5t42 84.5h248q-16 -139 -111.5 -224.5t-271.5 -85.5t-270.5 85t-110.5 225z" />
<glyph unicode="&#x16d;" horiz-adv-x="1132" d="M117 487v537h274v-541q0 -152 24.5 -207t123 -55t192.5 37v766h275v-1024h-273v57l-41 -20q-39 -20 -102.5 -41t-112.5 -21q-213 0 -286.5 118t-73.5 394zM229 1495h207q8 -63 42 -99t87 -36q115 0 129 135h207q-16 -133 -103 -227t-232.5 -94t-232.5 94t-104 227z" />
<glyph unicode="&#x16e;" horiz-adv-x="1339" d="M139 471v922h283v-928q0 -244 248 -244t248 244v928h282v-922q0 -256 -132 -376t-398.5 -120t-398.5 120t-132 376zM371 1718q0 121 88 189.5t219 68.5t219 -68.5t88 -189.5t-88 -189.5t-219 -68.5t-219 68.5t-88 189.5zM557 1718q0 -39 33 -61.5t88 -22.5t88 22.5 t33 61.5t-33 61.5t-88 22.5t-88 -22.5t-33 -61.5z" />
<glyph unicode="&#x16f;" horiz-adv-x="1132" d="M117 487v537h274v-541q0 -152 24.5 -207t123 -55t192.5 37v766h275v-1024h-273v57l-41 -20q-39 -20 -102.5 -41t-112.5 -21q-213 0 -286.5 118t-73.5 394zM326 1305.5q0 101.5 68.5 171t170 69.5t171 -69.5t69.5 -171t-69.5 -170t-171 -68.5t-170 68.5t-68.5 170z M473 1307q0 -41 26.5 -68t67.5 -27t66.5 27t25.5 68t-26.5 66.5t-66.5 25.5t-66.5 -25.5t-26.5 -66.5z" />
<glyph unicode="&#x170;" horiz-adv-x="1339" d="M139 471v922h283v-928q0 -244 248 -244t248 244v928h282v-922q0 -256 -132 -376t-398.5 -120t-398.5 120t-132 376zM313 1589l138 404l237 -86l-160 -391zM739 1587l138 404l237 -86l-160 -392z" />
<glyph unicode="&#x171;" horiz-adv-x="1132" d="M117 487v537h274v-541q0 -152 24.5 -207t123 -55t192.5 37v766h275v-1024h-273v57l-41 -20q-39 -20 -102.5 -41t-112.5 -21q-213 0 -286.5 118t-73.5 394zM197 1286l239 387l223 -135l-256 -385zM614 1286l240 389l223 -137l-256 -385z" />
<glyph unicode="&#x172;" horiz-adv-x="1343" d="M141 471v922h283v-928q0 -244 248 -244t248 244v928h282v-922q0 -403 -342 -477q-109 -92 -108.5 -159.5t65.5 -67.5l86 8l25 -195q-121 -23 -214 -22.5t-155.5 53.5t-62.5 143q0 123 108 223q-231 14 -347 133t-116 361z" />
<glyph unicode="&#x173;" horiz-adv-x="1132" d="M117 487v535h274v-535q0 -156 23.5 -210t120.5 -54q53 0 161 27l35 8v764h275v-1022q-16 -8 -41 -22.5t-65 -58.5t-40 -85q0 -68 66 -67l86 8l24 -195q-121 -23 -214 -22.5t-155.5 53.5t-62.5 143q0 135 152 246h-25v43q-160 -68 -254 -68q-215 0 -287.5 117t-72.5 395z " />
<glyph unicode="&#x174;" horiz-adv-x="1904" d="M39 1393h297l172 -1151h31l249 1151h328l250 -1151h31l172 1151h297l-267 -1393h-428l-219 1059l-219 -1059h-428zM559 1567l275 305h239l275 -305h-291l-101 114l-106 -114h-291z" />
<glyph unicode="&#x175;" horiz-adv-x="1581" d="M49 1024h270l125 -791h50l155 771h283l155 -771h50l125 791h270l-195 -1024h-430l-116 627l-117 -627h-430zM449 1188l258 309h172l262 -309h-252l-94 137l-95 -137h-251z" />
<glyph unicode="&#x176;" horiz-adv-x="1140" d="M0 1393h313l256 -555l256 555h314l-426 -830v-563h-283v563zM180 1567l275 305h239l275 -305h-291l-100 114l-107 -114h-291z" />
<glyph unicode="&#x177;" horiz-adv-x="1024" d="M33 1024h270l186 -791h48l186 791h270l-360 -1454h-268l112 430h-192zM168 1188l258 309h172l262 -309h-252l-94 137l-94 -137h-252z" />
<glyph unicode="&#x178;" horiz-adv-x="1140" d="M0 1393h313l256 -555l256 555h314l-426 -830v-563h-283v563zM215 1565v270h262v-270h-262zM653 1565v270h262v-270h-262z" />
<glyph unicode="&#x179;" horiz-adv-x="1089" d="M84 0v244l582 866v37h-582v246h922v-246l-582 -864v-37h582v-246h-922zM248 1718l516 217l84 -245l-533 -162z" />
<glyph unicode="&#x17a;" horiz-adv-x="929" d="M84 0v246l430 532h-430v246h760v-246l-430 -532h430v-246h-760zM168 1333l516 209l82 -235l-537 -152z" />
<glyph unicode="&#x17b;" horiz-adv-x="1089" d="M84 0v244l582 866v37h-582v246h922v-246l-582 -864v-37h582v-246h-922zM408 1571v276h274v-276h-274z" />
<glyph unicode="&#x17c;" horiz-adv-x="929" d="M84 0v246l430 532h-430v246h760v-246l-430 -532h430v-246h-760zM326 1139v276h274v-276h-274z" />
<glyph unicode="&#x17d;" horiz-adv-x="1089" d="M84 0v244l582 866v37h-582v246h922v-246l-582 -864v-37h582v-246h-922zM156 1872h290l107 -115l100 115h291l-274 -305h-240z" />
<glyph unicode="&#x17e;" horiz-adv-x="929" d="M84 0v246l430 532h-430v246h760v-246l-430 -532h430v-246h-760zM139 1497h252l94 -137l95 137h251l-262 -309h-172z" />
<glyph unicode="&#x192;" d="M90 -219q131 -6 189 -6q104 0 104 135v881h-111v233h111v49q0 219 64.5 302t234.5 83q74 0 186 -20l43 -6v-224q-90 4 -151.5 4t-82 -31.5t-20.5 -109.5v-47h248v-233h-248v-887q0 -211 -80.5 -293t-263.5 -82q-104 0 -190 20l-33 7v225z" />
<glyph unicode="&#x1fa;" horiz-adv-x="1232" d="M35 0l303 1352q-31 53 -31 125q0 121 88 189.5t219 68.5t219.5 -68.5t88.5 -189.5q0 -68 -29 -119l305 -1358h-283l-55 252h-487l-56 -252h-282zM291 1923l516 217l84 -246l-533 -161zM426 498h381l-141 661h-99zM494 1477q0 -39 32.5 -61.5t87.5 -22.5h11 q51 2 80.5 24.5t29.5 60.5t-32.5 60.5t-88 22.5t-88 -22.5t-32.5 -61.5z" />
<glyph unicode="&#x1fb;" horiz-adv-x="1046" d="M59 311q0 164 88.5 232.5t270.5 81.5l219 16v64q0 63 -28.5 86.5t-92.5 23.5l-385 -16l-8 190q219 59 416.5 59.5t284.5 -81.5t87 -262v-426q4 -49 16.5 -69t51.5 -28l-8 -207q-104 0 -167 14.5t-126 57.5q-147 -72 -301 -72q-317 0 -318 336zM221 1745l516 209l82 -236 l-536 -151zM276 1305.5q0 101.5 69 171t170 69.5t171 -69.5t70 -171t-70 -170t-171 -68.5t-170 68.5t-69 170zM336 311.5q0 -112.5 100 -112.5q78 0 170 24l31 8v220l-186 -17q-115 -10 -115 -122.5zM424 1307q0 -41 26.5 -68t67.5 -27t66.5 27t25.5 68t-26.5 66.5 t-66.5 25.5t-66.5 -25.5t-26.5 -66.5z" />
<glyph unicode="&#x1fc;" horiz-adv-x="1785" d="M27 0l376 1413h1299v-270h-619v-293h496v-268h-496v-312h619v-270h-897v238h-436l-60 -238h-282zM438 510h367l2 633h-205zM748 1718l516 217l84 -245l-533 -162z" />
<glyph unicode="&#x1fd;" horiz-adv-x="1605" d="M59 314.5q0 160.5 89.5 231t267.5 81.5l221 14v51q0 41 -28.5 66.5t-84.5 25.5q-147 0 -321 -16l-66 -6l-8 239q272 45 435 45.5t251 -75.5q106 76 272 76q446 0 447 -461l-21 -193h-602q2 -88 49.5 -127t139.5 -39q139 0 340 15l57 4l4 -213q-236 -57 -435.5 -57.5 t-303.5 98.5l-47 -23q-172 -76 -336 -76t-242 89.5t-78 250zM336 319q0 -109 84 -108q82 0 182 22l35 9l-2 190l-184 -8q-115 -6 -115 -105zM524 1333l516 209l82 -235l-536 -152zM911 604h353q0 109 -40 154t-137.5 45t-136.5 -47.5t-39 -151.5z" />
<glyph unicode="&#x1fe;" horiz-adv-x="1339" d="M92 688q0 371 130 550t448 179q117 0 207 -24l124 266l205 -88l-133 -287q174 -168 174 -596q0 -367 -130 -540t-447 -173q-100 0 -197 21l-119 -256l-200 100l118 254q-180 160 -180 594zM348 1718l516 217l84 -245l-532 -162zM383 720q0 -222 31 -323l354 760 q-51 14 -98 14q-164 0 -225.5 -114.5t-61.5 -336.5zM582 229q35 -8 88 -8q166 0 226 107.5t60 324.5t-28 320z" />
<glyph unicode="&#x1ff;" horiz-adv-x="1089" d="M74 514q0 254 108.5 394.5t362.5 140.5q57 0 114 -11l82 205l166 -61l-84 -201q193 -121 193 -467q0 -539 -471 -539q-70 0 -129 13l-84 -209l-164 61l88 211q-182 123 -182 463zM248 1333l516 209l82 -235l-537 -152zM352 528.5q0 -143.5 25 -209.5l194 494q-4 0 -13 1 t-13 1q-109 0 -151 -71.5t-42 -215zM504 211q12 -2 41 -2q109 0 150.5 73.5t41.5 219t-28 215.5z" />
<glyph unicode="&#x218;" horiz-adv-x="1114" d="M76 1004q0 209 127 311t348 102q152 0 385 -41l74 -14l-23 -223q-288 32 -407 32q-223 0 -224 -147q0 -66 55.5 -100.5t259 -100t285.5 -148.5t82 -255q0 -219 -133 -332t-348 -113q-160 0 -391 50l-74 14l29 219q274 -37 420 -37q217 0 217 180q0 66 -50.5 103 t-199.5 80q-238 68 -335 161t-97 259zM346 -637l94 479h267l-113 -479h-248z" />
<glyph unicode="&#x219;" horiz-adv-x="966" d="M-35 -637l94 479h267l-113 -479h-248zM74 718q0 163 110.5 246t284.5 83q121 0 330 -39l67 -13l-4 -227q-254 33 -355 33t-131 -18.5t-30 -58.5t40 -55.5t205 -45t234.5 -97t69.5 -219.5q0 -332 -412 -332q-135 0 -327 37l-66 13l8 229q254 -33 353.5 -33t135.5 19.5 t36 58.5t-38 57.5t-197 45t-236.5 90t-77.5 226.5z" />
<glyph unicode="&#x21a;" horiz-adv-x="1077" d="M27 1143v250h1024v-250h-369v-1143h-283v1143h-372zM362 -637l95 479h266l-113 -479h-248z" />
<glyph unicode="&#x21b;" horiz-adv-x="745" d="M6 -637l94 479h267l-113 -479h-248zM47 791v233h121v285h274v-285h252v-233h-252v-420q0 -61 3.5 -87t22.5 -44.5t60 -18.5l156 4l12 -219q-137 -31 -209 -31q-184 0 -251.5 83t-67.5 307v426h-121z" />
<glyph unicode="&#x2c6;" horiz-adv-x="548" d="M-49 1188l258 309h172l262 -309h-252l-94 137l-94 -137h-252z" />
<glyph unicode="&#x2da;" horiz-adv-x="548" d="M41 1305.5q0 101.5 68.5 171t170 69.5t171 -69.5t69.5 -171t-69.5 -170t-171 -68.5t-170 68.5t-68.5 170zM188 1307q0 -41 27 -68t68 -27t66.5 27t25.5 68t-25.5 66.5t-66.5 25.5t-68 -25.5t-27 -66.5z" />
<glyph unicode="&#x2dc;" horiz-adv-x="548" d="M-45 1362q41 47 103.5 85t108.5 38t141 -37t120 -37q49 0 135 51l29 19l59 -156q-12 -14 -32.5 -35.5t-78 -56.5t-103.5 -35t-140 37t-121 37q-59 0 -135 -47l-27 -17z" />
<glyph unicode="&#x2000;" horiz-adv-x="1070" />
<glyph unicode="&#x2001;" horiz-adv-x="2140" />
<glyph unicode="&#x2002;" horiz-adv-x="1070" />
<glyph unicode="&#x2003;" horiz-adv-x="2140" />
<glyph unicode="&#x2004;" horiz-adv-x="713" />
<glyph unicode="&#x2005;" horiz-adv-x="535" />
<glyph unicode="&#x2006;" horiz-adv-x="356" />
<glyph unicode="&#x2007;" horiz-adv-x="356" />
<glyph unicode="&#x2008;" horiz-adv-x="267" />
<glyph unicode="&#x2009;" horiz-adv-x="428" />
<glyph unicode="&#x200a;" horiz-adv-x="118" />
<glyph unicode="&#x2010;" horiz-adv-x="835" d="M115 420v250h606v-250h-606z" />
<glyph unicode="&#x2011;" horiz-adv-x="835" d="M115 420v250h606v-250h-606z" />
<glyph unicode="&#x2012;" horiz-adv-x="835" d="M115 420v250h606v-250h-606z" />
<glyph unicode="&#x2013;" horiz-adv-x="1257" d="M117 428v234h1024v-234h-1024z" />
<glyph unicode="&#x2014;" horiz-adv-x="2281" d="M117 428v234h2048v-234h-2048z" />
<glyph unicode="&#x2018;" horiz-adv-x="530" d="M72 885l174 506h211l-99 -506h-286z" />
<glyph unicode="&#x2019;" horiz-adv-x="520" d="M84 887l96 506h289l-174 -506h-211z" />
<glyph unicode="&#x201a;" horiz-adv-x="524" d="M47 -240l96 506h289l-174 -506h-211z" />
<glyph unicode="&#x201c;" horiz-adv-x="907" d="M72 885l174 506h211l-99 -506h-286zM449 885l174 506h211l-99 -506h-286z" />
<glyph unicode="&#x201d;" horiz-adv-x="899" d="M84 889l96 506h289l-176 -506h-209zM461 889l96 506h289l-174 -506h-211z" />
<glyph unicode="&#x201e;" horiz-adv-x="862" d="M-14 -266l174 506h211l-99 -506h-286zM375 -266l174 506h211l-98 -506h-287z" />
<glyph unicode="&#x2022;" horiz-adv-x="921" d="M205 205v573h512v-573h-512z" />
<glyph unicode="&#x2026;" horiz-adv-x="1568" d="M115 0v340h295v-340h-295zM637 0v340h295v-340h-295zM1159 0v340h295v-340h-295z" />
<glyph unicode="&#x202f;" horiz-adv-x="428" />
<glyph unicode="&#x2039;" horiz-adv-x="651" d="M82 397v197l457 336v-277l-224 -153l224 -176v-277z" />
<glyph unicode="&#x203a;" horiz-adv-x="651" d="M111 68v276l223 176l-223 154v276l456 -336v-196z" />
<glyph unicode="&#x205f;" horiz-adv-x="535" />
<glyph unicode="&#x20ac;" d="M53 383v209h113v135h-113v209h127q31 240 144.5 340t357.5 100q158 0 395 -53l-8 -219q-180 29 -322.5 29t-200 -41t-81.5 -156h481v-209h-497v-135h497v-209h-473q25 -92 82 -128t184 -36t330 27l6 -223q-207 -47 -416.5 -47.5t-324.5 98t-150 309.5h-131z" />
<glyph unicode="&#x2122;" horiz-adv-x="1351" d="M164 1157v162h409v-162h-84v-477h-180v477h-145zM625 678v641h211l84 -324l92 324h209v-641h-168v356l-70 -313h-113l-77 313v-356h-168z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1024" d="M0 0v1024h1024v-1024h-1024z" />
<hkern u1="&#x22;" u2="&#x129;" k="-16" />
<hkern u1="&#x22;" u2="&#xef;" k="-37" />
<hkern u1="&#x22;" u2="&#xee;" k="-25" />
<hkern u1="&#x22;" u2="&#xec;" k="-55" />
<hkern u1="&#x22;" u2="&#xc6;" k="78" />
<hkern u1="&#x22;" u2="&#x40;" k="20" />
<hkern u1="&#x22;" u2="&#x2f;" k="131" />
<hkern u1="&#x22;" u2="&#x26;" k="43" />
<hkern u1="&#x26;" u2="&#x201d;" k="70" />
<hkern u1="&#x26;" u2="&#x2019;" k="70" />
<hkern u1="&#x26;" u2="&#x21a;" k="68" />
<hkern u1="&#x26;" u2="&#x178;" k="96" />
<hkern u1="&#x26;" u2="&#x176;" k="96" />
<hkern u1="&#x26;" u2="&#x174;" k="27" />
<hkern u1="&#x26;" u2="&#x166;" k="68" />
<hkern u1="&#x26;" u2="&#x164;" k="68" />
<hkern u1="&#x26;" u2="&#xdd;" k="96" />
<hkern u1="&#x26;" u2="Y" k="96" />
<hkern u1="&#x26;" u2="W" k="27" />
<hkern u1="&#x26;" u2="V" k="47" />
<hkern u1="&#x26;" u2="T" k="68" />
<hkern u1="&#x26;" u2="&#x27;" k="78" />
<hkern u1="&#x26;" u2="&#x22;" k="78" />
<hkern u1="&#x27;" u2="&#x129;" k="-16" />
<hkern u1="&#x27;" u2="&#xef;" k="-37" />
<hkern u1="&#x27;" u2="&#xee;" k="-25" />
<hkern u1="&#x27;" u2="&#xec;" k="-55" />
<hkern u1="&#x27;" u2="&#xc6;" k="78" />
<hkern u1="&#x27;" u2="&#x40;" k="20" />
<hkern u1="&#x27;" u2="&#x2f;" k="131" />
<hkern u1="&#x27;" u2="&#x26;" k="43" />
<hkern u1="&#x28;" u2="&#x219;" k="20" />
<hkern u1="&#x28;" u2="&#x1ff;" k="41" />
<hkern u1="&#x28;" u2="&#x1fe;" k="31" />
<hkern u1="&#x28;" u2="&#x1fd;" k="25" />
<hkern u1="&#x28;" u2="&#x1fb;" k="25" />
<hkern u1="&#x28;" u2="&#x177;" k="20" />
<hkern u1="&#x28;" u2="&#x175;" k="29" />
<hkern u1="&#x28;" u2="&#x173;" k="31" />
<hkern u1="&#x28;" u2="&#x171;" k="31" />
<hkern u1="&#x28;" u2="&#x16f;" k="31" />
<hkern u1="&#x28;" u2="&#x16d;" k="31" />
<hkern u1="&#x28;" u2="&#x16b;" k="31" />
<hkern u1="&#x28;" u2="&#x169;" k="31" />
<hkern u1="&#x28;" u2="&#x161;" k="20" />
<hkern u1="&#x28;" u2="&#x15f;" k="20" />
<hkern u1="&#x28;" u2="&#x15d;" k="20" />
<hkern u1="&#x28;" u2="&#x15b;" k="20" />
<hkern u1="&#x28;" u2="&#x159;" k="23" />
<hkern u1="&#x28;" u2="&#x157;" k="23" />
<hkern u1="&#x28;" u2="&#x155;" k="23" />
<hkern u1="&#x28;" u2="&#x153;" k="41" />
<hkern u1="&#x28;" u2="&#x152;" k="31" />
<hkern u1="&#x28;" u2="&#x151;" k="41" />
<hkern u1="&#x28;" u2="&#x150;" k="31" />
<hkern u1="&#x28;" u2="&#x14f;" k="41" />
<hkern u1="&#x28;" u2="&#x14e;" k="31" />
<hkern u1="&#x28;" u2="&#x14d;" k="41" />
<hkern u1="&#x28;" u2="&#x14c;" k="31" />
<hkern u1="&#x28;" u2="&#x14b;" k="23" />
<hkern u1="&#x28;" u2="&#x148;" k="23" />
<hkern u1="&#x28;" u2="&#x146;" k="23" />
<hkern u1="&#x28;" u2="&#x144;" k="23" />
<hkern u1="&#x28;" u2="&#x135;" k="-45" />
<hkern u1="&#x28;" u2="&#x12d;" k="-51" />
<hkern u1="&#x28;" u2="&#x129;" k="-14" />
<hkern u1="&#x28;" u2="&#x122;" k="31" />
<hkern u1="&#x28;" u2="&#x120;" k="31" />
<hkern u1="&#x28;" u2="&#x11e;" k="31" />
<hkern u1="&#x28;" u2="&#x11c;" k="31" />
<hkern u1="&#x28;" u2="&#x11b;" k="41" />
<hkern u1="&#x28;" u2="&#x119;" k="41" />
<hkern u1="&#x28;" u2="&#x117;" k="41" />
<hkern u1="&#x28;" u2="&#x115;" k="41" />
<hkern u1="&#x28;" u2="&#x113;" k="41" />
<hkern u1="&#x28;" u2="&#x111;" k="39" />
<hkern u1="&#x28;" u2="&#x10f;" k="39" />
<hkern u1="&#x28;" u2="&#x10d;" k="41" />
<hkern u1="&#x28;" u2="&#x10c;" k="29" />
<hkern u1="&#x28;" u2="&#x10b;" k="41" />
<hkern u1="&#x28;" u2="&#x10a;" k="29" />
<hkern u1="&#x28;" u2="&#x109;" k="41" />
<hkern u1="&#x28;" u2="&#x108;" k="29" />
<hkern u1="&#x28;" u2="&#x107;" k="41" />
<hkern u1="&#x28;" u2="&#x106;" k="29" />
<hkern u1="&#x28;" u2="&#x105;" k="25" />
<hkern u1="&#x28;" u2="&#x103;" k="25" />
<hkern u1="&#x28;" u2="&#x101;" k="25" />
<hkern u1="&#x28;" u2="&#xff;" k="20" />
<hkern u1="&#x28;" u2="&#xfd;" k="20" />
<hkern u1="&#x28;" u2="&#xfc;" k="31" />
<hkern u1="&#x28;" u2="&#xfb;" k="31" />
<hkern u1="&#x28;" u2="&#xfa;" k="31" />
<hkern u1="&#x28;" u2="&#xf9;" k="31" />
<hkern u1="&#x28;" u2="&#xf8;" k="41" />
<hkern u1="&#x28;" u2="&#xf6;" k="41" />
<hkern u1="&#x28;" u2="&#xf5;" k="41" />
<hkern u1="&#x28;" u2="&#xf4;" k="41" />
<hkern u1="&#x28;" u2="&#xf3;" k="41" />
<hkern u1="&#x28;" u2="&#xf2;" k="41" />
<hkern u1="&#x28;" u2="&#xf1;" k="23" />
<hkern u1="&#x28;" u2="&#xef;" k="-78" />
<hkern u1="&#x28;" u2="&#xec;" k="-41" />
<hkern u1="&#x28;" u2="&#xeb;" k="41" />
<hkern u1="&#x28;" u2="&#xea;" k="41" />
<hkern u1="&#x28;" u2="&#xe9;" k="41" />
<hkern u1="&#x28;" u2="&#xe8;" k="41" />
<hkern u1="&#x28;" u2="&#xe7;" k="41" />
<hkern u1="&#x28;" u2="&#xe6;" k="25" />
<hkern u1="&#x28;" u2="&#xe5;" k="25" />
<hkern u1="&#x28;" u2="&#xe4;" k="25" />
<hkern u1="&#x28;" u2="&#xe3;" k="25" />
<hkern u1="&#x28;" u2="&#xe2;" k="25" />
<hkern u1="&#x28;" u2="&#xe1;" k="25" />
<hkern u1="&#x28;" u2="&#xe0;" k="25" />
<hkern u1="&#x28;" u2="&#xd8;" k="31" />
<hkern u1="&#x28;" u2="&#xd6;" k="31" />
<hkern u1="&#x28;" u2="&#xd5;" k="31" />
<hkern u1="&#x28;" u2="&#xd4;" k="31" />
<hkern u1="&#x28;" u2="&#xd3;" k="31" />
<hkern u1="&#x28;" u2="&#xd2;" k="31" />
<hkern u1="&#x28;" u2="&#xc7;" k="29" />
<hkern u1="&#x28;" u2="&#x7b;" k="27" />
<hkern u1="&#x28;" u2="y" k="20" />
<hkern u1="&#x28;" u2="w" k="29" />
<hkern u1="&#x28;" u2="v" k="20" />
<hkern u1="&#x28;" u2="u" k="31" />
<hkern u1="&#x28;" u2="s" k="20" />
<hkern u1="&#x28;" u2="r" k="23" />
<hkern u1="&#x28;" u2="q" k="39" />
<hkern u1="&#x28;" u2="p" k="23" />
<hkern u1="&#x28;" u2="o" k="41" />
<hkern u1="&#x28;" u2="n" k="23" />
<hkern u1="&#x28;" u2="m" k="23" />
<hkern u1="&#x28;" u2="j" k="-45" />
<hkern u1="&#x28;" u2="f" k="20" />
<hkern u1="&#x28;" u2="e" k="41" />
<hkern u1="&#x28;" u2="d" k="39" />
<hkern u1="&#x28;" u2="c" k="41" />
<hkern u1="&#x28;" u2="a" k="25" />
<hkern u1="&#x28;" u2="Q" k="31" />
<hkern u1="&#x28;" u2="O" k="31" />
<hkern u1="&#x28;" u2="G" k="31" />
<hkern u1="&#x28;" u2="C" k="29" />
<hkern u1="&#x2a;" u2="&#x21a;" k="-20" />
<hkern u1="&#x2a;" u2="&#x219;" k="23" />
<hkern u1="&#x2a;" u2="&#x1ff;" k="33" />
<hkern u1="&#x2a;" u2="&#x1fc;" k="59" />
<hkern u1="&#x2a;" u2="&#x1fa;" k="59" />
<hkern u1="&#x2a;" u2="&#x167;" k="-39" />
<hkern u1="&#x2a;" u2="&#x166;" k="-20" />
<hkern u1="&#x2a;" u2="&#x165;" k="-37" />
<hkern u1="&#x2a;" u2="&#x164;" k="-20" />
<hkern u1="&#x2a;" u2="&#x161;" k="23" />
<hkern u1="&#x2a;" u2="&#x15f;" k="23" />
<hkern u1="&#x2a;" u2="&#x15d;" k="23" />
<hkern u1="&#x2a;" u2="&#x15b;" k="23" />
<hkern u1="&#x2a;" u2="&#x153;" k="33" />
<hkern u1="&#x2a;" u2="&#x151;" k="33" />
<hkern u1="&#x2a;" u2="&#x14f;" k="33" />
<hkern u1="&#x2a;" u2="&#x14d;" k="33" />
<hkern u1="&#x2a;" u2="&#x135;" k="-94" />
<hkern u1="&#x2a;" u2="&#x134;" k="20" />
<hkern u1="&#x2a;" u2="&#x129;" k="-51" />
<hkern u1="&#x2a;" u2="&#x123;" k="29" />
<hkern u1="&#x2a;" u2="&#x121;" k="29" />
<hkern u1="&#x2a;" u2="&#x11f;" k="29" />
<hkern u1="&#x2a;" u2="&#x11d;" k="29" />
<hkern u1="&#x2a;" u2="&#x11b;" k="33" />
<hkern u1="&#x2a;" u2="&#x119;" k="33" />
<hkern u1="&#x2a;" u2="&#x117;" k="33" />
<hkern u1="&#x2a;" u2="&#x115;" k="33" />
<hkern u1="&#x2a;" u2="&#x113;" k="33" />
<hkern u1="&#x2a;" u2="&#x111;" k="37" />
<hkern u1="&#x2a;" u2="&#x10f;" k="37" />
<hkern u1="&#x2a;" u2="&#x10d;" k="33" />
<hkern u1="&#x2a;" u2="&#x10b;" k="33" />
<hkern u1="&#x2a;" u2="&#x109;" k="33" />
<hkern u1="&#x2a;" u2="&#x107;" k="33" />
<hkern u1="&#x2a;" u2="&#x104;" k="59" />
<hkern u1="&#x2a;" u2="&#x102;" k="59" />
<hkern u1="&#x2a;" u2="&#x100;" k="59" />
<hkern u1="&#x2a;" u2="&#xf8;" k="33" />
<hkern u1="&#x2a;" u2="&#xf6;" k="33" />
<hkern u1="&#x2a;" u2="&#xf5;" k="33" />
<hkern u1="&#x2a;" u2="&#xf4;" k="33" />
<hkern u1="&#x2a;" u2="&#xf3;" k="33" />
<hkern u1="&#x2a;" u2="&#xf2;" k="33" />
<hkern u1="&#x2a;" u2="&#xef;" k="-82" />
<hkern u1="&#x2a;" u2="&#xee;" k="-109" />
<hkern u1="&#x2a;" u2="&#xec;" k="-51" />
<hkern u1="&#x2a;" u2="&#xeb;" k="33" />
<hkern u1="&#x2a;" u2="&#xea;" k="33" />
<hkern u1="&#x2a;" u2="&#xe9;" k="33" />
<hkern u1="&#x2a;" u2="&#xe8;" k="33" />
<hkern u1="&#x2a;" u2="&#xe7;" k="33" />
<hkern u1="&#x2a;" u2="&#xc6;" k="74" />
<hkern u1="&#x2a;" u2="&#xc5;" k="59" />
<hkern u1="&#x2a;" u2="&#xc4;" k="59" />
<hkern u1="&#x2a;" u2="&#xc3;" k="59" />
<hkern u1="&#x2a;" u2="&#xc2;" k="59" />
<hkern u1="&#x2a;" u2="&#xc1;" k="59" />
<hkern u1="&#x2a;" u2="&#xc0;" k="59" />
<hkern u1="&#x2a;" u2="s" k="23" />
<hkern u1="&#x2a;" u2="q" k="37" />
<hkern u1="&#x2a;" u2="o" k="33" />
<hkern u1="&#x2a;" u2="g" k="29" />
<hkern u1="&#x2a;" u2="e" k="33" />
<hkern u1="&#x2a;" u2="d" k="37" />
<hkern u1="&#x2a;" u2="c" k="33" />
<hkern u1="&#x2a;" u2="T" k="-20" />
<hkern u1="&#x2a;" u2="J" k="20" />
<hkern u1="&#x2a;" u2="A" k="59" />
<hkern u1="&#x2c;" u2="v" k="47" />
<hkern u1="&#x2c;" u2="f" k="23" />
<hkern u1="&#x2c;" u2="V" k="82" />
<hkern u1="&#x2d;" u2="&#xc6;" k="18" />
<hkern u1="&#x2d;" u2="x" k="47" />
<hkern u1="&#x2d;" u2="v" k="18" />
<hkern u1="&#x2d;" u2="f" k="23" />
<hkern u1="&#x2d;" u2="X" k="66" />
<hkern u1="&#x2d;" u2="V" k="49" />
<hkern u1="&#x2e;" u2="v" k="47" />
<hkern u1="&#x2e;" u2="f" k="23" />
<hkern u1="&#x2e;" u2="V" k="82" />
<hkern u1="&#x2f;" u2="&#x219;" k="61" />
<hkern u1="&#x2f;" u2="&#x1ff;" k="74" />
<hkern u1="&#x2f;" u2="&#x1fe;" k="31" />
<hkern u1="&#x2f;" u2="&#x1fd;" k="57" />
<hkern u1="&#x2f;" u2="&#x1fc;" k="76" />
<hkern u1="&#x2f;" u2="&#x1fb;" k="57" />
<hkern u1="&#x2f;" u2="&#x1fa;" k="76" />
<hkern u1="&#x2f;" u2="&#x17e;" k="35" />
<hkern u1="&#x2f;" u2="&#x17c;" k="35" />
<hkern u1="&#x2f;" u2="&#x17a;" k="35" />
<hkern u1="&#x2f;" u2="&#x177;" k="29" />
<hkern u1="&#x2f;" u2="&#x175;" k="25" />
<hkern u1="&#x2f;" u2="&#x173;" k="41" />
<hkern u1="&#x2f;" u2="&#x171;" k="41" />
<hkern u1="&#x2f;" u2="&#x16f;" k="41" />
<hkern u1="&#x2f;" u2="&#x16d;" k="41" />
<hkern u1="&#x2f;" u2="&#x16b;" k="41" />
<hkern u1="&#x2f;" u2="&#x169;" k="41" />
<hkern u1="&#x2f;" u2="&#x161;" k="61" />
<hkern u1="&#x2f;" u2="&#x15f;" k="61" />
<hkern u1="&#x2f;" u2="&#x15d;" k="61" />
<hkern u1="&#x2f;" u2="&#x15b;" k="61" />
<hkern u1="&#x2f;" u2="&#x159;" k="45" />
<hkern u1="&#x2f;" u2="&#x157;" k="45" />
<hkern u1="&#x2f;" u2="&#x155;" k="45" />
<hkern u1="&#x2f;" u2="&#x153;" k="74" />
<hkern u1="&#x2f;" u2="&#x152;" k="31" />
<hkern u1="&#x2f;" u2="&#x151;" k="74" />
<hkern u1="&#x2f;" u2="&#x150;" k="31" />
<hkern u1="&#x2f;" u2="&#x14f;" k="74" />
<hkern u1="&#x2f;" u2="&#x14e;" k="31" />
<hkern u1="&#x2f;" u2="&#x14d;" k="74" />
<hkern u1="&#x2f;" u2="&#x14c;" k="31" />
<hkern u1="&#x2f;" u2="&#x14b;" k="45" />
<hkern u1="&#x2f;" u2="&#x148;" k="45" />
<hkern u1="&#x2f;" u2="&#x146;" k="45" />
<hkern u1="&#x2f;" u2="&#x144;" k="45" />
<hkern u1="&#x2f;" u2="&#x134;" k="29" />
<hkern u1="&#x2f;" u2="&#x12d;" k="-35" />
<hkern u1="&#x2f;" u2="&#x12b;" k="-23" />
<hkern u1="&#x2f;" u2="&#x129;" k="-61" />
<hkern u1="&#x2f;" u2="&#x123;" k="70" />
<hkern u1="&#x2f;" u2="&#x122;" k="31" />
<hkern u1="&#x2f;" u2="&#x121;" k="70" />
<hkern u1="&#x2f;" u2="&#x120;" k="31" />
<hkern u1="&#x2f;" u2="&#x11f;" k="70" />
<hkern u1="&#x2f;" u2="&#x11e;" k="31" />
<hkern u1="&#x2f;" u2="&#x11d;" k="70" />
<hkern u1="&#x2f;" u2="&#x11c;" k="31" />
<hkern u1="&#x2f;" u2="&#x11b;" k="74" />
<hkern u1="&#x2f;" u2="&#x119;" k="74" />
<hkern u1="&#x2f;" u2="&#x117;" k="74" />
<hkern u1="&#x2f;" u2="&#x115;" k="74" />
<hkern u1="&#x2f;" u2="&#x113;" k="74" />
<hkern u1="&#x2f;" u2="&#x111;" k="74" />
<hkern u1="&#x2f;" u2="&#x10f;" k="74" />
<hkern u1="&#x2f;" u2="&#x10d;" k="74" />
<hkern u1="&#x2f;" u2="&#x10c;" k="25" />
<hkern u1="&#x2f;" u2="&#x10b;" k="74" />
<hkern u1="&#x2f;" u2="&#x10a;" k="25" />
<hkern u1="&#x2f;" u2="&#x109;" k="74" />
<hkern u1="&#x2f;" u2="&#x108;" k="25" />
<hkern u1="&#x2f;" u2="&#x107;" k="74" />
<hkern u1="&#x2f;" u2="&#x106;" k="25" />
<hkern u1="&#x2f;" u2="&#x105;" k="57" />
<hkern u1="&#x2f;" u2="&#x104;" k="76" />
<hkern u1="&#x2f;" u2="&#x103;" k="57" />
<hkern u1="&#x2f;" u2="&#x102;" k="76" />
<hkern u1="&#x2f;" u2="&#x101;" k="57" />
<hkern u1="&#x2f;" u2="&#x100;" k="76" />
<hkern u1="&#x2f;" u2="&#xff;" k="29" />
<hkern u1="&#x2f;" u2="&#xfd;" k="29" />
<hkern u1="&#x2f;" u2="&#xfc;" k="41" />
<hkern u1="&#x2f;" u2="&#xfb;" k="41" />
<hkern u1="&#x2f;" u2="&#xfa;" k="41" />
<hkern u1="&#x2f;" u2="&#xf9;" k="41" />
<hkern u1="&#x2f;" u2="&#xf8;" k="74" />
<hkern u1="&#x2f;" u2="&#xf6;" k="74" />
<hkern u1="&#x2f;" u2="&#xf5;" k="74" />
<hkern u1="&#x2f;" u2="&#xf4;" k="74" />
<hkern u1="&#x2f;" u2="&#xf3;" k="74" />
<hkern u1="&#x2f;" u2="&#xf2;" k="74" />
<hkern u1="&#x2f;" u2="&#xf1;" k="45" />
<hkern u1="&#x2f;" u2="&#xef;" k="-92" />
<hkern u1="&#x2f;" u2="&#xec;" k="-88" />
<hkern u1="&#x2f;" u2="&#xeb;" k="74" />
<hkern u1="&#x2f;" u2="&#xea;" k="74" />
<hkern u1="&#x2f;" u2="&#xe9;" k="74" />
<hkern u1="&#x2f;" u2="&#xe8;" k="74" />
<hkern u1="&#x2f;" u2="&#xe7;" k="74" />
<hkern u1="&#x2f;" u2="&#xe6;" k="57" />
<hkern u1="&#x2f;" u2="&#xe5;" k="57" />
<hkern u1="&#x2f;" u2="&#xe4;" k="57" />
<hkern u1="&#x2f;" u2="&#xe3;" k="57" />
<hkern u1="&#x2f;" u2="&#xe2;" k="57" />
<hkern u1="&#x2f;" u2="&#xe1;" k="57" />
<hkern u1="&#x2f;" u2="&#xe0;" k="57" />
<hkern u1="&#x2f;" u2="&#xd8;" k="31" />
<hkern u1="&#x2f;" u2="&#xd6;" k="31" />
<hkern u1="&#x2f;" u2="&#xd5;" k="31" />
<hkern u1="&#x2f;" u2="&#xd4;" k="31" />
<hkern u1="&#x2f;" u2="&#xd3;" k="31" />
<hkern u1="&#x2f;" u2="&#xd2;" k="31" />
<hkern u1="&#x2f;" u2="&#xc7;" k="25" />
<hkern u1="&#x2f;" u2="&#xc6;" k="90" />
<hkern u1="&#x2f;" u2="&#xc5;" k="76" />
<hkern u1="&#x2f;" u2="&#xc4;" k="76" />
<hkern u1="&#x2f;" u2="&#xc3;" k="76" />
<hkern u1="&#x2f;" u2="&#xc2;" k="76" />
<hkern u1="&#x2f;" u2="&#xc1;" k="76" />
<hkern u1="&#x2f;" u2="&#xc0;" k="76" />
<hkern u1="&#x2f;" u2="z" k="35" />
<hkern u1="&#x2f;" u2="y" k="29" />
<hkern u1="&#x2f;" u2="w" k="25" />
<hkern u1="&#x2f;" u2="v" k="27" />
<hkern u1="&#x2f;" u2="u" k="41" />
<hkern u1="&#x2f;" u2="s" k="61" />
<hkern u1="&#x2f;" u2="r" k="45" />
<hkern u1="&#x2f;" u2="q" k="74" />
<hkern u1="&#x2f;" u2="p" k="45" />
<hkern u1="&#x2f;" u2="o" k="74" />
<hkern u1="&#x2f;" u2="n" k="45" />
<hkern u1="&#x2f;" u2="m" k="45" />
<hkern u1="&#x2f;" u2="g" k="70" />
<hkern u1="&#x2f;" u2="e" k="74" />
<hkern u1="&#x2f;" u2="d" k="74" />
<hkern u1="&#x2f;" u2="c" k="74" />
<hkern u1="&#x2f;" u2="a" k="57" />
<hkern u1="&#x2f;" u2="Q" k="31" />
<hkern u1="&#x2f;" u2="O" k="31" />
<hkern u1="&#x2f;" u2="J" k="29" />
<hkern u1="&#x2f;" u2="G" k="31" />
<hkern u1="&#x2f;" u2="C" k="25" />
<hkern u1="&#x2f;" u2="A" k="76" />
<hkern u1="&#x2f;" u2="&#x2f;" k="614" />
<hkern u1="&#x3a;" u2="V" k="20" />
<hkern u1="&#x3b;" u2="V" k="20" />
<hkern u1="&#x40;" u2="&#x178;" k="51" />
<hkern u1="&#x40;" u2="&#x176;" k="51" />
<hkern u1="&#x40;" u2="&#xdd;" k="51" />
<hkern u1="&#x40;" u2="Y" k="51" />
<hkern u1="A" u2="&#x2122;" k="61" />
<hkern u1="A" u2="&#xae;" k="37" />
<hkern u1="A" u2="v" k="29" />
<hkern u1="A" u2="f" k="16" />
<hkern u1="A" u2="\" k="86" />
<hkern u1="A" u2="V" k="55" />
<hkern u1="A" u2="&#x3f;" k="29" />
<hkern u1="A" u2="&#x2a;" k="55" />
<hkern u1="B" u2="&#x1fc;" k="16" />
<hkern u1="B" u2="&#x1fa;" k="16" />
<hkern u1="B" u2="&#x178;" k="45" />
<hkern u1="B" u2="&#x176;" k="45" />
<hkern u1="B" u2="&#x123;" k="14" />
<hkern u1="B" u2="&#x121;" k="14" />
<hkern u1="B" u2="&#x11f;" k="14" />
<hkern u1="B" u2="&#x11d;" k="14" />
<hkern u1="B" u2="&#x104;" k="16" />
<hkern u1="B" u2="&#x102;" k="16" />
<hkern u1="B" u2="&#x100;" k="16" />
<hkern u1="B" u2="&#xdd;" k="45" />
<hkern u1="B" u2="&#xc6;" k="23" />
<hkern u1="B" u2="&#xc5;" k="16" />
<hkern u1="B" u2="&#xc4;" k="16" />
<hkern u1="B" u2="&#xc3;" k="16" />
<hkern u1="B" u2="&#xc2;" k="16" />
<hkern u1="B" u2="&#xc1;" k="16" />
<hkern u1="B" u2="&#xc0;" k="16" />
<hkern u1="B" u2="g" k="14" />
<hkern u1="B" u2="]" k="29" />
<hkern u1="B" u2="\" k="33" />
<hkern u1="B" u2="Y" k="45" />
<hkern u1="B" u2="X" k="23" />
<hkern u1="B" u2="V" k="18" />
<hkern u1="B" u2="A" k="16" />
<hkern u1="C" u2="&#x135;" k="-43" />
<hkern u1="C" u2="&#x12d;" k="-20" />
<hkern u1="C" u2="&#x129;" k="-49" />
<hkern u1="C" u2="&#xef;" k="-72" />
<hkern u1="C" u2="&#xee;" k="-59" />
<hkern u1="C" u2="&#xec;" k="-90" />
<hkern u1="C" u2="v" k="10" />
<hkern u1="C" u2="f" k="10" />
<hkern u1="D" u2="&#xc6;" k="27" />
<hkern u1="D" u2="&#x7d;" k="25" />
<hkern u1="D" u2="]" k="39" />
<hkern u1="D" u2="\" k="37" />
<hkern u1="D" u2="X" k="37" />
<hkern u1="D" u2="V" k="20" />
<hkern u1="D" u2="&#x2f;" k="31" />
<hkern u1="D" u2="&#x29;" k="27" />
<hkern u1="E" u2="&#x135;" k="-43" />
<hkern u1="E" u2="&#x12d;" k="-12" />
<hkern u1="E" u2="&#x129;" k="-45" />
<hkern u1="E" u2="&#xef;" k="-66" />
<hkern u1="E" u2="&#xee;" k="-59" />
<hkern u1="E" u2="&#xec;" k="-86" />
<hkern u1="F" u2="&#x2026;" k="102" />
<hkern u1="F" u2="&#x201e;" k="102" />
<hkern u1="F" u2="&#x201a;" k="102" />
<hkern u1="F" u2="&#x2014;" k="18" />
<hkern u1="F" u2="&#x2013;" k="18" />
<hkern u1="F" u2="&#x219;" k="33" />
<hkern u1="F" u2="&#x1ff;" k="35" />
<hkern u1="F" u2="&#x1fd;" k="35" />
<hkern u1="F" u2="&#x1fc;" k="43" />
<hkern u1="F" u2="&#x1fb;" k="35" />
<hkern u1="F" u2="&#x1fa;" k="43" />
<hkern u1="F" u2="&#x17e;" k="20" />
<hkern u1="F" u2="&#x17c;" k="20" />
<hkern u1="F" u2="&#x17a;" k="20" />
<hkern u1="F" u2="&#x177;" k="10" />
<hkern u1="F" u2="&#x175;" k="12" />
<hkern u1="F" u2="&#x173;" k="27" />
<hkern u1="F" u2="&#x171;" k="27" />
<hkern u1="F" u2="&#x16f;" k="27" />
<hkern u1="F" u2="&#x16d;" k="27" />
<hkern u1="F" u2="&#x16b;" k="27" />
<hkern u1="F" u2="&#x169;" k="27" />
<hkern u1="F" u2="&#x161;" k="33" />
<hkern u1="F" u2="&#x15f;" k="33" />
<hkern u1="F" u2="&#x15d;" k="33" />
<hkern u1="F" u2="&#x15b;" k="33" />
<hkern u1="F" u2="&#x159;" k="35" />
<hkern u1="F" u2="&#x157;" k="35" />
<hkern u1="F" u2="&#x155;" k="35" />
<hkern u1="F" u2="&#x153;" k="35" />
<hkern u1="F" u2="&#x151;" k="35" />
<hkern u1="F" u2="&#x14f;" k="35" />
<hkern u1="F" u2="&#x14d;" k="35" />
<hkern u1="F" u2="&#x14b;" k="35" />
<hkern u1="F" u2="&#x148;" k="35" />
<hkern u1="F" u2="&#x146;" k="35" />
<hkern u1="F" u2="&#x144;" k="35" />
<hkern u1="F" u2="&#x135;" k="-80" />
<hkern u1="F" u2="&#x134;" k="16" />
<hkern u1="F" u2="&#x131;" k="35" />
<hkern u1="F" u2="&#x12d;" k="-47" />
<hkern u1="F" u2="&#x12b;" k="-31" />
<hkern u1="F" u2="&#x129;" k="-80" />
<hkern u1="F" u2="&#x123;" k="43" />
<hkern u1="F" u2="&#x121;" k="43" />
<hkern u1="F" u2="&#x11f;" k="43" />
<hkern u1="F" u2="&#x11d;" k="43" />
<hkern u1="F" u2="&#x11b;" k="35" />
<hkern u1="F" u2="&#x119;" k="35" />
<hkern u1="F" u2="&#x117;" k="35" />
<hkern u1="F" u2="&#x115;" k="35" />
<hkern u1="F" u2="&#x113;" k="35" />
<hkern u1="F" u2="&#x111;" k="37" />
<hkern u1="F" u2="&#x10f;" k="37" />
<hkern u1="F" u2="&#x10d;" k="35" />
<hkern u1="F" u2="&#x10b;" k="35" />
<hkern u1="F" u2="&#x109;" k="35" />
<hkern u1="F" u2="&#x107;" k="35" />
<hkern u1="F" u2="&#x105;" k="35" />
<hkern u1="F" u2="&#x104;" k="43" />
<hkern u1="F" u2="&#x103;" k="35" />
<hkern u1="F" u2="&#x102;" k="43" />
<hkern u1="F" u2="&#x101;" k="35" />
<hkern u1="F" u2="&#x100;" k="43" />
<hkern u1="F" u2="&#xff;" k="10" />
<hkern u1="F" u2="&#xfd;" k="10" />
<hkern u1="F" u2="&#xfc;" k="27" />
<hkern u1="F" u2="&#xfb;" k="27" />
<hkern u1="F" u2="&#xfa;" k="27" />
<hkern u1="F" u2="&#xf9;" k="27" />
<hkern u1="F" u2="&#xf8;" k="35" />
<hkern u1="F" u2="&#xf6;" k="35" />
<hkern u1="F" u2="&#xf5;" k="35" />
<hkern u1="F" u2="&#xf4;" k="35" />
<hkern u1="F" u2="&#xf3;" k="35" />
<hkern u1="F" u2="&#xf2;" k="35" />
<hkern u1="F" u2="&#xf1;" k="35" />
<hkern u1="F" u2="&#xef;" k="-100" />
<hkern u1="F" u2="&#xee;" k="-94" />
<hkern u1="F" u2="&#xec;" k="-121" />
<hkern u1="F" u2="&#xeb;" k="35" />
<hkern u1="F" u2="&#xea;" k="35" />
<hkern u1="F" u2="&#xe9;" k="35" />
<hkern u1="F" u2="&#xe8;" k="35" />
<hkern u1="F" u2="&#xe7;" k="35" />
<hkern u1="F" u2="&#xe6;" k="35" />
<hkern u1="F" u2="&#xe5;" k="35" />
<hkern u1="F" u2="&#xe4;" k="35" />
<hkern u1="F" u2="&#xe3;" k="35" />
<hkern u1="F" u2="&#xe2;" k="35" />
<hkern u1="F" u2="&#xe1;" k="35" />
<hkern u1="F" u2="&#xe0;" k="35" />
<hkern u1="F" u2="&#xc6;" k="59" />
<hkern u1="F" u2="&#xc5;" k="43" />
<hkern u1="F" u2="&#xc4;" k="43" />
<hkern u1="F" u2="&#xc3;" k="43" />
<hkern u1="F" u2="&#xc2;" k="43" />
<hkern u1="F" u2="&#xc1;" k="43" />
<hkern u1="F" u2="&#xc0;" k="43" />
<hkern u1="F" u2="z" k="20" />
<hkern u1="F" u2="y" k="10" />
<hkern u1="F" u2="x" k="14" />
<hkern u1="F" u2="w" k="12" />
<hkern u1="F" u2="u" k="27" />
<hkern u1="F" u2="s" k="33" />
<hkern u1="F" u2="r" k="35" />
<hkern u1="F" u2="q" k="37" />
<hkern u1="F" u2="p" k="35" />
<hkern u1="F" u2="o" k="35" />
<hkern u1="F" u2="n" k="35" />
<hkern u1="F" u2="m" k="35" />
<hkern u1="F" u2="g" k="43" />
<hkern u1="F" u2="f" k="10" />
<hkern u1="F" u2="e" k="35" />
<hkern u1="F" u2="d" k="37" />
<hkern u1="F" u2="c" k="35" />
<hkern u1="F" u2="a" k="35" />
<hkern u1="F" u2="J" k="16" />
<hkern u1="F" u2="A" k="43" />
<hkern u1="F" u2="&#x2f;" k="78" />
<hkern u1="F" u2="&#x2e;" k="102" />
<hkern u1="F" u2="&#x2d;" k="18" />
<hkern u1="F" u2="&#x2c;" k="102" />
<hkern u1="G" u2="&#xef;" k="-31" />
<hkern u1="G" u2="&#xee;" k="-18" />
<hkern u1="G" u2="&#xec;" k="-51" />
<hkern u1="G" u2="v" k="10" />
<hkern u1="G" u2="f" k="12" />
<hkern u1="G" u2="\" k="20" />
<hkern u1="G" u2="V" k="16" />
<hkern u1="H" u2="&#xec;" k="-16" />
<hkern u1="H" u2="f" k="12" />
<hkern u1="I" u2="&#xec;" k="-16" />
<hkern u1="I" u2="f" k="12" />
<hkern u1="J" u2="&#xec;" k="-23" />
<hkern u1="J" u2="f" k="10" />
<hkern u1="K" u2="&#x12d;" k="-51" />
<hkern u1="K" u2="&#x12b;" k="-35" />
<hkern u1="K" u2="&#x129;" k="-72" />
<hkern u1="K" u2="&#xef;" k="-104" />
<hkern u1="K" u2="&#xee;" k="-12" />
<hkern u1="K" u2="&#xec;" k="-96" />
<hkern u1="K" u2="v" k="35" />
<hkern u1="K" u2="f" k="23" />
<hkern u1="L" u2="&#x2122;" k="156" />
<hkern u1="L" u2="&#xae;" k="127" />
<hkern u1="L" u2="v" k="66" />
<hkern u1="L" u2="f" k="16" />
<hkern u1="L" u2="\" k="147" />
<hkern u1="L" u2="V" k="113" />
<hkern u1="L" u2="&#x2a;" k="154" />
<hkern u1="M" u2="&#xec;" k="-16" />
<hkern u1="M" u2="f" k="12" />
<hkern u1="N" u2="&#xec;" k="-16" />
<hkern u1="N" u2="f" k="12" />
<hkern u1="O" u2="&#xc6;" k="27" />
<hkern u1="O" u2="&#x7d;" k="25" />
<hkern u1="O" u2="]" k="41" />
<hkern u1="O" u2="\" k="39" />
<hkern u1="O" u2="X" k="35" />
<hkern u1="O" u2="V" k="20" />
<hkern u1="O" u2="&#x2f;" k="29" />
<hkern u1="O" u2="&#x29;" k="27" />
<hkern u1="P" u2="&#x2026;" k="125" />
<hkern u1="P" u2="&#x201e;" k="125" />
<hkern u1="P" u2="&#x201a;" k="125" />
<hkern u1="P" u2="&#x1fd;" k="10" />
<hkern u1="P" u2="&#x1fc;" k="39" />
<hkern u1="P" u2="&#x1fb;" k="10" />
<hkern u1="P" u2="&#x1fa;" k="39" />
<hkern u1="P" u2="&#x178;" k="41" />
<hkern u1="P" u2="&#x176;" k="41" />
<hkern u1="P" u2="&#x135;" k="-16" />
<hkern u1="P" u2="&#x134;" k="18" />
<hkern u1="P" u2="&#x105;" k="10" />
<hkern u1="P" u2="&#x104;" k="39" />
<hkern u1="P" u2="&#x103;" k="10" />
<hkern u1="P" u2="&#x102;" k="39" />
<hkern u1="P" u2="&#x101;" k="10" />
<hkern u1="P" u2="&#x100;" k="39" />
<hkern u1="P" u2="&#xef;" k="-16" />
<hkern u1="P" u2="&#xee;" k="-33" />
<hkern u1="P" u2="&#xec;" k="-23" />
<hkern u1="P" u2="&#xe6;" k="10" />
<hkern u1="P" u2="&#xe5;" k="10" />
<hkern u1="P" u2="&#xe4;" k="10" />
<hkern u1="P" u2="&#xe3;" k="10" />
<hkern u1="P" u2="&#xe2;" k="10" />
<hkern u1="P" u2="&#xe1;" k="10" />
<hkern u1="P" u2="&#xe0;" k="10" />
<hkern u1="P" u2="&#xdd;" k="41" />
<hkern u1="P" u2="&#xc6;" k="51" />
<hkern u1="P" u2="&#xc5;" k="39" />
<hkern u1="P" u2="&#xc4;" k="39" />
<hkern u1="P" u2="&#xc3;" k="39" />
<hkern u1="P" u2="&#xc2;" k="39" />
<hkern u1="P" u2="&#xc1;" k="39" />
<hkern u1="P" u2="&#xc0;" k="39" />
<hkern u1="P" u2="&#x7d;" k="20" />
<hkern u1="P" u2="a" k="10" />
<hkern u1="P" u2="]" k="27" />
<hkern u1="P" u2="\" k="25" />
<hkern u1="P" u2="Y" k="41" />
<hkern u1="P" u2="X" k="35" />
<hkern u1="P" u2="V" k="14" />
<hkern u1="P" u2="J" k="18" />
<hkern u1="P" u2="A" k="39" />
<hkern u1="P" u2="&#x2f;" k="78" />
<hkern u1="P" u2="&#x2e;" k="125" />
<hkern u1="P" u2="&#x2c;" k="125" />
<hkern u1="P" u2="&#x29;" k="20" />
<hkern u1="Q" u2="&#xc6;" k="27" />
<hkern u1="Q" u2="&#x7d;" k="25" />
<hkern u1="Q" u2="]" k="41" />
<hkern u1="Q" u2="\" k="39" />
<hkern u1="Q" u2="X" k="35" />
<hkern u1="Q" u2="V" k="20" />
<hkern u1="Q" u2="&#x2f;" k="29" />
<hkern u1="Q" u2="&#x29;" k="27" />
<hkern u1="R" u2="&#xc6;" k="20" />
<hkern u1="R" u2="\" k="31" />
<hkern u1="R" u2="X" k="12" />
<hkern u1="R" u2="V" k="18" />
<hkern u1="S" u2="&#x129;" k="-20" />
<hkern u1="S" u2="&#xef;" k="-41" />
<hkern u1="S" u2="&#xee;" k="-16" />
<hkern u1="S" u2="&#xec;" k="-57" />
<hkern u1="S" u2="&#xc6;" k="20" />
<hkern u1="S" u2="x" k="16" />
<hkern u1="S" u2="v" k="12" />
<hkern u1="S" u2="f" k="18" />
<hkern u1="S" u2="X" k="10" />
<hkern u1="S" u2="V" k="16" />
<hkern u1="T" u2="&#x16d;" k="111" />
<hkern u1="T" u2="&#x169;" k="111" />
<hkern u1="T" u2="&#x15d;" k="117" />
<hkern u1="T" u2="&#x159;" k="76" />
<hkern u1="T" u2="&#x155;" k="78" />
<hkern u1="T" u2="&#x151;" k="86" />
<hkern u1="T" u2="&#x135;" k="-98" />
<hkern u1="T" u2="&#x131;" k="106" />
<hkern u1="T" u2="&#x12d;" k="-66" />
<hkern u1="T" u2="&#x12b;" k="-49" />
<hkern u1="T" u2="&#x129;" k="-98" />
<hkern u1="T" u2="&#x11f;" k="154" />
<hkern u1="T" u2="&#x109;" k="88" />
<hkern u1="T" u2="&#xef;" k="-119" />
<hkern u1="T" u2="&#xee;" k="-113" />
<hkern u1="T" u2="&#xec;" k="-139" />
<hkern u1="T" u2="&#xe4;" k="111" />
<hkern u1="T" u2="&#xe3;" k="90" />
<hkern u1="T" u2="&#xc6;" k="100" />
<hkern u1="T" u2="x" k="100" />
<hkern u1="T" u2="v" k="100" />
<hkern u1="T" u2="f" k="31" />
<hkern u1="T" u2="&#x40;" k="31" />
<hkern u1="T" u2="&#x2f;" k="100" />
<hkern u1="T" u2="&#x26;" k="20" />
<hkern u1="U" u2="&#xec;" k="-29" />
<hkern u1="U" u2="&#xc6;" k="14" />
<hkern u1="U" u2="f" k="10" />
<hkern u1="U" u2="&#x2f;" k="33" />
<hkern u1="V" u2="&#x203a;" k="23" />
<hkern u1="V" u2="&#x2039;" k="49" />
<hkern u1="V" u2="&#x2026;" k="82" />
<hkern u1="V" u2="&#x201e;" k="82" />
<hkern u1="V" u2="&#x201a;" k="82" />
<hkern u1="V" u2="&#x2014;" k="49" />
<hkern u1="V" u2="&#x2013;" k="49" />
<hkern u1="V" u2="&#x219;" k="45" />
<hkern u1="V" u2="&#x218;" k="16" />
<hkern u1="V" u2="&#x1ff;" k="59" />
<hkern u1="V" u2="&#x1fe;" k="20" />
<hkern u1="V" u2="&#x1fd;" k="47" />
<hkern u1="V" u2="&#x1fc;" k="55" />
<hkern u1="V" u2="&#x1fb;" k="47" />
<hkern u1="V" u2="&#x1fa;" k="55" />
<hkern u1="V" u2="&#x17e;" k="27" />
<hkern u1="V" u2="&#x17c;" k="27" />
<hkern u1="V" u2="&#x17a;" k="27" />
<hkern u1="V" u2="&#x177;" k="12" />
<hkern u1="V" u2="&#x175;" k="18" />
<hkern u1="V" u2="&#x173;" k="35" />
<hkern u1="V" u2="&#x171;" k="35" />
<hkern u1="V" u2="&#x16f;" k="35" />
<hkern u1="V" u2="&#x16d;" k="35" />
<hkern u1="V" u2="&#x16b;" k="35" />
<hkern u1="V" u2="&#x169;" k="35" />
<hkern u1="V" u2="&#x161;" k="45" />
<hkern u1="V" u2="&#x160;" k="16" />
<hkern u1="V" u2="&#x15f;" k="45" />
<hkern u1="V" u2="&#x15e;" k="16" />
<hkern u1="V" u2="&#x15d;" k="45" />
<hkern u1="V" u2="&#x15c;" k="16" />
<hkern u1="V" u2="&#x15b;" k="45" />
<hkern u1="V" u2="&#x15a;" k="16" />
<hkern u1="V" u2="&#x159;" k="43" />
<hkern u1="V" u2="&#x157;" k="43" />
<hkern u1="V" u2="&#x155;" k="43" />
<hkern u1="V" u2="&#x153;" k="59" />
<hkern u1="V" u2="&#x152;" k="20" />
<hkern u1="V" u2="&#x151;" k="59" />
<hkern u1="V" u2="&#x150;" k="20" />
<hkern u1="V" u2="&#x14f;" k="59" />
<hkern u1="V" u2="&#x14e;" k="20" />
<hkern u1="V" u2="&#x14d;" k="59" />
<hkern u1="V" u2="&#x14c;" k="20" />
<hkern u1="V" u2="&#x14b;" k="43" />
<hkern u1="V" u2="&#x148;" k="43" />
<hkern u1="V" u2="&#x146;" k="43" />
<hkern u1="V" u2="&#x144;" k="43" />
<hkern u1="V" u2="&#x135;" k="-47" />
<hkern u1="V" u2="&#x134;" k="23" />
<hkern u1="V" u2="&#x131;" k="43" />
<hkern u1="V" u2="&#x12d;" k="-61" />
<hkern u1="V" u2="&#x12b;" k="-45" />
<hkern u1="V" u2="&#x129;" k="-86" />
<hkern u1="V" u2="&#x123;" k="61" />
<hkern u1="V" u2="&#x122;" k="20" />
<hkern u1="V" u2="&#x121;" k="61" />
<hkern u1="V" u2="&#x120;" k="20" />
<hkern u1="V" u2="&#x11f;" k="61" />
<hkern u1="V" u2="&#x11e;" k="20" />
<hkern u1="V" u2="&#x11d;" k="61" />
<hkern u1="V" u2="&#x11c;" k="20" />
<hkern u1="V" u2="&#x11b;" k="59" />
<hkern u1="V" u2="&#x119;" k="59" />
<hkern u1="V" u2="&#x117;" k="59" />
<hkern u1="V" u2="&#x115;" k="59" />
<hkern u1="V" u2="&#x113;" k="59" />
<hkern u1="V" u2="&#x111;" k="57" />
<hkern u1="V" u2="&#x10f;" k="57" />
<hkern u1="V" u2="&#x10d;" k="59" />
<hkern u1="V" u2="&#x10c;" k="18" />
<hkern u1="V" u2="&#x10b;" k="59" />
<hkern u1="V" u2="&#x10a;" k="18" />
<hkern u1="V" u2="&#x109;" k="59" />
<hkern u1="V" u2="&#x108;" k="18" />
<hkern u1="V" u2="&#x107;" k="59" />
<hkern u1="V" u2="&#x106;" k="18" />
<hkern u1="V" u2="&#x105;" k="47" />
<hkern u1="V" u2="&#x104;" k="55" />
<hkern u1="V" u2="&#x103;" k="47" />
<hkern u1="V" u2="&#x102;" k="55" />
<hkern u1="V" u2="&#x101;" k="47" />
<hkern u1="V" u2="&#x100;" k="55" />
<hkern u1="V" u2="&#xff;" k="12" />
<hkern u1="V" u2="&#xfd;" k="12" />
<hkern u1="V" u2="&#xfc;" k="35" />
<hkern u1="V" u2="&#xfb;" k="35" />
<hkern u1="V" u2="&#xfa;" k="35" />
<hkern u1="V" u2="&#xf9;" k="35" />
<hkern u1="V" u2="&#xf8;" k="59" />
<hkern u1="V" u2="&#xf6;" k="59" />
<hkern u1="V" u2="&#xf5;" k="59" />
<hkern u1="V" u2="&#xf4;" k="59" />
<hkern u1="V" u2="&#xf3;" k="59" />
<hkern u1="V" u2="&#xf2;" k="59" />
<hkern u1="V" u2="&#xf1;" k="43" />
<hkern u1="V" u2="&#xef;" k="-115" />
<hkern u1="V" u2="&#xee;" k="-61" />
<hkern u1="V" u2="&#xec;" k="-117" />
<hkern u1="V" u2="&#xeb;" k="59" />
<hkern u1="V" u2="&#xea;" k="59" />
<hkern u1="V" u2="&#xe9;" k="59" />
<hkern u1="V" u2="&#xe8;" k="59" />
<hkern u1="V" u2="&#xe7;" k="59" />
<hkern u1="V" u2="&#xe6;" k="47" />
<hkern u1="V" u2="&#xe5;" k="47" />
<hkern u1="V" u2="&#xe4;" k="47" />
<hkern u1="V" u2="&#xe3;" k="47" />
<hkern u1="V" u2="&#xe2;" k="47" />
<hkern u1="V" u2="&#xe1;" k="47" />
<hkern u1="V" u2="&#xe0;" k="47" />
<hkern u1="V" u2="&#xd8;" k="20" />
<hkern u1="V" u2="&#xd6;" k="20" />
<hkern u1="V" u2="&#xd5;" k="20" />
<hkern u1="V" u2="&#xd4;" k="20" />
<hkern u1="V" u2="&#xd3;" k="20" />
<hkern u1="V" u2="&#xd2;" k="20" />
<hkern u1="V" u2="&#xc7;" k="18" />
<hkern u1="V" u2="&#xc6;" k="63" />
<hkern u1="V" u2="&#xc5;" k="55" />
<hkern u1="V" u2="&#xc4;" k="55" />
<hkern u1="V" u2="&#xc3;" k="55" />
<hkern u1="V" u2="&#xc2;" k="55" />
<hkern u1="V" u2="&#xc1;" k="55" />
<hkern u1="V" u2="&#xc0;" k="55" />
<hkern u1="V" u2="&#xbb;" k="23" />
<hkern u1="V" u2="&#xab;" k="49" />
<hkern u1="V" u2="z" k="27" />
<hkern u1="V" u2="y" k="12" />
<hkern u1="V" u2="x" k="14" />
<hkern u1="V" u2="w" k="18" />
<hkern u1="V" u2="v" k="12" />
<hkern u1="V" u2="u" k="35" />
<hkern u1="V" u2="s" k="45" />
<hkern u1="V" u2="r" k="43" />
<hkern u1="V" u2="q" k="57" />
<hkern u1="V" u2="p" k="43" />
<hkern u1="V" u2="o" k="59" />
<hkern u1="V" u2="n" k="43" />
<hkern u1="V" u2="m" k="43" />
<hkern u1="V" u2="g" k="61" />
<hkern u1="V" u2="f" k="14" />
<hkern u1="V" u2="e" k="59" />
<hkern u1="V" u2="d" k="57" />
<hkern u1="V" u2="c" k="59" />
<hkern u1="V" u2="a" k="47" />
<hkern u1="V" u2="S" k="16" />
<hkern u1="V" u2="Q" k="20" />
<hkern u1="V" u2="O" k="20" />
<hkern u1="V" u2="J" k="23" />
<hkern u1="V" u2="G" k="20" />
<hkern u1="V" u2="C" k="18" />
<hkern u1="V" u2="A" k="55" />
<hkern u1="V" u2="&#x40;" k="25" />
<hkern u1="V" u2="&#x3b;" k="20" />
<hkern u1="V" u2="&#x3a;" k="20" />
<hkern u1="V" u2="&#x2f;" k="82" />
<hkern u1="V" u2="&#x2e;" k="82" />
<hkern u1="V" u2="&#x2d;" k="49" />
<hkern u1="V" u2="&#x2c;" k="82" />
<hkern u1="V" u2="&#x26;" k="29" />
<hkern u1="W" u2="&#x135;" k="-49" />
<hkern u1="W" u2="&#x131;" k="27" />
<hkern u1="W" u2="&#x12d;" k="-53" />
<hkern u1="W" u2="&#x12b;" k="-37" />
<hkern u1="W" u2="&#x129;" k="-80" />
<hkern u1="W" u2="&#xef;" k="-109" />
<hkern u1="W" u2="&#xee;" k="-61" />
<hkern u1="W" u2="&#xec;" k="-111" />
<hkern u1="W" u2="&#xc6;" k="47" />
<hkern u1="W" u2="&#x2f;" k="59" />
<hkern u1="X" u2="&#x2039;" k="37" />
<hkern u1="X" u2="&#x2014;" k="66" />
<hkern u1="X" u2="&#x2013;" k="66" />
<hkern u1="X" u2="&#x21b;" k="20" />
<hkern u1="X" u2="&#x1ff;" k="53" />
<hkern u1="X" u2="&#x1fe;" k="35" />
<hkern u1="X" u2="&#x1fd;" k="10" />
<hkern u1="X" u2="&#x1fb;" k="10" />
<hkern u1="X" u2="&#x177;" k="47" />
<hkern u1="X" u2="&#x175;" k="49" />
<hkern u1="X" u2="&#x173;" k="39" />
<hkern u1="X" u2="&#x171;" k="39" />
<hkern u1="X" u2="&#x16f;" k="39" />
<hkern u1="X" u2="&#x16d;" k="39" />
<hkern u1="X" u2="&#x16b;" k="39" />
<hkern u1="X" u2="&#x169;" k="39" />
<hkern u1="X" u2="&#x167;" k="20" />
<hkern u1="X" u2="&#x165;" k="20" />
<hkern u1="X" u2="&#x159;" k="10" />
<hkern u1="X" u2="&#x157;" k="10" />
<hkern u1="X" u2="&#x155;" k="10" />
<hkern u1="X" u2="&#x153;" k="53" />
<hkern u1="X" u2="&#x152;" k="35" />
<hkern u1="X" u2="&#x151;" k="53" />
<hkern u1="X" u2="&#x150;" k="35" />
<hkern u1="X" u2="&#x14f;" k="53" />
<hkern u1="X" u2="&#x14e;" k="35" />
<hkern u1="X" u2="&#x14d;" k="53" />
<hkern u1="X" u2="&#x14c;" k="35" />
<hkern u1="X" u2="&#x14b;" k="10" />
<hkern u1="X" u2="&#x148;" k="10" />
<hkern u1="X" u2="&#x146;" k="10" />
<hkern u1="X" u2="&#x144;" k="10" />
<hkern u1="X" u2="&#x135;" k="-14" />
<hkern u1="X" u2="&#x12d;" k="-74" />
<hkern u1="X" u2="&#x12b;" k="-57" />
<hkern u1="X" u2="&#x129;" k="-86" />
<hkern u1="X" u2="&#x123;" k="41" />
<hkern u1="X" u2="&#x122;" k="35" />
<hkern u1="X" u2="&#x121;" k="41" />
<hkern u1="X" u2="&#x120;" k="35" />
<hkern u1="X" u2="&#x11f;" k="41" />
<hkern u1="X" u2="&#x11e;" k="35" />
<hkern u1="X" u2="&#x11d;" k="41" />
<hkern u1="X" u2="&#x11c;" k="35" />
<hkern u1="X" u2="&#x11b;" k="53" />
<hkern u1="X" u2="&#x119;" k="53" />
<hkern u1="X" u2="&#x117;" k="53" />
<hkern u1="X" u2="&#x115;" k="53" />
<hkern u1="X" u2="&#x113;" k="53" />
<hkern u1="X" u2="&#x111;" k="41" />
<hkern u1="X" u2="&#x10f;" k="41" />
<hkern u1="X" u2="&#x10d;" k="53" />
<hkern u1="X" u2="&#x10c;" k="33" />
<hkern u1="X" u2="&#x10b;" k="53" />
<hkern u1="X" u2="&#x10a;" k="33" />
<hkern u1="X" u2="&#x109;" k="53" />
<hkern u1="X" u2="&#x108;" k="33" />
<hkern u1="X" u2="&#x107;" k="53" />
<hkern u1="X" u2="&#x106;" k="33" />
<hkern u1="X" u2="&#x105;" k="10" />
<hkern u1="X" u2="&#x103;" k="10" />
<hkern u1="X" u2="&#x101;" k="10" />
<hkern u1="X" u2="&#xff;" k="47" />
<hkern u1="X" u2="&#xfd;" k="47" />
<hkern u1="X" u2="&#xfc;" k="39" />
<hkern u1="X" u2="&#xfb;" k="39" />
<hkern u1="X" u2="&#xfa;" k="39" />
<hkern u1="X" u2="&#xf9;" k="39" />
<hkern u1="X" u2="&#xf8;" k="53" />
<hkern u1="X" u2="&#xf6;" k="53" />
<hkern u1="X" u2="&#xf5;" k="53" />
<hkern u1="X" u2="&#xf4;" k="53" />
<hkern u1="X" u2="&#xf3;" k="53" />
<hkern u1="X" u2="&#xf2;" k="53" />
<hkern u1="X" u2="&#xf1;" k="10" />
<hkern u1="X" u2="&#xef;" k="-127" />
<hkern u1="X" u2="&#xee;" k="-27" />
<hkern u1="X" u2="&#xec;" k="-113" />
<hkern u1="X" u2="&#xeb;" k="53" />
<hkern u1="X" u2="&#xea;" k="53" />
<hkern u1="X" u2="&#xe9;" k="53" />
<hkern u1="X" u2="&#xe8;" k="53" />
<hkern u1="X" u2="&#xe7;" k="53" />
<hkern u1="X" u2="&#xe6;" k="10" />
<hkern u1="X" u2="&#xe5;" k="10" />
<hkern u1="X" u2="&#xe4;" k="10" />
<hkern u1="X" u2="&#xe3;" k="10" />
<hkern u1="X" u2="&#xe2;" k="10" />
<hkern u1="X" u2="&#xe1;" k="10" />
<hkern u1="X" u2="&#xe0;" k="10" />
<hkern u1="X" u2="&#xd8;" k="35" />
<hkern u1="X" u2="&#xd6;" k="35" />
<hkern u1="X" u2="&#xd5;" k="35" />
<hkern u1="X" u2="&#xd4;" k="35" />
<hkern u1="X" u2="&#xd3;" k="35" />
<hkern u1="X" u2="&#xd2;" k="35" />
<hkern u1="X" u2="&#xc7;" k="33" />
<hkern u1="X" u2="&#xab;" k="37" />
<hkern u1="X" u2="y" k="47" />
<hkern u1="X" u2="w" k="49" />
<hkern u1="X" u2="v" k="45" />
<hkern u1="X" u2="u" k="39" />
<hkern u1="X" u2="t" k="20" />
<hkern u1="X" u2="r" k="10" />
<hkern u1="X" u2="q" k="41" />
<hkern u1="X" u2="p" k="10" />
<hkern u1="X" u2="o" k="53" />
<hkern u1="X" u2="n" k="10" />
<hkern u1="X" u2="m" k="10" />
<hkern u1="X" u2="g" k="41" />
<hkern u1="X" u2="f" k="18" />
<hkern u1="X" u2="e" k="53" />
<hkern u1="X" u2="d" k="41" />
<hkern u1="X" u2="c" k="53" />
<hkern u1="X" u2="a" k="10" />
<hkern u1="X" u2="Q" k="35" />
<hkern u1="X" u2="O" k="35" />
<hkern u1="X" u2="G" k="35" />
<hkern u1="X" u2="C" k="33" />
<hkern u1="X" u2="&#x2d;" k="66" />
<hkern u1="Y" u2="&#x159;" k="53" />
<hkern u1="Y" u2="&#x155;" k="66" />
<hkern u1="Y" u2="&#x151;" k="98" />
<hkern u1="Y" u2="&#x142;" k="12" />
<hkern u1="Y" u2="&#x135;" k="-31" />
<hkern u1="Y" u2="&#x131;" k="121" />
<hkern u1="Y" u2="&#x12d;" k="-90" />
<hkern u1="Y" u2="&#x12b;" k="-74" />
<hkern u1="Y" u2="&#x129;" k="-100" />
<hkern u1="Y" u2="&#x103;" k="115" />
<hkern u1="Y" u2="&#xff;" k="49" />
<hkern u1="Y" u2="&#xef;" k="-143" />
<hkern u1="Y" u2="&#xee;" k="-43" />
<hkern u1="Y" u2="&#xec;" k="-127" />
<hkern u1="Y" u2="&#xeb;" k="125" />
<hkern u1="Y" u2="&#xe4;" k="88" />
<hkern u1="Y" u2="&#xe3;" k="70" />
<hkern u1="Y" u2="&#xdf;" k="29" />
<hkern u1="Y" u2="&#xc6;" k="121" />
<hkern u1="Y" u2="&#xae;" k="39" />
<hkern u1="Y" u2="x" k="70" />
<hkern u1="Y" u2="v" k="68" />
<hkern u1="Y" u2="f" k="47" />
<hkern u1="Y" u2="&#x40;" k="72" />
<hkern u1="Y" u2="&#x2f;" k="133" />
<hkern u1="Y" u2="&#x2a;" k="-10" />
<hkern u1="Y" u2="&#x26;" k="57" />
<hkern u1="Z" u2="&#x135;" k="-41" />
<hkern u1="Z" u2="&#x129;" k="-41" />
<hkern u1="Z" u2="&#xef;" k="-61" />
<hkern u1="Z" u2="&#xee;" k="-57" />
<hkern u1="Z" u2="&#xec;" k="-84" />
<hkern u1="Z" u2="v" k="14" />
<hkern u1="Z" u2="f" k="14" />
<hkern u1="[" u2="&#x21b;" k="23" />
<hkern u1="[" u2="&#x1ff;" k="55" />
<hkern u1="[" u2="&#x1fe;" k="41" />
<hkern u1="[" u2="&#x1fd;" k="35" />
<hkern u1="[" u2="&#x1fb;" k="35" />
<hkern u1="[" u2="&#x177;" k="41" />
<hkern u1="[" u2="&#x175;" k="43" />
<hkern u1="[" u2="&#x173;" k="41" />
<hkern u1="[" u2="&#x171;" k="41" />
<hkern u1="[" u2="&#x16f;" k="41" />
<hkern u1="[" u2="&#x16d;" k="41" />
<hkern u1="[" u2="&#x16b;" k="41" />
<hkern u1="[" u2="&#x169;" k="41" />
<hkern u1="[" u2="&#x167;" k="23" />
<hkern u1="[" u2="&#x165;" k="23" />
<hkern u1="[" u2="&#x153;" k="55" />
<hkern u1="[" u2="&#x152;" k="41" />
<hkern u1="[" u2="&#x151;" k="55" />
<hkern u1="[" u2="&#x150;" k="41" />
<hkern u1="[" u2="&#x14f;" k="55" />
<hkern u1="[" u2="&#x14e;" k="41" />
<hkern u1="[" u2="&#x14d;" k="55" />
<hkern u1="[" u2="&#x14c;" k="41" />
<hkern u1="[" u2="&#x135;" k="-18" />
<hkern u1="[" u2="&#x12d;" k="-51" />
<hkern u1="[" u2="&#x129;" k="-57" />
<hkern u1="[" u2="&#x122;" k="41" />
<hkern u1="[" u2="&#x120;" k="41" />
<hkern u1="[" u2="&#x11e;" k="41" />
<hkern u1="[" u2="&#x11c;" k="41" />
<hkern u1="[" u2="&#x11b;" k="55" />
<hkern u1="[" u2="&#x119;" k="55" />
<hkern u1="[" u2="&#x117;" k="55" />
<hkern u1="[" u2="&#x115;" k="55" />
<hkern u1="[" u2="&#x113;" k="55" />
<hkern u1="[" u2="&#x111;" k="53" />
<hkern u1="[" u2="&#x10f;" k="53" />
<hkern u1="[" u2="&#x10d;" k="55" />
<hkern u1="[" u2="&#x10c;" k="33" />
<hkern u1="[" u2="&#x10b;" k="55" />
<hkern u1="[" u2="&#x10a;" k="33" />
<hkern u1="[" u2="&#x109;" k="55" />
<hkern u1="[" u2="&#x108;" k="33" />
<hkern u1="[" u2="&#x107;" k="55" />
<hkern u1="[" u2="&#x106;" k="33" />
<hkern u1="[" u2="&#x105;" k="35" />
<hkern u1="[" u2="&#x103;" k="35" />
<hkern u1="[" u2="&#x101;" k="35" />
<hkern u1="[" u2="&#xff;" k="41" />
<hkern u1="[" u2="&#xfd;" k="41" />
<hkern u1="[" u2="&#xfc;" k="41" />
<hkern u1="[" u2="&#xfb;" k="41" />
<hkern u1="[" u2="&#xfa;" k="41" />
<hkern u1="[" u2="&#xf9;" k="41" />
<hkern u1="[" u2="&#xf8;" k="55" />
<hkern u1="[" u2="&#xf6;" k="55" />
<hkern u1="[" u2="&#xf5;" k="55" />
<hkern u1="[" u2="&#xf4;" k="55" />
<hkern u1="[" u2="&#xf3;" k="55" />
<hkern u1="[" u2="&#xf2;" k="55" />
<hkern u1="[" u2="&#xef;" k="-78" />
<hkern u1="[" u2="&#xec;" k="-98" />
<hkern u1="[" u2="&#xeb;" k="55" />
<hkern u1="[" u2="&#xea;" k="55" />
<hkern u1="[" u2="&#xe9;" k="55" />
<hkern u1="[" u2="&#xe8;" k="55" />
<hkern u1="[" u2="&#xe7;" k="55" />
<hkern u1="[" u2="&#xe6;" k="35" />
<hkern u1="[" u2="&#xe5;" k="35" />
<hkern u1="[" u2="&#xe4;" k="35" />
<hkern u1="[" u2="&#xe3;" k="35" />
<hkern u1="[" u2="&#xe2;" k="35" />
<hkern u1="[" u2="&#xe1;" k="35" />
<hkern u1="[" u2="&#xe0;" k="35" />
<hkern u1="[" u2="&#xd8;" k="41" />
<hkern u1="[" u2="&#xd6;" k="41" />
<hkern u1="[" u2="&#xd5;" k="41" />
<hkern u1="[" u2="&#xd4;" k="41" />
<hkern u1="[" u2="&#xd3;" k="41" />
<hkern u1="[" u2="&#xd2;" k="41" />
<hkern u1="[" u2="&#xc7;" k="33" />
<hkern u1="[" u2="&#x7b;" k="37" />
<hkern u1="[" u2="y" k="41" />
<hkern u1="[" u2="w" k="43" />
<hkern u1="[" u2="v" k="41" />
<hkern u1="[" u2="u" k="41" />
<hkern u1="[" u2="t" k="23" />
<hkern u1="[" u2="q" k="53" />
<hkern u1="[" u2="o" k="55" />
<hkern u1="[" u2="j" k="-18" />
<hkern u1="[" u2="f" k="20" />
<hkern u1="[" u2="e" k="55" />
<hkern u1="[" u2="d" k="53" />
<hkern u1="[" u2="c" k="55" />
<hkern u1="[" u2="a" k="35" />
<hkern u1="[" u2="Q" k="41" />
<hkern u1="[" u2="O" k="41" />
<hkern u1="[" u2="G" k="41" />
<hkern u1="[" u2="C" k="33" />
<hkern u1="\" u2="&#x201d;" k="139" />
<hkern u1="\" u2="&#x2019;" k="139" />
<hkern u1="\" u2="&#x21b;" k="33" />
<hkern u1="\" u2="&#x21a;" k="115" />
<hkern u1="\" u2="&#x218;" k="20" />
<hkern u1="\" u2="&#x1ff;" k="20" />
<hkern u1="\" u2="&#x1fe;" k="41" />
<hkern u1="\" u2="&#x178;" k="145" />
<hkern u1="\" u2="&#x177;" k="45" />
<hkern u1="\" u2="&#x176;" k="145" />
<hkern u1="\" u2="&#x175;" k="35" />
<hkern u1="\" u2="&#x174;" k="70" />
<hkern u1="\" u2="&#x172;" k="45" />
<hkern u1="\" u2="&#x170;" k="45" />
<hkern u1="\" u2="&#x16e;" k="45" />
<hkern u1="\" u2="&#x16c;" k="45" />
<hkern u1="\" u2="&#x16a;" k="45" />
<hkern u1="\" u2="&#x168;" k="45" />
<hkern u1="\" u2="&#x167;" k="33" />
<hkern u1="\" u2="&#x166;" k="115" />
<hkern u1="\" u2="&#x165;" k="33" />
<hkern u1="\" u2="&#x164;" k="115" />
<hkern u1="\" u2="&#x160;" k="20" />
<hkern u1="\" u2="&#x15e;" k="20" />
<hkern u1="\" u2="&#x15c;" k="20" />
<hkern u1="\" u2="&#x15a;" k="20" />
<hkern u1="\" u2="&#x153;" k="20" />
<hkern u1="\" u2="&#x152;" k="41" />
<hkern u1="\" u2="&#x151;" k="20" />
<hkern u1="\" u2="&#x150;" k="41" />
<hkern u1="\" u2="&#x14f;" k="20" />
<hkern u1="\" u2="&#x14e;" k="41" />
<hkern u1="\" u2="&#x14d;" k="20" />
<hkern u1="\" u2="&#x14c;" k="41" />
<hkern u1="\" u2="&#x122;" k="41" />
<hkern u1="\" u2="&#x120;" k="41" />
<hkern u1="\" u2="&#x11e;" k="41" />
<hkern u1="\" u2="&#x11c;" k="41" />
<hkern u1="\" u2="&#x11b;" k="20" />
<hkern u1="\" u2="&#x119;" k="20" />
<hkern u1="\" u2="&#x117;" k="20" />
<hkern u1="\" u2="&#x115;" k="20" />
<hkern u1="\" u2="&#x113;" k="20" />
<hkern u1="\" u2="&#x10d;" k="20" />
<hkern u1="\" u2="&#x10c;" k="39" />
<hkern u1="\" u2="&#x10b;" k="20" />
<hkern u1="\" u2="&#x10a;" k="39" />
<hkern u1="\" u2="&#x109;" k="20" />
<hkern u1="\" u2="&#x108;" k="39" />
<hkern u1="\" u2="&#x107;" k="20" />
<hkern u1="\" u2="&#x106;" k="39" />
<hkern u1="\" u2="&#xff;" k="45" />
<hkern u1="\" u2="&#xfd;" k="45" />
<hkern u1="\" u2="&#xf8;" k="20" />
<hkern u1="\" u2="&#xf6;" k="20" />
<hkern u1="\" u2="&#xf5;" k="20" />
<hkern u1="\" u2="&#xf4;" k="20" />
<hkern u1="\" u2="&#xf3;" k="20" />
<hkern u1="\" u2="&#xf2;" k="20" />
<hkern u1="\" u2="&#xeb;" k="20" />
<hkern u1="\" u2="&#xea;" k="20" />
<hkern u1="\" u2="&#xe9;" k="20" />
<hkern u1="\" u2="&#xe8;" k="20" />
<hkern u1="\" u2="&#xe7;" k="20" />
<hkern u1="\" u2="&#xdd;" k="145" />
<hkern u1="\" u2="&#xdc;" k="45" />
<hkern u1="\" u2="&#xdb;" k="45" />
<hkern u1="\" u2="&#xda;" k="45" />
<hkern u1="\" u2="&#xd9;" k="45" />
<hkern u1="\" u2="&#xd8;" k="41" />
<hkern u1="\" u2="&#xd6;" k="41" />
<hkern u1="\" u2="&#xd5;" k="41" />
<hkern u1="\" u2="&#xd4;" k="41" />
<hkern u1="\" u2="&#xd3;" k="41" />
<hkern u1="\" u2="&#xd2;" k="41" />
<hkern u1="\" u2="&#xc7;" k="39" />
<hkern u1="\" u2="y" k="45" />
<hkern u1="\" u2="w" k="35" />
<hkern u1="\" u2="v" k="43" />
<hkern u1="\" u2="t" k="33" />
<hkern u1="\" u2="o" k="20" />
<hkern u1="\" u2="f" k="25" />
<hkern u1="\" u2="e" k="20" />
<hkern u1="\" u2="c" k="20" />
<hkern u1="\" u2="Y" k="145" />
<hkern u1="\" u2="W" k="70" />
<hkern u1="\" u2="V" k="92" />
<hkern u1="\" u2="U" k="45" />
<hkern u1="\" u2="T" k="115" />
<hkern u1="\" u2="S" k="20" />
<hkern u1="\" u2="Q" k="41" />
<hkern u1="\" u2="O" k="41" />
<hkern u1="\" u2="G" k="41" />
<hkern u1="\" u2="C" k="39" />
<hkern u1="\" u2="&#x27;" k="150" />
<hkern u1="\" u2="&#x22;" k="150" />
<hkern u1="a" u2="&#x2122;" k="31" />
<hkern u1="a" u2="v" k="12" />
<hkern u1="a" u2="\" k="88" />
<hkern u1="a" u2="V" k="57" />
<hkern u1="a" u2="&#x3f;" k="27" />
<hkern u1="a" u2="&#x2a;" k="16" />
<hkern u1="b" u2="&#x2122;" k="31" />
<hkern u1="b" u2="&#xc6;" k="12" />
<hkern u1="b" u2="&#x7d;" k="37" />
<hkern u1="b" u2="x" k="16" />
<hkern u1="b" u2="v" k="12" />
<hkern u1="b" u2="]" k="53" />
<hkern u1="b" u2="\" k="82" />
<hkern u1="b" u2="X" k="45" />
<hkern u1="b" u2="V" k="55" />
<hkern u1="b" u2="&#x3f;" k="33" />
<hkern u1="b" u2="&#x2a;" k="16" />
<hkern u1="b" u2="&#x29;" k="39" />
<hkern u1="c" u2="\" k="49" />
<hkern u1="c" u2="V" k="25" />
<hkern u1="d" u2="&#xef;" k="-16" />
<hkern u1="d" u2="&#xec;" k="-37" />
<hkern u1="e" u2="&#x2122;" k="25" />
<hkern u1="e" u2="&#xc6;" k="10" />
<hkern u1="e" u2="&#x7d;" k="20" />
<hkern u1="e" u2="v" k="12" />
<hkern u1="e" u2="\" k="78" />
<hkern u1="e" u2="X" k="10" />
<hkern u1="e" u2="V" k="51" />
<hkern u1="e" u2="&#x3f;" k="23" />
<hkern u1="e" u2="&#x29;" k="25" />
<hkern u1="f" u2="&#x2039;" k="49" />
<hkern u1="f" u2="&#x2026;" k="70" />
<hkern u1="f" u2="&#x201e;" k="70" />
<hkern u1="f" u2="&#x201a;" k="70" />
<hkern u1="f" u2="&#x2014;" k="63" />
<hkern u1="f" u2="&#x2013;" k="63" />
<hkern u1="f" u2="&#x1ff;" k="8" />
<hkern u1="f" u2="&#x1fc;" k="37" />
<hkern u1="f" u2="&#x1fa;" k="37" />
<hkern u1="f" u2="&#x178;" k="-27" />
<hkern u1="f" u2="&#x176;" k="-27" />
<hkern u1="f" u2="&#x153;" k="8" />
<hkern u1="f" u2="&#x151;" k="8" />
<hkern u1="f" u2="&#x14f;" k="8" />
<hkern u1="f" u2="&#x14d;" k="8" />
<hkern u1="f" u2="&#x135;" k="-76" />
<hkern u1="f" u2="&#x134;" k="14" />
<hkern u1="f" u2="&#x12d;" k="-80" />
<hkern u1="f" u2="&#x12b;" k="-51" />
<hkern u1="f" u2="&#x129;" k="-98" />
<hkern u1="f" u2="&#x11b;" k="8" />
<hkern u1="f" u2="&#x119;" k="8" />
<hkern u1="f" u2="&#x117;" k="8" />
<hkern u1="f" u2="&#x115;" k="8" />
<hkern u1="f" u2="&#x113;" k="8" />
<hkern u1="f" u2="&#x10d;" k="8" />
<hkern u1="f" u2="&#x10b;" k="8" />
<hkern u1="f" u2="&#x109;" k="8" />
<hkern u1="f" u2="&#x107;" k="8" />
<hkern u1="f" u2="&#x104;" k="37" />
<hkern u1="f" u2="&#x102;" k="37" />
<hkern u1="f" u2="&#x100;" k="37" />
<hkern u1="f" u2="&#xf8;" k="8" />
<hkern u1="f" u2="&#xf6;" k="8" />
<hkern u1="f" u2="&#xf5;" k="8" />
<hkern u1="f" u2="&#xf4;" k="8" />
<hkern u1="f" u2="&#xf3;" k="8" />
<hkern u1="f" u2="&#xf2;" k="8" />
<hkern u1="f" u2="&#xef;" k="-121" />
<hkern u1="f" u2="&#xee;" k="-90" />
<hkern u1="f" u2="&#xec;" k="-139" />
<hkern u1="f" u2="&#xeb;" k="8" />
<hkern u1="f" u2="&#xea;" k="8" />
<hkern u1="f" u2="&#xe9;" k="8" />
<hkern u1="f" u2="&#xe8;" k="8" />
<hkern u1="f" u2="&#xe7;" k="8" />
<hkern u1="f" u2="&#xdd;" k="-27" />
<hkern u1="f" u2="&#xc6;" k="49" />
<hkern u1="f" u2="&#xc5;" k="37" />
<hkern u1="f" u2="&#xc4;" k="37" />
<hkern u1="f" u2="&#xc3;" k="37" />
<hkern u1="f" u2="&#xc2;" k="37" />
<hkern u1="f" u2="&#xc1;" k="37" />
<hkern u1="f" u2="&#xc0;" k="37" />
<hkern u1="f" u2="&#xab;" k="49" />
<hkern u1="f" u2="o" k="8" />
<hkern u1="f" u2="e" k="8" />
<hkern u1="f" u2="c" k="8" />
<hkern u1="f" u2="Y" k="-27" />
<hkern u1="f" u2="J" k="14" />
<hkern u1="f" u2="A" k="37" />
<hkern u1="f" u2="&#x2f;" k="51" />
<hkern u1="f" u2="&#x2e;" k="70" />
<hkern u1="f" u2="&#x2d;" k="63" />
<hkern u1="f" u2="&#x2c;" k="70" />
<hkern u1="g" u2="&#x135;" k="-49" />
<hkern u1="g" u2="j" k="-49" />
<hkern u1="g" u2="\" k="31" />
<hkern u1="g" u2="V" k="10" />
<hkern u1="h" u2="&#x2122;" k="29" />
<hkern u1="h" u2="v" k="8" />
<hkern u1="h" u2="\" k="82" />
<hkern u1="h" u2="V" k="55" />
<hkern u1="h" u2="&#x3f;" k="29" />
<hkern u1="h" u2="&#x2a;" k="16" />
<hkern u1="h" u2="&#x29;" k="23" />
<hkern u1="i" u2="&#xef;" k="-16" />
<hkern u1="i" u2="&#xec;" k="-37" />
<hkern u1="j" u2="&#xef;" k="-16" />
<hkern u1="j" u2="&#xec;" k="-37" />
<hkern u1="k" u2="\" k="35" />
<hkern u1="k" u2="V" k="16" />
<hkern u1="l" u2="&#xec;" k="-25" />
<hkern u1="m" u2="&#x2122;" k="29" />
<hkern u1="m" u2="v" k="8" />
<hkern u1="m" u2="\" k="82" />
<hkern u1="m" u2="V" k="55" />
<hkern u1="m" u2="&#x3f;" k="29" />
<hkern u1="m" u2="&#x2a;" k="16" />
<hkern u1="m" u2="&#x29;" k="23" />
<hkern u1="n" u2="&#x2122;" k="29" />
<hkern u1="n" u2="v" k="8" />
<hkern u1="n" u2="\" k="82" />
<hkern u1="n" u2="V" k="55" />
<hkern u1="n" u2="&#x3f;" k="29" />
<hkern u1="n" u2="&#x2a;" k="16" />
<hkern u1="n" u2="&#x29;" k="23" />
<hkern u1="o" u2="&#x2122;" k="29" />
<hkern u1="o" u2="&#xc6;" k="14" />
<hkern u1="o" u2="&#x7d;" k="39" />
<hkern u1="o" u2="x" k="20" />
<hkern u1="o" u2="v" k="14" />
<hkern u1="o" u2="]" k="55" />
<hkern u1="o" u2="\" k="84" />
<hkern u1="o" u2="X" k="53" />
<hkern u1="o" u2="V" k="59" />
<hkern u1="o" u2="&#x3f;" k="31" />
<hkern u1="o" u2="&#x2a;" k="16" />
<hkern u1="o" u2="&#x29;" k="41" />
<hkern u1="p" u2="&#x2122;" k="31" />
<hkern u1="p" u2="&#xc6;" k="12" />
<hkern u1="p" u2="&#x7d;" k="37" />
<hkern u1="p" u2="x" k="16" />
<hkern u1="p" u2="v" k="12" />
<hkern u1="p" u2="]" k="53" />
<hkern u1="p" u2="\" k="82" />
<hkern u1="p" u2="X" k="45" />
<hkern u1="p" u2="V" k="55" />
<hkern u1="p" u2="&#x3f;" k="33" />
<hkern u1="p" u2="&#x2a;" k="16" />
<hkern u1="p" u2="&#x29;" k="39" />
<hkern u1="q" u2="&#x2122;" k="18" />
<hkern u1="q" u2="\" k="57" />
<hkern u1="q" u2="X" k="10" />
<hkern u1="q" u2="V" k="43" />
<hkern u1="q" u2="&#x29;" k="23" />
<hkern u1="r" u2="&#xc6;" k="63" />
<hkern u1="r" u2="&#x7d;" k="25" />
<hkern u1="r" u2="]" k="39" />
<hkern u1="r" u2="\" k="23" />
<hkern u1="r" u2="X" k="59" />
<hkern u1="r" u2="&#x2f;" k="68" />
<hkern u1="r" u2="&#x29;" k="20" />
<hkern u1="s" u2="&#x2122;" k="23" />
<hkern u1="s" u2="&#xc6;" k="10" />
<hkern u1="s" u2="&#x7d;" k="25" />
<hkern u1="s" u2="v" k="10" />
<hkern u1="s" u2="]" k="37" />
<hkern u1="s" u2="\" k="59" />
<hkern u1="s" u2="X" k="16" />
<hkern u1="s" u2="V" k="37" />
<hkern u1="s" u2="&#x29;" k="27" />
<hkern u1="t" u2="\" k="35" />
<hkern u1="t" u2="V" k="12" />
<hkern u1="u" u2="&#x2122;" k="18" />
<hkern u1="u" u2="\" k="57" />
<hkern u1="u" u2="X" k="10" />
<hkern u1="u" u2="V" k="43" />
<hkern u1="u" u2="&#x29;" k="23" />
<hkern u1="v" u2="&#x2039;" k="20" />
<hkern u1="v" u2="&#x2026;" k="47" />
<hkern u1="v" u2="&#x201e;" k="47" />
<hkern u1="v" u2="&#x201a;" k="47" />
<hkern u1="v" u2="&#x2014;" k="18" />
<hkern u1="v" u2="&#x2013;" k="18" />
<hkern u1="v" u2="&#x21a;" k="102" />
<hkern u1="v" u2="&#x219;" k="10" />
<hkern u1="v" u2="&#x1ff;" k="14" />
<hkern u1="v" u2="&#x1fd;" k="12" />
<hkern u1="v" u2="&#x1fc;" k="29" />
<hkern u1="v" u2="&#x1fb;" k="12" />
<hkern u1="v" u2="&#x1fa;" k="29" />
<hkern u1="v" u2="&#x17d;" k="14" />
<hkern u1="v" u2="&#x17b;" k="14" />
<hkern u1="v" u2="&#x179;" k="14" />
<hkern u1="v" u2="&#x178;" k="68" />
<hkern u1="v" u2="&#x176;" k="68" />
<hkern u1="v" u2="&#x166;" k="102" />
<hkern u1="v" u2="&#x164;" k="102" />
<hkern u1="v" u2="&#x161;" k="10" />
<hkern u1="v" u2="&#x15f;" k="10" />
<hkern u1="v" u2="&#x15d;" k="10" />
<hkern u1="v" u2="&#x15b;" k="10" />
<hkern u1="v" u2="&#x153;" k="14" />
<hkern u1="v" u2="&#x151;" k="14" />
<hkern u1="v" u2="&#x14f;" k="14" />
<hkern u1="v" u2="&#x14d;" k="14" />
<hkern u1="v" u2="&#x134;" k="25" />
<hkern u1="v" u2="&#x123;" k="14" />
<hkern u1="v" u2="&#x121;" k="14" />
<hkern u1="v" u2="&#x11f;" k="14" />
<hkern u1="v" u2="&#x11d;" k="14" />
<hkern u1="v" u2="&#x11b;" k="14" />
<hkern u1="v" u2="&#x119;" k="14" />
<hkern u1="v" u2="&#x117;" k="14" />
<hkern u1="v" u2="&#x115;" k="14" />
<hkern u1="v" u2="&#x113;" k="14" />
<hkern u1="v" u2="&#x111;" k="12" />
<hkern u1="v" u2="&#x10f;" k="12" />
<hkern u1="v" u2="&#x10d;" k="14" />
<hkern u1="v" u2="&#x10b;" k="14" />
<hkern u1="v" u2="&#x109;" k="14" />
<hkern u1="v" u2="&#x107;" k="14" />
<hkern u1="v" u2="&#x105;" k="12" />
<hkern u1="v" u2="&#x104;" k="29" />
<hkern u1="v" u2="&#x103;" k="12" />
<hkern u1="v" u2="&#x102;" k="29" />
<hkern u1="v" u2="&#x101;" k="12" />
<hkern u1="v" u2="&#x100;" k="29" />
<hkern u1="v" u2="&#xf8;" k="14" />
<hkern u1="v" u2="&#xf6;" k="14" />
<hkern u1="v" u2="&#xf5;" k="14" />
<hkern u1="v" u2="&#xf4;" k="14" />
<hkern u1="v" u2="&#xf3;" k="14" />
<hkern u1="v" u2="&#xf2;" k="14" />
<hkern u1="v" u2="&#xeb;" k="14" />
<hkern u1="v" u2="&#xea;" k="14" />
<hkern u1="v" u2="&#xe9;" k="14" />
<hkern u1="v" u2="&#xe8;" k="14" />
<hkern u1="v" u2="&#xe7;" k="14" />
<hkern u1="v" u2="&#xe6;" k="12" />
<hkern u1="v" u2="&#xe5;" k="12" />
<hkern u1="v" u2="&#xe4;" k="12" />
<hkern u1="v" u2="&#xe3;" k="12" />
<hkern u1="v" u2="&#xe2;" k="12" />
<hkern u1="v" u2="&#xe1;" k="12" />
<hkern u1="v" u2="&#xe0;" k="12" />
<hkern u1="v" u2="&#xdd;" k="68" />
<hkern u1="v" u2="&#xc6;" k="35" />
<hkern u1="v" u2="&#xc5;" k="29" />
<hkern u1="v" u2="&#xc4;" k="29" />
<hkern u1="v" u2="&#xc3;" k="29" />
<hkern u1="v" u2="&#xc2;" k="29" />
<hkern u1="v" u2="&#xc1;" k="29" />
<hkern u1="v" u2="&#xc0;" k="29" />
<hkern u1="v" u2="&#xab;" k="20" />
<hkern u1="v" u2="&#x7d;" k="25" />
<hkern u1="v" u2="s" k="10" />
<hkern u1="v" u2="q" k="12" />
<hkern u1="v" u2="o" k="14" />
<hkern u1="v" u2="g" k="14" />
<hkern u1="v" u2="e" k="14" />
<hkern u1="v" u2="d" k="12" />
<hkern u1="v" u2="c" k="14" />
<hkern u1="v" u2="a" k="12" />
<hkern u1="v" u2="]" k="41" />
<hkern u1="v" u2="\" k="37" />
<hkern u1="v" u2="Z" k="14" />
<hkern u1="v" u2="Y" k="68" />
<hkern u1="v" u2="X" k="45" />
<hkern u1="v" u2="V" k="12" />
<hkern u1="v" u2="T" k="102" />
<hkern u1="v" u2="J" k="25" />
<hkern u1="v" u2="A" k="29" />
<hkern u1="v" u2="&#x2f;" k="37" />
<hkern u1="v" u2="&#x2e;" k="47" />
<hkern u1="v" u2="&#x2d;" k="18" />
<hkern u1="v" u2="&#x2c;" k="47" />
<hkern u1="v" u2="&#x29;" k="23" />
<hkern u1="w" u2="&#xc6;" k="29" />
<hkern u1="w" u2="&#x7d;" k="31" />
<hkern u1="w" u2="]" k="43" />
<hkern u1="w" u2="\" k="37" />
<hkern u1="w" u2="X" k="47" />
<hkern u1="w" u2="V" k="18" />
<hkern u1="w" u2="&#x2f;" k="29" />
<hkern u1="w" u2="&#x29;" k="29" />
<hkern u1="x" u2="&#x2039;" k="43" />
<hkern u1="x" u2="&#x2014;" k="49" />
<hkern u1="x" u2="&#x2013;" k="49" />
<hkern u1="x" u2="&#x21a;" k="98" />
<hkern u1="x" u2="&#x1ff;" k="20" />
<hkern u1="x" u2="&#x178;" k="66" />
<hkern u1="x" u2="&#x176;" k="66" />
<hkern u1="x" u2="&#x166;" k="98" />
<hkern u1="x" u2="&#x164;" k="98" />
<hkern u1="x" u2="&#x153;" k="20" />
<hkern u1="x" u2="&#x151;" k="20" />
<hkern u1="x" u2="&#x14f;" k="20" />
<hkern u1="x" u2="&#x14d;" k="20" />
<hkern u1="x" u2="&#x123;" k="16" />
<hkern u1="x" u2="&#x121;" k="16" />
<hkern u1="x" u2="&#x11f;" k="16" />
<hkern u1="x" u2="&#x11d;" k="16" />
<hkern u1="x" u2="&#x11b;" k="20" />
<hkern u1="x" u2="&#x119;" k="20" />
<hkern u1="x" u2="&#x117;" k="20" />
<hkern u1="x" u2="&#x115;" k="20" />
<hkern u1="x" u2="&#x113;" k="20" />
<hkern u1="x" u2="&#x111;" k="18" />
<hkern u1="x" u2="&#x10f;" k="18" />
<hkern u1="x" u2="&#x10d;" k="20" />
<hkern u1="x" u2="&#x10b;" k="20" />
<hkern u1="x" u2="&#x109;" k="20" />
<hkern u1="x" u2="&#x107;" k="20" />
<hkern u1="x" u2="&#xf8;" k="20" />
<hkern u1="x" u2="&#xf6;" k="20" />
<hkern u1="x" u2="&#xf5;" k="20" />
<hkern u1="x" u2="&#xf4;" k="20" />
<hkern u1="x" u2="&#xf3;" k="20" />
<hkern u1="x" u2="&#xf2;" k="20" />
<hkern u1="x" u2="&#xeb;" k="20" />
<hkern u1="x" u2="&#xea;" k="20" />
<hkern u1="x" u2="&#xe9;" k="20" />
<hkern u1="x" u2="&#xe8;" k="20" />
<hkern u1="x" u2="&#xe7;" k="20" />
<hkern u1="x" u2="&#xdd;" k="66" />
<hkern u1="x" u2="&#xab;" k="43" />
<hkern u1="x" u2="q" k="18" />
<hkern u1="x" u2="o" k="20" />
<hkern u1="x" u2="g" k="16" />
<hkern u1="x" u2="e" k="20" />
<hkern u1="x" u2="d" k="18" />
<hkern u1="x" u2="c" k="20" />
<hkern u1="x" u2="\" k="31" />
<hkern u1="x" u2="Y" k="66" />
<hkern u1="x" u2="V" k="12" />
<hkern u1="x" u2="T" k="98" />
<hkern u1="x" u2="&#x2d;" k="49" />
<hkern u1="y" u2="&#xc6;" k="37" />
<hkern u1="y" u2="&#x7d;" k="23" />
<hkern u1="y" u2="]" k="39" />
<hkern u1="y" u2="\" k="37" />
<hkern u1="y" u2="X" k="45" />
<hkern u1="y" u2="V" k="12" />
<hkern u1="y" u2="&#x2f;" k="39" />
<hkern u1="z" u2="\" k="51" />
<hkern u1="z" u2="V" k="27" />
<hkern u1="&#x7b;" u2="&#x1ff;" k="39" />
<hkern u1="&#x7b;" u2="&#x1fe;" k="29" />
<hkern u1="&#x7b;" u2="&#x1fd;" k="25" />
<hkern u1="&#x7b;" u2="&#x1fb;" k="25" />
<hkern u1="&#x7b;" u2="&#x177;" k="25" />
<hkern u1="&#x7b;" u2="&#x175;" k="31" />
<hkern u1="&#x7b;" u2="&#x173;" k="31" />
<hkern u1="&#x7b;" u2="&#x171;" k="31" />
<hkern u1="&#x7b;" u2="&#x16f;" k="31" />
<hkern u1="&#x7b;" u2="&#x16d;" k="31" />
<hkern u1="&#x7b;" u2="&#x16b;" k="31" />
<hkern u1="&#x7b;" u2="&#x169;" k="31" />
<hkern u1="&#x7b;" u2="&#x153;" k="39" />
<hkern u1="&#x7b;" u2="&#x152;" k="29" />
<hkern u1="&#x7b;" u2="&#x151;" k="39" />
<hkern u1="&#x7b;" u2="&#x150;" k="29" />
<hkern u1="&#x7b;" u2="&#x14f;" k="39" />
<hkern u1="&#x7b;" u2="&#x14e;" k="29" />
<hkern u1="&#x7b;" u2="&#x14d;" k="39" />
<hkern u1="&#x7b;" u2="&#x14c;" k="29" />
<hkern u1="&#x7b;" u2="&#x135;" k="-43" />
<hkern u1="&#x7b;" u2="&#x12d;" k="-49" />
<hkern u1="&#x7b;" u2="&#x129;" k="-55" />
<hkern u1="&#x7b;" u2="&#x122;" k="29" />
<hkern u1="&#x7b;" u2="&#x120;" k="29" />
<hkern u1="&#x7b;" u2="&#x11e;" k="29" />
<hkern u1="&#x7b;" u2="&#x11c;" k="29" />
<hkern u1="&#x7b;" u2="&#x11b;" k="39" />
<hkern u1="&#x7b;" u2="&#x119;" k="39" />
<hkern u1="&#x7b;" u2="&#x117;" k="39" />
<hkern u1="&#x7b;" u2="&#x115;" k="39" />
<hkern u1="&#x7b;" u2="&#x113;" k="39" />
<hkern u1="&#x7b;" u2="&#x111;" k="37" />
<hkern u1="&#x7b;" u2="&#x10f;" k="37" />
<hkern u1="&#x7b;" u2="&#x10d;" k="39" />
<hkern u1="&#x7b;" u2="&#x10c;" k="25" />
<hkern u1="&#x7b;" u2="&#x10b;" k="39" />
<hkern u1="&#x7b;" u2="&#x10a;" k="25" />
<hkern u1="&#x7b;" u2="&#x109;" k="39" />
<hkern u1="&#x7b;" u2="&#x108;" k="25" />
<hkern u1="&#x7b;" u2="&#x107;" k="39" />
<hkern u1="&#x7b;" u2="&#x106;" k="25" />
<hkern u1="&#x7b;" u2="&#x105;" k="25" />
<hkern u1="&#x7b;" u2="&#x103;" k="25" />
<hkern u1="&#x7b;" u2="&#x101;" k="25" />
<hkern u1="&#x7b;" u2="&#xff;" k="25" />
<hkern u1="&#x7b;" u2="&#xfd;" k="25" />
<hkern u1="&#x7b;" u2="&#xfc;" k="31" />
<hkern u1="&#x7b;" u2="&#xfb;" k="31" />
<hkern u1="&#x7b;" u2="&#xfa;" k="31" />
<hkern u1="&#x7b;" u2="&#xf9;" k="31" />
<hkern u1="&#x7b;" u2="&#xf8;" k="39" />
<hkern u1="&#x7b;" u2="&#xf6;" k="39" />
<hkern u1="&#x7b;" u2="&#xf5;" k="39" />
<hkern u1="&#x7b;" u2="&#xf4;" k="39" />
<hkern u1="&#x7b;" u2="&#xf3;" k="39" />
<hkern u1="&#x7b;" u2="&#xf2;" k="39" />
<hkern u1="&#x7b;" u2="&#xef;" k="-76" />
<hkern u1="&#x7b;" u2="&#xec;" k="-92" />
<hkern u1="&#x7b;" u2="&#xeb;" k="39" />
<hkern u1="&#x7b;" u2="&#xea;" k="39" />
<hkern u1="&#x7b;" u2="&#xe9;" k="39" />
<hkern u1="&#x7b;" u2="&#xe8;" k="39" />
<hkern u1="&#x7b;" u2="&#xe7;" k="39" />
<hkern u1="&#x7b;" u2="&#xe6;" k="25" />
<hkern u1="&#x7b;" u2="&#xe5;" k="25" />
<hkern u1="&#x7b;" u2="&#xe4;" k="25" />
<hkern u1="&#x7b;" u2="&#xe3;" k="25" />
<hkern u1="&#x7b;" u2="&#xe2;" k="25" />
<hkern u1="&#x7b;" u2="&#xe1;" k="25" />
<hkern u1="&#x7b;" u2="&#xe0;" k="25" />
<hkern u1="&#x7b;" u2="&#xd8;" k="29" />
<hkern u1="&#x7b;" u2="&#xd6;" k="29" />
<hkern u1="&#x7b;" u2="&#xd5;" k="29" />
<hkern u1="&#x7b;" u2="&#xd4;" k="29" />
<hkern u1="&#x7b;" u2="&#xd3;" k="29" />
<hkern u1="&#x7b;" u2="&#xd2;" k="29" />
<hkern u1="&#x7b;" u2="&#xc7;" k="25" />
<hkern u1="&#x7b;" u2="&#x7b;" k="25" />
<hkern u1="&#x7b;" u2="y" k="25" />
<hkern u1="&#x7b;" u2="w" k="31" />
<hkern u1="&#x7b;" u2="v" k="25" />
<hkern u1="&#x7b;" u2="u" k="31" />
<hkern u1="&#x7b;" u2="q" k="37" />
<hkern u1="&#x7b;" u2="o" k="39" />
<hkern u1="&#x7b;" u2="j" k="-43" />
<hkern u1="&#x7b;" u2="e" k="39" />
<hkern u1="&#x7b;" u2="d" k="37" />
<hkern u1="&#x7b;" u2="c" k="39" />
<hkern u1="&#x7b;" u2="a" k="25" />
<hkern u1="&#x7b;" u2="Q" k="29" />
<hkern u1="&#x7b;" u2="O" k="29" />
<hkern u1="&#x7b;" u2="G" k="29" />
<hkern u1="&#x7b;" u2="C" k="25" />
<hkern u1="&#x7c;" u2="&#xec;" k="-31" />
<hkern u1="&#x7d;" u2="&#x7d;" k="25" />
<hkern u1="&#x7d;" u2="]" k="37" />
<hkern u1="&#x7d;" u2="&#x29;" k="27" />
<hkern u1="&#xa1;" u2="&#x21a;" k="61" />
<hkern u1="&#xa1;" u2="&#x178;" k="55" />
<hkern u1="&#xa1;" u2="&#x176;" k="55" />
<hkern u1="&#xa1;" u2="&#x166;" k="61" />
<hkern u1="&#xa1;" u2="&#x164;" k="61" />
<hkern u1="&#xa1;" u2="&#xdd;" k="55" />
<hkern u1="&#xa1;" u2="Y" k="55" />
<hkern u1="&#xa1;" u2="T" k="61" />
<hkern u1="&#xab;" u2="V" k="23" />
<hkern u1="&#xae;" u2="&#x1fc;" k="41" />
<hkern u1="&#xae;" u2="&#x1fa;" k="41" />
<hkern u1="&#xae;" u2="&#x178;" k="39" />
<hkern u1="&#xae;" u2="&#x176;" k="39" />
<hkern u1="&#xae;" u2="&#x134;" k="27" />
<hkern u1="&#xae;" u2="&#x104;" k="41" />
<hkern u1="&#xae;" u2="&#x102;" k="41" />
<hkern u1="&#xae;" u2="&#x100;" k="41" />
<hkern u1="&#xae;" u2="&#xdd;" k="39" />
<hkern u1="&#xae;" u2="&#xc6;" k="53" />
<hkern u1="&#xae;" u2="&#xc5;" k="41" />
<hkern u1="&#xae;" u2="&#xc4;" k="41" />
<hkern u1="&#xae;" u2="&#xc3;" k="41" />
<hkern u1="&#xae;" u2="&#xc2;" k="41" />
<hkern u1="&#xae;" u2="&#xc1;" k="41" />
<hkern u1="&#xae;" u2="&#xc0;" k="41" />
<hkern u1="&#xae;" u2="Y" k="39" />
<hkern u1="&#xae;" u2="J" k="27" />
<hkern u1="&#xae;" u2="A" k="41" />
<hkern u1="&#xbb;" u2="&#x141;" k="-14" />
<hkern u1="&#xbb;" u2="x" k="43" />
<hkern u1="&#xbb;" u2="f" k="20" />
<hkern u1="&#xbb;" u2="X" k="41" />
<hkern u1="&#xbb;" u2="V" k="47" />
<hkern u1="&#xbf;" u2="&#x21b;" k="41" />
<hkern u1="&#xbf;" u2="&#x21a;" k="121" />
<hkern u1="&#xbf;" u2="&#x219;" k="41" />
<hkern u1="&#xbf;" u2="&#x218;" k="25" />
<hkern u1="&#xbf;" u2="&#x1ff;" k="45" />
<hkern u1="&#xbf;" u2="&#x1fe;" k="35" />
<hkern u1="&#xbf;" u2="&#x1fd;" k="41" />
<hkern u1="&#xbf;" u2="&#x1fc;" k="33" />
<hkern u1="&#xbf;" u2="&#x1fb;" k="41" />
<hkern u1="&#xbf;" u2="&#x1fa;" k="33" />
<hkern u1="&#xbf;" u2="&#x17e;" k="37" />
<hkern u1="&#xbf;" u2="&#x17d;" k="33" />
<hkern u1="&#xbf;" u2="&#x17c;" k="37" />
<hkern u1="&#xbf;" u2="&#x17b;" k="33" />
<hkern u1="&#xbf;" u2="&#x17a;" k="37" />
<hkern u1="&#xbf;" u2="&#x179;" k="33" />
<hkern u1="&#xbf;" u2="&#x178;" k="115" />
<hkern u1="&#xbf;" u2="&#x177;" k="51" />
<hkern u1="&#xbf;" u2="&#x176;" k="115" />
<hkern u1="&#xbf;" u2="&#x175;" k="45" />
<hkern u1="&#xbf;" u2="&#x174;" k="53" />
<hkern u1="&#xbf;" u2="&#x173;" k="41" />
<hkern u1="&#xbf;" u2="&#x172;" k="37" />
<hkern u1="&#xbf;" u2="&#x171;" k="41" />
<hkern u1="&#xbf;" u2="&#x170;" k="37" />
<hkern u1="&#xbf;" u2="&#x16f;" k="41" />
<hkern u1="&#xbf;" u2="&#x16e;" k="37" />
<hkern u1="&#xbf;" u2="&#x16d;" k="41" />
<hkern u1="&#xbf;" u2="&#x16c;" k="37" />
<hkern u1="&#xbf;" u2="&#x16b;" k="41" />
<hkern u1="&#xbf;" u2="&#x16a;" k="37" />
<hkern u1="&#xbf;" u2="&#x169;" k="41" />
<hkern u1="&#xbf;" u2="&#x168;" k="37" />
<hkern u1="&#xbf;" u2="&#x167;" k="41" />
<hkern u1="&#xbf;" u2="&#x166;" k="121" />
<hkern u1="&#xbf;" u2="&#x165;" k="41" />
<hkern u1="&#xbf;" u2="&#x164;" k="121" />
<hkern u1="&#xbf;" u2="&#x161;" k="41" />
<hkern u1="&#xbf;" u2="&#x160;" k="25" />
<hkern u1="&#xbf;" u2="&#x15f;" k="41" />
<hkern u1="&#xbf;" u2="&#x15e;" k="25" />
<hkern u1="&#xbf;" u2="&#x15d;" k="41" />
<hkern u1="&#xbf;" u2="&#x15c;" k="25" />
<hkern u1="&#xbf;" u2="&#x15b;" k="41" />
<hkern u1="&#xbf;" u2="&#x15a;" k="25" />
<hkern u1="&#xbf;" u2="&#x159;" k="37" />
<hkern u1="&#xbf;" u2="&#x158;" k="29" />
<hkern u1="&#xbf;" u2="&#x157;" k="37" />
<hkern u1="&#xbf;" u2="&#x156;" k="29" />
<hkern u1="&#xbf;" u2="&#x155;" k="37" />
<hkern u1="&#xbf;" u2="&#x154;" k="29" />
<hkern u1="&#xbf;" u2="&#x153;" k="45" />
<hkern u1="&#xbf;" u2="&#x152;" k="35" />
<hkern u1="&#xbf;" u2="&#x151;" k="45" />
<hkern u1="&#xbf;" u2="&#x150;" k="35" />
<hkern u1="&#xbf;" u2="&#x14f;" k="45" />
<hkern u1="&#xbf;" u2="&#x14e;" k="35" />
<hkern u1="&#xbf;" u2="&#x14d;" k="45" />
<hkern u1="&#xbf;" u2="&#x14c;" k="35" />
<hkern u1="&#xbf;" u2="&#x14b;" k="37" />
<hkern u1="&#xbf;" u2="&#x14a;" k="29" />
<hkern u1="&#xbf;" u2="&#x148;" k="37" />
<hkern u1="&#xbf;" u2="&#x147;" k="29" />
<hkern u1="&#xbf;" u2="&#x146;" k="37" />
<hkern u1="&#xbf;" u2="&#x145;" k="29" />
<hkern u1="&#xbf;" u2="&#x144;" k="37" />
<hkern u1="&#xbf;" u2="&#x143;" k="29" />
<hkern u1="&#xbf;" u2="&#x142;" k="37" />
<hkern u1="&#xbf;" u2="&#x141;" k="29" />
<hkern u1="&#xbf;" u2="&#x13e;" k="37" />
<hkern u1="&#xbf;" u2="&#x13d;" k="29" />
<hkern u1="&#xbf;" u2="&#x13c;" k="37" />
<hkern u1="&#xbf;" u2="&#x13b;" k="29" />
<hkern u1="&#xbf;" u2="&#x13a;" k="37" />
<hkern u1="&#xbf;" u2="&#x139;" k="29" />
<hkern u1="&#xbf;" u2="&#x137;" k="37" />
<hkern u1="&#xbf;" u2="&#x136;" k="29" />
<hkern u1="&#xbf;" u2="&#x135;" k="37" />
<hkern u1="&#xbf;" u2="&#x131;" k="37" />
<hkern u1="&#xbf;" u2="&#x130;" k="29" />
<hkern u1="&#xbf;" u2="&#x12f;" k="23" />
<hkern u1="&#xbf;" u2="&#x12e;" k="29" />
<hkern u1="&#xbf;" u2="&#x12d;" k="37" />
<hkern u1="&#xbf;" u2="&#x12c;" k="29" />
<hkern u1="&#xbf;" u2="&#x12b;" k="37" />
<hkern u1="&#xbf;" u2="&#x12a;" k="29" />
<hkern u1="&#xbf;" u2="&#x129;" k="37" />
<hkern u1="&#xbf;" u2="&#x128;" k="29" />
<hkern u1="&#xbf;" u2="&#x127;" k="37" />
<hkern u1="&#xbf;" u2="&#x126;" k="29" />
<hkern u1="&#xbf;" u2="&#x125;" k="37" />
<hkern u1="&#xbf;" u2="&#x124;" k="29" />
<hkern u1="&#xbf;" u2="&#x122;" k="35" />
<hkern u1="&#xbf;" u2="&#x120;" k="35" />
<hkern u1="&#xbf;" u2="&#x11e;" k="35" />
<hkern u1="&#xbf;" u2="&#x11c;" k="35" />
<hkern u1="&#xbf;" u2="&#x11b;" k="45" />
<hkern u1="&#xbf;" u2="&#x11a;" k="29" />
<hkern u1="&#xbf;" u2="&#x119;" k="45" />
<hkern u1="&#xbf;" u2="&#x118;" k="29" />
<hkern u1="&#xbf;" u2="&#x117;" k="45" />
<hkern u1="&#xbf;" u2="&#x116;" k="29" />
<hkern u1="&#xbf;" u2="&#x115;" k="45" />
<hkern u1="&#xbf;" u2="&#x114;" k="29" />
<hkern u1="&#xbf;" u2="&#x113;" k="45" />
<hkern u1="&#xbf;" u2="&#x112;" k="29" />
<hkern u1="&#xbf;" u2="&#x111;" k="45" />
<hkern u1="&#xbf;" u2="&#x110;" k="29" />
<hkern u1="&#xbf;" u2="&#x10f;" k="45" />
<hkern u1="&#xbf;" u2="&#x10e;" k="29" />
<hkern u1="&#xbf;" u2="&#x10d;" k="45" />
<hkern u1="&#xbf;" u2="&#x10c;" k="33" />
<hkern u1="&#xbf;" u2="&#x10b;" k="45" />
<hkern u1="&#xbf;" u2="&#x10a;" k="33" />
<hkern u1="&#xbf;" u2="&#x109;" k="45" />
<hkern u1="&#xbf;" u2="&#x108;" k="33" />
<hkern u1="&#xbf;" u2="&#x107;" k="45" />
<hkern u1="&#xbf;" u2="&#x106;" k="33" />
<hkern u1="&#xbf;" u2="&#x105;" k="41" />
<hkern u1="&#xbf;" u2="&#x104;" k="33" />
<hkern u1="&#xbf;" u2="&#x103;" k="41" />
<hkern u1="&#xbf;" u2="&#x102;" k="33" />
<hkern u1="&#xbf;" u2="&#x101;" k="41" />
<hkern u1="&#xbf;" u2="&#x100;" k="33" />
<hkern u1="&#xbf;" u2="&#xff;" k="51" />
<hkern u1="&#xbf;" u2="&#xfd;" k="51" />
<hkern u1="&#xbf;" u2="&#xfc;" k="41" />
<hkern u1="&#xbf;" u2="&#xfb;" k="41" />
<hkern u1="&#xbf;" u2="&#xfa;" k="41" />
<hkern u1="&#xbf;" u2="&#xf9;" k="41" />
<hkern u1="&#xbf;" u2="&#xf8;" k="45" />
<hkern u1="&#xbf;" u2="&#xf6;" k="45" />
<hkern u1="&#xbf;" u2="&#xf5;" k="45" />
<hkern u1="&#xbf;" u2="&#xf4;" k="45" />
<hkern u1="&#xbf;" u2="&#xf3;" k="45" />
<hkern u1="&#xbf;" u2="&#xf2;" k="45" />
<hkern u1="&#xbf;" u2="&#xf1;" k="37" />
<hkern u1="&#xbf;" u2="&#xef;" k="37" />
<hkern u1="&#xbf;" u2="&#xee;" k="37" />
<hkern u1="&#xbf;" u2="&#xed;" k="37" />
<hkern u1="&#xbf;" u2="&#xec;" k="37" />
<hkern u1="&#xbf;" u2="&#xeb;" k="45" />
<hkern u1="&#xbf;" u2="&#xea;" k="45" />
<hkern u1="&#xbf;" u2="&#xe9;" k="45" />
<hkern u1="&#xbf;" u2="&#xe8;" k="45" />
<hkern u1="&#xbf;" u2="&#xe7;" k="45" />
<hkern u1="&#xbf;" u2="&#xe6;" k="41" />
<hkern u1="&#xbf;" u2="&#xe5;" k="41" />
<hkern u1="&#xbf;" u2="&#xe4;" k="41" />
<hkern u1="&#xbf;" u2="&#xe3;" k="41" />
<hkern u1="&#xbf;" u2="&#xe2;" k="41" />
<hkern u1="&#xbf;" u2="&#xe1;" k="41" />
<hkern u1="&#xbf;" u2="&#xe0;" k="41" />
<hkern u1="&#xbf;" u2="&#xdf;" k="37" />
<hkern u1="&#xbf;" u2="&#xdd;" k="115" />
<hkern u1="&#xbf;" u2="&#xdc;" k="37" />
<hkern u1="&#xbf;" u2="&#xdb;" k="37" />
<hkern u1="&#xbf;" u2="&#xda;" k="37" />
<hkern u1="&#xbf;" u2="&#xd9;" k="37" />
<hkern u1="&#xbf;" u2="&#xd8;" k="35" />
<hkern u1="&#xbf;" u2="&#xd6;" k="35" />
<hkern u1="&#xbf;" u2="&#xd5;" k="35" />
<hkern u1="&#xbf;" u2="&#xd4;" k="35" />
<hkern u1="&#xbf;" u2="&#xd3;" k="35" />
<hkern u1="&#xbf;" u2="&#xd2;" k="35" />
<hkern u1="&#xbf;" u2="&#xd1;" k="29" />
<hkern u1="&#xbf;" u2="&#xcf;" k="29" />
<hkern u1="&#xbf;" u2="&#xce;" k="29" />
<hkern u1="&#xbf;" u2="&#xcd;" k="29" />
<hkern u1="&#xbf;" u2="&#xcc;" k="29" />
<hkern u1="&#xbf;" u2="&#xcb;" k="29" />
<hkern u1="&#xbf;" u2="&#xca;" k="29" />
<hkern u1="&#xbf;" u2="&#xc9;" k="29" />
<hkern u1="&#xbf;" u2="&#xc8;" k="29" />
<hkern u1="&#xbf;" u2="&#xc7;" k="33" />
<hkern u1="&#xbf;" u2="&#xc6;" k="35" />
<hkern u1="&#xbf;" u2="&#xc5;" k="33" />
<hkern u1="&#xbf;" u2="&#xc4;" k="33" />
<hkern u1="&#xbf;" u2="&#xc3;" k="33" />
<hkern u1="&#xbf;" u2="&#xc2;" k="33" />
<hkern u1="&#xbf;" u2="&#xc1;" k="33" />
<hkern u1="&#xbf;" u2="&#xc0;" k="33" />
<hkern u1="&#xbf;" u2="z" k="37" />
<hkern u1="&#xbf;" u2="y" k="51" />
<hkern u1="&#xbf;" u2="x" k="35" />
<hkern u1="&#xbf;" u2="w" k="45" />
<hkern u1="&#xbf;" u2="v" k="49" />
<hkern u1="&#xbf;" u2="u" k="41" />
<hkern u1="&#xbf;" u2="t" k="41" />
<hkern u1="&#xbf;" u2="s" k="41" />
<hkern u1="&#xbf;" u2="r" k="37" />
<hkern u1="&#xbf;" u2="q" k="45" />
<hkern u1="&#xbf;" u2="p" k="37" />
<hkern u1="&#xbf;" u2="o" k="45" />
<hkern u1="&#xbf;" u2="n" k="37" />
<hkern u1="&#xbf;" u2="m" k="37" />
<hkern u1="&#xbf;" u2="l" k="37" />
<hkern u1="&#xbf;" u2="k" k="37" />
<hkern u1="&#xbf;" u2="j" k="37" />
<hkern u1="&#xbf;" u2="i" k="37" />
<hkern u1="&#xbf;" u2="h" k="37" />
<hkern u1="&#xbf;" u2="f" k="41" />
<hkern u1="&#xbf;" u2="e" k="45" />
<hkern u1="&#xbf;" u2="d" k="45" />
<hkern u1="&#xbf;" u2="c" k="45" />
<hkern u1="&#xbf;" u2="b" k="37" />
<hkern u1="&#xbf;" u2="a" k="41" />
<hkern u1="&#xbf;" u2="Z" k="33" />
<hkern u1="&#xbf;" u2="Y" k="115" />
<hkern u1="&#xbf;" u2="X" k="37" />
<hkern u1="&#xbf;" u2="W" k="53" />
<hkern u1="&#xbf;" u2="V" k="68" />
<hkern u1="&#xbf;" u2="U" k="37" />
<hkern u1="&#xbf;" u2="T" k="121" />
<hkern u1="&#xbf;" u2="S" k="25" />
<hkern u1="&#xbf;" u2="R" k="29" />
<hkern u1="&#xbf;" u2="Q" k="35" />
<hkern u1="&#xbf;" u2="P" k="29" />
<hkern u1="&#xbf;" u2="O" k="35" />
<hkern u1="&#xbf;" u2="N" k="29" />
<hkern u1="&#xbf;" u2="M" k="29" />
<hkern u1="&#xbf;" u2="L" k="29" />
<hkern u1="&#xbf;" u2="K" k="29" />
<hkern u1="&#xbf;" u2="I" k="29" />
<hkern u1="&#xbf;" u2="H" k="29" />
<hkern u1="&#xbf;" u2="G" k="35" />
<hkern u1="&#xbf;" u2="F" k="29" />
<hkern u1="&#xbf;" u2="E" k="29" />
<hkern u1="&#xbf;" u2="D" k="29" />
<hkern u1="&#xbf;" u2="C" k="33" />
<hkern u1="&#xbf;" u2="B" k="29" />
<hkern u1="&#xbf;" u2="A" k="33" />
<hkern u1="&#xc0;" u2="&#x2122;" k="61" />
<hkern u1="&#xc0;" u2="&#xae;" k="37" />
<hkern u1="&#xc0;" u2="v" k="29" />
<hkern u1="&#xc0;" u2="f" k="16" />
<hkern u1="&#xc0;" u2="\" k="86" />
<hkern u1="&#xc0;" u2="V" k="55" />
<hkern u1="&#xc0;" u2="&#x3f;" k="29" />
<hkern u1="&#xc0;" u2="&#x2a;" k="55" />
<hkern u1="&#xc1;" u2="&#x2122;" k="61" />
<hkern u1="&#xc1;" u2="&#xae;" k="37" />
<hkern u1="&#xc1;" u2="v" k="29" />
<hkern u1="&#xc1;" u2="f" k="16" />
<hkern u1="&#xc1;" u2="\" k="86" />
<hkern u1="&#xc1;" u2="V" k="55" />
<hkern u1="&#xc1;" u2="&#x3f;" k="29" />
<hkern u1="&#xc1;" u2="&#x2a;" k="55" />
<hkern u1="&#xc2;" u2="&#x2122;" k="61" />
<hkern u1="&#xc2;" u2="&#xae;" k="37" />
<hkern u1="&#xc2;" u2="v" k="29" />
<hkern u1="&#xc2;" u2="f" k="16" />
<hkern u1="&#xc2;" u2="\" k="86" />
<hkern u1="&#xc2;" u2="V" k="55" />
<hkern u1="&#xc2;" u2="&#x3f;" k="29" />
<hkern u1="&#xc2;" u2="&#x2a;" k="55" />
<hkern u1="&#xc3;" u2="&#x2122;" k="61" />
<hkern u1="&#xc3;" u2="&#xae;" k="37" />
<hkern u1="&#xc3;" u2="v" k="29" />
<hkern u1="&#xc3;" u2="f" k="16" />
<hkern u1="&#xc3;" u2="\" k="86" />
<hkern u1="&#xc3;" u2="V" k="55" />
<hkern u1="&#xc3;" u2="&#x3f;" k="29" />
<hkern u1="&#xc3;" u2="&#x2a;" k="55" />
<hkern u1="&#xc4;" u2="&#x2122;" k="61" />
<hkern u1="&#xc4;" u2="&#xae;" k="37" />
<hkern u1="&#xc4;" u2="v" k="29" />
<hkern u1="&#xc4;" u2="f" k="16" />
<hkern u1="&#xc4;" u2="\" k="86" />
<hkern u1="&#xc4;" u2="V" k="55" />
<hkern u1="&#xc4;" u2="&#x3f;" k="29" />
<hkern u1="&#xc4;" u2="&#x2a;" k="55" />
<hkern u1="&#xc5;" u2="&#x2122;" k="61" />
<hkern u1="&#xc5;" u2="&#xae;" k="37" />
<hkern u1="&#xc5;" u2="v" k="29" />
<hkern u1="&#xc5;" u2="f" k="16" />
<hkern u1="&#xc5;" u2="\" k="86" />
<hkern u1="&#xc5;" u2="V" k="55" />
<hkern u1="&#xc5;" u2="&#x3f;" k="29" />
<hkern u1="&#xc5;" u2="&#x2a;" k="55" />
<hkern u1="&#xc6;" u2="&#x135;" k="-43" />
<hkern u1="&#xc6;" u2="&#x12d;" k="-12" />
<hkern u1="&#xc6;" u2="&#x129;" k="-45" />
<hkern u1="&#xc6;" u2="&#xef;" k="-66" />
<hkern u1="&#xc6;" u2="&#xee;" k="-59" />
<hkern u1="&#xc6;" u2="&#xec;" k="-86" />
<hkern u1="&#xc7;" u2="&#x135;" k="-43" />
<hkern u1="&#xc7;" u2="&#x12d;" k="-20" />
<hkern u1="&#xc7;" u2="&#x129;" k="-49" />
<hkern u1="&#xc7;" u2="&#xef;" k="-72" />
<hkern u1="&#xc7;" u2="&#xee;" k="-59" />
<hkern u1="&#xc7;" u2="&#xec;" k="-90" />
<hkern u1="&#xc7;" u2="v" k="10" />
<hkern u1="&#xc7;" u2="f" k="10" />
<hkern u1="&#xc8;" u2="&#x135;" k="-43" />
<hkern u1="&#xc8;" u2="&#x12d;" k="-12" />
<hkern u1="&#xc8;" u2="&#x129;" k="-45" />
<hkern u1="&#xc8;" u2="&#xef;" k="-66" />
<hkern u1="&#xc8;" u2="&#xee;" k="-59" />
<hkern u1="&#xc8;" u2="&#xec;" k="-86" />
<hkern u1="&#xc9;" u2="&#x135;" k="-43" />
<hkern u1="&#xc9;" u2="&#x12d;" k="-12" />
<hkern u1="&#xc9;" u2="&#x129;" k="-45" />
<hkern u1="&#xc9;" u2="&#xef;" k="-66" />
<hkern u1="&#xc9;" u2="&#xee;" k="-59" />
<hkern u1="&#xc9;" u2="&#xec;" k="-86" />
<hkern u1="&#xca;" u2="&#x135;" k="-43" />
<hkern u1="&#xca;" u2="&#x12d;" k="-12" />
<hkern u1="&#xca;" u2="&#x129;" k="-45" />
<hkern u1="&#xca;" u2="&#xef;" k="-66" />
<hkern u1="&#xca;" u2="&#xee;" k="-59" />
<hkern u1="&#xca;" u2="&#xec;" k="-86" />
<hkern u1="&#xcb;" u2="&#x135;" k="-43" />
<hkern u1="&#xcb;" u2="&#x12d;" k="-12" />
<hkern u1="&#xcb;" u2="&#x129;" k="-45" />
<hkern u1="&#xcb;" u2="&#xef;" k="-66" />
<hkern u1="&#xcb;" u2="&#xee;" k="-59" />
<hkern u1="&#xcb;" u2="&#xec;" k="-86" />
<hkern u1="&#xcc;" u2="&#xec;" k="-16" />
<hkern u1="&#xcc;" u2="f" k="12" />
<hkern u1="&#xcd;" u2="&#xec;" k="-16" />
<hkern u1="&#xcd;" u2="f" k="12" />
<hkern u1="&#xce;" u2="&#xec;" k="-16" />
<hkern u1="&#xce;" u2="f" k="12" />
<hkern u1="&#xcf;" u2="&#xec;" k="-16" />
<hkern u1="&#xcf;" u2="f" k="12" />
<hkern u1="&#xd1;" u2="&#xec;" k="-16" />
<hkern u1="&#xd1;" u2="f" k="12" />
<hkern u1="&#xd2;" u2="&#xc6;" k="27" />
<hkern u1="&#xd2;" u2="&#x7d;" k="25" />
<hkern u1="&#xd2;" u2="]" k="41" />
<hkern u1="&#xd2;" u2="\" k="39" />
<hkern u1="&#xd2;" u2="X" k="35" />
<hkern u1="&#xd2;" u2="V" k="20" />
<hkern u1="&#xd2;" u2="&#x2f;" k="29" />
<hkern u1="&#xd2;" u2="&#x29;" k="27" />
<hkern u1="&#xd3;" u2="&#xc6;" k="27" />
<hkern u1="&#xd3;" u2="&#x7d;" k="25" />
<hkern u1="&#xd3;" u2="]" k="41" />
<hkern u1="&#xd3;" u2="\" k="39" />
<hkern u1="&#xd3;" u2="X" k="35" />
<hkern u1="&#xd3;" u2="V" k="20" />
<hkern u1="&#xd3;" u2="&#x2f;" k="29" />
<hkern u1="&#xd3;" u2="&#x29;" k="27" />
<hkern u1="&#xd4;" u2="&#xc6;" k="27" />
<hkern u1="&#xd4;" u2="&#x7d;" k="25" />
<hkern u1="&#xd4;" u2="]" k="41" />
<hkern u1="&#xd4;" u2="\" k="39" />
<hkern u1="&#xd4;" u2="X" k="35" />
<hkern u1="&#xd4;" u2="V" k="20" />
<hkern u1="&#xd4;" u2="&#x2f;" k="29" />
<hkern u1="&#xd4;" u2="&#x29;" k="27" />
<hkern u1="&#xd5;" u2="&#xc6;" k="27" />
<hkern u1="&#xd5;" u2="&#x7d;" k="25" />
<hkern u1="&#xd5;" u2="]" k="41" />
<hkern u1="&#xd5;" u2="\" k="39" />
<hkern u1="&#xd5;" u2="X" k="35" />
<hkern u1="&#xd5;" u2="V" k="20" />
<hkern u1="&#xd5;" u2="&#x2f;" k="29" />
<hkern u1="&#xd5;" u2="&#x29;" k="27" />
<hkern u1="&#xd6;" u2="&#xc6;" k="27" />
<hkern u1="&#xd6;" u2="&#x7d;" k="25" />
<hkern u1="&#xd6;" u2="]" k="41" />
<hkern u1="&#xd6;" u2="\" k="39" />
<hkern u1="&#xd6;" u2="X" k="35" />
<hkern u1="&#xd6;" u2="V" k="20" />
<hkern u1="&#xd6;" u2="&#x2f;" k="29" />
<hkern u1="&#xd6;" u2="&#x29;" k="27" />
<hkern u1="&#xd8;" u2="&#xc6;" k="27" />
<hkern u1="&#xd8;" u2="&#x7d;" k="25" />
<hkern u1="&#xd8;" u2="]" k="41" />
<hkern u1="&#xd8;" u2="\" k="39" />
<hkern u1="&#xd8;" u2="X" k="35" />
<hkern u1="&#xd8;" u2="V" k="20" />
<hkern u1="&#xd8;" u2="&#x2f;" k="29" />
<hkern u1="&#xd8;" u2="&#x29;" k="27" />
<hkern u1="&#xd9;" u2="&#xec;" k="-29" />
<hkern u1="&#xd9;" u2="&#xc6;" k="14" />
<hkern u1="&#xd9;" u2="f" k="10" />
<hkern u1="&#xd9;" u2="&#x2f;" k="33" />
<hkern u1="&#xda;" u2="&#xec;" k="-29" />
<hkern u1="&#xda;" u2="&#xc6;" k="14" />
<hkern u1="&#xda;" u2="f" k="10" />
<hkern u1="&#xda;" u2="&#x2f;" k="33" />
<hkern u1="&#xdb;" u2="&#xec;" k="-29" />
<hkern u1="&#xdb;" u2="&#xc6;" k="14" />
<hkern u1="&#xdb;" u2="f" k="10" />
<hkern u1="&#xdb;" u2="&#x2f;" k="33" />
<hkern u1="&#xdc;" u2="&#xec;" k="-29" />
<hkern u1="&#xdc;" u2="&#xc6;" k="14" />
<hkern u1="&#xdc;" u2="f" k="10" />
<hkern u1="&#xdc;" u2="&#x2f;" k="33" />
<hkern u1="&#xdd;" u2="&#x159;" k="53" />
<hkern u1="&#xdd;" u2="&#x155;" k="66" />
<hkern u1="&#xdd;" u2="&#x151;" k="98" />
<hkern u1="&#xdd;" u2="&#x142;" k="12" />
<hkern u1="&#xdd;" u2="&#x135;" k="-31" />
<hkern u1="&#xdd;" u2="&#x131;" k="121" />
<hkern u1="&#xdd;" u2="&#x12d;" k="-90" />
<hkern u1="&#xdd;" u2="&#x12b;" k="-74" />
<hkern u1="&#xdd;" u2="&#x129;" k="-100" />
<hkern u1="&#xdd;" u2="&#x103;" k="115" />
<hkern u1="&#xdd;" u2="&#xff;" k="49" />
<hkern u1="&#xdd;" u2="&#xef;" k="-143" />
<hkern u1="&#xdd;" u2="&#xee;" k="-43" />
<hkern u1="&#xdd;" u2="&#xec;" k="-127" />
<hkern u1="&#xdd;" u2="&#xeb;" k="125" />
<hkern u1="&#xdd;" u2="&#xe4;" k="88" />
<hkern u1="&#xdd;" u2="&#xe3;" k="70" />
<hkern u1="&#xdd;" u2="&#xdf;" k="29" />
<hkern u1="&#xdd;" u2="&#xc6;" k="121" />
<hkern u1="&#xdd;" u2="&#xae;" k="39" />
<hkern u1="&#xdd;" u2="x" k="70" />
<hkern u1="&#xdd;" u2="v" k="68" />
<hkern u1="&#xdd;" u2="f" k="47" />
<hkern u1="&#xdd;" u2="&#x40;" k="72" />
<hkern u1="&#xdd;" u2="&#x2f;" k="133" />
<hkern u1="&#xdd;" u2="&#x2a;" k="-10" />
<hkern u1="&#xdd;" u2="&#x26;" k="57" />
<hkern u1="&#xdf;" u2="&#x2122;" k="18" />
<hkern u1="&#xdf;" u2="&#x201d;" k="25" />
<hkern u1="&#xdf;" u2="&#x201c;" k="29" />
<hkern u1="&#xdf;" u2="&#x2019;" k="25" />
<hkern u1="&#xdf;" u2="&#x2018;" k="29" />
<hkern u1="&#xdf;" u2="&#x21b;" k="10" />
<hkern u1="&#xdf;" u2="&#x21a;" k="37" />
<hkern u1="&#xdf;" u2="&#x178;" k="74" />
<hkern u1="&#xdf;" u2="&#x177;" k="27" />
<hkern u1="&#xdf;" u2="&#x176;" k="74" />
<hkern u1="&#xdf;" u2="&#x175;" k="12" />
<hkern u1="&#xdf;" u2="&#x174;" k="29" />
<hkern u1="&#xdf;" u2="&#x172;" k="12" />
<hkern u1="&#xdf;" u2="&#x170;" k="12" />
<hkern u1="&#xdf;" u2="&#x16e;" k="12" />
<hkern u1="&#xdf;" u2="&#x16c;" k="12" />
<hkern u1="&#xdf;" u2="&#x16a;" k="12" />
<hkern u1="&#xdf;" u2="&#x168;" k="12" />
<hkern u1="&#xdf;" u2="&#x167;" k="10" />
<hkern u1="&#xdf;" u2="&#x166;" k="37" />
<hkern u1="&#xdf;" u2="&#x165;" k="10" />
<hkern u1="&#xdf;" u2="&#x164;" k="37" />
<hkern u1="&#xdf;" u2="&#x123;" k="8" />
<hkern u1="&#xdf;" u2="&#x121;" k="8" />
<hkern u1="&#xdf;" u2="&#x11f;" k="8" />
<hkern u1="&#xdf;" u2="&#x11d;" k="8" />
<hkern u1="&#xdf;" u2="&#xff;" k="27" />
<hkern u1="&#xdf;" u2="&#xfd;" k="27" />
<hkern u1="&#xdf;" u2="&#xdd;" k="74" />
<hkern u1="&#xdf;" u2="&#xdc;" k="12" />
<hkern u1="&#xdf;" u2="&#xdb;" k="12" />
<hkern u1="&#xdf;" u2="&#xda;" k="12" />
<hkern u1="&#xdf;" u2="&#xd9;" k="12" />
<hkern u1="&#xdf;" u2="&#xae;" k="25" />
<hkern u1="&#xdf;" u2="y" k="27" />
<hkern u1="&#xdf;" u2="x" k="8" />
<hkern u1="&#xdf;" u2="w" k="12" />
<hkern u1="&#xdf;" u2="v" k="23" />
<hkern u1="&#xdf;" u2="t" k="10" />
<hkern u1="&#xdf;" u2="g" k="8" />
<hkern u1="&#xdf;" u2="f" k="12" />
<hkern u1="&#xdf;" u2="]" k="20" />
<hkern u1="&#xdf;" u2="\" k="43" />
<hkern u1="&#xdf;" u2="Y" k="74" />
<hkern u1="&#xdf;" u2="X" k="10" />
<hkern u1="&#xdf;" u2="W" k="29" />
<hkern u1="&#xdf;" u2="V" k="43" />
<hkern u1="&#xdf;" u2="U" k="12" />
<hkern u1="&#xdf;" u2="T" k="37" />
<hkern u1="&#xdf;" u2="&#x2a;" k="25" />
<hkern u1="&#xdf;" u2="&#x27;" k="23" />
<hkern u1="&#xdf;" u2="&#x22;" k="23" />
<hkern u1="&#xe0;" u2="&#x2122;" k="31" />
<hkern u1="&#xe0;" u2="v" k="12" />
<hkern u1="&#xe0;" u2="\" k="88" />
<hkern u1="&#xe0;" u2="V" k="57" />
<hkern u1="&#xe0;" u2="&#x3f;" k="27" />
<hkern u1="&#xe0;" u2="&#x2a;" k="16" />
<hkern u1="&#xe1;" u2="&#x2122;" k="31" />
<hkern u1="&#xe1;" u2="v" k="12" />
<hkern u1="&#xe1;" u2="\" k="88" />
<hkern u1="&#xe1;" u2="V" k="57" />
<hkern u1="&#xe1;" u2="&#x3f;" k="27" />
<hkern u1="&#xe1;" u2="&#x2a;" k="16" />
<hkern u1="&#xe2;" u2="&#x2122;" k="31" />
<hkern u1="&#xe2;" u2="v" k="12" />
<hkern u1="&#xe2;" u2="\" k="88" />
<hkern u1="&#xe2;" u2="V" k="57" />
<hkern u1="&#xe2;" u2="&#x3f;" k="27" />
<hkern u1="&#xe2;" u2="&#x2a;" k="16" />
<hkern u1="&#xe3;" u2="&#x2122;" k="31" />
<hkern u1="&#xe3;" u2="v" k="12" />
<hkern u1="&#xe3;" u2="\" k="88" />
<hkern u1="&#xe3;" u2="V" k="57" />
<hkern u1="&#xe3;" u2="&#x3f;" k="27" />
<hkern u1="&#xe3;" u2="&#x2a;" k="16" />
<hkern u1="&#xe4;" u2="&#x2122;" k="31" />
<hkern u1="&#xe4;" u2="v" k="12" />
<hkern u1="&#xe4;" u2="\" k="88" />
<hkern u1="&#xe4;" u2="V" k="57" />
<hkern u1="&#xe4;" u2="&#x3f;" k="27" />
<hkern u1="&#xe4;" u2="&#x2a;" k="16" />
<hkern u1="&#xe5;" u2="&#x2122;" k="31" />
<hkern u1="&#xe5;" u2="v" k="12" />
<hkern u1="&#xe5;" u2="\" k="88" />
<hkern u1="&#xe5;" u2="V" k="57" />
<hkern u1="&#xe5;" u2="&#x3f;" k="27" />
<hkern u1="&#xe5;" u2="&#x2a;" k="16" />
<hkern u1="&#xe6;" u2="&#x2122;" k="25" />
<hkern u1="&#xe6;" u2="&#xc6;" k="10" />
<hkern u1="&#xe6;" u2="&#x7d;" k="20" />
<hkern u1="&#xe6;" u2="v" k="12" />
<hkern u1="&#xe6;" u2="\" k="78" />
<hkern u1="&#xe6;" u2="X" k="10" />
<hkern u1="&#xe6;" u2="V" k="51" />
<hkern u1="&#xe6;" u2="&#x3f;" k="23" />
<hkern u1="&#xe6;" u2="&#x29;" k="25" />
<hkern u1="&#xe7;" u2="\" k="49" />
<hkern u1="&#xe7;" u2="V" k="25" />
<hkern u1="&#xe8;" u2="&#x2122;" k="25" />
<hkern u1="&#xe8;" u2="&#xc6;" k="10" />
<hkern u1="&#xe8;" u2="&#x7d;" k="20" />
<hkern u1="&#xe8;" u2="v" k="12" />
<hkern u1="&#xe8;" u2="\" k="78" />
<hkern u1="&#xe8;" u2="X" k="10" />
<hkern u1="&#xe8;" u2="V" k="51" />
<hkern u1="&#xe8;" u2="&#x3f;" k="23" />
<hkern u1="&#xe8;" u2="&#x29;" k="25" />
<hkern u1="&#xe9;" u2="&#x2122;" k="25" />
<hkern u1="&#xe9;" u2="&#xc6;" k="10" />
<hkern u1="&#xe9;" u2="&#x7d;" k="20" />
<hkern u1="&#xe9;" u2="v" k="12" />
<hkern u1="&#xe9;" u2="\" k="78" />
<hkern u1="&#xe9;" u2="X" k="10" />
<hkern u1="&#xe9;" u2="V" k="51" />
<hkern u1="&#xe9;" u2="&#x3f;" k="23" />
<hkern u1="&#xe9;" u2="&#x29;" k="25" />
<hkern u1="&#xea;" u2="&#x2122;" k="25" />
<hkern u1="&#xea;" u2="&#xc6;" k="10" />
<hkern u1="&#xea;" u2="&#x7d;" k="20" />
<hkern u1="&#xea;" u2="v" k="12" />
<hkern u1="&#xea;" u2="\" k="78" />
<hkern u1="&#xea;" u2="X" k="10" />
<hkern u1="&#xea;" u2="V" k="51" />
<hkern u1="&#xea;" u2="&#x3f;" k="23" />
<hkern u1="&#xea;" u2="&#x29;" k="25" />
<hkern u1="&#xeb;" u2="&#x2122;" k="25" />
<hkern u1="&#xeb;" u2="&#xc6;" k="10" />
<hkern u1="&#xeb;" u2="&#x7d;" k="20" />
<hkern u1="&#xeb;" u2="v" k="12" />
<hkern u1="&#xeb;" u2="\" k="78" />
<hkern u1="&#xeb;" u2="X" k="10" />
<hkern u1="&#xeb;" u2="V" k="51" />
<hkern u1="&#xeb;" u2="&#x3f;" k="23" />
<hkern u1="&#xeb;" u2="&#x29;" k="25" />
<hkern u1="&#xec;" u2="&#xef;" k="-16" />
<hkern u1="&#xec;" u2="&#xec;" k="-37" />
<hkern u1="&#xed;" u2="&#x2122;" k="-74" />
<hkern u1="&#xed;" u2="&#x201d;" k="-16" />
<hkern u1="&#xed;" u2="&#x2019;" k="-16" />
<hkern u1="&#xed;" u2="&#x165;" k="-27" />
<hkern u1="&#xed;" u2="&#x159;" k="-98" />
<hkern u1="&#xed;" u2="&#x142;" k="-37" />
<hkern u1="&#xed;" u2="&#x13e;" k="-37" />
<hkern u1="&#xed;" u2="&#x13c;" k="-37" />
<hkern u1="&#xed;" u2="&#x13a;" k="-37" />
<hkern u1="&#xed;" u2="&#x137;" k="-49" />
<hkern u1="&#xed;" u2="&#x135;" k="-49" />
<hkern u1="&#xed;" u2="&#x131;" k="-49" />
<hkern u1="&#xed;" u2="&#x12f;" k="-49" />
<hkern u1="&#xed;" u2="&#x12d;" k="-49" />
<hkern u1="&#xed;" u2="&#x12b;" k="-49" />
<hkern u1="&#xed;" u2="&#x129;" k="-49" />
<hkern u1="&#xed;" u2="&#x127;" k="-49" />
<hkern u1="&#xed;" u2="&#x125;" k="-49" />
<hkern u1="&#xed;" u2="&#xef;" k="-16" />
<hkern u1="&#xed;" u2="&#xee;" k="-49" />
<hkern u1="&#xed;" u2="&#xed;" k="-49" />
<hkern u1="&#xed;" u2="&#xec;" k="-37" />
<hkern u1="&#xed;" u2="&#xdf;" k="-49" />
<hkern u1="&#xed;" u2="&#x7d;" k="-104" />
<hkern u1="&#xed;" u2="&#x7c;" k="-43" />
<hkern u1="&#xed;" u2="l" k="-37" />
<hkern u1="&#xed;" u2="k" k="-49" />
<hkern u1="&#xed;" u2="j" k="-49" />
<hkern u1="&#xed;" u2="i" k="-49" />
<hkern u1="&#xed;" u2="h" k="-49" />
<hkern u1="&#xed;" u2="b" k="-51" />
<hkern u1="&#xed;" u2="]" k="-111" />
<hkern u1="&#xed;" u2="\" k="-100" />
<hkern u1="&#xed;" u2="&#x3f;" k="-121" />
<hkern u1="&#xed;" u2="&#x2a;" k="-100" />
<hkern u1="&#xed;" u2="&#x29;" k="-55" />
<hkern u1="&#xed;" u2="&#x27;" k="-63" />
<hkern u1="&#xed;" u2="&#x22;" k="-63" />
<hkern u1="&#xed;" u2="&#x21;" k="-41" />
<hkern u1="&#xee;" u2="&#x2122;" k="-14" />
<hkern u1="&#xee;" u2="&#xef;" k="-16" />
<hkern u1="&#xee;" u2="&#xec;" k="-37" />
<hkern u1="&#xee;" u2="&#x3f;" k="-53" />
<hkern u1="&#xee;" u2="&#x2a;" k="-68" />
<hkern u1="&#xef;" u2="&#x2122;" k="-20" />
<hkern u1="&#xef;" u2="&#xef;" k="-16" />
<hkern u1="&#xef;" u2="&#xec;" k="-37" />
<hkern u1="&#xef;" u2="&#x7d;" k="-55" />
<hkern u1="&#xef;" u2="]" k="-57" />
<hkern u1="&#xef;" u2="\" k="-63" />
<hkern u1="&#xef;" u2="&#x3f;" k="-70" />
<hkern u1="&#xef;" u2="&#x2a;" k="-92" />
<hkern u1="&#xef;" u2="&#x29;" k="-57" />
<hkern u1="&#xef;" u2="&#x27;" k="-12" />
<hkern u1="&#xef;" u2="&#x22;" k="-12" />
<hkern u1="&#xf1;" u2="&#x2122;" k="29" />
<hkern u1="&#xf1;" u2="v" k="8" />
<hkern u1="&#xf1;" u2="\" k="82" />
<hkern u1="&#xf1;" u2="V" k="55" />
<hkern u1="&#xf1;" u2="&#x3f;" k="29" />
<hkern u1="&#xf1;" u2="&#x2a;" k="16" />
<hkern u1="&#xf1;" u2="&#x29;" k="23" />
<hkern u1="&#xf2;" u2="&#x2122;" k="29" />
<hkern u1="&#xf2;" u2="&#xc6;" k="14" />
<hkern u1="&#xf2;" u2="&#x7d;" k="39" />
<hkern u1="&#xf2;" u2="x" k="20" />
<hkern u1="&#xf2;" u2="v" k="14" />
<hkern u1="&#xf2;" u2="]" k="55" />
<hkern u1="&#xf2;" u2="\" k="84" />
<hkern u1="&#xf2;" u2="X" k="53" />
<hkern u1="&#xf2;" u2="V" k="59" />
<hkern u1="&#xf2;" u2="&#x3f;" k="31" />
<hkern u1="&#xf2;" u2="&#x2a;" k="16" />
<hkern u1="&#xf2;" u2="&#x29;" k="41" />
<hkern u1="&#xf3;" u2="&#x2122;" k="29" />
<hkern u1="&#xf3;" u2="&#xc6;" k="14" />
<hkern u1="&#xf3;" u2="&#x7d;" k="39" />
<hkern u1="&#xf3;" u2="x" k="20" />
<hkern u1="&#xf3;" u2="v" k="14" />
<hkern u1="&#xf3;" u2="]" k="55" />
<hkern u1="&#xf3;" u2="\" k="84" />
<hkern u1="&#xf3;" u2="X" k="53" />
<hkern u1="&#xf3;" u2="V" k="59" />
<hkern u1="&#xf3;" u2="&#x3f;" k="31" />
<hkern u1="&#xf3;" u2="&#x2a;" k="16" />
<hkern u1="&#xf3;" u2="&#x29;" k="41" />
<hkern u1="&#xf4;" u2="&#x2122;" k="29" />
<hkern u1="&#xf4;" u2="&#xc6;" k="14" />
<hkern u1="&#xf4;" u2="&#x7d;" k="39" />
<hkern u1="&#xf4;" u2="x" k="20" />
<hkern u1="&#xf4;" u2="v" k="14" />
<hkern u1="&#xf4;" u2="]" k="55" />
<hkern u1="&#xf4;" u2="\" k="84" />
<hkern u1="&#xf4;" u2="X" k="53" />
<hkern u1="&#xf4;" u2="V" k="59" />
<hkern u1="&#xf4;" u2="&#x3f;" k="31" />
<hkern u1="&#xf4;" u2="&#x2a;" k="16" />
<hkern u1="&#xf4;" u2="&#x29;" k="41" />
<hkern u1="&#xf5;" u2="&#x2122;" k="29" />
<hkern u1="&#xf5;" u2="&#xc6;" k="14" />
<hkern u1="&#xf5;" u2="&#x7d;" k="39" />
<hkern u1="&#xf5;" u2="x" k="20" />
<hkern u1="&#xf5;" u2="v" k="14" />
<hkern u1="&#xf5;" u2="]" k="55" />
<hkern u1="&#xf5;" u2="\" k="84" />
<hkern u1="&#xf5;" u2="X" k="53" />
<hkern u1="&#xf5;" u2="V" k="59" />
<hkern u1="&#xf5;" u2="&#x3f;" k="31" />
<hkern u1="&#xf5;" u2="&#x2a;" k="16" />
<hkern u1="&#xf5;" u2="&#x29;" k="41" />
<hkern u1="&#xf6;" u2="&#x2122;" k="29" />
<hkern u1="&#xf6;" u2="&#xc6;" k="14" />
<hkern u1="&#xf6;" u2="&#x7d;" k="39" />
<hkern u1="&#xf6;" u2="x" k="20" />
<hkern u1="&#xf6;" u2="v" k="14" />
<hkern u1="&#xf6;" u2="]" k="55" />
<hkern u1="&#xf6;" u2="\" k="84" />
<hkern u1="&#xf6;" u2="X" k="53" />
<hkern u1="&#xf6;" u2="V" k="59" />
<hkern u1="&#xf6;" u2="&#x3f;" k="31" />
<hkern u1="&#xf6;" u2="&#x2a;" k="16" />
<hkern u1="&#xf6;" u2="&#x29;" k="41" />
<hkern u1="&#xf8;" u2="&#x2122;" k="29" />
<hkern u1="&#xf8;" u2="&#xc6;" k="14" />
<hkern u1="&#xf8;" u2="&#x7d;" k="39" />
<hkern u1="&#xf8;" u2="x" k="20" />
<hkern u1="&#xf8;" u2="v" k="14" />
<hkern u1="&#xf8;" u2="]" k="55" />
<hkern u1="&#xf8;" u2="\" k="84" />
<hkern u1="&#xf8;" u2="X" k="53" />
<hkern u1="&#xf8;" u2="V" k="59" />
<hkern u1="&#xf8;" u2="&#x3f;" k="31" />
<hkern u1="&#xf8;" u2="&#x2a;" k="16" />
<hkern u1="&#xf8;" u2="&#x29;" k="41" />
<hkern u1="&#xf9;" u2="&#x2122;" k="18" />
<hkern u1="&#xf9;" u2="\" k="57" />
<hkern u1="&#xf9;" u2="X" k="10" />
<hkern u1="&#xf9;" u2="V" k="43" />
<hkern u1="&#xf9;" u2="&#x29;" k="23" />
<hkern u1="&#xfa;" u2="&#x2122;" k="18" />
<hkern u1="&#xfa;" u2="\" k="57" />
<hkern u1="&#xfa;" u2="X" k="10" />
<hkern u1="&#xfa;" u2="V" k="43" />
<hkern u1="&#xfa;" u2="&#x29;" k="23" />
<hkern u1="&#xfb;" u2="&#x2122;" k="18" />
<hkern u1="&#xfb;" u2="\" k="57" />
<hkern u1="&#xfb;" u2="X" k="10" />
<hkern u1="&#xfb;" u2="V" k="43" />
<hkern u1="&#xfb;" u2="&#x29;" k="23" />
<hkern u1="&#xfc;" u2="&#x2122;" k="18" />
<hkern u1="&#xfc;" u2="\" k="57" />
<hkern u1="&#xfc;" u2="X" k="10" />
<hkern u1="&#xfc;" u2="V" k="43" />
<hkern u1="&#xfc;" u2="&#x29;" k="23" />
<hkern u1="&#xfd;" u2="&#xc6;" k="37" />
<hkern u1="&#xfd;" u2="&#x7d;" k="23" />
<hkern u1="&#xfd;" u2="]" k="39" />
<hkern u1="&#xfd;" u2="\" k="37" />
<hkern u1="&#xfd;" u2="X" k="45" />
<hkern u1="&#xfd;" u2="V" k="12" />
<hkern u1="&#xfd;" u2="&#x2f;" k="39" />
<hkern u1="&#xff;" u2="&#xc6;" k="37" />
<hkern u1="&#xff;" u2="&#x7d;" k="23" />
<hkern u1="&#xff;" u2="]" k="39" />
<hkern u1="&#xff;" u2="\" k="37" />
<hkern u1="&#xff;" u2="X" k="45" />
<hkern u1="&#xff;" u2="V" k="12" />
<hkern u1="&#xff;" u2="&#x2f;" k="39" />
<hkern u1="&#x100;" u2="&#x2122;" k="61" />
<hkern u1="&#x100;" u2="&#xae;" k="37" />
<hkern u1="&#x100;" u2="v" k="29" />
<hkern u1="&#x100;" u2="f" k="16" />
<hkern u1="&#x100;" u2="\" k="86" />
<hkern u1="&#x100;" u2="V" k="55" />
<hkern u1="&#x100;" u2="&#x3f;" k="29" />
<hkern u1="&#x100;" u2="&#x2a;" k="55" />
<hkern u1="&#x101;" u2="&#x2122;" k="31" />
<hkern u1="&#x101;" u2="v" k="12" />
<hkern u1="&#x101;" u2="\" k="88" />
<hkern u1="&#x101;" u2="V" k="57" />
<hkern u1="&#x101;" u2="&#x3f;" k="27" />
<hkern u1="&#x101;" u2="&#x2a;" k="16" />
<hkern u1="&#x102;" u2="&#x2122;" k="61" />
<hkern u1="&#x102;" u2="&#xae;" k="37" />
<hkern u1="&#x102;" u2="v" k="29" />
<hkern u1="&#x102;" u2="f" k="16" />
<hkern u1="&#x102;" u2="\" k="86" />
<hkern u1="&#x102;" u2="V" k="55" />
<hkern u1="&#x102;" u2="&#x3f;" k="29" />
<hkern u1="&#x102;" u2="&#x2a;" k="55" />
<hkern u1="&#x103;" u2="&#x2122;" k="31" />
<hkern u1="&#x103;" u2="v" k="12" />
<hkern u1="&#x103;" u2="\" k="88" />
<hkern u1="&#x103;" u2="V" k="57" />
<hkern u1="&#x103;" u2="&#x3f;" k="27" />
<hkern u1="&#x103;" u2="&#x2a;" k="16" />
<hkern u1="&#x104;" u2="&#x2122;" k="61" />
<hkern u1="&#x104;" u2="&#x201e;" k="-31" />
<hkern u1="&#x104;" u2="&#xae;" k="37" />
<hkern u1="&#x104;" u2="v" k="29" />
<hkern u1="&#x104;" u2="j" k="-90" />
<hkern u1="&#x104;" u2="f" k="16" />
<hkern u1="&#x104;" u2="\" k="86" />
<hkern u1="&#x104;" u2="V" k="55" />
<hkern u1="&#x104;" u2="&#x3f;" k="29" />
<hkern u1="&#x104;" u2="&#x2a;" k="55" />
<hkern u1="&#x105;" u2="&#x2122;" k="31" />
<hkern u1="&#x105;" u2="v" k="12" />
<hkern u1="&#x105;" u2="j" k="-33" />
<hkern u1="&#x105;" u2="\" k="88" />
<hkern u1="&#x105;" u2="V" k="57" />
<hkern u1="&#x105;" u2="&#x3f;" k="27" />
<hkern u1="&#x105;" u2="&#x2a;" k="16" />
<hkern u1="&#x106;" u2="&#x135;" k="-43" />
<hkern u1="&#x106;" u2="&#x12d;" k="-20" />
<hkern u1="&#x106;" u2="&#x129;" k="-49" />
<hkern u1="&#x106;" u2="&#xef;" k="-72" />
<hkern u1="&#x106;" u2="&#xee;" k="-59" />
<hkern u1="&#x106;" u2="&#xec;" k="-90" />
<hkern u1="&#x106;" u2="v" k="10" />
<hkern u1="&#x106;" u2="f" k="10" />
<hkern u1="&#x107;" u2="\" k="49" />
<hkern u1="&#x107;" u2="V" k="25" />
<hkern u1="&#x108;" u2="&#x135;" k="-43" />
<hkern u1="&#x108;" u2="&#x12d;" k="-20" />
<hkern u1="&#x108;" u2="&#x129;" k="-49" />
<hkern u1="&#x108;" u2="&#xef;" k="-72" />
<hkern u1="&#x108;" u2="&#xee;" k="-59" />
<hkern u1="&#x108;" u2="&#xec;" k="-90" />
<hkern u1="&#x108;" u2="v" k="10" />
<hkern u1="&#x108;" u2="f" k="10" />
<hkern u1="&#x109;" u2="\" k="49" />
<hkern u1="&#x109;" u2="V" k="25" />
<hkern u1="&#x10a;" u2="&#x135;" k="-43" />
<hkern u1="&#x10a;" u2="&#x12d;" k="-20" />
<hkern u1="&#x10a;" u2="&#x129;" k="-49" />
<hkern u1="&#x10a;" u2="&#xef;" k="-72" />
<hkern u1="&#x10a;" u2="&#xee;" k="-59" />
<hkern u1="&#x10a;" u2="&#xec;" k="-90" />
<hkern u1="&#x10a;" u2="v" k="10" />
<hkern u1="&#x10a;" u2="f" k="10" />
<hkern u1="&#x10b;" u2="\" k="49" />
<hkern u1="&#x10b;" u2="V" k="25" />
<hkern u1="&#x10c;" u2="&#x135;" k="-43" />
<hkern u1="&#x10c;" u2="&#x12d;" k="-20" />
<hkern u1="&#x10c;" u2="&#x129;" k="-49" />
<hkern u1="&#x10c;" u2="&#xef;" k="-72" />
<hkern u1="&#x10c;" u2="&#xee;" k="-59" />
<hkern u1="&#x10c;" u2="&#xec;" k="-90" />
<hkern u1="&#x10c;" u2="v" k="10" />
<hkern u1="&#x10c;" u2="f" k="10" />
<hkern u1="&#x10d;" u2="\" k="49" />
<hkern u1="&#x10d;" u2="V" k="25" />
<hkern u1="&#x10e;" u2="&#xc6;" k="27" />
<hkern u1="&#x10e;" u2="&#x7d;" k="25" />
<hkern u1="&#x10e;" u2="]" k="39" />
<hkern u1="&#x10e;" u2="\" k="37" />
<hkern u1="&#x10e;" u2="X" k="37" />
<hkern u1="&#x10e;" u2="V" k="20" />
<hkern u1="&#x10e;" u2="&#x2f;" k="31" />
<hkern u1="&#x10e;" u2="&#x29;" k="27" />
<hkern u1="&#x10f;" u2="&#x2122;" k="-41" />
<hkern u1="&#x10f;" u2="&#x17e;" k="-45" />
<hkern u1="&#x10f;" u2="&#x161;" k="-35" />
<hkern u1="&#x10f;" u2="&#x10d;" k="-27" />
<hkern u1="&#x10f;" u2="&#xe1;" k="-18" />
<hkern u1="&#x10f;" u2="&#xdf;" k="-47" />
<hkern u1="&#x10f;" u2="&#x7d;" k="-92" />
<hkern u1="&#x10f;" u2="&#x7c;" k="-27" />
<hkern u1="&#x10f;" u2="x" k="-47" />
<hkern u1="&#x10f;" u2="v" k="-45" />
<hkern u1="&#x10f;" u2="f" k="-10" />
<hkern u1="&#x10f;" u2="]" k="-94" />
<hkern u1="&#x10f;" u2="\" k="-94" />
<hkern u1="&#x10f;" u2="&#x3f;" k="-96" />
<hkern u1="&#x10f;" u2="&#x2f;" k="72" />
<hkern u1="&#x10f;" u2="&#x2a;" k="-98" />
<hkern u1="&#x10f;" u2="&#x29;" k="-59" />
<hkern u1="&#x10f;" u2="&#x21;" k="-27" />
<hkern u1="&#x110;" u2="&#xc6;" k="27" />
<hkern u1="&#x110;" u2="&#x7d;" k="25" />
<hkern u1="&#x110;" u2="]" k="39" />
<hkern u1="&#x110;" u2="\" k="37" />
<hkern u1="&#x110;" u2="X" k="37" />
<hkern u1="&#x110;" u2="V" k="20" />
<hkern u1="&#x110;" u2="&#x2f;" k="31" />
<hkern u1="&#x110;" u2="&#x29;" k="27" />
<hkern u1="&#x111;" u2="&#xef;" k="-16" />
<hkern u1="&#x111;" u2="&#xec;" k="-37" />
<hkern u1="&#x112;" u2="&#x135;" k="-43" />
<hkern u1="&#x112;" u2="&#x12d;" k="-12" />
<hkern u1="&#x112;" u2="&#x129;" k="-45" />
<hkern u1="&#x112;" u2="&#xef;" k="-66" />
<hkern u1="&#x112;" u2="&#xee;" k="-59" />
<hkern u1="&#x112;" u2="&#xec;" k="-86" />
<hkern u1="&#x113;" u2="&#x2122;" k="25" />
<hkern u1="&#x113;" u2="&#xc6;" k="10" />
<hkern u1="&#x113;" u2="&#x7d;" k="20" />
<hkern u1="&#x113;" u2="v" k="12" />
<hkern u1="&#x113;" u2="\" k="78" />
<hkern u1="&#x113;" u2="X" k="10" />
<hkern u1="&#x113;" u2="V" k="51" />
<hkern u1="&#x113;" u2="&#x3f;" k="23" />
<hkern u1="&#x113;" u2="&#x29;" k="25" />
<hkern u1="&#x114;" u2="&#x135;" k="-43" />
<hkern u1="&#x114;" u2="&#x12d;" k="-12" />
<hkern u1="&#x114;" u2="&#x129;" k="-45" />
<hkern u1="&#x114;" u2="&#xef;" k="-66" />
<hkern u1="&#x114;" u2="&#xee;" k="-59" />
<hkern u1="&#x114;" u2="&#xec;" k="-86" />
<hkern u1="&#x115;" u2="&#x2122;" k="25" />
<hkern u1="&#x115;" u2="&#xc6;" k="10" />
<hkern u1="&#x115;" u2="&#x7d;" k="20" />
<hkern u1="&#x115;" u2="v" k="12" />
<hkern u1="&#x115;" u2="\" k="78" />
<hkern u1="&#x115;" u2="X" k="10" />
<hkern u1="&#x115;" u2="V" k="51" />
<hkern u1="&#x115;" u2="&#x3f;" k="23" />
<hkern u1="&#x115;" u2="&#x29;" k="25" />
<hkern u1="&#x116;" u2="&#x135;" k="-43" />
<hkern u1="&#x116;" u2="&#x12d;" k="-12" />
<hkern u1="&#x116;" u2="&#x129;" k="-45" />
<hkern u1="&#x116;" u2="&#xef;" k="-66" />
<hkern u1="&#x116;" u2="&#xee;" k="-59" />
<hkern u1="&#x116;" u2="&#xec;" k="-86" />
<hkern u1="&#x117;" u2="&#x2122;" k="25" />
<hkern u1="&#x117;" u2="&#xc6;" k="10" />
<hkern u1="&#x117;" u2="&#x7d;" k="20" />
<hkern u1="&#x117;" u2="v" k="12" />
<hkern u1="&#x117;" u2="\" k="78" />
<hkern u1="&#x117;" u2="X" k="10" />
<hkern u1="&#x117;" u2="V" k="51" />
<hkern u1="&#x117;" u2="&#x3f;" k="23" />
<hkern u1="&#x117;" u2="&#x29;" k="25" />
<hkern u1="&#x118;" u2="&#x135;" k="-43" />
<hkern u1="&#x118;" u2="&#x12d;" k="-12" />
<hkern u1="&#x118;" u2="&#x129;" k="-45" />
<hkern u1="&#x118;" u2="&#xef;" k="-66" />
<hkern u1="&#x118;" u2="&#xee;" k="-59" />
<hkern u1="&#x118;" u2="&#xec;" k="-86" />
<hkern u1="&#x118;" u2="j" k="-23" />
<hkern u1="&#x119;" u2="&#x2122;" k="25" />
<hkern u1="&#x119;" u2="&#xc6;" k="10" />
<hkern u1="&#x119;" u2="&#x7d;" k="20" />
<hkern u1="&#x119;" u2="v" k="12" />
<hkern u1="&#x119;" u2="\" k="78" />
<hkern u1="&#x119;" u2="X" k="10" />
<hkern u1="&#x119;" u2="V" k="51" />
<hkern u1="&#x119;" u2="&#x3f;" k="23" />
<hkern u1="&#x119;" u2="&#x29;" k="25" />
<hkern u1="&#x11a;" u2="&#x135;" k="-43" />
<hkern u1="&#x11a;" u2="&#x12d;" k="-12" />
<hkern u1="&#x11a;" u2="&#x129;" k="-45" />
<hkern u1="&#x11a;" u2="&#xef;" k="-66" />
<hkern u1="&#x11a;" u2="&#xee;" k="-59" />
<hkern u1="&#x11a;" u2="&#xec;" k="-86" />
<hkern u1="&#x11b;" u2="&#x2122;" k="25" />
<hkern u1="&#x11b;" u2="&#xc6;" k="10" />
<hkern u1="&#x11b;" u2="&#x7d;" k="20" />
<hkern u1="&#x11b;" u2="v" k="12" />
<hkern u1="&#x11b;" u2="\" k="78" />
<hkern u1="&#x11b;" u2="X" k="10" />
<hkern u1="&#x11b;" u2="V" k="51" />
<hkern u1="&#x11b;" u2="&#x3f;" k="23" />
<hkern u1="&#x11b;" u2="&#x29;" k="25" />
<hkern u1="&#x11c;" u2="&#xef;" k="-31" />
<hkern u1="&#x11c;" u2="&#xee;" k="-18" />
<hkern u1="&#x11c;" u2="&#xec;" k="-51" />
<hkern u1="&#x11c;" u2="v" k="10" />
<hkern u1="&#x11c;" u2="f" k="12" />
<hkern u1="&#x11c;" u2="\" k="20" />
<hkern u1="&#x11c;" u2="V" k="16" />
<hkern u1="&#x11d;" u2="&#x135;" k="-49" />
<hkern u1="&#x11d;" u2="j" k="-49" />
<hkern u1="&#x11d;" u2="\" k="31" />
<hkern u1="&#x11d;" u2="V" k="10" />
<hkern u1="&#x11e;" u2="&#xef;" k="-31" />
<hkern u1="&#x11e;" u2="&#xee;" k="-18" />
<hkern u1="&#x11e;" u2="&#xec;" k="-51" />
<hkern u1="&#x11e;" u2="v" k="10" />
<hkern u1="&#x11e;" u2="f" k="12" />
<hkern u1="&#x11e;" u2="\" k="20" />
<hkern u1="&#x11e;" u2="V" k="16" />
<hkern u1="&#x11f;" u2="&#x135;" k="-49" />
<hkern u1="&#x11f;" u2="j" k="-49" />
<hkern u1="&#x11f;" u2="\" k="31" />
<hkern u1="&#x11f;" u2="V" k="10" />
<hkern u1="&#x120;" u2="&#xef;" k="-31" />
<hkern u1="&#x120;" u2="&#xee;" k="-18" />
<hkern u1="&#x120;" u2="&#xec;" k="-51" />
<hkern u1="&#x120;" u2="v" k="10" />
<hkern u1="&#x120;" u2="f" k="12" />
<hkern u1="&#x120;" u2="\" k="20" />
<hkern u1="&#x120;" u2="V" k="16" />
<hkern u1="&#x121;" u2="&#x135;" k="-49" />
<hkern u1="&#x121;" u2="j" k="-49" />
<hkern u1="&#x121;" u2="\" k="31" />
<hkern u1="&#x121;" u2="V" k="10" />
<hkern u1="&#x122;" u2="&#xef;" k="-31" />
<hkern u1="&#x122;" u2="&#xee;" k="-18" />
<hkern u1="&#x122;" u2="&#xec;" k="-51" />
<hkern u1="&#x122;" u2="v" k="10" />
<hkern u1="&#x122;" u2="f" k="12" />
<hkern u1="&#x122;" u2="\" k="20" />
<hkern u1="&#x122;" u2="V" k="16" />
<hkern u1="&#x123;" u2="&#x135;" k="-49" />
<hkern u1="&#x123;" u2="j" k="-49" />
<hkern u1="&#x123;" u2="\" k="31" />
<hkern u1="&#x123;" u2="V" k="10" />
<hkern u1="&#x124;" u2="&#xec;" k="-16" />
<hkern u1="&#x124;" u2="f" k="12" />
<hkern u1="&#x125;" u2="&#x2122;" k="29" />
<hkern u1="&#x125;" u2="v" k="8" />
<hkern u1="&#x125;" u2="\" k="82" />
<hkern u1="&#x125;" u2="V" k="55" />
<hkern u1="&#x125;" u2="&#x3f;" k="29" />
<hkern u1="&#x125;" u2="&#x2a;" k="16" />
<hkern u1="&#x125;" u2="&#x29;" k="23" />
<hkern u1="&#x126;" u2="&#xec;" k="-16" />
<hkern u1="&#x126;" u2="f" k="12" />
<hkern u1="&#x126;" u2="&#x2a;" k="-45" />
<hkern u1="&#x127;" u2="&#x2122;" k="29" />
<hkern u1="&#x127;" u2="v" k="8" />
<hkern u1="&#x127;" u2="\" k="82" />
<hkern u1="&#x127;" u2="V" k="55" />
<hkern u1="&#x127;" u2="&#x3f;" k="29" />
<hkern u1="&#x127;" u2="&#x2a;" k="16" />
<hkern u1="&#x127;" u2="&#x29;" k="23" />
<hkern u1="&#x128;" u2="&#xec;" k="-16" />
<hkern u1="&#x128;" u2="f" k="12" />
<hkern u1="&#x129;" u2="&#x2122;" k="-27" />
<hkern u1="&#x129;" u2="&#xef;" k="-16" />
<hkern u1="&#x129;" u2="&#xec;" k="-37" />
<hkern u1="&#x129;" u2="&#x7d;" k="-68" />
<hkern u1="&#x129;" u2="]" k="-70" />
<hkern u1="&#x129;" u2="\" k="-63" />
<hkern u1="&#x129;" u2="&#x3f;" k="-80" />
<hkern u1="&#x129;" u2="&#x2a;" k="-63" />
<hkern u1="&#x129;" u2="&#x29;" k="-18" />
<hkern u1="&#x129;" u2="&#x27;" k="-23" />
<hkern u1="&#x129;" u2="&#x22;" k="-23" />
<hkern u1="&#x12a;" u2="&#xec;" k="-16" />
<hkern u1="&#x12a;" u2="f" k="12" />
<hkern u1="&#x12b;" u2="&#xef;" k="-16" />
<hkern u1="&#x12b;" u2="&#xec;" k="-37" />
<hkern u1="&#x12b;" u2="\" k="-16" />
<hkern u1="&#x12b;" u2="&#x3f;" k="-23" />
<hkern u1="&#x12b;" u2="&#x2a;" k="-45" />
<hkern u1="&#x12c;" u2="&#xec;" k="-16" />
<hkern u1="&#x12c;" u2="f" k="12" />
<hkern u1="&#x12d;" u2="&#xef;" k="-16" />
<hkern u1="&#x12d;" u2="&#xec;" k="-37" />
<hkern u1="&#x12d;" u2="&#x7d;" k="-49" />
<hkern u1="&#x12d;" u2="]" k="-51" />
<hkern u1="&#x12d;" u2="\" k="-16" />
<hkern u1="&#x12d;" u2="&#x3f;" k="-23" />
<hkern u1="&#x12d;" u2="&#x2a;" k="-27" />
<hkern u1="&#x12d;" u2="&#x29;" k="-51" />
<hkern u1="&#x12e;" u2="&#xec;" k="-16" />
<hkern u1="&#x12e;" u2="f" k="12" />
<hkern u1="&#x12f;" u2="&#xef;" k="-16" />
<hkern u1="&#x12f;" u2="&#xec;" k="-37" />
<hkern u1="&#x130;" u2="&#xec;" k="-16" />
<hkern u1="&#x130;" u2="f" k="12" />
<hkern u1="&#x131;" u2="&#xef;" k="-16" />
<hkern u1="&#x131;" u2="&#xec;" k="-37" />
<hkern u1="&#x134;" u2="&#xec;" k="-23" />
<hkern u1="&#x134;" u2="f" k="10" />
<hkern u1="&#x135;" u2="&#x2122;" k="-29" />
<hkern u1="&#x135;" u2="&#xef;" k="-16" />
<hkern u1="&#x135;" u2="&#xec;" k="-37" />
<hkern u1="&#x135;" u2="&#x3f;" k="-68" />
<hkern u1="&#x135;" u2="&#x2a;" k="-76" />
<hkern u1="&#x135;" u2="&#x27;" k="-16" />
<hkern u1="&#x135;" u2="&#x22;" k="-16" />
<hkern u1="&#x136;" u2="&#x12d;" k="-51" />
<hkern u1="&#x136;" u2="&#x12b;" k="-35" />
<hkern u1="&#x136;" u2="&#x129;" k="-72" />
<hkern u1="&#x136;" u2="&#xef;" k="-104" />
<hkern u1="&#x136;" u2="&#xee;" k="-12" />
<hkern u1="&#x136;" u2="&#xec;" k="-96" />
<hkern u1="&#x136;" u2="v" k="35" />
<hkern u1="&#x136;" u2="f" k="23" />
<hkern u1="&#x137;" u2="\" k="35" />
<hkern u1="&#x137;" u2="V" k="16" />
<hkern u1="&#x139;" u2="&#x2122;" k="156" />
<hkern u1="&#x139;" u2="&#xae;" k="127" />
<hkern u1="&#x139;" u2="v" k="66" />
<hkern u1="&#x139;" u2="f" k="16" />
<hkern u1="&#x139;" u2="\" k="147" />
<hkern u1="&#x139;" u2="V" k="113" />
<hkern u1="&#x139;" u2="&#x2a;" k="154" />
<hkern u1="&#x13a;" u2="&#xec;" k="-25" />
<hkern u1="&#x13b;" u2="&#x2122;" k="156" />
<hkern u1="&#x13b;" u2="&#xae;" k="127" />
<hkern u1="&#x13b;" u2="v" k="66" />
<hkern u1="&#x13b;" u2="f" k="16" />
<hkern u1="&#x13b;" u2="\" k="147" />
<hkern u1="&#x13b;" u2="V" k="113" />
<hkern u1="&#x13b;" u2="&#x2a;" k="154" />
<hkern u1="&#x13c;" u2="&#xec;" k="-25" />
<hkern u1="&#x13d;" u2="&#x2122;" k="119" />
<hkern u1="&#x13d;" u2="&#x201d;" k="100" />
<hkern u1="&#x13d;" u2="&#x201c;" k="92" />
<hkern u1="&#x13d;" u2="&#x2019;" k="100" />
<hkern u1="&#x13d;" u2="&#x2018;" k="92" />
<hkern u1="&#x13d;" u2="&#x21a;" k="43" />
<hkern u1="&#x13d;" u2="&#x178;" k="16" />
<hkern u1="&#x13d;" u2="&#x176;" k="16" />
<hkern u1="&#x13d;" u2="&#x174;" k="55" />
<hkern u1="&#x13d;" u2="&#x166;" k="43" />
<hkern u1="&#x13d;" u2="&#x164;" k="43" />
<hkern u1="&#x13d;" u2="&#xdd;" k="16" />
<hkern u1="&#x13d;" u2="&#xae;" k="111" />
<hkern u1="&#x13d;" u2="v" k="66" />
<hkern u1="&#x13d;" u2="f" k="16" />
<hkern u1="&#x13d;" u2="\" k="82" />
<hkern u1="&#x13d;" u2="Y" k="16" />
<hkern u1="&#x13d;" u2="W" k="55" />
<hkern u1="&#x13d;" u2="V" k="45" />
<hkern u1="&#x13d;" u2="T" k="43" />
<hkern u1="&#x13d;" u2="&#x2a;" k="41" />
<hkern u1="&#x13d;" u2="&#x27;" k="133" />
<hkern u1="&#x13d;" u2="&#x22;" k="133" />
<hkern u1="&#x13e;" u2="&#x2122;" k="-41" />
<hkern u1="&#x13e;" u2="&#x17e;" k="-45" />
<hkern u1="&#x13e;" u2="&#x161;" k="-35" />
<hkern u1="&#x13e;" u2="&#x10d;" k="-27" />
<hkern u1="&#x13e;" u2="&#xe1;" k="-18" />
<hkern u1="&#x13e;" u2="&#xdf;" k="-47" />
<hkern u1="&#x13e;" u2="&#x7d;" k="-92" />
<hkern u1="&#x13e;" u2="&#x7c;" k="-27" />
<hkern u1="&#x13e;" u2="x" k="-47" />
<hkern u1="&#x13e;" u2="v" k="-45" />
<hkern u1="&#x13e;" u2="f" k="-10" />
<hkern u1="&#x13e;" u2="]" k="-94" />
<hkern u1="&#x13e;" u2="\" k="-94" />
<hkern u1="&#x13e;" u2="&#x3f;" k="-96" />
<hkern u1="&#x13e;" u2="&#x2f;" k="72" />
<hkern u1="&#x13e;" u2="&#x2a;" k="-98" />
<hkern u1="&#x13e;" u2="&#x29;" k="-59" />
<hkern u1="&#x13e;" u2="&#x21;" k="-27" />
<hkern u1="&#x141;" u2="&#x2122;" k="156" />
<hkern u1="&#x141;" u2="&#xae;" k="127" />
<hkern u1="&#x141;" u2="v" k="66" />
<hkern u1="&#x141;" u2="f" k="16" />
<hkern u1="&#x141;" u2="\" k="147" />
<hkern u1="&#x141;" u2="V" k="113" />
<hkern u1="&#x141;" u2="&#x2a;" k="154" />
<hkern u1="&#x142;" u2="&#xec;" k="-25" />
<hkern u1="&#x143;" u2="&#xec;" k="-16" />
<hkern u1="&#x143;" u2="f" k="12" />
<hkern u1="&#x144;" u2="&#x2122;" k="29" />
<hkern u1="&#x144;" u2="v" k="8" />
<hkern u1="&#x144;" u2="\" k="82" />
<hkern u1="&#x144;" u2="V" k="55" />
<hkern u1="&#x144;" u2="&#x3f;" k="29" />
<hkern u1="&#x144;" u2="&#x2a;" k="16" />
<hkern u1="&#x144;" u2="&#x29;" k="23" />
<hkern u1="&#x145;" u2="&#xec;" k="-16" />
<hkern u1="&#x145;" u2="f" k="12" />
<hkern u1="&#x146;" u2="&#x2122;" k="29" />
<hkern u1="&#x146;" u2="v" k="8" />
<hkern u1="&#x146;" u2="\" k="82" />
<hkern u1="&#x146;" u2="V" k="55" />
<hkern u1="&#x146;" u2="&#x3f;" k="29" />
<hkern u1="&#x146;" u2="&#x2a;" k="16" />
<hkern u1="&#x146;" u2="&#x29;" k="23" />
<hkern u1="&#x147;" u2="&#xec;" k="-16" />
<hkern u1="&#x147;" u2="f" k="12" />
<hkern u1="&#x148;" u2="&#x2122;" k="29" />
<hkern u1="&#x148;" u2="v" k="8" />
<hkern u1="&#x148;" u2="\" k="82" />
<hkern u1="&#x148;" u2="V" k="55" />
<hkern u1="&#x148;" u2="&#x3f;" k="29" />
<hkern u1="&#x148;" u2="&#x2a;" k="16" />
<hkern u1="&#x148;" u2="&#x29;" k="23" />
<hkern u1="&#x14a;" u2="&#xec;" k="-16" />
<hkern u1="&#x14a;" u2="f" k="12" />
<hkern u1="&#x14b;" u2="&#x2122;" k="29" />
<hkern u1="&#x14b;" u2="v" k="8" />
<hkern u1="&#x14b;" u2="\" k="82" />
<hkern u1="&#x14b;" u2="V" k="55" />
<hkern u1="&#x14b;" u2="&#x3f;" k="29" />
<hkern u1="&#x14b;" u2="&#x2a;" k="16" />
<hkern u1="&#x14b;" u2="&#x29;" k="23" />
<hkern u1="&#x14c;" u2="&#xc6;" k="27" />
<hkern u1="&#x14c;" u2="&#x7d;" k="25" />
<hkern u1="&#x14c;" u2="]" k="41" />
<hkern u1="&#x14c;" u2="\" k="39" />
<hkern u1="&#x14c;" u2="X" k="35" />
<hkern u1="&#x14c;" u2="V" k="20" />
<hkern u1="&#x14c;" u2="&#x2f;" k="29" />
<hkern u1="&#x14c;" u2="&#x29;" k="27" />
<hkern u1="&#x14d;" u2="&#x2122;" k="29" />
<hkern u1="&#x14d;" u2="&#xc6;" k="14" />
<hkern u1="&#x14d;" u2="&#x7d;" k="39" />
<hkern u1="&#x14d;" u2="x" k="20" />
<hkern u1="&#x14d;" u2="v" k="14" />
<hkern u1="&#x14d;" u2="]" k="55" />
<hkern u1="&#x14d;" u2="\" k="84" />
<hkern u1="&#x14d;" u2="X" k="53" />
<hkern u1="&#x14d;" u2="V" k="59" />
<hkern u1="&#x14d;" u2="&#x3f;" k="31" />
<hkern u1="&#x14d;" u2="&#x2a;" k="16" />
<hkern u1="&#x14d;" u2="&#x29;" k="41" />
<hkern u1="&#x14e;" u2="&#xc6;" k="27" />
<hkern u1="&#x14e;" u2="&#x7d;" k="25" />
<hkern u1="&#x14e;" u2="]" k="41" />
<hkern u1="&#x14e;" u2="\" k="39" />
<hkern u1="&#x14e;" u2="X" k="35" />
<hkern u1="&#x14e;" u2="V" k="20" />
<hkern u1="&#x14e;" u2="&#x2f;" k="29" />
<hkern u1="&#x14e;" u2="&#x29;" k="27" />
<hkern u1="&#x14f;" u2="&#x2122;" k="29" />
<hkern u1="&#x14f;" u2="&#xc6;" k="14" />
<hkern u1="&#x14f;" u2="&#x7d;" k="39" />
<hkern u1="&#x14f;" u2="x" k="20" />
<hkern u1="&#x14f;" u2="v" k="14" />
<hkern u1="&#x14f;" u2="]" k="55" />
<hkern u1="&#x14f;" u2="\" k="84" />
<hkern u1="&#x14f;" u2="X" k="53" />
<hkern u1="&#x14f;" u2="V" k="59" />
<hkern u1="&#x14f;" u2="&#x3f;" k="31" />
<hkern u1="&#x14f;" u2="&#x2a;" k="16" />
<hkern u1="&#x14f;" u2="&#x29;" k="41" />
<hkern u1="&#x150;" u2="&#xc6;" k="27" />
<hkern u1="&#x150;" u2="&#x7d;" k="25" />
<hkern u1="&#x150;" u2="]" k="41" />
<hkern u1="&#x150;" u2="\" k="39" />
<hkern u1="&#x150;" u2="X" k="35" />
<hkern u1="&#x150;" u2="V" k="20" />
<hkern u1="&#x150;" u2="&#x2f;" k="29" />
<hkern u1="&#x150;" u2="&#x29;" k="27" />
<hkern u1="&#x151;" u2="&#x2122;" k="29" />
<hkern u1="&#x151;" u2="&#xc6;" k="14" />
<hkern u1="&#x151;" u2="&#x7d;" k="39" />
<hkern u1="&#x151;" u2="x" k="20" />
<hkern u1="&#x151;" u2="v" k="14" />
<hkern u1="&#x151;" u2="]" k="55" />
<hkern u1="&#x151;" u2="\" k="84" />
<hkern u1="&#x151;" u2="X" k="53" />
<hkern u1="&#x151;" u2="V" k="59" />
<hkern u1="&#x151;" u2="&#x3f;" k="31" />
<hkern u1="&#x151;" u2="&#x2a;" k="16" />
<hkern u1="&#x151;" u2="&#x29;" k="41" />
<hkern u1="&#x152;" u2="&#x135;" k="-43" />
<hkern u1="&#x152;" u2="&#x12d;" k="-12" />
<hkern u1="&#x152;" u2="&#x129;" k="-45" />
<hkern u1="&#x152;" u2="&#xef;" k="-66" />
<hkern u1="&#x152;" u2="&#xee;" k="-59" />
<hkern u1="&#x152;" u2="&#xec;" k="-86" />
<hkern u1="&#x153;" u2="&#x2122;" k="25" />
<hkern u1="&#x153;" u2="&#xc6;" k="10" />
<hkern u1="&#x153;" u2="&#x7d;" k="20" />
<hkern u1="&#x153;" u2="v" k="12" />
<hkern u1="&#x153;" u2="\" k="78" />
<hkern u1="&#x153;" u2="X" k="10" />
<hkern u1="&#x153;" u2="V" k="51" />
<hkern u1="&#x153;" u2="&#x3f;" k="23" />
<hkern u1="&#x153;" u2="&#x29;" k="25" />
<hkern u1="&#x154;" u2="&#xc6;" k="20" />
<hkern u1="&#x154;" u2="\" k="31" />
<hkern u1="&#x154;" u2="X" k="12" />
<hkern u1="&#x154;" u2="V" k="18" />
<hkern u1="&#x155;" u2="&#xc6;" k="63" />
<hkern u1="&#x155;" u2="&#x7d;" k="25" />
<hkern u1="&#x155;" u2="]" k="39" />
<hkern u1="&#x155;" u2="\" k="23" />
<hkern u1="&#x155;" u2="X" k="59" />
<hkern u1="&#x155;" u2="&#x2f;" k="68" />
<hkern u1="&#x155;" u2="&#x29;" k="20" />
<hkern u1="&#x156;" u2="&#xc6;" k="20" />
<hkern u1="&#x156;" u2="\" k="31" />
<hkern u1="&#x156;" u2="X" k="12" />
<hkern u1="&#x156;" u2="V" k="18" />
<hkern u1="&#x157;" u2="&#xc6;" k="63" />
<hkern u1="&#x157;" u2="&#x7d;" k="25" />
<hkern u1="&#x157;" u2="]" k="39" />
<hkern u1="&#x157;" u2="\" k="23" />
<hkern u1="&#x157;" u2="X" k="59" />
<hkern u1="&#x157;" u2="&#x2f;" k="68" />
<hkern u1="&#x157;" u2="&#x29;" k="20" />
<hkern u1="&#x158;" u2="&#xc6;" k="20" />
<hkern u1="&#x158;" u2="\" k="31" />
<hkern u1="&#x158;" u2="X" k="12" />
<hkern u1="&#x158;" u2="V" k="18" />
<hkern u1="&#x159;" u2="&#xc6;" k="63" />
<hkern u1="&#x159;" u2="&#x7d;" k="25" />
<hkern u1="&#x159;" u2="]" k="39" />
<hkern u1="&#x159;" u2="\" k="23" />
<hkern u1="&#x159;" u2="X" k="59" />
<hkern u1="&#x159;" u2="&#x2f;" k="68" />
<hkern u1="&#x159;" u2="&#x29;" k="20" />
<hkern u1="&#x15a;" u2="&#x129;" k="-20" />
<hkern u1="&#x15a;" u2="&#xef;" k="-41" />
<hkern u1="&#x15a;" u2="&#xee;" k="-16" />
<hkern u1="&#x15a;" u2="&#xec;" k="-57" />
<hkern u1="&#x15a;" u2="&#xc6;" k="20" />
<hkern u1="&#x15a;" u2="x" k="16" />
<hkern u1="&#x15a;" u2="v" k="12" />
<hkern u1="&#x15a;" u2="f" k="18" />
<hkern u1="&#x15a;" u2="X" k="10" />
<hkern u1="&#x15a;" u2="V" k="16" />
<hkern u1="&#x15b;" u2="&#x2122;" k="23" />
<hkern u1="&#x15b;" u2="&#xc6;" k="10" />
<hkern u1="&#x15b;" u2="&#x7d;" k="25" />
<hkern u1="&#x15b;" u2="v" k="10" />
<hkern u1="&#x15b;" u2="]" k="37" />
<hkern u1="&#x15b;" u2="\" k="59" />
<hkern u1="&#x15b;" u2="X" k="16" />
<hkern u1="&#x15b;" u2="V" k="37" />
<hkern u1="&#x15b;" u2="&#x29;" k="27" />
<hkern u1="&#x15c;" u2="&#x129;" k="-20" />
<hkern u1="&#x15c;" u2="&#xef;" k="-41" />
<hkern u1="&#x15c;" u2="&#xee;" k="-16" />
<hkern u1="&#x15c;" u2="&#xec;" k="-57" />
<hkern u1="&#x15c;" u2="&#xc6;" k="20" />
<hkern u1="&#x15c;" u2="x" k="16" />
<hkern u1="&#x15c;" u2="v" k="12" />
<hkern u1="&#x15c;" u2="f" k="18" />
<hkern u1="&#x15c;" u2="X" k="10" />
<hkern u1="&#x15c;" u2="V" k="16" />
<hkern u1="&#x15d;" u2="&#x2122;" k="23" />
<hkern u1="&#x15d;" u2="&#xc6;" k="10" />
<hkern u1="&#x15d;" u2="&#x7d;" k="25" />
<hkern u1="&#x15d;" u2="v" k="10" />
<hkern u1="&#x15d;" u2="]" k="37" />
<hkern u1="&#x15d;" u2="\" k="59" />
<hkern u1="&#x15d;" u2="X" k="16" />
<hkern u1="&#x15d;" u2="V" k="37" />
<hkern u1="&#x15d;" u2="&#x29;" k="27" />
<hkern u1="&#x15e;" u2="&#x129;" k="-20" />
<hkern u1="&#x15e;" u2="&#xef;" k="-41" />
<hkern u1="&#x15e;" u2="&#xee;" k="-16" />
<hkern u1="&#x15e;" u2="&#xec;" k="-57" />
<hkern u1="&#x15e;" u2="&#xc6;" k="20" />
<hkern u1="&#x15e;" u2="x" k="16" />
<hkern u1="&#x15e;" u2="v" k="12" />
<hkern u1="&#x15e;" u2="f" k="18" />
<hkern u1="&#x15e;" u2="X" k="10" />
<hkern u1="&#x15e;" u2="V" k="16" />
<hkern u1="&#x15f;" u2="&#x2122;" k="23" />
<hkern u1="&#x15f;" u2="&#xc6;" k="10" />
<hkern u1="&#x15f;" u2="&#x7d;" k="25" />
<hkern u1="&#x15f;" u2="v" k="10" />
<hkern u1="&#x15f;" u2="]" k="37" />
<hkern u1="&#x15f;" u2="\" k="59" />
<hkern u1="&#x15f;" u2="X" k="16" />
<hkern u1="&#x15f;" u2="V" k="37" />
<hkern u1="&#x15f;" u2="&#x29;" k="27" />
<hkern u1="&#x160;" u2="&#x129;" k="-20" />
<hkern u1="&#x160;" u2="&#xef;" k="-41" />
<hkern u1="&#x160;" u2="&#xee;" k="-16" />
<hkern u1="&#x160;" u2="&#xec;" k="-57" />
<hkern u1="&#x160;" u2="&#xc6;" k="20" />
<hkern u1="&#x160;" u2="x" k="16" />
<hkern u1="&#x160;" u2="v" k="12" />
<hkern u1="&#x160;" u2="f" k="18" />
<hkern u1="&#x160;" u2="X" k="10" />
<hkern u1="&#x160;" u2="V" k="16" />
<hkern u1="&#x161;" u2="&#x2122;" k="23" />
<hkern u1="&#x161;" u2="&#xc6;" k="10" />
<hkern u1="&#x161;" u2="&#x7d;" k="25" />
<hkern u1="&#x161;" u2="v" k="10" />
<hkern u1="&#x161;" u2="]" k="37" />
<hkern u1="&#x161;" u2="\" k="59" />
<hkern u1="&#x161;" u2="X" k="16" />
<hkern u1="&#x161;" u2="V" k="37" />
<hkern u1="&#x161;" u2="&#x29;" k="27" />
<hkern u1="&#x164;" u2="&#x16d;" k="111" />
<hkern u1="&#x164;" u2="&#x169;" k="111" />
<hkern u1="&#x164;" u2="&#x15d;" k="117" />
<hkern u1="&#x164;" u2="&#x159;" k="76" />
<hkern u1="&#x164;" u2="&#x155;" k="78" />
<hkern u1="&#x164;" u2="&#x151;" k="86" />
<hkern u1="&#x164;" u2="&#x135;" k="-98" />
<hkern u1="&#x164;" u2="&#x131;" k="106" />
<hkern u1="&#x164;" u2="&#x12d;" k="-66" />
<hkern u1="&#x164;" u2="&#x12b;" k="-49" />
<hkern u1="&#x164;" u2="&#x129;" k="-98" />
<hkern u1="&#x164;" u2="&#x11f;" k="154" />
<hkern u1="&#x164;" u2="&#x109;" k="88" />
<hkern u1="&#x164;" u2="&#xef;" k="-119" />
<hkern u1="&#x164;" u2="&#xee;" k="-113" />
<hkern u1="&#x164;" u2="&#xec;" k="-139" />
<hkern u1="&#x164;" u2="&#xe4;" k="111" />
<hkern u1="&#x164;" u2="&#xe3;" k="90" />
<hkern u1="&#x164;" u2="&#xc6;" k="100" />
<hkern u1="&#x164;" u2="x" k="100" />
<hkern u1="&#x164;" u2="v" k="100" />
<hkern u1="&#x164;" u2="f" k="31" />
<hkern u1="&#x164;" u2="&#x40;" k="31" />
<hkern u1="&#x164;" u2="&#x2f;" k="100" />
<hkern u1="&#x164;" u2="&#x26;" k="20" />
<hkern u1="&#x165;" u2="&#x2039;" k="92" />
<hkern u1="&#x165;" u2="&#x2026;" k="106" />
<hkern u1="&#x165;" u2="&#x201e;" k="106" />
<hkern u1="&#x165;" u2="&#x201c;" k="-16" />
<hkern u1="&#x165;" u2="&#x201a;" k="106" />
<hkern u1="&#x165;" u2="&#x2018;" k="-16" />
<hkern u1="&#x165;" u2="&#x2014;" k="119" />
<hkern u1="&#x165;" u2="&#x2013;" k="119" />
<hkern u1="&#x165;" u2="&#x21b;" k="-39" />
<hkern u1="&#x165;" u2="&#x1ff;" k="20" />
<hkern u1="&#x165;" u2="&#x177;" k="-53" />
<hkern u1="&#x165;" u2="&#x175;" k="-37" />
<hkern u1="&#x165;" u2="&#x167;" k="-39" />
<hkern u1="&#x165;" u2="&#x165;" k="-39" />
<hkern u1="&#x165;" u2="&#x153;" k="20" />
<hkern u1="&#x165;" u2="&#x151;" k="20" />
<hkern u1="&#x165;" u2="&#x14f;" k="20" />
<hkern u1="&#x165;" u2="&#x14d;" k="20" />
<hkern u1="&#x165;" u2="&#x142;" k="-16" />
<hkern u1="&#x165;" u2="&#x13e;" k="-16" />
<hkern u1="&#x165;" u2="&#x13c;" k="-16" />
<hkern u1="&#x165;" u2="&#x13a;" k="-16" />
<hkern u1="&#x165;" u2="&#x137;" k="-29" />
<hkern u1="&#x165;" u2="&#x135;" k="-29" />
<hkern u1="&#x165;" u2="&#x131;" k="-29" />
<hkern u1="&#x165;" u2="&#x12f;" k="-29" />
<hkern u1="&#x165;" u2="&#x12d;" k="-29" />
<hkern u1="&#x165;" u2="&#x12b;" k="-29" />
<hkern u1="&#x165;" u2="&#x129;" k="-29" />
<hkern u1="&#x165;" u2="&#x127;" k="-29" />
<hkern u1="&#x165;" u2="&#x125;" k="-29" />
<hkern u1="&#x165;" u2="&#x123;" k="10" />
<hkern u1="&#x165;" u2="&#x121;" k="10" />
<hkern u1="&#x165;" u2="&#x11f;" k="10" />
<hkern u1="&#x165;" u2="&#x11d;" k="10" />
<hkern u1="&#x165;" u2="&#x11b;" k="20" />
<hkern u1="&#x165;" u2="&#x119;" k="20" />
<hkern u1="&#x165;" u2="&#x117;" k="20" />
<hkern u1="&#x165;" u2="&#x115;" k="20" />
<hkern u1="&#x165;" u2="&#x113;" k="20" />
<hkern u1="&#x165;" u2="&#x111;" k="16" />
<hkern u1="&#x165;" u2="&#x10f;" k="16" />
<hkern u1="&#x165;" u2="&#x10d;" k="20" />
<hkern u1="&#x165;" u2="&#x10b;" k="20" />
<hkern u1="&#x165;" u2="&#x109;" k="20" />
<hkern u1="&#x165;" u2="&#x107;" k="20" />
<hkern u1="&#x165;" u2="&#xff;" k="-53" />
<hkern u1="&#x165;" u2="&#xfd;" k="-53" />
<hkern u1="&#x165;" u2="&#xf8;" k="20" />
<hkern u1="&#x165;" u2="&#xf6;" k="20" />
<hkern u1="&#x165;" u2="&#xf5;" k="20" />
<hkern u1="&#x165;" u2="&#xf4;" k="20" />
<hkern u1="&#x165;" u2="&#xf3;" k="20" />
<hkern u1="&#x165;" u2="&#xf2;" k="20" />
<hkern u1="&#x165;" u2="&#xef;" k="-29" />
<hkern u1="&#x165;" u2="&#xee;" k="-29" />
<hkern u1="&#x165;" u2="&#xed;" k="-29" />
<hkern u1="&#x165;" u2="&#xec;" k="-29" />
<hkern u1="&#x165;" u2="&#xeb;" k="20" />
<hkern u1="&#x165;" u2="&#xea;" k="20" />
<hkern u1="&#x165;" u2="&#xe9;" k="20" />
<hkern u1="&#x165;" u2="&#xe8;" k="20" />
<hkern u1="&#x165;" u2="&#xe7;" k="20" />
<hkern u1="&#x165;" u2="&#xe4;" k="-23" />
<hkern u1="&#x165;" u2="&#xdf;" k="-29" />
<hkern u1="&#x165;" u2="&#xab;" k="92" />
<hkern u1="&#x165;" u2="&#x7d;" k="-16" />
<hkern u1="&#x165;" u2="y" k="-53" />
<hkern u1="&#x165;" u2="x" k="-55" />
<hkern u1="&#x165;" u2="w" k="-37" />
<hkern u1="&#x165;" u2="v" k="-55" />
<hkern u1="&#x165;" u2="t" k="-39" />
<hkern u1="&#x165;" u2="q" k="16" />
<hkern u1="&#x165;" u2="o" k="20" />
<hkern u1="&#x165;" u2="l" k="-16" />
<hkern u1="&#x165;" u2="k" k="-29" />
<hkern u1="&#x165;" u2="j" k="-29" />
<hkern u1="&#x165;" u2="i" k="-29" />
<hkern u1="&#x165;" u2="h" k="-29" />
<hkern u1="&#x165;" u2="g" k="10" />
<hkern u1="&#x165;" u2="f" k="-18" />
<hkern u1="&#x165;" u2="e" k="20" />
<hkern u1="&#x165;" u2="d" k="16" />
<hkern u1="&#x165;" u2="c" k="20" />
<hkern u1="&#x165;" u2="b" k="-29" />
<hkern u1="&#x165;" u2="]" k="-18" />
<hkern u1="&#x165;" u2="\" k="-27" />
<hkern u1="&#x165;" u2="&#x3f;" k="-29" />
<hkern u1="&#x165;" u2="&#x2f;" k="74" />
<hkern u1="&#x165;" u2="&#x2e;" k="106" />
<hkern u1="&#x165;" u2="&#x2d;" k="119" />
<hkern u1="&#x165;" u2="&#x2c;" k="106" />
<hkern u1="&#x165;" u2="&#x2a;" k="-59" />
<hkern u1="&#x166;" u2="&#x16d;" k="111" />
<hkern u1="&#x166;" u2="&#x169;" k="111" />
<hkern u1="&#x166;" u2="&#x15d;" k="117" />
<hkern u1="&#x166;" u2="&#x159;" k="76" />
<hkern u1="&#x166;" u2="&#x155;" k="78" />
<hkern u1="&#x166;" u2="&#x151;" k="86" />
<hkern u1="&#x166;" u2="&#x135;" k="-98" />
<hkern u1="&#x166;" u2="&#x131;" k="106" />
<hkern u1="&#x166;" u2="&#x12d;" k="-66" />
<hkern u1="&#x166;" u2="&#x12b;" k="-49" />
<hkern u1="&#x166;" u2="&#x129;" k="-98" />
<hkern u1="&#x166;" u2="&#x11f;" k="154" />
<hkern u1="&#x166;" u2="&#x109;" k="88" />
<hkern u1="&#x166;" u2="&#xef;" k="-119" />
<hkern u1="&#x166;" u2="&#xee;" k="-113" />
<hkern u1="&#x166;" u2="&#xec;" k="-139" />
<hkern u1="&#x166;" u2="&#xe4;" k="111" />
<hkern u1="&#x166;" u2="&#xe3;" k="90" />
<hkern u1="&#x166;" u2="&#xc6;" k="100" />
<hkern u1="&#x166;" u2="x" k="100" />
<hkern u1="&#x166;" u2="v" k="100" />
<hkern u1="&#x166;" u2="f" k="31" />
<hkern u1="&#x166;" u2="&#x40;" k="31" />
<hkern u1="&#x166;" u2="&#x2f;" k="100" />
<hkern u1="&#x166;" u2="&#x26;" k="20" />
<hkern u1="&#x167;" u2="\" k="35" />
<hkern u1="&#x167;" u2="V" k="12" />
<hkern u1="&#x168;" u2="&#xec;" k="-29" />
<hkern u1="&#x168;" u2="&#xc6;" k="14" />
<hkern u1="&#x168;" u2="f" k="10" />
<hkern u1="&#x168;" u2="&#x2f;" k="33" />
<hkern u1="&#x169;" u2="&#x2122;" k="18" />
<hkern u1="&#x169;" u2="\" k="57" />
<hkern u1="&#x169;" u2="X" k="10" />
<hkern u1="&#x169;" u2="V" k="43" />
<hkern u1="&#x169;" u2="&#x29;" k="23" />
<hkern u1="&#x16a;" u2="&#xec;" k="-29" />
<hkern u1="&#x16a;" u2="&#xc6;" k="14" />
<hkern u1="&#x16a;" u2="f" k="10" />
<hkern u1="&#x16a;" u2="&#x2f;" k="33" />
<hkern u1="&#x16b;" u2="&#x2122;" k="18" />
<hkern u1="&#x16b;" u2="\" k="57" />
<hkern u1="&#x16b;" u2="X" k="10" />
<hkern u1="&#x16b;" u2="V" k="43" />
<hkern u1="&#x16b;" u2="&#x29;" k="23" />
<hkern u1="&#x16c;" u2="&#xec;" k="-29" />
<hkern u1="&#x16c;" u2="&#xc6;" k="14" />
<hkern u1="&#x16c;" u2="f" k="10" />
<hkern u1="&#x16c;" u2="&#x2f;" k="33" />
<hkern u1="&#x16d;" u2="&#x2122;" k="18" />
<hkern u1="&#x16d;" u2="\" k="57" />
<hkern u1="&#x16d;" u2="X" k="10" />
<hkern u1="&#x16d;" u2="V" k="43" />
<hkern u1="&#x16d;" u2="&#x29;" k="23" />
<hkern u1="&#x16e;" u2="&#xec;" k="-29" />
<hkern u1="&#x16e;" u2="&#xc6;" k="14" />
<hkern u1="&#x16e;" u2="f" k="10" />
<hkern u1="&#x16e;" u2="&#x2f;" k="33" />
<hkern u1="&#x16f;" u2="&#x2122;" k="18" />
<hkern u1="&#x16f;" u2="\" k="57" />
<hkern u1="&#x16f;" u2="X" k="10" />
<hkern u1="&#x16f;" u2="V" k="43" />
<hkern u1="&#x16f;" u2="&#x29;" k="23" />
<hkern u1="&#x170;" u2="&#xec;" k="-29" />
<hkern u1="&#x170;" u2="&#xc6;" k="14" />
<hkern u1="&#x170;" u2="f" k="10" />
<hkern u1="&#x170;" u2="&#x2f;" k="33" />
<hkern u1="&#x171;" u2="&#x2122;" k="18" />
<hkern u1="&#x171;" u2="\" k="57" />
<hkern u1="&#x171;" u2="X" k="10" />
<hkern u1="&#x171;" u2="V" k="43" />
<hkern u1="&#x171;" u2="&#x29;" k="23" />
<hkern u1="&#x172;" u2="&#xec;" k="-29" />
<hkern u1="&#x172;" u2="&#xc6;" k="14" />
<hkern u1="&#x172;" u2="f" k="10" />
<hkern u1="&#x172;" u2="&#x2f;" k="33" />
<hkern u1="&#x173;" u2="&#x2122;" k="18" />
<hkern u1="&#x173;" u2="\" k="57" />
<hkern u1="&#x173;" u2="X" k="10" />
<hkern u1="&#x173;" u2="V" k="43" />
<hkern u1="&#x173;" u2="&#x29;" k="23" />
<hkern u1="&#x174;" u2="&#x135;" k="-49" />
<hkern u1="&#x174;" u2="&#x131;" k="27" />
<hkern u1="&#x174;" u2="&#x12d;" k="-53" />
<hkern u1="&#x174;" u2="&#x12b;" k="-37" />
<hkern u1="&#x174;" u2="&#x129;" k="-80" />
<hkern u1="&#x174;" u2="&#xef;" k="-109" />
<hkern u1="&#x174;" u2="&#xee;" k="-61" />
<hkern u1="&#x174;" u2="&#xec;" k="-111" />
<hkern u1="&#x174;" u2="&#xc6;" k="47" />
<hkern u1="&#x174;" u2="&#x2f;" k="59" />
<hkern u1="&#x175;" u2="&#xc6;" k="29" />
<hkern u1="&#x175;" u2="&#x7d;" k="31" />
<hkern u1="&#x175;" u2="]" k="43" />
<hkern u1="&#x175;" u2="\" k="37" />
<hkern u1="&#x175;" u2="X" k="47" />
<hkern u1="&#x175;" u2="V" k="18" />
<hkern u1="&#x175;" u2="&#x2f;" k="29" />
<hkern u1="&#x175;" u2="&#x29;" k="29" />
<hkern u1="&#x176;" u2="&#x159;" k="53" />
<hkern u1="&#x176;" u2="&#x155;" k="66" />
<hkern u1="&#x176;" u2="&#x151;" k="98" />
<hkern u1="&#x176;" u2="&#x142;" k="12" />
<hkern u1="&#x176;" u2="&#x135;" k="-31" />
<hkern u1="&#x176;" u2="&#x131;" k="121" />
<hkern u1="&#x176;" u2="&#x12d;" k="-90" />
<hkern u1="&#x176;" u2="&#x12b;" k="-74" />
<hkern u1="&#x176;" u2="&#x129;" k="-100" />
<hkern u1="&#x176;" u2="&#x103;" k="115" />
<hkern u1="&#x176;" u2="&#xff;" k="49" />
<hkern u1="&#x176;" u2="&#xef;" k="-143" />
<hkern u1="&#x176;" u2="&#xee;" k="-43" />
<hkern u1="&#x176;" u2="&#xec;" k="-127" />
<hkern u1="&#x176;" u2="&#xeb;" k="125" />
<hkern u1="&#x176;" u2="&#xe4;" k="88" />
<hkern u1="&#x176;" u2="&#xe3;" k="70" />
<hkern u1="&#x176;" u2="&#xdf;" k="29" />
<hkern u1="&#x176;" u2="&#xc6;" k="121" />
<hkern u1="&#x176;" u2="&#xae;" k="39" />
<hkern u1="&#x176;" u2="x" k="70" />
<hkern u1="&#x176;" u2="v" k="68" />
<hkern u1="&#x176;" u2="f" k="47" />
<hkern u1="&#x176;" u2="&#x40;" k="72" />
<hkern u1="&#x176;" u2="&#x2f;" k="133" />
<hkern u1="&#x176;" u2="&#x2a;" k="-10" />
<hkern u1="&#x176;" u2="&#x26;" k="57" />
<hkern u1="&#x177;" u2="&#xc6;" k="37" />
<hkern u1="&#x177;" u2="&#x7d;" k="23" />
<hkern u1="&#x177;" u2="]" k="39" />
<hkern u1="&#x177;" u2="\" k="37" />
<hkern u1="&#x177;" u2="X" k="45" />
<hkern u1="&#x177;" u2="V" k="12" />
<hkern u1="&#x177;" u2="&#x2f;" k="39" />
<hkern u1="&#x178;" u2="&#x159;" k="53" />
<hkern u1="&#x178;" u2="&#x155;" k="66" />
<hkern u1="&#x178;" u2="&#x151;" k="98" />
<hkern u1="&#x178;" u2="&#x142;" k="12" />
<hkern u1="&#x178;" u2="&#x135;" k="-31" />
<hkern u1="&#x178;" u2="&#x131;" k="121" />
<hkern u1="&#x178;" u2="&#x12d;" k="-90" />
<hkern u1="&#x178;" u2="&#x12b;" k="-74" />
<hkern u1="&#x178;" u2="&#x129;" k="-100" />
<hkern u1="&#x178;" u2="&#x103;" k="115" />
<hkern u1="&#x178;" u2="&#xff;" k="49" />
<hkern u1="&#x178;" u2="&#xef;" k="-143" />
<hkern u1="&#x178;" u2="&#xee;" k="-43" />
<hkern u1="&#x178;" u2="&#xec;" k="-127" />
<hkern u1="&#x178;" u2="&#xeb;" k="125" />
<hkern u1="&#x178;" u2="&#xe4;" k="88" />
<hkern u1="&#x178;" u2="&#xe3;" k="70" />
<hkern u1="&#x178;" u2="&#xdf;" k="29" />
<hkern u1="&#x178;" u2="&#xc6;" k="121" />
<hkern u1="&#x178;" u2="&#xae;" k="39" />
<hkern u1="&#x178;" u2="x" k="70" />
<hkern u1="&#x178;" u2="v" k="68" />
<hkern u1="&#x178;" u2="f" k="47" />
<hkern u1="&#x178;" u2="&#x40;" k="72" />
<hkern u1="&#x178;" u2="&#x2f;" k="133" />
<hkern u1="&#x178;" u2="&#x2a;" k="-10" />
<hkern u1="&#x178;" u2="&#x26;" k="57" />
<hkern u1="&#x179;" u2="&#x135;" k="-41" />
<hkern u1="&#x179;" u2="&#x129;" k="-41" />
<hkern u1="&#x179;" u2="&#xef;" k="-61" />
<hkern u1="&#x179;" u2="&#xee;" k="-57" />
<hkern u1="&#x179;" u2="&#xec;" k="-84" />
<hkern u1="&#x179;" u2="v" k="14" />
<hkern u1="&#x179;" u2="f" k="14" />
<hkern u1="&#x17a;" u2="\" k="51" />
<hkern u1="&#x17a;" u2="V" k="27" />
<hkern u1="&#x17b;" u2="&#x135;" k="-41" />
<hkern u1="&#x17b;" u2="&#x129;" k="-41" />
<hkern u1="&#x17b;" u2="&#xef;" k="-61" />
<hkern u1="&#x17b;" u2="&#xee;" k="-57" />
<hkern u1="&#x17b;" u2="&#xec;" k="-84" />
<hkern u1="&#x17b;" u2="v" k="14" />
<hkern u1="&#x17b;" u2="f" k="14" />
<hkern u1="&#x17c;" u2="\" k="51" />
<hkern u1="&#x17c;" u2="V" k="27" />
<hkern u1="&#x17d;" u2="&#x135;" k="-41" />
<hkern u1="&#x17d;" u2="&#x129;" k="-41" />
<hkern u1="&#x17d;" u2="&#xef;" k="-61" />
<hkern u1="&#x17d;" u2="&#xee;" k="-57" />
<hkern u1="&#x17d;" u2="&#xec;" k="-84" />
<hkern u1="&#x17d;" u2="v" k="14" />
<hkern u1="&#x17d;" u2="f" k="14" />
<hkern u1="&#x17e;" u2="\" k="51" />
<hkern u1="&#x17e;" u2="V" k="27" />
<hkern u1="&#x1fa;" u2="&#x2122;" k="61" />
<hkern u1="&#x1fa;" u2="&#xae;" k="37" />
<hkern u1="&#x1fa;" u2="v" k="29" />
<hkern u1="&#x1fa;" u2="f" k="16" />
<hkern u1="&#x1fa;" u2="\" k="86" />
<hkern u1="&#x1fa;" u2="V" k="55" />
<hkern u1="&#x1fa;" u2="&#x3f;" k="29" />
<hkern u1="&#x1fa;" u2="&#x2a;" k="55" />
<hkern u1="&#x1fb;" u2="&#x2122;" k="31" />
<hkern u1="&#x1fb;" u2="v" k="12" />
<hkern u1="&#x1fb;" u2="\" k="88" />
<hkern u1="&#x1fb;" u2="V" k="57" />
<hkern u1="&#x1fb;" u2="&#x3f;" k="27" />
<hkern u1="&#x1fb;" u2="&#x2a;" k="16" />
<hkern u1="&#x1fc;" u2="&#x135;" k="-43" />
<hkern u1="&#x1fc;" u2="&#x12d;" k="-12" />
<hkern u1="&#x1fc;" u2="&#x129;" k="-45" />
<hkern u1="&#x1fc;" u2="&#xef;" k="-66" />
<hkern u1="&#x1fc;" u2="&#xee;" k="-59" />
<hkern u1="&#x1fc;" u2="&#xec;" k="-86" />
<hkern u1="&#x1fd;" u2="&#x2122;" k="25" />
<hkern u1="&#x1fd;" u2="&#xc6;" k="10" />
<hkern u1="&#x1fd;" u2="&#x7d;" k="20" />
<hkern u1="&#x1fd;" u2="v" k="12" />
<hkern u1="&#x1fd;" u2="\" k="78" />
<hkern u1="&#x1fd;" u2="X" k="10" />
<hkern u1="&#x1fd;" u2="V" k="51" />
<hkern u1="&#x1fd;" u2="&#x3f;" k="23" />
<hkern u1="&#x1fd;" u2="&#x29;" k="25" />
<hkern u1="&#x1fe;" u2="&#xc6;" k="27" />
<hkern u1="&#x1fe;" u2="&#x7d;" k="25" />
<hkern u1="&#x1fe;" u2="]" k="41" />
<hkern u1="&#x1fe;" u2="\" k="39" />
<hkern u1="&#x1fe;" u2="X" k="35" />
<hkern u1="&#x1fe;" u2="V" k="20" />
<hkern u1="&#x1fe;" u2="&#x2f;" k="29" />
<hkern u1="&#x1fe;" u2="&#x29;" k="27" />
<hkern u1="&#x1ff;" u2="&#x2122;" k="29" />
<hkern u1="&#x1ff;" u2="&#xc6;" k="14" />
<hkern u1="&#x1ff;" u2="&#x7d;" k="39" />
<hkern u1="&#x1ff;" u2="x" k="20" />
<hkern u1="&#x1ff;" u2="v" k="14" />
<hkern u1="&#x1ff;" u2="]" k="55" />
<hkern u1="&#x1ff;" u2="\" k="84" />
<hkern u1="&#x1ff;" u2="X" k="53" />
<hkern u1="&#x1ff;" u2="V" k="59" />
<hkern u1="&#x1ff;" u2="&#x3f;" k="31" />
<hkern u1="&#x1ff;" u2="&#x2a;" k="16" />
<hkern u1="&#x1ff;" u2="&#x29;" k="41" />
<hkern u1="&#x218;" u2="&#x129;" k="-20" />
<hkern u1="&#x218;" u2="&#xef;" k="-41" />
<hkern u1="&#x218;" u2="&#xee;" k="-16" />
<hkern u1="&#x218;" u2="&#xec;" k="-57" />
<hkern u1="&#x218;" u2="&#xc6;" k="20" />
<hkern u1="&#x218;" u2="x" k="16" />
<hkern u1="&#x218;" u2="v" k="12" />
<hkern u1="&#x218;" u2="f" k="18" />
<hkern u1="&#x218;" u2="X" k="10" />
<hkern u1="&#x218;" u2="V" k="16" />
<hkern u1="&#x219;" u2="&#x2122;" k="23" />
<hkern u1="&#x219;" u2="&#xc6;" k="10" />
<hkern u1="&#x219;" u2="&#x7d;" k="25" />
<hkern u1="&#x219;" u2="v" k="10" />
<hkern u1="&#x219;" u2="]" k="37" />
<hkern u1="&#x219;" u2="\" k="59" />
<hkern u1="&#x219;" u2="X" k="16" />
<hkern u1="&#x219;" u2="V" k="37" />
<hkern u1="&#x219;" u2="&#x29;" k="27" />
<hkern u1="&#x21a;" u2="&#x16d;" k="111" />
<hkern u1="&#x21a;" u2="&#x169;" k="111" />
<hkern u1="&#x21a;" u2="&#x15d;" k="117" />
<hkern u1="&#x21a;" u2="&#x159;" k="76" />
<hkern u1="&#x21a;" u2="&#x155;" k="78" />
<hkern u1="&#x21a;" u2="&#x151;" k="86" />
<hkern u1="&#x21a;" u2="&#x135;" k="-98" />
<hkern u1="&#x21a;" u2="&#x131;" k="106" />
<hkern u1="&#x21a;" u2="&#x12d;" k="-66" />
<hkern u1="&#x21a;" u2="&#x12b;" k="-49" />
<hkern u1="&#x21a;" u2="&#x129;" k="-98" />
<hkern u1="&#x21a;" u2="&#x11f;" k="154" />
<hkern u1="&#x21a;" u2="&#x109;" k="88" />
<hkern u1="&#x21a;" u2="&#xef;" k="-119" />
<hkern u1="&#x21a;" u2="&#xee;" k="-113" />
<hkern u1="&#x21a;" u2="&#xec;" k="-139" />
<hkern u1="&#x21a;" u2="&#xe4;" k="111" />
<hkern u1="&#x21a;" u2="&#xe3;" k="90" />
<hkern u1="&#x21a;" u2="&#xc6;" k="100" />
<hkern u1="&#x21a;" u2="x" k="100" />
<hkern u1="&#x21a;" u2="v" k="100" />
<hkern u1="&#x21a;" u2="f" k="31" />
<hkern u1="&#x21a;" u2="&#x40;" k="31" />
<hkern u1="&#x21a;" u2="&#x2f;" k="100" />
<hkern u1="&#x21a;" u2="&#x26;" k="20" />
<hkern u1="&#x21b;" u2="\" k="35" />
<hkern u1="&#x21b;" u2="V" k="12" />
<hkern u1="&#x2013;" u2="&#xc6;" k="18" />
<hkern u1="&#x2013;" u2="x" k="47" />
<hkern u1="&#x2013;" u2="v" k="18" />
<hkern u1="&#x2013;" u2="f" k="23" />
<hkern u1="&#x2013;" u2="X" k="66" />
<hkern u1="&#x2013;" u2="V" k="49" />
<hkern u1="&#x2014;" u2="&#xc6;" k="18" />
<hkern u1="&#x2014;" u2="x" k="47" />
<hkern u1="&#x2014;" u2="v" k="18" />
<hkern u1="&#x2014;" u2="f" k="23" />
<hkern u1="&#x2014;" u2="X" k="66" />
<hkern u1="&#x2014;" u2="V" k="49" />
<hkern u1="&#x2018;" u2="&#x135;" k="-16" />
<hkern u1="&#x2018;" u2="&#x12d;" k="-23" />
<hkern u1="&#x2018;" u2="&#x129;" k="-49" />
<hkern u1="&#x2018;" u2="&#xef;" k="-74" />
<hkern u1="&#x2018;" u2="&#xee;" k="-31" />
<hkern u1="&#x2018;" u2="&#xec;" k="-80" />
<hkern u1="&#x2018;" u2="&#xc6;" k="86" />
<hkern u1="&#x2019;" u2="&#x12d;" k="-41" />
<hkern u1="&#x2019;" u2="&#x12b;" k="-25" />
<hkern u1="&#x2019;" u2="&#x129;" k="-61" />
<hkern u1="&#x2019;" u2="&#xef;" k="-94" />
<hkern u1="&#x2019;" u2="&#xee;" k="-18" />
<hkern u1="&#x2019;" u2="&#xec;" k="-88" />
<hkern u1="&#x2019;" u2="&#xc6;" k="94" />
<hkern u1="&#x2019;" u2="&#x40;" k="49" />
<hkern u1="&#x2019;" u2="&#x2f;" k="150" />
<hkern u1="&#x2019;" u2="&#x26;" k="47" />
<hkern u1="&#x201a;" u2="v" k="47" />
<hkern u1="&#x201a;" u2="f" k="23" />
<hkern u1="&#x201a;" u2="V" k="82" />
<hkern u1="&#x201c;" u2="&#x135;" k="-16" />
<hkern u1="&#x201c;" u2="&#x12d;" k="-23" />
<hkern u1="&#x201c;" u2="&#x129;" k="-49" />
<hkern u1="&#x201c;" u2="&#xef;" k="-74" />
<hkern u1="&#x201c;" u2="&#xee;" k="-31" />
<hkern u1="&#x201c;" u2="&#xec;" k="-80" />
<hkern u1="&#x201c;" u2="&#xc6;" k="86" />
<hkern u1="&#x201d;" u2="&#x135;" k="-14" />
<hkern u1="&#x201d;" u2="&#x12d;" k="-41" />
<hkern u1="&#x201d;" u2="&#x12b;" k="-25" />
<hkern u1="&#x201d;" u2="&#x129;" k="-61" />
<hkern u1="&#x201d;" u2="&#xef;" k="-94" />
<hkern u1="&#x201d;" u2="&#xee;" k="-18" />
<hkern u1="&#x201d;" u2="&#xec;" k="-88" />
<hkern u1="&#x201d;" u2="&#xc6;" k="94" />
<hkern u1="&#x201d;" u2="&#x40;" k="49" />
<hkern u1="&#x201d;" u2="&#x2f;" k="150" />
<hkern u1="&#x201d;" u2="&#x26;" k="47" />
<hkern u1="&#x201e;" u2="v" k="47" />
<hkern u1="&#x201e;" u2="f" k="23" />
<hkern u1="&#x201e;" u2="V" k="82" />
<hkern u1="&#x2039;" u2="V" k="23" />
<hkern u1="&#x203a;" u2="&#x141;" k="-14" />
<hkern u1="&#x203a;" u2="x" k="43" />
<hkern u1="&#x203a;" u2="f" k="20" />
<hkern u1="&#x203a;" u2="X" k="41" />
<hkern u1="&#x203a;" u2="V" k="47" />
<hkern u1="&#x2122;" u2="&#x1fc;" k="45" />
<hkern u1="&#x2122;" u2="&#x1fa;" k="45" />
<hkern u1="&#x2122;" u2="&#x135;" k="-51" />
<hkern u1="&#x2122;" u2="&#x134;" k="23" />
<hkern u1="&#x2122;" u2="&#x12d;" k="-33" />
<hkern u1="&#x2122;" u2="&#x129;" k="-35" />
<hkern u1="&#x2122;" u2="&#x104;" k="45" />
<hkern u1="&#x2122;" u2="&#x102;" k="45" />
<hkern u1="&#x2122;" u2="&#x100;" k="45" />
<hkern u1="&#x2122;" u2="&#xef;" k="-72" />
<hkern u1="&#x2122;" u2="&#xee;" k="-66" />
<hkern u1="&#x2122;" u2="&#xec;" k="-92" />
<hkern u1="&#x2122;" u2="&#xc6;" k="57" />
<hkern u1="&#x2122;" u2="&#xc5;" k="45" />
<hkern u1="&#x2122;" u2="&#xc4;" k="45" />
<hkern u1="&#x2122;" u2="&#xc3;" k="45" />
<hkern u1="&#x2122;" u2="&#xc2;" k="45" />
<hkern u1="&#x2122;" u2="&#xc1;" k="45" />
<hkern u1="&#x2122;" u2="&#xc0;" k="45" />
<hkern u1="&#x2122;" u2="J" k="23" />
<hkern u1="&#x2122;" u2="A" k="45" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="12" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="d,q,dcaron,dcroat" 	k="12" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="27" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="16" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="w,wcircumflex" 	k="14" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="y,yacute,ydieresis,ycircumflex" 	k="10" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="hyphen,endash,emdash" 	k="37" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="12" />
<hkern g1="D,Dcaron,Dcroat" 	g2="T,Tcaron,Tbar,uni021A" 	k="10" />
<hkern g1="D,Dcaron,Dcroat" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="53" />
<hkern g1="D,Dcaron,Dcroat" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="20" />
<hkern g1="D,Dcaron,Dcroat" 	g2="J,Jcircumflex" 	k="12" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="12" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="d,q,dcaron,dcroat" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="25" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="12" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="w,wcircumflex" 	k="12" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="hyphen,endash,emdash" 	k="29" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="33" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="12" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent" 	g2="w,wcircumflex" 	k="12" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent" 	g2="y,yacute,ydieresis,ycircumflex" 	k="10" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng" 	g2="d,q,dcaron,dcroat" 	k="10" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="18" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="12" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng" 	g2="w,wcircumflex" 	k="12" />
<hkern g1="J,Jcircumflex" 	g2="d,q,dcaron,dcroat" 	k="10" />
<hkern g1="J,Jcircumflex" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="16" />
<hkern g1="J,Jcircumflex" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="10" />
<hkern g1="J,Jcircumflex" 	g2="w,wcircumflex" 	k="10" />
<hkern g1="K,Kcommaaccent" 	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	k="10" />
<hkern g1="K,Kcommaaccent" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="14" />
<hkern g1="K,Kcommaaccent" 	g2="d,q,dcaron,dcroat" 	k="29" />
<hkern g1="K,Kcommaaccent" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="35" />
<hkern g1="K,Kcommaaccent" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="35" />
<hkern g1="K,Kcommaaccent" 	g2="t,tcaron,tbar,uni021B" 	k="20" />
<hkern g1="K,Kcommaaccent" 	g2="w,wcircumflex" 	k="41" />
<hkern g1="K,Kcommaaccent" 	g2="y,yacute,ydieresis,ycircumflex" 	k="37" />
<hkern g1="K,Kcommaaccent" 	g2="hyphen,endash,emdash" 	k="51" />
<hkern g1="K,Kcommaaccent" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="35" />
<hkern g1="K,Kcommaaccent" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute" 	k="10" />
<hkern g1="K,Kcommaaccent" 	g2="guillemotleft,guilsinglleft" 	k="35" />
<hkern g1="K,Kcommaaccent" 	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron" 	k="14" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="12" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="T,Tcaron,Tbar,uni021A" 	k="156" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="10" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="W,Wcircumflex" 	k="90" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="174" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="10" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="quoteleft,quotedblleft" 	k="156" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="quoteright,quotedblright" 	k="154" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="quotedbl,quotesingle" 	k="156" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="t,tcaron,tbar,uni021B" 	k="23" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="w,wcircumflex" 	k="37" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="y,yacute,ydieresis,ycircumflex" 	k="70" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="hyphen,endash,emdash" 	k="98" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="T,Tcaron,Tbar,uni021A" 	k="14" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="55" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="20" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="J,Jcircumflex" 	k="12" />
<hkern g1="R,Racute,Rcommaaccent,Rcaron" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="45" />
<hkern g1="R,Racute,Rcommaaccent,Rcaron" 	g2="d,q,dcaron,dcroat" 	k="14" />
<hkern g1="R,Racute,Rcommaaccent,Rcaron" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="14" />
<hkern g1="R,Racute,Rcommaaccent,Rcaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="16" />
<hkern g1="R,Racute,Rcommaaccent,Rcaron" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="16" />
<hkern g1="R,Racute,Rcommaaccent,Rcaron" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute" 	k="10" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="31" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="14" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	g2="t,tcaron,tbar,uni021B" 	k="10" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	g2="w,wcircumflex" 	k="14" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	g2="y,yacute,ydieresis,ycircumflex" 	k="14" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="16" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="12" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="d,q,dcaron,dcroat" 	k="137" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="154" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="141" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="t,tcaron,tbar,uni021B" 	k="20" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="w,wcircumflex" 	k="102" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="y,yacute,ydieresis,ycircumflex" 	k="102" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="hyphen,endash,emdash" 	k="117" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="111" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="76" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="J,Jcircumflex" 	k="14" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute" 	k="147" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="guillemotleft,guilsinglleft" 	k="113" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron" 	k="106" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="colon,semicolon" 	k="98" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="guillemotright,guilsinglright" 	k="98" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="115" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	k="141" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="z,zacute,zdotaccent,zcaron" 	k="143" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="d,q,dcaron,dcroat" 	k="12" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="12" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="J,Jcircumflex" 	k="12" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute" 	k="10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron" 	k="10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="18" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	k="10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="z,zacute,zdotaccent,zcaron" 	k="10" />
<hkern g1="W,Wcircumflex" 	g2="d,q,dcaron,dcroat" 	k="37" />
<hkern g1="W,Wcircumflex" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="43" />
<hkern g1="W,Wcircumflex" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="39" />
<hkern g1="W,Wcircumflex" 	g2="hyphen,endash,emdash" 	k="31" />
<hkern g1="W,Wcircumflex" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="20" />
<hkern g1="W,Wcircumflex" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="39" />
<hkern g1="W,Wcircumflex" 	g2="J,Jcircumflex" 	k="12" />
<hkern g1="W,Wcircumflex" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute" 	k="31" />
<hkern g1="W,Wcircumflex" 	g2="guillemotleft,guilsinglleft" 	k="31" />
<hkern g1="W,Wcircumflex" 	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron" 	k="27" />
<hkern g1="W,Wcircumflex" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="59" />
<hkern g1="W,Wcircumflex" 	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	k="29" />
<hkern g1="W,Wcircumflex" 	g2="z,zacute,zdotaccent,zcaron" 	k="14" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	k="51" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="57" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	k="37" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="d,q,dcaron,dcroat" 	k="137" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="147" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="141" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="t,tcaron,tbar,uni021B" 	k="33" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="w,wcircumflex" 	k="82" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="y,yacute,ydieresis,ycircumflex" 	k="72" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="hyphen,endash,emdash" 	k="131" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="106" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="109" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="J,Jcircumflex" 	k="39" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute" 	k="129" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="119" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron" 	k="121" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="colon,semicolon" 	k="76" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="guillemotright,guilsinglright" 	k="74" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="143" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	k="141" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="z,zacute,zdotaccent,zcaron" 	k="100" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="d,q,dcaron,dcroat" 	k="14" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="29" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="16" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="w,wcircumflex" 	k="18" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="y,yacute,ydieresis,ycircumflex" 	k="14" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="hyphen,endash,emdash" 	k="39" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="16" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron" 	k="10" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute" 	g2="T,Tcaron,Tbar,uni021A" 	k="141" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="18" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute" 	g2="W,Wcircumflex" 	k="39" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="135" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute" 	g2="quotedbl,quotesingle" 	k="18" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute" 	g2="w,wcircumflex" 	k="8" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute" 	g2="y,yacute,ydieresis,ycircumflex" 	k="14" />
<hkern g1="b,p" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	k="10" />
<hkern g1="b,p" 	g2="T,Tcaron,Tbar,uni021A" 	k="139" />
<hkern g1="b,p" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="10" />
<hkern g1="b,p" 	g2="W,Wcircumflex" 	k="35" />
<hkern g1="b,p" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="137" />
<hkern g1="b,p" 	g2="quotedbl,quotesingle" 	k="25" />
<hkern g1="b,p" 	g2="y,yacute,ydieresis,ycircumflex" 	k="16" />
<hkern g1="b,p" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="10" />
<hkern g1="b,p" 	g2="J,Jcircumflex" 	k="20" />
<hkern g1="b,p" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron" 	k="10" />
<hkern g1="b,p" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="18" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="T,Tcaron,Tbar,uni021A" 	k="152" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="W,Wcircumflex" 	k="10" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="98" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="8" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="8" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="hyphen,endash,emdash" 	k="35" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="guillemotleft,guilsinglleft" 	k="27" />
<hkern g1="colon,semicolon" 	g2="T,Tcaron,Tbar,uni021A" 	k="98" />
<hkern g1="colon,semicolon" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="76" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="T,Tcaron,Tbar,uni021A" 	k="150" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="W,Wcircumflex" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="162" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="quotedbl,quotesingle" 	k="18" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="y,yacute,ydieresis,ycircumflex" 	k="16" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="10" />
<hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	g2="T,Tcaron,Tbar,uni021A" 	k="102" />
<hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="66" />
<hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	g2="hyphen,endash,emdash" 	k="20" />
<hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T,Tcaron,Tbar,uni021A" 	k="104" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="78" />
<hkern g1="guillemotright,guilsinglright" 	g2="T,Tcaron,Tbar,uni021A" 	k="113" />
<hkern g1="guillemotright,guilsinglright" 	g2="W,Wcircumflex" 	k="29" />
<hkern g1="guillemotright,guilsinglright" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="115" />
<hkern g1="guillemotright,guilsinglright" 	g2="quoteright,quotedblright" 	k="39" />
<hkern g1="guillemotright,guilsinglright" 	g2="quotedbl,quotesingle" 	k="49" />
<hkern g1="guillemotright,guilsinglright" 	g2="y,yacute,ydieresis,ycircumflex" 	k="23" />
<hkern g1="guillemotright,guilsinglright" 	g2="z,zacute,zdotaccent,zcaron" 	k="25" />
<hkern g1="hyphen,endash,emdash" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	k="37" />
<hkern g1="hyphen,endash,emdash" 	g2="T,Tcaron,Tbar,uni021A" 	k="117" />
<hkern g1="hyphen,endash,emdash" 	g2="W,Wcircumflex" 	k="31" />
<hkern g1="hyphen,endash,emdash" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="131" />
<hkern g1="hyphen,endash,emdash" 	g2="quoteright,quotedblright" 	k="113" />
<hkern g1="hyphen,endash,emdash" 	g2="quotedbl,quotesingle" 	k="123" />
<hkern g1="hyphen,endash,emdash" 	g2="t,tcaron,tbar,uni021B" 	k="18" />
<hkern g1="hyphen,endash,emdash" 	g2="y,yacute,ydieresis,ycircumflex" 	k="20" />
<hkern g1="hyphen,endash,emdash" 	g2="J,Jcircumflex" 	k="35" />
<hkern g1="hyphen,endash,emdash" 	g2="z,zacute,zdotaccent,zcaron" 	k="31" />
<hkern g1="hyphen,endash,emdash" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="41" />
<hkern g1="k,kcommaaccent" 	g2="T,Tcaron,Tbar,uni021A" 	k="111" />
<hkern g1="k,kcommaaccent" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="10" />
<hkern g1="k,kcommaaccent" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="74" />
<hkern g1="k,kcommaaccent" 	g2="d,q,dcaron,dcroat" 	k="14" />
<hkern g1="k,kcommaaccent" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="14" />
<hkern g1="k,kcommaaccent" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="18" />
<hkern g1="k,kcommaaccent" 	g2="hyphen,endash,emdash" 	k="47" />
<hkern g1="k,kcommaaccent" 	g2="guillemotleft,guilsinglleft" 	k="41" />
<hkern g1="dcaron,lcaron" 	g2="d,q,dcaron,dcroat" 	k="25" />
<hkern g1="dcaron,lcaron" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="18" />
<hkern g1="dcaron,lcaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="29" />
<hkern g1="dcaron,lcaron" 	g2="quotedbl,quotesingle" 	k="-51" />
<hkern g1="dcaron,lcaron" 	g2="t,tcaron,tbar,uni021B" 	k="-45" />
<hkern g1="dcaron,lcaron" 	g2="w,wcircumflex" 	k="-27" />
<hkern g1="dcaron,lcaron" 	g2="y,yacute,ydieresis,ycircumflex" 	k="-43" />
<hkern g1="dcaron,lcaron" 	g2="hyphen,endash,emdash" 	k="111" />
<hkern g1="dcaron,lcaron" 	g2="guillemotleft,guilsinglleft" 	k="90" />
<hkern g1="dcaron,lcaron" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="109" />
<hkern g1="dcaron,lcaron" 	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	k="8" />
<hkern g1="dcaron,lcaron" 	g2="b" 	k="-104" />
<hkern g1="dcaron,lcaron" 	g2="h,k,germandbls,hcircumflex,hbar,kcommaaccent" 	k="-104" />
<hkern g1="dcaron,lcaron" 	g2="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,jcircumflex" 	k="-104" />
<hkern g1="dcaron,lcaron" 	g2="l,lacute,lcommaaccent,lcaron,lslash" 	k="-92" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	k="10" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="T,Tcaron,Tbar,uni021A" 	k="145" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="14" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="W,Wcircumflex" 	k="35" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="137" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="quotedbl,quotesingle" 	k="20" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="w,wcircumflex" 	k="8" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="y,yacute,ydieresis,ycircumflex" 	k="10" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="14" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	k="14" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="T,Tcaron,Tbar,uni021A" 	k="145" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="10" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="W,Wcircumflex" 	k="39" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="141" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="quotedbl,quotesingle" 	k="23" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="y,yacute,ydieresis,ycircumflex" 	k="18" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="10" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="J,Jcircumflex" 	k="23" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron" 	k="12" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="T,Tcaron,Tbar,uni021A" 	k="117" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="18" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="W,Wcircumflex" 	k="59" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="143" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="quoteleft,quotedblleft" 	k="236" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="quoteright,quotedblright" 	k="244" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="quotedbl,quotesingle" 	k="256" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="t,tcaron,tbar,uni021B" 	k="29" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="w,wcircumflex" 	k="35" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="y,yacute,ydieresis,ycircumflex" 	k="51" />
<hkern g1="quoteleft,quotedblleft" 	g2="d,q,dcaron,dcroat" 	k="35" />
<hkern g1="quoteleft,quotedblleft" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="27" />
<hkern g1="quoteleft,quotedblleft" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="31" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="68" />
<hkern g1="quoteleft,quotedblleft" 	g2="J,Jcircumflex" 	k="23" />
<hkern g1="quoteleft,quotedblleft" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="264" />
<hkern g1="quoteleft,quotedblleft" 	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="d,q,dcaron,dcroat" 	k="51" />
<hkern g1="quoteright,quotedblright" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="41" />
<hkern g1="quoteright,quotedblright" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="47" />
<hkern g1="quoteright,quotedblright" 	g2="hyphen,endash,emdash" 	k="154" />
<hkern g1="quoteright,quotedblright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="76" />
<hkern g1="quoteright,quotedblright" 	g2="J,Jcircumflex" 	k="23" />
<hkern g1="quoteright,quotedblright" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute" 	k="27" />
<hkern g1="quoteright,quotedblright" 	g2="guillemotleft,guilsinglleft" 	k="94" />
<hkern g1="quoteright,quotedblright" 	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron" 	k="16" />
<hkern g1="quoteright,quotedblright" 	g2="colon,semicolon" 	k="25" />
<hkern g1="quoteright,quotedblright" 	g2="guillemotright,guilsinglright" 	k="27" />
<hkern g1="quoteright,quotedblright" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="285" />
<hkern g1="quoteright,quotedblright" 	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	k="35" />
<hkern g1="quotedbl,quotesingle" 	g2="d,q,dcaron,dcroat" 	k="27" />
<hkern g1="quotedbl,quotesingle" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="16" />
<hkern g1="quotedbl,quotesingle" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="23" />
<hkern g1="quotedbl,quotesingle" 	g2="hyphen,endash,emdash" 	k="125" />
<hkern g1="quotedbl,quotesingle" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="61" />
<hkern g1="quotedbl,quotesingle" 	g2="J,Jcircumflex" 	k="25" />
<hkern g1="quotedbl,quotesingle" 	g2="guillemotleft,guilsinglleft" 	k="57" />
<hkern g1="quotedbl,quotesingle" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="256" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="T,Tcaron,Tbar,uni021A" 	k="88" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="49" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="hyphen,endash,emdash" 	k="53" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="47" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="J,Jcircumflex" 	k="31" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="guillemotleft,guilsinglleft" 	k="51" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="104" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="16" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	g2="T,Tcaron,Tbar,uni021A" 	k="143" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	g2="W,Wcircumflex" 	k="23" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="104" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	g2="y,yacute,ydieresis,ycircumflex" 	k="14" />
<hkern g1="t,tbar,uni021B" 	g2="T,Tcaron,Tbar,uni021A" 	k="96" />
<hkern g1="t,tbar,uni021B" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="68" />
<hkern g1="t,tbar,uni021B" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="T,Tcaron,Tbar,uni021A" 	k="109" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="10" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="W,Wcircumflex" 	k="27" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="121" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="10" />
<hkern g1="w,wcircumflex" 	g2="T,Tcaron,Tbar,uni021A" 	k="102" />
<hkern g1="w,wcircumflex" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="82" />
<hkern g1="w,wcircumflex" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="8" />
<hkern g1="w,wcircumflex" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="25" />
<hkern g1="w,wcircumflex" 	g2="J,Jcircumflex" 	k="27" />
<hkern g1="w,wcircumflex" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="35" />
<hkern g1="w,wcircumflex" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron" 	k="12" />
<hkern g1="w,wcircumflex" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="20" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="T,Tcaron,Tbar,uni021A" 	k="102" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="70" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="d,q,dcaron,dcroat" 	k="16" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="18" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="16" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="hyphen,endash,emdash" 	k="20" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="31" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="J,Jcircumflex" 	k="25" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute" 	k="16" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="guillemotleft,guilsinglleft" 	k="23" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="53" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	k="14" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="16" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="T,Tcaron,Tbar,uni021A" 	k="145" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="10" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="W,Wcircumflex" 	k="14" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="102" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="hyphen,endash,emdash" 	k="31" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="guillemotleft,guilsinglleft" 	k="25" />
</font>
</defs></svg> 