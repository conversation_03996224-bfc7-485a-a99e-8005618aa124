<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.3.1.final using JasperReports Library version 6.3.1  -->
<!-- 2018-03-23T10:34:04 -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="subRelatorioAcumulacaoVinculos" pageWidth="842" pageHeight="595" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="800" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isFloatColumnFooter="true" uuid="4de3fdd0-fb92-43a5-af2e-3ff2c9c0ea70">
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="346"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="647"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w1" value="730"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w2" value="256"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="sicap"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<parameter name="cpf" class="java.lang.String"/>
	<parameter name="mes" class="java.lang.Integer"/>
	<parameter name="ano" class="java.lang.Integer"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String"/>
	<queryString>
		<![CDATA[SELECT da.idBeneficiario,
       e.idEntidadeCjur,
       e.nome AS entidade,
       b.matricula,
       c.nome AS cargo,
       c.cargaHorariaMensal,
       (c.cargaHorariaMensal / 4) as cargaHorariaSemanal,
       CONVERT (VARCHAR (10), vf.dataAdmissao, 103) AS dataAdmissao,
       CASE vf.regimePrevidenciario WHEN 1 THEN 'RGPS' WHEN 2 THEN 'RPPS' END AS regimePrevidenciario,
       CASE vf.tipoServidor WHEN 1 THEN 'Civil' WHEN 2 THEN 'Militar' END AS tipoServidor,
       tv.descricao as tipoVinculo,
       CONVERT (VARCHAR (10), hf.dataOcorrencia, 103) AS dataOcorrencia,
       sf.descricao AS situacaoFuncional,
       lotacao.nome AS unidade,
       (m.nome + ' - ' + u.nome) AS cidade
FROM auditoria.DetalhamentoAcumulacao da
     LEFT JOIN Entidade e ON e.idEntidadeCjur = da.idEntidadeCjur
     LEFT JOIN Beneficiario b ON b.id = da.idBeneficiario
     LEFT JOIN Cargo c ON c.id = da.idCargo
     LEFT JOIN VinculoFuncional vf ON vf.idBeneficiario = da.idBeneficiario AND
      vf.idCargo = da.idCargo AND vf.registroAtivo = 1
     LEFT JOIN TipoVinculo tv ON tv.id = vf.idTipoVinculo
     LEFT JOIN Municipio m ON m.id = da.idMunicipio
     LEFT JOIN Uf u ON u.id = m.idUf
     LEFT JOIN 
     (SELECT MAX(hf.id) as id, hf.idBeneficiario FROM HistoricoFuncional hf
      GROUP BY hf.idBeneficiario) historico ON historico.idBeneficiario =
       da.idBeneficiario
     LEFT JOIN HistoricoFuncional hf ON hf.id = historico.id
     LEFT JOIN SituacaoFuncional sf ON sf.id = hf.idSituacaoFuncional
     LEFT JOIN 
     (SELECT cc.idBeneficiario, cc.ano, cc.mes, ul.nome FROM ContraCheque cc
      INNER JOIN UnidadeLotacao ul ON ul.id = cc.idUnidadeLotacao) lotacao ON
       lotacao.idBeneficiario = da.idBeneficiario AND lotacao.ano = da.ano AND
        lotacao.mes = da.mes
WHERE da.cpf =  $P{cpf}  AND
      da.ano = $P{ano} AND
      da.mes = $P{mes} 
GROUP BY da.idBeneficiario,
         e.idEntidadeCjur,
         e.nome,
         b.matricula,
         c.nome,
         c.cargaHorariaMensal,
         (c.cargaHorariaMensal / 4),
         CONVERT (VARCHAR (10), vf.dataAdmissao, 103),
         CASE vf.regimePrevidenciario
           WHEN 1 THEN 'RGPS'
           WHEN 2 THEN 'RPPS'
         END,
         CASE vf.tipoServidor
           WHEN 1 THEN 'Civil'
           WHEN 2 THEN 'Militar'
         END,
         tv.descricao,
         CONVERT (VARCHAR (10), hf.dataOcorrencia, 103),
         sf.descricao,
         lotacao.nome,
         (m.nome + ' - ' + u.nome)]]>
	</queryString>
	<field name="idBeneficiario" class="java.lang.Long"/>
	<field name="idEntidadeCjur" class="java.lang.Integer"/>
	<field name="entidade" class="java.lang.String"/>
	<field name="matricula" class="java.lang.Integer"/>
	<field name="cargo" class="java.lang.String"/>
	<field name="cargaHorariaMensal" class="java.lang.Integer"/>
	<field name="cargaHorariaSemanal" class="java.lang.Integer"/>
	<field name="dataAdmissao" class="java.lang.String"/>
	<field name="regimePrevidenciario" class="java.lang.String"/>
	<field name="tipoServidor" class="java.lang.String"/>
	<field name="tipoVinculo" class="java.lang.String"/>
	<field name="dataOcorrencia" class="java.lang.String"/>
	<field name="situacaoFuncional" class="java.lang.String"/>
	<field name="unidade" class="java.lang.String"/>
	<field name="cidade" class="java.lang.String"/>
	<detail>
		<band height="103" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<frame>
				<reportElement x="0" y="0" width="802" height="72" uuid="c20dada9-25c0-449c-909d-270d62b15210">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement mode="Opaque" x="1" y="2" width="52" height="12" backcolor="#C0C0C0" uuid="fc406edc-12bb-4164-9f59-9f620b74a0dd">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Vínculo " + $V{REPORT_COUNT} + ": "]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement mode="Opaque" x="53" y="2" width="748" height="12" backcolor="#C0C0C0" uuid="986c6f48-48c3-425f-ab44-2cdb1a324f7f"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{entidade}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="2" y="16" width="47" height="12" uuid="121163ea-6f11-4c3b-84cd-9aba5674a41f"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Matricula:]]></text>
				</staticText>
				<textField isBlankWhenNull="true">
					<reportElement x="50" y="16" width="70" height="12" uuid="0fc50f67-8bc2-4d6e-9cad-efadf3361037">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{matricula}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="177" y="16" width="453" height="12" uuid="cad581b9-7e00-41d4-8a88-4bd1850d56e8">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{cargo}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="146" y="16" width="30" height="12" uuid="653a7b20-253b-4a1a-96de-c7b9f2f81ff0">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Cargo:]]></text>
				</staticText>
				<staticText>
					<reportElement x="2" y="30" width="47" height="12" uuid="aebd53bf-3d9a-4950-a03d-b5d92075ef1a">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Admissão:]]></text>
				</staticText>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement x="50" y="30" width="70" height="12" uuid="87a55cdb-3055-4970-a850-422d953e3456"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{dataAdmissao}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="300" y="44" width="111" height="12" uuid="88c6c48e-48d7-4929-9b91-f534959c145c">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Útima situação funcional:]]></text>
				</staticText>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement x="739" y="44" width="58" height="12" uuid="82db2e9f-49a9-4118-a6eb-5454abaa244c">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{dataOcorrencia}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="146" y="30" width="70" height="12" uuid="ec248cfe-7b4a-46eb-92cf-94a3c403a0c9">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Tipo de Vínculo:]]></text>
				</staticText>
				<textField isBlankWhenNull="true">
					<reportElement x="217" y="30" width="413" height="12" uuid="89ca43b4-95e8-4a6f-b7e3-a52862807c4a"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{tipoVinculo}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="2" y="44" width="101" height="12" uuid="73355933-7fc4-48fd-983a-9d5efbdd9aed">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Regime Previdenciário:]]></text>
				</staticText>
				<textField isBlankWhenNull="true">
					<reportElement x="104" y="44" width="39" height="12" uuid="fd403900-a139-4b67-a5de-ee1b092c7c55">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{regimePrevidenciario}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="146" y="44" width="62" height="12" uuid="d2f0dfea-4652-4c27-bd5d-aceead7eb8a6">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Tipo Servidor:]]></text>
				</staticText>
				<textField isBlankWhenNull="true">
					<reportElement x="209" y="44" width="60" height="12" uuid="d2283cf0-f6a8-4cfb-bf7c-8965fb26f9d1">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{tipoServidor}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="636" y="44" width="102" height="12" uuid="b2ed1305-0045-42bf-acb5-c502fc1b7b5a">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Data da movimentação:]]></text>
				</staticText>
				<textField isBlankWhenNull="true">
					<reportElement x="412" y="44" width="218" height="12" uuid="d369004d-c91a-4d5f-808c-65d1367280d9">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{situacaoFuncional}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="642" y="16" width="96" height="12" uuid="a78cb9ad-4994-450f-97e9-f86cd1032f6a">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Carga Horária Mensal:]]></text>
				</staticText>
				<textField isBlankWhenNull="true">
					<reportElement x="739" y="16" width="58" height="12" uuid="904b6714-8f5f-4336-953b-276594844cb0">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{cargaHorariaMensal}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="635" y="30" width="103" height="12" uuid="f7025848-134b-4f07-afda-46dba585c8a6">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Carga Horária Semanal:]]></text>
				</staticText>
				<textField>
					<reportElement x="739" y="30" width="58" height="12" uuid="6c6b7256-6dee-42a3-9e37-79f8aea767dd">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{cargaHorariaSemanal}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="92" y="58" width="388" height="12" uuid="87ec6f3a-8649-478c-9857-9ebf8c946a34">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{unidade}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="2" y="58" width="89" height="12" uuid="ab18a021-e3cb-4008-8c4c-8beb03c4d7ad">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Unidade de Lotação:]]></text>
				</staticText>
				<staticText>
					<reportElement x="490" y="58" width="35" height="12" uuid="ab770b4b-47e3-43c2-a1b0-af39e99a7d4d">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Cidade:]]></text>
				</staticText>
				<textField isBlankWhenNull="true">
					<reportElement x="526" y="58" width="271" height="12" uuid="227eeef0-24f1-4db6-be16-bc9a09226a4a">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{cidade}]]></textFieldExpression>
				</textField>
			</frame>
			<subreport>
				<reportElement x="0" y="76" width="802" height="23" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="5b478739-263a-45b8-beb1-c0441569367f">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<subreportParameter name="cpf">
					<subreportParameterExpression><![CDATA[$P{cpf}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="ano">
					<subreportParameterExpression><![CDATA[$P{ano}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="idEntidadeCjur">
					<subreportParameterExpression><![CDATA[$F{idEntidadeCjur}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="idBeneficiario">
					<subreportParameterExpression><![CDATA[$F{idBeneficiario}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "subRelatorioAcumulacao1Semestre.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="30" splitType="Stretch">
			<subreport isUsingCache="false" runToBottom="false">
				<reportElement x="0" y="3" width="802" height="23" isPrintWhenDetailOverflows="true" uuid="26500403-5a01-4f35-89a9-fec09ed3694f">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<subreportParameter name="ano">
					<subreportParameterExpression><![CDATA[$P{ano}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="cpf">
					<subreportParameterExpression><![CDATA[$P{cpf}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="idBeneficiario">
					<subreportParameterExpression><![CDATA[$F{idBeneficiario}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="idEntidadeCjur">
					<subreportParameterExpression><![CDATA[$F{idEntidadeCjur}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "subRelatorioAcumulacao2Semestre.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
</jasperReport>
