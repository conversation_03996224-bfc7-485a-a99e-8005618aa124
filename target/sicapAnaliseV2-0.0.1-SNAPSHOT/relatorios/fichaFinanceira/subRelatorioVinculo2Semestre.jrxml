<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.6.0.final using JasperReports Library version 6.6.0  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="subRelatorioVinculo2Semestre" pageWidth="802" pageHeight="595" orientation="Landscape" whenNoDataType="BlankPage" columnWidth="802" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isFloatColumnFooter="true" isIgnorePagination="true" uuid="ea4ee8f4-fb15-4c87-b8b2-df2d6cd5ed7f">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="314"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="679"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="sicap"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<parameter name="idBeneficiario" class="java.lang.String"/>
	<parameter name="idEntidadeCjur" class="java.lang.String"/>
	<parameter name="ano" class="java.lang.String"/>
	<parameter name="cpf" class="java.lang.String"/>
	<queryString language="SQL">
		<![CDATA[SELECT 
	   r.codigo,
	   r.descricao AS nomeVerba,
       r.ano,
       r.folha,
       r.natureza,
       SUM(r.julho) AS julho,
       SUM(r.agosto) AS agosto,
       SUM(r.setembro) AS setembro,
       SUM(r.outubro) AS outubro,
       SUM(r.novembro) AS novembro,
       SUM(r.dezembro) AS dezembro,
       SUM(r.julho) AS decimo,
       (SUM(r.julho) + SUM(r.agosto) + SUM(r.setembro) + SUM(r.outubro) + SUM(r.novembro) + SUM(r.dezembro)) as totalSemestre
FROM (SELECT
	    v.codigo, 
		vcc.idContraCheque, 
        v.descricao, 
        cc.ano,
        v.natureza, 
		CASE WHEN cc.mes = 7 THEN vcc.valor ELSE 0 END AS julho, 
        CASE WHEN cc.mes = 8 THEN vcc.valor ELSE 0 END AS agosto, 
        CASE WHEN cc.mes = 9 THEN vcc.valor ELSE 0 END AS setembro, 
        CASE WHEN cc.mes = 10 THEN vcc.valor ELSE 0 END AS outubro, 
        CASE WHEN cc.mes = 11 THEN vcc.valor ELSE 0 END AS novembro, 
        CASE WHEN cc.mes = 12 THEN vcc.valor ELSE 0 END AS dezembro, 
        CASE WHEN (cc.mes = 12) AND (tf.descricao like '%13%' OR tf.descricao like '%decimo%' OR tf.descricao like '%terceiro%') THEN vcc.valor ELSE 0 END AS decimo,         
        tf.descricao as folha 
	 FROM ContraCheque cc 
     INNER JOIN VerbasContraCheque vcc ON vcc.idContraCheque = cc.id 
     INNER JOIN TipoFolha tf ON vcc.idTipoFolha = tf.id 
     INNER JOIN Verba v ON vcc.idVerba = v.id 
     INNER JOIN Beneficiario b ON cc.idBeneficiario = b.id 
     INNER JOIN CadastroUnico cadu ON b.idCadastroUnico = cadu.id 
     WHERE cc.ano = $P{ano} AND cadu.cpf = $P{cpf} AND cc.idEntidadeCjur = $P{idEntidadeCjur} AND cc.idBeneficiario = $P{idBeneficiario} AND cc.mes IN(7, 8, 9, 10, 11, 12) ) r
GROUP BY 
	r.codigo,
	r.descricao,
    r.ano,
    r.folha,
    r.natureza
ORDER BY 
	r.natureza, r.folha, r.descricao asc]]>
	</queryString>
	<field name="codigo" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="codigo"/>
	</field>
	<field name="nomeVerba" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="nomeVerba"/>
	</field>
	<field name="ano" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.label" value="ano"/>
	</field>
	<field name="folha" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="folha"/>
	</field>
	<field name="natureza" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="natureza"/>
	</field>
	<field name="julho" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.label" value="julho"/>
	</field>
	<field name="agosto" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.label" value="agosto"/>
	</field>
	<field name="setembro" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.label" value="setembro"/>
	</field>
	<field name="outubro" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.label" value="outubro"/>
	</field>
	<field name="novembro" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.label" value="novembro"/>
	</field>
	<field name="dezembro" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.label" value="dezembro"/>
	</field>
	<variable name="totalBrutoJulho" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{natureza}.equals("C") ? $F{julho} : new BigDecimal(0)]]></variableExpression>
	</variable>
	<variable name="totalDescontosJulho" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{natureza}.equals("D") ? $F{julho} : new BigDecimal(0)]]></variableExpression>
	</variable>
	<variable name="totalLiquidoJulho" class="java.math.BigDecimal">
		<variableExpression><![CDATA[$V{totalBrutoJulho}.subtract($V{totalDescontosJulho})]]></variableExpression>
	</variable>
	<variable name="totalBrutoAgosto" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{natureza}.equals("C") ? $F{agosto} : new BigDecimal(0)]]></variableExpression>
	</variable>
	<variable name="totalDescontosAgosto" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{natureza}.equals("D") ? $F{agosto} : new BigDecimal(0)]]></variableExpression>
	</variable>
	<variable name="totalLiquidoAgosto" class="java.math.BigDecimal">
		<variableExpression><![CDATA[$V{totalBrutoAgosto}.subtract($V{totalDescontosAgosto})]]></variableExpression>
	</variable>
	<variable name="totalBrutoSetembro" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{natureza}.equals("C") ? $F{setembro} : new BigDecimal(0)]]></variableExpression>
	</variable>
	<variable name="totalDescontosSetembro" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{natureza}.equals("D") ? $F{setembro} : new BigDecimal(0)]]></variableExpression>
	</variable>
	<variable name="totalLiquidoSetembro" class="java.math.BigDecimal">
		<variableExpression><![CDATA[$V{totalBrutoSetembro}.subtract($V{totalDescontosSetembro})]]></variableExpression>
	</variable>
	<variable name="totalBrutoOutubro" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{natureza}.equals("C") ? $F{outubro} : new BigDecimal(0)]]></variableExpression>
	</variable>
	<variable name="totalDescontosOutubro" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{natureza}.equals("D") ? $F{outubro} : new BigDecimal(0)]]></variableExpression>
	</variable>
	<variable name="totalLiquidoOutubro" class="java.math.BigDecimal">
		<variableExpression><![CDATA[$V{totalBrutoOutubro}.subtract($V{totalDescontosOutubro})]]></variableExpression>
	</variable>
	<variable name="totalBrutoNovembro" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{natureza}.equals("C") ? $F{novembro} : new BigDecimal(0)]]></variableExpression>
	</variable>
	<variable name="totalDescontosNovembro" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{natureza}.equals("D") ? $F{novembro} : new BigDecimal(0)]]></variableExpression>
	</variable>
	<variable name="totalLiquidoNovembro" class="java.math.BigDecimal">
		<variableExpression><![CDATA[$V{totalBrutoNovembro}.subtract($V{totalDescontosNovembro})]]></variableExpression>
	</variable>
	<variable name="totalBrutoDezembro" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{natureza}.equals("C") ? $F{dezembro} : new BigDecimal(0)]]></variableExpression>
	</variable>
	<variable name="totalDescontosDezembro" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{natureza}.equals("D") ? $F{dezembro} : new BigDecimal(0)]]></variableExpression>
	</variable>
	<variable name="totalLiquidoDezembro" class="java.math.BigDecimal">
		<variableExpression><![CDATA[$V{totalBrutoDezembro}.subtract($V{totalDescontosDezembro})]]></variableExpression>
	</variable>
	<columnHeader>
		<band height="24" splitType="Stretch">
			<staticText>
				<reportElement positionType="Float" mode="Opaque" x="0" y="12" width="34" height="12" backcolor="#C0C0C0" uuid="51949dd3-36d5-44be-b88c-4223a4d79653"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Código]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Opaque" x="34" y="12" width="260" height="12" backcolor="#C0C0C0" uuid="0ae32778-de07-41bb-bac7-5bfad5512ce5"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Verba]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Opaque" x="294" y="12" width="43" height="12" backcolor="#C0C0C0" uuid="e3baa464-baaa-4b29-b511-e0779cc7482b"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Natureza]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Opaque" x="337" y="12" width="105" height="12" backcolor="#C0C0C0" uuid="3d356419-4165-4c6d-b4bd-43f878632fb9"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Tipo de Folha]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Opaque" x="442" y="12" width="60" height="12" backcolor="#C0C0C0" uuid="87229e4c-4397-45be-9962-c92d8b016a11"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Julho]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Opaque" x="502" y="12" width="60" height="12" backcolor="#C0C0C0" uuid="d8f83dcf-befd-4b75-9e99-a6fb38324d9c"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Agosto]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Opaque" x="562" y="12" width="60" height="12" backcolor="#C0C0C0" uuid="3dbcaa7c-3f54-4255-bf50-9bf17adf39fc"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Setembro]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Opaque" x="622" y="12" width="60" height="12" backcolor="#C0C0C0" uuid="c98297f1-144d-41cf-866c-356116e3c312"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Outubro]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Opaque" x="682" y="12" width="60" height="12" backcolor="#C0C0C0" uuid="b2a02c96-56fd-4e1f-89b0-cf22a42ce687"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Novembro]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Opaque" x="742" y="12" width="60" height="12" backcolor="#C0C0C0" uuid="2520e2cc-87da-4a06-9fbf-c4d7d98c0c85"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Dezembro]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="0" y="0" width="802" height="12" backcolor="#C0C0C0" uuid="5cc6b380-32e4-43c3-b01e-ae044210fa52"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[2º Semestre]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="12" splitType="Stretch">
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="0" width="34" height="12" uuid="86be129f-957b-406c-b20e-807ba61b1faa">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="aeee0ec9-2abb-4784-99d6-dbee5d2d7917"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{codigo}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="34" y="0" width="260" height="12" uuid="50e074b0-e4cc-46a0-8aa1-07e29f976c6a">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="f5a945ef-3714-4e14-9812-569ad302079f"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{nomeVerba}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="294" y="0" width="43" height="12" uuid="745838e9-7bd6-4f77-b92e-f4333bf6807d">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="09ea65fe-8a9b-4d17-9499-cabd87435b93"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{natureza}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="337" y="0" width="105" height="12" uuid="91070f34-4277-4870-b44b-6da46db10a5f">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="bfd1d540-9d98-4ae1-b7e4-030e9ce25099"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
					<paragraph leftIndent="2" rightIndent="0"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{folha}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement x="442" y="0" width="60" height="12" uuid="d527a759-a498-4454-841c-ffcc5ac3a906">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="60ff5d34-7ebf-4eaf-93a1-69a2e190b0a9"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{julho}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement x="502" y="0" width="60" height="12" uuid="1a6993f7-c1e4-43ec-8853-4c0b91382f66">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4e0097ca-9d0c-444b-b15d-ec6b20e792e6"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{agosto}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement x="562" y="0" width="60" height="12" uuid="e069fd3c-fd11-465b-89ec-7c85693d5e65">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4eadf663-f4fc-48d0-b2f3-de2c26ada2c7"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{setembro}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement x="622" y="0" width="60" height="12" uuid="ac97507f-e8cc-4ce2-a5a1-11babeee2513">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="d660bc7f-d313-4467-8fe2-37bb7cdc7c4b"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{outubro}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement x="682" y="0" width="60" height="12" uuid="280518a2-6073-4fa1-bfbf-d2db56ed5b7c">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="ad12853a-4ec5-4d55-985c-fbf589439aa7"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{novembro}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement x="742" y="0" width="60" height="12" uuid="df8a94c1-52c5-4808-8efc-024a1a8eefe6">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="bdaae1ea-ed27-4622-bd1b-4023db2f7de7"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dezembro}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band height="36" splitType="Stretch">
			<staticText>
				<reportElement positionType="Float" mode="Opaque" x="0" y="12" width="442" height="12" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" backcolor="#C0C0C0" uuid="f78d87d3-1525-40f4-94df-66ab2b94f692"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<text><![CDATA[Total de Descontos]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Opaque" x="0" y="0" width="442" height="12" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" backcolor="#C0C0C0" uuid="c6f50cdf-c16f-4e8a-84f0-a06d13e3b943"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<text><![CDATA[Total Bruto]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Opaque" x="0" y="24" width="442" height="12" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" backcolor="#C0C0C0" uuid="*************-401b-aa80-cd50c6608260"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<text><![CDATA[Total de Líquido]]></text>
			</staticText>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement mode="Opaque" x="442" y="0" width="60" height="12" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" backcolor="#C0C0C0" uuid="d3e4b757-e04f-42df-9a7f-b674e90118bb"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalBrutoJulho}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement mode="Opaque" x="442" y="12" width="60" height="12" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" backcolor="#C0C0C0" uuid="60395326-172f-4d36-b697-b26c68cb6787"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalDescontosJulho}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement mode="Opaque" x="442" y="24" width="60" height="12" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" backcolor="#C0C0C0" uuid="8f637379-6029-4a93-9cb0-9d0e3df6e81e"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalLiquidoJulho}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement mode="Opaque" x="502" y="0" width="60" height="12" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" backcolor="#C0C0C0" uuid="4f4ebf83-0e8b-46cc-94bc-f495a0ee6128"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalBrutoAgosto}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement mode="Opaque" x="502" y="12" width="60" height="12" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" backcolor="#C0C0C0" uuid="61dea6ca-d724-4a8b-87f2-977a8fb27cdf"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalDescontosAgosto}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement mode="Opaque" x="502" y="24" width="60" height="12" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" backcolor="#C0C0C0" uuid="46793d03-7373-43e1-82b2-152a1014d10d"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalLiquidoAgosto}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement mode="Opaque" x="562" y="0" width="60" height="12" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" backcolor="#C0C0C0" uuid="05c3883f-30c0-4f76-9c81-622579fcbc12"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalBrutoSetembro}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement mode="Opaque" x="562" y="12" width="60" height="12" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" backcolor="#C0C0C0" uuid="3801fdbc-8457-4980-a954-ffa21c5e8c86"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalDescontosSetembro}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement mode="Opaque" x="562" y="24" width="60" height="12" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" backcolor="#C0C0C0" uuid="d61672a6-7935-4d20-932c-b51e9039ab66"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalLiquidoSetembro}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement mode="Opaque" x="622" y="0" width="60" height="12" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" backcolor="#C0C0C0" uuid="62a0c85a-3722-4116-b7b9-926918f62a01"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalBrutoOutubro}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement mode="Opaque" x="622" y="12" width="60" height="12" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" backcolor="#C0C0C0" uuid="14004c69-0d03-49ef-97cb-3a02130189e7"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalDescontosOutubro}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement mode="Opaque" x="622" y="24" width="60" height="12" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" backcolor="#C0C0C0" uuid="ba88609e-d9ad-49f1-a9cf-33217cfef2de"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalLiquidoOutubro}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement mode="Opaque" x="682" y="0" width="60" height="12" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" backcolor="#C0C0C0" uuid="9cb03dfa-6ddf-4002-94e7-0a2e3ebed701"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalBrutoNovembro}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement mode="Opaque" x="682" y="12" width="60" height="12" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" backcolor="#C0C0C0" uuid="e1577e36-8a75-4c07-9d98-b796bfcaa58e"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalDescontosNovembro}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement mode="Opaque" x="682" y="24" width="60" height="12" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" backcolor="#C0C0C0" uuid="5b3668cb-**************-1eb7119a927f"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalLiquidoNovembro}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement mode="Opaque" x="742" y="0" width="60" height="12" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" backcolor="#C0C0C0" uuid="60756c7c-3340-458b-9c7f-b280713057f0"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalBrutoDezembro}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement mode="Opaque" x="742" y="12" width="60" height="12" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" backcolor="#C0C0C0" uuid="547944e8-de98-4690-b798-4a6a97fe4f40"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalDescontosDezembro}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement mode="Opaque" x="742" y="24" width="60" height="12" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" backcolor="#C0C0C0" uuid="7b926f6f-e706-43d4-a29f-cfaac4bad3a0"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalLiquidoDezembro}]]></textFieldExpression>
			</textField>
		</band>
	</columnFooter>
</jasperReport>
