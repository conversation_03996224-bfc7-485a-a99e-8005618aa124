<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.6.0.final using JasperReports Library version 6.6.0  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="subRelatorioVinculo1Semestre" pageWidth="802" pageHeight="595" orientation="Landscape" whenNoDataType="BlankPage" columnWidth="802" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isFloatColumnFooter="true" isIgnorePagination="true" uuid="71ab11e6-6b7f-4129-9c55-2019cf5c7310">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="sicap"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w1" value="685"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w2" value="304"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="175"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="818"/>
	<parameter name="idBeneficiario" class="java.lang.String"/>
	<parameter name="idEntidadeCjur" class="java.lang.String"/>
	<parameter name="cpf" class="java.lang.String"/>
	<parameter name="ano" class="java.lang.String"/>
	<queryString language="SQL">
		<![CDATA[SELECT 
	   r.codigo,
	   r.descricao AS nomeVerba,
       r.ano,
       r.folha,
       r.natureza,
       SUM(r.janeiro) AS janeiro,
       SUM(r.fevereiro) AS fevereiro,
       SUM(r.marco) AS marco,
       SUM(r.abril) AS abril,
       SUM(r.maio) AS maio,
       SUM(r.junho) AS junho,
       (SUM(r.janeiro) + SUM(r.fevereiro) + SUM(r.marco) + SUM(r.abril) + SUM(r.maio) + SUM(r.junho)) as totalSemestre
FROM (SELECT
	    v.codigo, 
		vcc.idContraCheque, 
        v.descricao, 
        cc.ano,
        v.natureza, 
        CASE WHEN cc.mes = 1 THEN vcc.valor ELSE 0 END AS janeiro, 
        CASE WHEN cc.mes = 2 THEN vcc.valor ELSE 0 END AS fevereiro, 
        CASE WHEN cc.mes = 3 THEN vcc.valor ELSE 0 END AS marco, 
        CASE WHEN cc.mes = 4 THEN vcc.valor ELSE 0 END AS abril, 
        CASE WHEN cc.mes = 5 THEN vcc.valor ELSE 0 END AS maio, 
        CASE WHEN cc.mes = 6 THEN vcc.valor ELSE 0 END AS junho, 
        tf.descricao as folha 
	 FROM ContraCheque cc 
     INNER JOIN VerbasContraCheque vcc ON vcc.idContraCheque = cc.id 
     INNER JOIN TipoFolha tf ON vcc.idTipoFolha = tf.id 
     INNER JOIN Verba v ON vcc.idVerba = v.id 
     INNER JOIN Beneficiario b ON cc.idBeneficiario = b.id 
     INNER JOIN CadastroUnico cadu ON b.idCadastroUnico = cadu.id 
     WHERE cc.ano = $P{ano} AND cadu.cpf = $P{cpf} AND cc.idEntidadeCjur = $P{idEntidadeCjur} AND cc.idBeneficiario = $P{idBeneficiario} AND cc.mes IN(1, 2, 3, 4, 5, 6) ) r
GROUP BY 
	r.codigo,
	r.descricao,
    r.ano,
    r.folha,
    r.natureza
ORDER BY 
	r.natureza, r.folha, r.descricao asc]]>
	</queryString>
	<field name="codigo" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="codigo"/>
	</field>
	<field name="nomeVerba" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="nomeVerba"/>
	</field>
	<field name="ano" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.label" value="ano"/>
	</field>
	<field name="folha" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="folha"/>
	</field>
	<field name="natureza" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="natureza"/>
	</field>
	<field name="janeiro" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.label" value="janeiro"/>
	</field>
	<field name="fevereiro" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.label" value="fevereiro"/>
	</field>
	<field name="marco" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.label" value="marco"/>
	</field>
	<field name="abril" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.label" value="abril"/>
	</field>
	<field name="maio" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.label" value="maio"/>
	</field>
	<field name="junho" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.label" value="junho"/>
	</field>
	<field name="totalSemestre" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.label" value="totalSemestre"/>
	</field>
	<variable name="totalBrutoJaneiro" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{natureza}.equals("C") ? $F{janeiro} : new BigDecimal(0)]]></variableExpression>
	</variable>
	<variable name="totalBrutoFevereiro" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{natureza}.equals("C") ? $F{fevereiro} : new BigDecimal(0)]]></variableExpression>
	</variable>
	<variable name="totalBrutoMarco" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{natureza}.equals("C") ? $F{marco} : new BigDecimal(0)]]></variableExpression>
	</variable>
	<variable name="totalBrutoAbril" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{natureza}.equals("C") ? $F{abril} : new BigDecimal(0)]]></variableExpression>
	</variable>
	<variable name="totalBrutoMaio" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{natureza}.equals("C") ? $F{maio} : new BigDecimal(0)]]></variableExpression>
	</variable>
	<variable name="totalBrutoJunho" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{natureza}.equals("C") ? $F{junho} : new BigDecimal(0)]]></variableExpression>
	</variable>
	<variable name="totalDescontosJaneiro" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{natureza}.equals("D") ? $F{janeiro} : new BigDecimal(0)]]></variableExpression>
	</variable>
	<variable name="totalLiquidoJaneiro" class="java.math.BigDecimal">
		<variableExpression><![CDATA[$V{totalBrutoJaneiro}.subtract($V{totalDescontosJaneiro})]]></variableExpression>
	</variable>
	<variable name="totalDescontosFevereiro" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{natureza}.equals("D") ? $F{fevereiro} : new BigDecimal(0)]]></variableExpression>
	</variable>
	<variable name="totalLiquidoFevereiro" class="java.math.BigDecimal">
		<variableExpression><![CDATA[$V{totalBrutoFevereiro}.subtract($V{totalDescontosFevereiro})]]></variableExpression>
	</variable>
	<variable name="totalDescontosMarco" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{natureza}.equals("D") ? $F{marco} : new BigDecimal(0)]]></variableExpression>
	</variable>
	<variable name="totalLiquidoMarco" class="java.math.BigDecimal">
		<variableExpression><![CDATA[$V{totalBrutoMarco}.subtract($V{totalDescontosMarco})]]></variableExpression>
	</variable>
	<variable name="totalDescontosAbril" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{natureza}.equals("D") ? $F{abril} : new BigDecimal(0)]]></variableExpression>
	</variable>
	<variable name="totalLiquidoAbril" class="java.math.BigDecimal">
		<variableExpression><![CDATA[$V{totalBrutoAbril}.subtract($V{totalDescontosAbril})]]></variableExpression>
	</variable>
	<variable name="totalDescontosMaio" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{natureza}.equals("D") ? $F{maio} : new BigDecimal(0)]]></variableExpression>
	</variable>
	<variable name="totalLiquidoMaio" class="java.math.BigDecimal">
		<variableExpression><![CDATA[$V{totalBrutoMaio}.subtract($V{totalDescontosMaio})]]></variableExpression>
	</variable>
	<variable name="totalDescontosJunho" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{natureza}.equals("D") ? $F{junho} : new BigDecimal(0)]]></variableExpression>
	</variable>
	<variable name="totalLiquidoJunho" class="java.math.BigDecimal">
		<variableExpression><![CDATA[$V{totalBrutoJunho}.subtract($V{totalDescontosJunho})]]></variableExpression>
	</variable>
	<columnHeader>
		<band height="24" splitType="Stretch">
			<staticText>
				<reportElement positionType="Float" mode="Opaque" x="0" y="12" width="34" height="12" backcolor="#C0C0C0" uuid="2826136c-2536-40e2-985e-5b35d4ab9543"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Código]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Opaque" x="34" y="12" width="259" height="12" backcolor="#C0C0C0" uuid="1aa460df-c0fa-41f5-b69f-3dc160d01200"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Verba]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Opaque" x="293" y="12" width="43" height="12" backcolor="#C0C0C0" uuid="516307f0-2382-443e-9e10-253d2fa1d167"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Natureza]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Opaque" x="336" y="12" width="105" height="12" backcolor="#C0C0C0" uuid="593ce1d7-64fa-4d24-83b4-73905529cff5"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Tipo de Folha]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Opaque" x="441" y="12" width="60" height="12" backcolor="#C0C0C0" uuid="9d490829-6f0b-4b4a-9502-9f08703cf6d0"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Janeiro]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Opaque" x="501" y="12" width="60" height="12" backcolor="#C0C0C0" uuid="2ebc8b21-3e3b-4119-9436-20de6662d99b"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Fevereiro]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Opaque" x="561" y="12" width="60" height="12" backcolor="#C0C0C0" uuid="9fb69c92-13f4-4b64-bafc-fa35fc06c2e6"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Março]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Opaque" x="621" y="12" width="60" height="12" backcolor="#C0C0C0" uuid="da58969a-2fd3-4e92-a9b6-68f4758805c4"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Abril]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Opaque" x="681" y="12" width="60" height="12" backcolor="#C0C0C0" uuid="11b3b743-2cda-40c6-acaa-5ed07eeb1844"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Maio]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Opaque" x="741" y="12" width="60" height="12" backcolor="#C0C0C0" uuid="db24deda-70c9-45df-91f3-0fdeb1334497"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Junho]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="0" y="0" width="802" height="12" isRemoveLineWhenBlank="true" backcolor="#C0C0C0" uuid="b50213aa-9433-4b6b-9f0e-97fe31f15872"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[1º Semestre]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="12" splitType="Stretch">
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="0" width="34" height="12" uuid="293448ce-2361-4a51-9d28-3f21894960cc">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="5af39805-7afb-4084-b6d9-17deaec087cc"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{codigo}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="34" y="0" width="259" height="12" uuid="19ea320b-09bc-4df9-943d-0f921997aedd">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="dc5e21ac-2d6a-44c6-b8a3-bcfa1ae15064"/>
				</reportElement>
				<box leftPadding="2">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{nomeVerba}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="293" y="0" width="43" height="12" uuid="d394abf8-f4a5-4e41-a0c0-f997b9038b27">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="ce7a9366-a052-4dee-a8ea-75cdd822136d"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{natureza}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="336" y="0" width="105" height="12" uuid="92dcd87b-4a9e-42fe-a2fc-78f5ad2e84a8">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="054e53e9-4c1b-44c0-a748-29f76640ba3a"/>
				</reportElement>
				<box leftPadding="2">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{folha}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement x="441" y="0" width="60" height="12" uuid="47f77c06-dd94-41a1-b7af-a61579692ea0">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="8ebca0aa-9674-42b9-b701-c4e60b975e8a"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{janeiro}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement x="501" y="0" width="60" height="12" uuid="6f6d7bf0-b561-4a39-bbbf-cea4c131e451">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="7e961961-9941-47e5-bd92-b454de5f956b"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{fevereiro}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement x="561" y="0" width="60" height="12" uuid="034c4d93-5ffa-427b-a4ba-d2a7ca0a2cb9">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="015c3908-3bce-42ac-9105-759a86de6fbd"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{marco}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement x="621" y="0" width="60" height="12" uuid="bec1266c-6756-40b7-bc08-67bd7e080a1e">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="d7ec3adf-27c4-4253-9a6c-2785e255464f"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{abril}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement x="681" y="0" width="60" height="12" uuid="66f5e501-c667-4caa-a667-0b887d3c80aa">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="db003992-c984-4d70-9d28-ea3ef9173483"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{maio}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement x="741" y="0" width="60" height="12" uuid="83d48aed-489b-4c84-8147-b856f95ec13b">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="c327ae6e-0659-4722-b075-09ecc4a99bd6"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{junho}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band height="36">
			<staticText>
				<reportElement positionType="Float" mode="Opaque" x="0" y="0" width="441" height="12" isRemoveLineWhenBlank="true" backcolor="#C0C0C0" uuid="e3c3ad8c-7103-4580-8e06-c9572842ca7c"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<text><![CDATA[Total Bruto]]></text>
			</staticText>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement mode="Opaque" x="501" y="0" width="60" height="12" backcolor="#C0C0C0" uuid="a1f2bfd9-8427-452c-9293-12b8ea08a586"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalBrutoFevereiro}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement mode="Opaque" x="561" y="0" width="60" height="12" backcolor="#C0C0C0" uuid="2b5becff-6864-448f-99bb-cb15714530fe"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalBrutoMarco}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement mode="Opaque" x="621" y="0" width="60" height="12" backcolor="#C0C0C0" uuid="7e8a6b8b-**************-bce3281fa9b6"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalBrutoAbril}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement mode="Opaque" x="681" y="0" width="60" height="12" backcolor="#C0C0C0" uuid="28bcd254-d654-4576-8b58-8b1fb3f13446"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalBrutoMaio}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement mode="Opaque" x="741" y="0" width="60" height="12" backcolor="#C0C0C0" uuid="f18f2371-0e46-4eca-9c8b-f9d3e0520042"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalBrutoJunho}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement mode="Opaque" x="441" y="0" width="60" height="12" backcolor="#C0C0C0" uuid="459000bb-9dac-4164-984e-2c77ccd4203d"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalBrutoJaneiro}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" mode="Opaque" x="0" y="12" width="441" height="12" isRemoveLineWhenBlank="true" backcolor="#C0C0C0" uuid="64678ea7-1992-47d6-8032-8f40d652cb11"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<text><![CDATA[Total de Descontos]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Opaque" x="0" y="24" width="441" height="12" isRemoveLineWhenBlank="true" backcolor="#C0C0C0" uuid="7653d019-fc38-4918-86bd-7cad80c04df4"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<text><![CDATA[Total de Líquido]]></text>
			</staticText>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement mode="Opaque" x="441" y="12" width="60" height="12" backcolor="#C0C0C0" uuid="ca719a74-93b7-4f59-a678-0bbdd2b943f3"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalDescontosJaneiro}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement mode="Opaque" x="441" y="24" width="60" height="12" backcolor="#C0C0C0" uuid="d8e2a78a-d71f-4a56-b316-c8f741dd81c3"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalLiquidoJaneiro}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement mode="Opaque" x="501" y="12" width="60" height="12" backcolor="#C0C0C0" uuid="988dd632-73c6-4fec-9f73-2d1bad4959f9"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalDescontosFevereiro}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement mode="Opaque" x="501" y="24" width="60" height="12" backcolor="#C0C0C0" uuid="ac950d27-1db1-4fc7-b816-c1e9bacbf00d"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalLiquidoFevereiro}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement mode="Opaque" x="561" y="12" width="60" height="12" backcolor="#C0C0C0" uuid="ad1374ca-12af-4eb3-8430-8cf6e47cf6d9"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalDescontosMarco}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement mode="Opaque" x="561" y="24" width="60" height="12" backcolor="#C0C0C0" uuid="643e7d72-4e14-468a-bec7-a35c7e69f00e"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalLiquidoMarco}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement mode="Opaque" x="621" y="12" width="60" height="12" backcolor="#C0C0C0" uuid="1b31b4b5-7223-4244-8c25-45aa119025ce"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalDescontosAbril}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement mode="Opaque" x="621" y="24" width="60" height="12" backcolor="#C0C0C0" uuid="ab8463bc-568b-4a32-b63e-6750a2f593a1"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalLiquidoAbril}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement mode="Opaque" x="681" y="12" width="60" height="12" backcolor="#C0C0C0" uuid="a75fc973-a840-4148-b5b8-b54f3db836e0"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalDescontosMaio}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement mode="Opaque" x="681" y="24" width="60" height="12" backcolor="#C0C0C0" uuid="89707f91-d622-4024-bcc5-8e4dd5740b29"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalLiquidoMaio}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement mode="Opaque" x="741" y="12" width="60" height="12" backcolor="#C0C0C0" uuid="07613848-d56c-43a5-851e-d885d190dd7f"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalDescontosJunho}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement mode="Opaque" x="741" y="24" width="60" height="12" backcolor="#C0C0C0" uuid="c4cc9eae-ab3f-4b70-964e-cfb2a3432baf"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{totalLiquidoJunho}]]></textFieldExpression>
			</textField>
		</band>
	</columnFooter>
</jasperReport>
