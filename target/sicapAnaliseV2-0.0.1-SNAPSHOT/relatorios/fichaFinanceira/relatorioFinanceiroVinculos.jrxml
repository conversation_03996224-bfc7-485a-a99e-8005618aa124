<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.6.0.final using JasperReports Library version 6.6.0  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorioFinanceiroVinculos" pageWidth="802" pageHeight="500" orientation="Landscape" whenNoDataType="BlankPage" columnWidth="802" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isSummaryNewPage="true" isFloatColumnFooter="true" uuid="cbf74d1b-5276-46f8-8b0f-d3eb1a991e26">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="319"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="673"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="sicap"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w1" value="513"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w2" value="476"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<parameter name="cpf" class="java.lang.String"/>
	<parameter name="ano" class="java.lang.Integer"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String"/>
	<queryString language="SQL">
		<![CDATA[SELECT
	cc.idBeneficiario,
    cc.idCadastroUnico,
	e.idEntidadeCjur,
    e.nome AS nomeEntidade,
	b.matricula,
	c.nome AS nomeCargo,
    c.tipo, 
    c.cargaHorariaMensal,
    (c.cargaHorariaMensal / 4) AS cargaHorariaSemanal,
	vf.dataAdmissao,
    tv.descricao AS tipoVinculo,    
    CASE vf.tipoServidor WHEN 1 THEN 'Civil' WHEN 2 THEN 'Militar' END AS tipoServidor,
    CASE vf.regimePrevidenciario WHEN 1 THEN 'RGPS' WHEN 2 THEN 'RPPS' END AS regimePrevidenciario,
	hf.dataOcorrencia AS historicoFuncionalDataOcorrencia,
    sf.descricao AS situacaoFuncional,
    ul.nome AS unidade,
    (m.nome + ' - ' + u.nome) AS cidade
FROM ContraCheque cc
LEFT JOIN CadastroUnico cadu ON cadu.id = cc.idCadastroUnico
LEFT JOIN Entidade e ON e.idEntidadeCjur = cc.idEntidadeCjur
LEFT JOIN Beneficiario b ON b.id = cc.idBeneficiario
LEFT JOIN VinculoFuncional vf ON vf.idBeneficiario = b.id AND vf.registroAtivo = 1
LEFT JOIN Cargo c ON c.id = vf.idCargo
LEFT JOIN TipoVinculo tv ON tv.id = vf.idTipoVinculo
LEFT JOIN (SELECT MAX(hf.id) as id, hf.idBeneficiario 
		   FROM HistoricoFuncional hf
           GROUP BY hf.idBeneficiario) historico ON historico.idBeneficiario = b.id
LEFT JOIN HistoricoFuncional hf ON hf.id = historico.id      
LEFT JOIN SituacaoFuncional sf ON sf.id = hf.idSituacaoFuncional
LEFT JOIN 
     (SELECT ccMes.idBeneficiario, ccMes.ano AS ano, MAX(ccMes.mes) AS mes
      FROM ContraCheque ccMes
      GROUP BY ccMes.idBeneficiario, ccMes.ano) maxMesContraCheque 
      ON maxMesContraCheque.idBeneficiario = cc.idBeneficiario AND maxMesContraCheque.ano = cc.ano
      
LEFT JOIN (SELECT cc.idBeneficiario, cc.ano, cc.mes, cc.idUnidadeLotacao
		   FROM ContraCheque cc
           GROUP BY cc.idBeneficiario, cc.ano, cc.mes, cc.idUnidadeLotacao) ccLotacao 
           ON ccLotacao.idBeneficiario = cc.idBeneficiario AND ccLotacao.mes = maxMesContraCheque.mes AND ccLotacao.ano = cc.ano
           
LEFT JOIN UnidadeLotacao ul ON ul.id = ccLotacao.idUnidadeLotacao 
LEFT JOIN Municipio m ON m.id = ul.idMunicipio
LEFT JOIN Uf u ON u.id = m.idUf
WHERE cadu.cpf = $P{cpf} AND cc.ano = $P{ano} AND cc.situacaoBeneficiario <> 'P'
GROUP BY
	cc.idBeneficiario,
    cc.idCadastroUnico,
	e.idEntidadeCjur,
    e.nome,
	b.matricula,
	c.nome,
    c.tipo, 
    c.cargaHorariaMensal,
    (c.cargaHorariaMensal / 4),
	vf.dataAdmissao,
    tv.descricao,    
    CASE vf.tipoServidor WHEN 1 THEN 'Civil' WHEN 2 THEN 'Militar' END,
    CASE vf.regimePrevidenciario WHEN 1 THEN 'RGPS' WHEN 2 THEN 'RPPS' END,
	hf.dataOcorrencia,
    sf.descricao,
    ul.nome,
    (m.nome + ' - ' + u.nome)
ORDER BY e.nome]]>
	</queryString>
	<field name="idBeneficiario" class="java.lang.Long">
		<property name="com.jaspersoft.studio.field.label" value="idBeneficiario"/>
	</field>
	<field name="idCadastroUnico" class="java.lang.Long">
		<property name="com.jaspersoft.studio.field.label" value="idCadastroUnico"/>
	</field>
	<field name="idEntidadeCjur" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.label" value="idEntidadeCjur"/>
	</field>
	<field name="nomeEntidade" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="nomeEntidade"/>
	</field>
	<field name="matricula" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.label" value="matricula"/>
	</field>
	<field name="nomeCargo" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="nomeCargo"/>
	</field>
	<field name="tipo" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.label" value="tipo"/>
	</field>
	<field name="cargaHorariaMensal" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.label" value="cargaHorariaMensal"/>
	</field>
	<field name="cargaHorariaSemanal" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.label" value="cargaHorariaSemanal"/>
	</field>
	<field name="dataAdmissao" class="java.util.Date">
		<property name="com.jaspersoft.studio.field.label" value="dataAdmissao"/>
	</field>
	<field name="tipoVinculo" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="tipoVinculo"/>
	</field>
	<field name="tipoServidor" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="tipoServidor"/>
	</field>
	<field name="regimePrevidenciario" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="regimePrevidenciario"/>
	</field>
	<field name="historicoFuncionalDataOcorrencia" class="java.util.Date">
		<property name="com.jaspersoft.studio.field.label" value="historicoFuncionalDataOcorrencia"/>
	</field>
	<field name="situacaoFuncional" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="situacaoFuncional"/>
	</field>
	<field name="unidade" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="unidade"/>
	</field>
	<field name="cidade" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="cidade"/>
	</field>
	<columnHeader>
		<band height="1" splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="130" splitType="Stretch">
			<frame>
				<reportElement x="0" y="0" width="802" height="90" uuid="1903a36c-be7c-4b41-91f0-74928161e7d5"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField isBlankWhenNull="true">
					<reportElement mode="Opaque" x="0" y="0" width="53" height="15" backcolor="#C0C0C0" uuid="5ccafd41-53ec-45e5-897f-4c934c75d055"/>
					<box leftPadding="5">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Vínculo " + $V{REPORT_COUNT} + ": "]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement mode="Opaque" x="53" y="0" width="749" height="15" backcolor="#C0C0C0" uuid="5ec5757c-7196-4297-abc1-657b03d80fc0"/>
					<box leftPadding="5">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{nomeEntidade}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="49" y="20" width="70" height="12" uuid="21590ebe-7db3-4d82-89b1-3fdba3dd4465"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{matricula}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="1" y="20" width="47" height="12" uuid="3d52d7e3-a362-4ddc-99d1-1ddef2f69cf1"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Matricula:]]></text>
				</staticText>
				<textField isBlankWhenNull="true">
					<reportElement x="177" y="20" width="453" height="12" uuid="26137b61-d679-4277-8299-610ad5e22152"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{nomeCargo}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="146" y="20" width="30" height="12" uuid="b5b66533-f94a-4c8a-8e47-0684dab40fb3"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Cargo:]]></text>
				</staticText>
				<staticText>
					<reportElement x="642" y="20" width="96" height="12" uuid="2ef9560f-d783-4671-8120-0423f17b60a1"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Carga Horária Mensal:]]></text>
				</staticText>
				<textField isBlankWhenNull="true">
					<reportElement x="739" y="20" width="58" height="12" uuid="944fe979-5b96-45e5-8258-edfc8da6e97c"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{cargaHorariaMensal}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="1" y="37" width="47" height="12" uuid="31ee7847-6f7f-449c-945c-b8055fb627d8"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Admissão:]]></text>
				</staticText>
				<textField pattern="dd/MM/Y" isBlankWhenNull="true">
					<reportElement x="49" y="37" width="70" height="12" uuid="1baffd4e-**************-ebc52740f9eb"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{dataAdmissao}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="146" y="37" width="70" height="12" uuid="335b3299-ae83-40d4-9627-6e96718aa50f"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Tipo de Vínculo:]]></text>
				</staticText>
				<textField isBlankWhenNull="true">
					<reportElement x="217" y="37" width="413" height="12" uuid="2f9ed1a4-0b08-4696-9f6d-1f041f5581cc"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{tipoVinculo}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="635" y="37" width="103" height="12" uuid="97e76b55-ff33-4c83-992d-2e79fbbd73ab"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Carga Horária Semanal:]]></text>
				</staticText>
				<textField isBlankWhenNull="true">
					<reportElement x="739" y="37" width="58" height="12" uuid="1d00eb5c-0d1a-4b36-b6d7-274335ccca8a"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{cargaHorariaSemanal}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="1" y="54" width="101" height="12" uuid="80d3dc93-29bb-450b-a05d-a2612ae4040d"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Regime Previdenciário:]]></text>
				</staticText>
				<textField isBlankWhenNull="true">
					<reportElement x="103" y="54" width="39" height="12" uuid="67fdeee1-3507-42be-bdfe-01aa5f6b7f2b"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{regimePrevidenciario}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="146" y="54" width="62" height="12" uuid="c27e7875-1c88-4f39-9564-c8042e779a42"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Tipo Servidor:]]></text>
				</staticText>
				<textField isBlankWhenNull="true">
					<reportElement x="209" y="54" width="60" height="12" uuid="bd849a02-01b4-401d-9954-ef07786c2b39"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{tipoServidor}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="300" y="54" width="111" height="12" uuid="2b1c249a-e0ec-4252-ba4f-aa3ac9440879"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Útima situação funcional:]]></text>
				</staticText>
				<textField isBlankWhenNull="true">
					<reportElement x="412" y="54" width="218" height="12" uuid="0204b269-0424-489d-9151-3dd752d6fe62"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{situacaoFuncional}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="636" y="54" width="102" height="12" uuid="e9e6b593-2ed8-4d4f-bfc0-0fd80eee085a"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Data da movimentação:]]></text>
				</staticText>
				<textField isBlankWhenNull="true">
					<reportElement x="739" y="54" width="58" height="12" uuid="9f6bc8c8-c0a4-4e80-8193-8b8d84c2d938"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{historicoFuncionalDataOcorrencia}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="1" y="71" width="118" height="12" uuid="90c46f72-9f0e-4c2a-b221-72e600f975b2"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Útima Unidade de Lotação:]]></text>
				</staticText>
				<textField isBlankWhenNull="true">
					<reportElement x="120" y="71" width="338" height="12" uuid="f3e3ed79-7e19-4cf6-b7a3-639b59bcd826"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{unidade}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="490" y="71" width="35" height="12" uuid="d507b156-c048-407e-a719-649a66736400"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Cidade:]]></text>
				</staticText>
				<textField isBlankWhenNull="true">
					<reportElement x="526" y="71" width="271" height="12" uuid="5d984694-d77f-4265-826a-6112b66f16ad"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{cidade}]]></textFieldExpression>
				</textField>
			</frame>
			<subreport>
				<reportElement x="0" y="96" width="802" height="20" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" uuid="062590fd-0f1f-4634-b1f4-a8b1f7de0e82"/>
				<subreportParameter name="idBeneficiario">
					<subreportParameterExpression><![CDATA[$F{idBeneficiario}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="idEntidadeCjur">
					<subreportParameterExpression><![CDATA[$F{idEntidadeCjur}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="cpf">
					<subreportParameterExpression><![CDATA[$P{cpf}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="ano">
					<subreportParameterExpression><![CDATA[$P{ano}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "subRelatorioVinculo1Semestre.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="38" splitType="Stretch">
			<subreport>
				<reportElement x="0" y="0" width="802" height="20" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" uuid="52ecc732-6f61-4984-a158-eab57115c5d5"/>
				<subreportParameter name="idBeneficiario">
					<subreportParameterExpression><![CDATA[$F{idBeneficiario}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="idEntidadeCjur">
					<subreportParameterExpression><![CDATA[$F{idEntidadeCjur}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="ano">
					<subreportParameterExpression><![CDATA[$P{ano}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="cpf">
					<subreportParameterExpression><![CDATA[$P{cpf}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "subRelatorioVinculo2Semestre.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
</jasperReport>
