<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:f="http://xmlns.jcp.org/jsf/core"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:p="http://primefaces.org/ui"
	xmlns:pe="http://primefaces.org/ui/extensions"
	xmlns:fn="http://xmlns.jcp.org/jsp/jstl/functions"
	xmlns:c="http://java.sun.com/jsp/jstl/core"
	template="/resources/template/template.xhtml">

	<ui:define name="content">
		<style type="text/css">
.ui-row-editor .ui-icon-pencil {
	margin-left: 12px;
}

span.ui-row-editor {
	display: inline-block !important;
}
</style>

		<h:form id="frmServidoresMultiplosVinculos" prependId="false">
			<p:fieldset legend="Dados para a consulta de Servidores com Múltiplos Vínculos">

				<div class="ui-g">
					<div class="ui-g-12 ui-md-6 ui-lg-4">
						<h:panelGroup>
							<h:outputText value="Competência: " styleClass="FontBold" />
							<p:selectOneMenu value="#{servidoresMultiplosVinculosBean.competencia}">
								<f:selectItem itemLabel="Todas" itemValue="#{0}" />
								<f:selectItems value="#{servidoresMultiplosVinculosBean.listaCompetencias}"
									var="competencia"
									itemLabel="#{competencia[2]}/#{competencia[0]}"
									itemValue="#{competencia[1]}/#{competencia[0]}" />
							</p:selectOneMenu>
						</h:panelGroup>
					</div>
					<div class="ui-g-12 ui-md-6 ui-lg-4">
						<h:panelGroup>
							<h:outputText value="Entidade: " styleClass="FontBold" />
							<p:selectOneMenu value="#{servidoresMultiplosVinculosBean.entidade}" filter="true"
								autoWidth="false" filterMatchMode="contains">
								<f:selectItem itemLabel="Todas" itemValue="#{0}" />
								<f:selectItems value="#{servidoresMultiplosVinculosBean.listaEntidades}"
									var="entidade" itemLabel="#{entidade.nome}"
									itemValue="#{entidade.idEntidadeCjur}" />
							</p:selectOneMenu>
						</h:panelGroup>
					</div>
					<div class="ui-g-12 ui-md-6 ui-lg-4">
						<h:panelGroup>
							<h:outputText value="Mínimo de Vínculos: " styleClass="FontBold" />
							<p:selectOneMenu value="#{servidoresMultiplosVinculosBean.minimoVinculos}">
								<f:selectItem itemLabel="3 ou mais" itemValue="3" />
								<f:selectItem itemLabel="4 ou mais" itemValue="4" />
								<f:selectItem itemLabel="5 ou mais" itemValue="5" />
								<f:selectItem itemLabel="6 ou mais" itemValue="6" />
							</p:selectOneMenu>
						</h:panelGroup>
					</div>
				</div>
				<div class="ui-g">
					<div class="ui-g-12 ui-md-6 ui-lg-4">
						<h:panelGroup>
							<h:outputText value="CPF: " styleClass="FontBold" />
							<p:inputText value="#{servidoresMultiplosVinculosBean.cpf}" />
						</h:panelGroup>
					</div>
					<div class="ui-g-12 ui-md-6 ui-lg-4">
						<h:panelGroup>
							<h:outputText value="Nome: " styleClass="FontBold" />
							<p:inputText value="#{servidoresMultiplosVinculosBean.nome}" />
						</h:panelGroup>
					</div>
				</div>

				<div class="ui-g">
					<div class="ui-g-12">
						<p:commandButton value="Pesquisar" style="width: auto;"
							icon="fa fa-fw fa-search white"
							actionListener="#{servidoresMultiplosVinculosBean.pesquisar()}"
							update="masterDetail" />
					</div>
				</div>
			</p:fieldset>

			<div class="EmptyBox10" />

			<p:fieldset legend="Resultados da consulta">
				<pe:masterDetail id="masterDetail"
					level="#{servidoresMultiplosVinculosBean.currentLevel}"
					showAllBreadcrumbItems="true">

					<f:facet name="header">
						<p:messages showDetail="true" showSummary="true" />
					</f:facet>

					<pe:masterDetailLevel id="detailInicio" level="1">
						<f:facet name="label">
							<h:outputText value="Início" />
						</f:facet>

						<p:dataTable id="tblServidoresMultiplosVinculos" var="servidor" widgetVar="tblServidoresMultiplosVinculosVar"
							rowKey="#{servidor.cpf}" tableStyle="table-layout: auto;"
							reflow="true"
							value="#{servidoresMultiplosVinculosBean.listaServidores}"
							sortBy="#{servidor.quantidadeVinculos}" sortOrder="descending"
							emptyMessage="Nenhum servidor encontrado." rows="15"
							paginatorAlwaysVisible="false" paginator="true"
							paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
							rowsPerPageTemplate="15,20,30,40,50,100,300,500"
							currentPageReportTemplate="página {currentPage} de {totalPages}"
							paginatorPosition="bottom">

							<p:column headerText="CPF" width="10%" styleClass="TexAlCenter">
								<h:outputText value="#{servidor.cpf}">
									<f:converter converterId="converter.CpfConverter" />
								</h:outputText>
							</p:column>

							<p:column headerText="Nome" width="25%"
								sortBy="#{servidor.nome}">
								<p:commandLink value="#{servidor.nome}" update="@form">
									<f:setPropertyActionListener value="#{servidor}"
										target="#{servidoresMultiplosVinculosBean.servidorSelecionado}" />
									<pe:selectDetailLevel contextValue="#{servidor}" />
								</p:commandLink>
							</p:column>

							<p:column headerText="Competência" width="7%"
								styleClass="TexAlCenter">
								<h:outputText value="#{servidor.mes}/#{servidor.ano}" />
							</p:column>

							<p:column headerText="Quantidade de Vínculos"
								styleClass="TexAlCenter"
								sortBy="#{servidor.quantidadeVinculos}">
								<h:outputText value="#{servidor.quantidadeVinculos}" />
							</p:column>

							<p:column headerText="Carga Horária Total"
								styleClass="TexAlCenter">
								<h:outputText value="#{servidor.cargaHorariaTotal}" />
							</p:column>

							<p:column headerText="Montante Total Proventos" width="12%">
								<h:outputText value="#{servidor.montanteProventos}"
									style="float:right">
									<f:convertNumber pattern="R$ ###,##0.00" locale="pt_br" />
								</h:outputText>
							</p:column>

							<p:column headerText="Quantidade Municípios"
								styleClass="TexAlCenter">
								<h:outputText value="#{servidor.quantidadeMunicipios}" />
							</p:column>

							<f:facet name="footer">
								<h:outputLabel
									value="Total de servidores: #{servidoresMultiplosVinculosBean.listaServidores.size()}" />
							</f:facet>
						</p:dataTable>
					</pe:masterDetailLevel>

					<pe:masterDetailLevel id="detailDadosServidor" level="2"
						contextVar="servidor">
						<f:facet name="label">
							<h:outputText value="Detalhes do Servidor" />
						</f:facet>

						<p:accordionPanel multiple="true" activeIndex="0,1,2">

							<p:tab title="Dados do Servidor">
								<h:panelGrid columns="2">
									<h:outputText value="CPF: " styleClass="Fs12 FontBold" />
									<h:outputText value="#{servidor.cpf}" styleClass="Fs12">
										<f:converter converterId="converter.CpfConverter" />
									</h:outputText>
									<h:outputText value="Nome: " styleClass="Fs12 FontBold" />
									<h:outputText value="#{servidor.nome}" styleClass="Fs12" />
									<h:outputText value="Competência: " styleClass="Fs12 FontBold" />
									<h:outputText value="#{servidor.mes}/#{servidor.ano}"
										styleClass="Fs12" />
									<h:outputText value="Quantidade de Vínculos: " styleClass="Fs12 FontBold" />
									<h:outputText value="#{servidor.quantidadeVinculos}" styleClass="Fs12" />
									<h:outputText value="Carga Horária Total: " styleClass="Fs12 FontBold" />
									<h:outputText value="#{servidor.cargaHorariaTotal}" styleClass="Fs12" />
									<h:outputText value="Montante Total: " styleClass="Fs12 FontBold" />
									<h:outputText value="#{servidor.montanteProventos}" styleClass="Fs12">
										<f:convertNumber pattern="R$ ###,##0.00" locale="pt_br" />
									</h:outputText>
								</h:panelGrid>
							</p:tab>

							<p:tab title="Vínculos">
								<p:dataTable id="tblVinculos" var="vinculo"
									widgetVar="tblVinculosVar" tableStyle="table-layout: auto"
									reflow="true"
									value="#{servidoresMultiplosVinculosBean.servidorSelecionado.vinculos}"
									emptyMessage="Nenhum vínculo encontrado." rows="20"
									paginator="true" paginatorAlwaysVisible="false"
									paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
									rowsPerPageTemplate="20,30,40,50,100"
									currentPageReportTemplate="página {currentPage} de {totalPages}"
									paginatorPosition="bottom">

									<p:column headerText="Entidade" width="25%"
										styleClass="TexAlCenter">
										<h:outputText value="#{vinculo.entidade}" />
									</p:column>

									<p:column headerText="Município" styleClass="TexAlCenter">
										<h:outputText value="#{vinculo.municipio}" />
									</p:column>

									<p:column headerText="Matrícula" styleClass="TexAlCenter">
										<h:outputText value="#{vinculo.matricula}" />
									</p:column>

									<p:column headerText="Cargo" styleClass="TexAlCenter">
										<h:outputText value="#{vinculo.cargo}" />
									</p:column>

									<p:column headerText="Tipo de Cargo" styleClass="TexAlCenter">
										<h:outputText value="#{vinculo.tipoCargo}" />
									</p:column>

									<p:column headerText="Carga Horária" styleClass="TexAlCenter">
										<h:outputText value="#{vinculo.cargaHorariaMensal}" />
									</p:column>

									<p:column headerText="Proventos" styleClass="TexAlCenter">
										<h:outputText value="#{vinculo.proventos}" style="float:right">
											<f:convertNumber pattern="R$ ###,##0.00" locale="pt_br" />
										</h:outputText>
									</p:column>

									<f:facet name="footer">
										<h:outputLabel
											value="Total de vínculos: #{servidoresMultiplosVinculosBean.servidorSelecionado.vinculos.size()}" />
									</f:facet>

								</p:dataTable>
							</p:tab>

							<p:tab title="Relatório">
								<h:panelGrid columns="1">
									<p:commandButton value="Gerar Relatório PDF"
										icon="fa fa-fw fa-file-pdf-o white"
										actionListener="#{servidoresMultiplosVinculosBean.gerarRelatorio()}"
										ajax="false" />
								</h:panelGrid>
							</p:tab>
						</p:accordionPanel>
						<div class="EmptyBox10" />
						<h:panelGrid columns="2">
							<p:commandButton value="Voltar" style="margin-top: 10px;"
								icon="ui-icon-arrowthick-1-w" process="@this" immediate="true">
								<pe:selectDetailLevel step="-1" />
							</p:commandButton>
						</h:panelGrid>

					</pe:masterDetailLevel>
				</pe:masterDetail>
			</p:fieldset>
			
		</h:form>

	</ui:define>
</ui:composition>
