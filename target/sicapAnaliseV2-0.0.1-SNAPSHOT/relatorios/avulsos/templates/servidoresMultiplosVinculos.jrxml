<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="servidoresMultiplosVinculos" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="4eedbb89-b4f6-4469-9ab6-f642a1688cf8">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<parameter name="COMPETENCIA" class="java.lang.String"/>
	<parameter name="ENTIDADE" class="java.lang.String"/>
	<parameter name="CPF" class="java.lang.String"/>
	<parameter name="NOME" class="java.lang.String"/>
	<parameter name="MINIMO_VINCULOS" class="java.lang.Integer"/>
	<parameter name="USUARIO" class="java.lang.String"/>
	<parameter name="ENTIDADE_USUARIO" class="java.lang.String"/>
	<queryString>
		<![CDATA[SELECT 
    a.cpf,
    a.nome,
    a.mes,
    a.ano,
    a.quantidadeVinculos,
    a.cargaHorariaTotal,
    a.montanteProventos,
    a.quantidadeMunicipiosLotacao
FROM auditoria.acumulacao a
WHERE a.quantidadeVinculos >= $P{MINIMO_VINCULOS}
ORDER BY a.quantidadeVinculos DESC, a.nome]]>
	</queryString>
	<field name="cpf" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="cpf"/>
	</field>
	<field name="nome" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="nome"/>
	</field>
	<field name="mes" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.label" value="mes"/>
	</field>
	<field name="ano" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.label" value="ano"/>
	</field>
	<field name="quantidadeVinculos" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.label" value="quantidadeVinculos"/>
	</field>
	<field name="cargaHorariaTotal" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.label" value="cargaHorariaTotal"/>
	</field>
	<field name="montanteProventos" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.label" value="montanteProventos"/>
	</field>
	<field name="quantidadeMunicipiosLotacao" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.label" value="quantidadeMunicipiosLotacao"/>
	</field>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="79" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="0" width="555" height="30" uuid="e622555d-23f3-4431-aed5-7ce0337a2b82"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="16" isBold="true"/>
				</textElement>
				<text><![CDATA[TRIBUNAL DE CONTAS DO ESTADO DO ACRE]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="30" width="555" height="25" uuid="b1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9e"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[RELATÓRIO DE SERVIDORES COM MÚLTIPLOS VÍNCULOS]]></text>
			</staticText>
			<textField>
				<reportElement x="0" y="55" width="555" height="20" uuid="c1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9e"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["Data de Geração: " + new java.text.SimpleDateFormat("dd/MM/yyyy HH:mm:ss").format(new java.util.Date())]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<pageHeader>
		<band height="75" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="0" width="80" height="15" uuid="d1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9e"/>
				<textElement>
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Competência:]]></text>
			</staticText>
			<textField>
				<reportElement x="80" y="0" width="100" height="15" uuid="e1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9e"/>
				<textElement>
					<font fontName="Arial" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{COMPETENCIA}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="200" y="0" width="60" height="15" uuid="f1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9e"/>
				<textElement>
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Entidade:]]></text>
			</staticText>
			<textField>
				<reportElement x="260" y="0" width="295" height="15" uuid="g1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9e"/>
				<textElement>
					<font fontName="Arial" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{ENTIDADE}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="15" width="80" height="15" uuid="h1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9e"/>
				<textElement>
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[CPF:]]></text>
			</staticText>
			<textField>
				<reportElement x="80" y="15" width="100" height="15" uuid="i1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9e"/>
				<textElement>
					<font fontName="Arial" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{CPF}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="200" y="15" width="60" height="15" uuid="j1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9e"/>
				<textElement>
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Nome:]]></text>
			</staticText>
			<textField>
				<reportElement x="260" y="15" width="295" height="15" uuid="k1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9e"/>
				<textElement>
					<font fontName="Arial" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{NOME}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="30" width="120" height="15" uuid="l1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9e"/>
				<textElement>
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Mínimo de Vínculos:]]></text>
			</staticText>
			<textField>
				<reportElement x="120" y="30" width="60" height="15" uuid="m1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9e"/>
				<textElement>
					<font fontName="Arial" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{MINIMO_VINCULOS}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="0" y="50" width="555" height="1" uuid="n1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9e"/>
			</line>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="30" splitType="Stretch">
			<staticText>
				<reportElement mode="Opaque" x="0" y="0" width="80" height="30" backcolor="#E6E6E6" uuid="o1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9e"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[CPF]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="80" y="0" width="150" height="30" backcolor="#E6E6E6" uuid="p1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9e"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Nome]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="230" y="0" width="50" height="30" backcolor="#E6E6E6" uuid="q1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9e"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Comp.]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="280" y="0" width="40" height="30" backcolor="#E6E6E6" uuid="r1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9e"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Qtd Vínculos]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="320" y="0" width="50" height="30" backcolor="#E6E6E6" uuid="s1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9e"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[CH Total]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="370" y="0" width="80" height="30" backcolor="#E6E6E6" uuid="t1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9e"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Montante Proventos]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="450" y="0" width="105" height="30" backcolor="#E6E6E6" uuid="u1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9e"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Qtd Municípios]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="20" splitType="Stretch">
			<textField>
				<reportElement x="0" y="0" width="80" height="20" uuid="v1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9e"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cpf}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="80" y="0" width="150" height="20" uuid="w1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9e"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{nome}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="230" y="0" width="50" height="20" uuid="x1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9e"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{mes} + "/" + $F{ano}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="280" y="0" width="40" height="20" uuid="y1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9e"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{quantidadeVinculos}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="320" y="0" width="50" height="20" uuid="z1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9e"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cargaHorariaTotal}]]></textFieldExpression>
			</textField>
			<textField pattern="¤#,##0.00">
				<reportElement x="370" y="0" width="80" height="20" uuid="a2581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9e"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{montanteProventos}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="450" y="0" width="105" height="20" uuid="b2581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9e"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{quantidadeMunicipiosLotacao}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<pageFooter>
		<band height="30" splitType="Stretch">
			<textField>
				<reportElement x="0" y="10" width="200" height="15" uuid="c2581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9e"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA["Usuário: " + $P{USUARIO}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="200" y="10" width="255" height="15" uuid="d2581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9e"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA["Entidade: " + $P{ENTIDADE_USUARIO}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="455" y="10" width="100" height="15" uuid="e2581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9e"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA["Página " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
</jasperReport>
