<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="compatibilidadeCH" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="4eedbb89-b4f6-4469-9ab6-f642a1688cf7">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<parameter name="COMPETENCIA" class="java.lang.String"/>
	<parameter name="ENTIDADE" class="java.lang.String"/>
	<parameter name="CPF" class="java.lang.String"/>
	<parameter name="NOME" class="java.lang.String"/>
	<parameter name="USUARIO" class="java.lang.String"/>
	<parameter name="ENTIDADE_USUARIO" class="java.lang.String"/>
	<queryString>
		<![CDATA[SELECT 
    a.cpf,
    a.nome,
    a.mes,
    a.ano,
    a.cargaHorariaTotal,
    (a.cargaHorariaTotal / 4.33) as cargaHorariaSemanal,
    CASE 
        WHEN (a.cargaHorariaTotal / 4.33) > 60 THEN 'INCOMPATÍVEL'
        WHEN (a.cargaHorariaTotal / 4.33) > 40 AND a.agentePolitico = 0 THEN 'ATENÇÃO'
        ELSE 'COMPATÍVEL'
    END as statusCompatibilidade
FROM auditoria.acumulacao a
WHERE (a.cargaHorariaTotal / 4.33) > 40
ORDER BY a.nome, a.ano DESC, a.mes DESC]]>
	</queryString>
	<field name="cpf" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="cpf"/>
	</field>
	<field name="nome" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="nome"/>
	</field>
	<field name="mes" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.label" value="mes"/>
	</field>
	<field name="ano" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.label" value="ano"/>
	</field>
	<field name="cargaHorariaTotal" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.label" value="cargaHorariaTotal"/>
	</field>
	<field name="cargaHorariaSemanal" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.label" value="cargaHorariaSemanal"/>
	</field>
	<field name="statusCompatibilidade" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="statusCompatibilidade"/>
	</field>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="79" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="0" width="555" height="30" uuid="e622555d-23f3-4431-aed5-7ce0337a2b81"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="16" isBold="true"/>
				</textElement>
				<text><![CDATA[TRIBUNAL DE CONTAS DO ESTADO DO ACRE]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="30" width="555" height="25" uuid="b1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9d"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[RELATÓRIO DE COMPATIBILIDADE DE CARGA HORÁRIA SEMANAL]]></text>
			</staticText>
			<textField>
				<reportElement x="0" y="55" width="555" height="20" uuid="c1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9d"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["Data de Geração: " + new java.text.SimpleDateFormat("dd/MM/yyyy HH:mm:ss").format(new java.util.Date())]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<pageHeader>
		<band height="60" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="0" width="80" height="15" uuid="d1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9d"/>
				<textElement>
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Competência:]]></text>
			</staticText>
			<textField>
				<reportElement x="80" y="0" width="100" height="15" uuid="e1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9d"/>
				<textElement>
					<font fontName="Arial" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{COMPETENCIA}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="200" y="0" width="60" height="15" uuid="f1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9d"/>
				<textElement>
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Entidade:]]></text>
			</staticText>
			<textField>
				<reportElement x="260" y="0" width="295" height="15" uuid="g1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9d"/>
				<textElement>
					<font fontName="Arial" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{ENTIDADE}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="15" width="80" height="15" uuid="h1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9d"/>
				<textElement>
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[CPF:]]></text>
			</staticText>
			<textField>
				<reportElement x="80" y="15" width="100" height="15" uuid="i1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9d"/>
				<textElement>
					<font fontName="Arial" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{CPF}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="200" y="15" width="60" height="15" uuid="j1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9d"/>
				<textElement>
					<font fontName="Arial" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Nome:]]></text>
			</staticText>
			<textField>
				<reportElement x="260" y="15" width="295" height="15" uuid="k1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9d"/>
				<textElement>
					<font fontName="Arial" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{NOME}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="0" y="35" width="555" height="1" uuid="l1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9d"/>
			</line>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="30" splitType="Stretch">
			<staticText>
				<reportElement mode="Opaque" x="0" y="0" width="80" height="30" backcolor="#E6E6E6" uuid="m1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9d"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[CPF]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="80" y="0" width="180" height="30" backcolor="#E6E6E6" uuid="n1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9d"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Nome]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="260" y="0" width="60" height="30" backcolor="#E6E6E6" uuid="o1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9d"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Competência]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="320" y="0" width="60" height="30" backcolor="#E6E6E6" uuid="p1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9d"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[CH Total]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="380" y="0" width="60" height="30" backcolor="#E6E6E6" uuid="q1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9d"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[CH Semanal]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="440" y="0" width="115" height="30" backcolor="#E6E6E6" uuid="r1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9d"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Status]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="20" splitType="Stretch">
			<textField>
				<reportElement x="0" y="0" width="80" height="20" uuid="s1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9d"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cpf}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="80" y="0" width="180" height="20" uuid="t1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9d"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{nome}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="260" y="0" width="60" height="20" uuid="u1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9d"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{mes} + "/" + $F{ano}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="320" y="0" width="60" height="20" uuid="v1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9d"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cargaHorariaTotal}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="380" y="0" width="60" height="20" uuid="w1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9d"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cargaHorariaSemanal}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="440" y="0" width="115" height="20" uuid="x1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9d"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{statusCompatibilidade}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<pageFooter>
		<band height="30" splitType="Stretch">
			<textField>
				<reportElement x="0" y="10" width="200" height="15" uuid="y1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9d"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA["Usuário: " + $P{USUARIO}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="200" y="10" width="255" height="15" uuid="z1581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9d"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA["Entidade: " + $P{ENTIDADE_USUARIO}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="455" y="10" width="100" height="15" uuid="a2581fe8-8c3a-4c3e-9c3e-2e7c5a7b8c9d"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA["Página " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
</jasperReport>
