<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:p="http://primefaces.org/ui"
	xmlns:pe="http://primefaces.org/ui/extensions"
	xmlns:fn="http://java.sun.com/jsp/jstl/functions"
	template="/resources/template/template.xhtml">

	<ui:define name="content">


		<h:form prependId="false">
			<p:messages id="msg" />
			<!-- 			<p:fieldset legend="Resumo da Folha"> -->
			<p:fieldset legend="Dados para a consulta por resumo de folha">

				<div class="ui-g">

					<div class="ui-g-12 ui-md-6 ui-lg-4">
						<h:panelGroup>
							<h:outputText value="Remessa: " styleClass="FontBold" />
							<p:selectOneMenu value="#{resumoFolhaBean.remessa.id}"
								required="true" requiredMessage="Escolha uma remessa.">
								<f:selectItem itemLabel="Selecione..." itemValue="#{null}"
									noSelectionOption="true" />
								<f:selectItems value="#{resumoFolhaBean.listaProcessadas}"
									var="remessa" itemLabel="#{remessa.competencia}"
									itemValue="#{remessa.id}" />
							</p:selectOneMenu>
						</h:panelGroup>
					</div>
				</div>
				<div class="ui-g">
					<div class="ui-g-12 ui-md-6 ui-lg-4">
						<h:panelGroup>
							<h:outputText value="Tipo de Folha: " styleClass="FontBold" />
							<p:selectOneMenu value="#{resumoFolhaBean.tipoFolha.id}">
								<f:selectItem itemLabel="Todas" itemValue="#{null}"
									noSelectionOption="true" />
								<f:selectItems value="#{resumoFolhaBean.listaTipoFolha}"
									var="tipoFolha" itemLabel="#{tipoFolha.descricao}"
									itemValue="#{tipoFolha.id}" />
							</p:selectOneMenu>

						</h:panelGroup>
					</div>
				</div>
				<div class="ui-g">
					<div class="ui-g-12">
						<p:commandButton value="Pesquisar" style="width: auto;"
							icon="fa fa-fw fa-search white"
							actionListener="#{resumoFolhaBean.pesquisar()}"
							update="msg resumoProcessamento" />
					</div>
				</div>

			</p:fieldset>
		</h:form>

		<div class="EmptyBox10"></div>

		<h:form prependId="false">
			<div class="OvAuto">
				<p:fieldset legend="Resultados da consulta">

					<p:tabView effect="drop" id="resumoProcessamento"
						widgetVar="resumoProcessamento">
						<p:tab title="Geral">
							<p:dataTable id="tabelaResumoProcessamentoGeral"
								value="#{resumoFolhaBean.resumoGeral}" var="resumo"
								emptyMessage="Nenhum registro encontrado." paginator="true"
								rows="10"
								paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown} {Exporters}"
								rowsPerPageTemplate="10,20,30,40,50,100,500"
								currentPageReportTemplate="página {currentPage} de {totalPages}"
								paginatorPosition="bottom" style="margin-bottom:20px">

								<f:facet name="header">
									<h:outputText value="Resumo da Folha Geral"
										styleClass="Fs16 white" />
								</f:facet>

								<f:facet name="{Exporters}">
									<h:commandLink style="float:right; padding:12px 6px"
										title="Exportar para o Excel">
										<p:graphicImage value="/resources/imagens/logoExcel.png"
											width="24" />
										<p:dataExporter type="xls"
											target="tabelaResumoProcessamentoGeral"
											fileName="resumoFolhaGeral" />
									</h:commandLink>
								</f:facet>

								<p:column headerText="Tipo de Folha">
									<h:outputText value="#{resumo[0]}" />
								</p:column>

								<p:column headerText="Situação Beneficiário"
									styleClass="TexAlCenter">
									<h:outputText value="#{resumo[1]}" />
								</p:column>

								<p:column headerText="Cargo">
									<h:outputText value="#{resumo[2]}" />
								</p:column>

								<p:column headerText="Tipo" sortBy="#{resumo[6]}"
									styleClass="TexAlRight">
									<h:outputText value="#{resumo[6]}" />
								</p:column>

								<p:column headerText="Quantidade" styleClass="TexAlRight">
									<h:outputText value="#{resumo[3]}" />
								</p:column>


								<p:column headerText="Total Descontos" styleClass="TexAlRight">
									<h:outputText value="#{resumo[4]}">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>

								<p:column headerText="Total Vencimentos" styleClass="TexAlRight">
									<h:outputText value="#{resumo[5]}">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>

								<p:columnGroup type="footer">
									<p:row>
										<p:column colspan="4" styleClass="TexAlRight fontBold"
											footerText="Total Geral:" />
										<p:column styleClass="TexAlRight fontBold">
											<f:facet name="footer">
												<h:outputText
													value="#{resumoFolhaBean.totalGeralQuantidade}" />
											</f:facet>
										</p:column>

										<p:column styleClass="TexAlRight fontBold">
											<f:facet name="footer">
												<h:outputText
													value="#{resumoFolhaBean.totalGeralDescontos}">
													<f:convertNumber type="currency" currencySymbol="R$" />
												</h:outputText>
											</f:facet>
										</p:column>

										<p:column styleClass="TexAlRight fontBold">
											<f:facet name="footer">
												<h:outputText
													value="#{resumoFolhaBean.totalGeralVencimentos}">
													<f:convertNumber type="currency" currencySymbol="R$" />
												</h:outputText>
											</f:facet>
										</p:column>
									</p:row>
								</p:columnGroup>

								<f:facet name="footer">
									<h:outputLabel
										value="Total de registros: #{resumoFolhaBean.resumoGeral.size()}" />
								</f:facet>

							</p:dataTable>

						</p:tab>
						<p:tab title="Tipo de Folha">
							<p:dataTable id="tabelaResumoProcessamentoTipoFolha"
								value="#{resumoFolhaBean.resumoPorTipoFolha}" var="resumo"
								emptyMessage="Nenhum registro encontrado." paginator="true"
								rows="10"
								paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown} {Exporters}"
								rowsPerPageTemplate="10,20,30,40,50,100,500"
								currentPageReportTemplate="página {currentPage} de {totalPages}"
								paginatorPosition="bottom" style="margin-bottom:20px">

								<f:facet name="header">
									<h:outputText value="Resumo da Folha por Tipo de Folha"
										styleClass="Fs16 white" />
								</f:facet>

								<f:facet name="{Exporters}">
									<h:commandLink style="float:right; padding:12px 6px"
										title="Exportar para o Excel">
										<p:graphicImage value="/resources/imagens/logoExcel.png"
											width="24" />
										<p:dataExporter type="xls"
											target="tabelaResumoProcessamentoTipoFolha"
											fileName="resumoFolhaPorTipoFolha" />
									</h:commandLink>
								</f:facet>

								<p:column headerText="Tipo de Folha">
									<h:outputText value="#{resumo[0]}" />
								</p:column>

								<p:column headerText="Situação Beneficiário"
									styleClass="TexAlCenter">
									<h:outputText value="#{resumo[4]}" />
								</p:column>

								<p:column headerText="Tipo" sortBy="#{resumo[4]}"
									styleClass="TexAlRight">
									<h:outputText value="#{resumo[5]}" />
								</p:column>

								<p:column headerText="Quantidade" styleClass="TexAlRight">
									<h:outputText value="#{resumo[1]}" />
								</p:column>

								<p:column headerText="Total Descontos" styleClass="TexAlRight">
									<h:outputText value="#{resumo[2]}">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>

								<p:column headerText="Total Vencimentos" styleClass="TexAlRight">
									<h:outputText value="#{resumo[3]}">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>

								<p:columnGroup type="footer">
									<p:row>
										<p:column colspan="3" styleClass="TexAlRight fontBold"
											footerText="Total Geral:" />

										<p:column styleClass="TexAlRight fontBold">
											<f:facet name="footer">
												<h:outputText
													value="#{resumoFolhaBean.totalTipoFolhaQuantidade}" />
											</f:facet>
										</p:column>

										<p:column styleClass="TexAlRight fontBold">
											<f:facet name="footer">
												<h:outputText
													value="#{resumoFolhaBean.totalTipoFolhaDescontos}">
													<f:convertNumber type="currency" currencySymbol="R$" />
												</h:outputText>
											</f:facet>
										</p:column>

										<p:column styleClass="TexAlRight fontBold">
											<f:facet name="footer">
												<h:outputText
													value="#{resumoFolhaBean.totalTipoFolhaVencimentos}">
													<f:convertNumber type="currency" currencySymbol="R$" />
												</h:outputText>
											</f:facet>
										</p:column>
									</p:row>
								</p:columnGroup>

								<f:facet name="footer">
									<h:outputLabel
										value="Total de registros: #{resumoFolhaBean.resumoPorTipoFolha.size()}" />
								</f:facet>


							</p:dataTable>
						</p:tab>
						<p:tab title="Tipo de Beneficiário">
							<p:dataTable id="tabelaResumoProcessamentoTipoBeneficiario"
								value="#{resumoFolhaBean.resumoPorSituacaoBeneficiario}"
								var="resumo" emptyMessage="Nenhum registro encontrado."
								paginator="true" rows="10"
								paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown} {Exporters}"
								rowsPerPageTemplate="10,20,30,40,50,100,500"
								currentPageReportTemplate="página {currentPage} de {totalPages}"
								paginatorPosition="bottom" style="margin-bottom:20px">

								<f:facet name="header">
									<h:outputText value="Resumo da Folha por Tipo de Beneficiário"
										styleClass="Fs16 white" />
								</f:facet>

								<f:facet name="{Exporters}">
									<h:commandLink style="float:right; padding:12px 6px"
										title="Exportar para o Excel">
										<p:graphicImage value="/resources/imagens/logoExcel.png"
											width="24" />
										<p:dataExporter type="xls"
											target="tabelaResumoProcessamentoTipoBeneficiario"
											fileName="resumoFolhaPorTipoBeneficiario" />
									</h:commandLink>
								</f:facet>

								<p:column headerText="Situação Beneficiário">
									<h:outputText value="#{resumo[0]}" />
								</p:column>

								<p:column headerText="Tipo" sortBy="#{resumo[4]}"
									styleClass="TexAlRight">
									<h:outputText value="#{resumo[4]}" />
								</p:column>

								<p:column headerText="Quantidade" styleClass="TexAlRight">
									<h:outputText value="#{resumo[1]}" />
								</p:column>

								<p:column headerText="Total Descontos" styleClass="TexAlRight">
									<h:outputText value="#{resumo[2]}">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>

								<p:column headerText="Total Vencimentos" styleClass="TexAlRight">
									<h:outputText value="#{resumo[3]}">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>

								<p:columnGroup type="footer">
									<p:row>
										<p:column colspan="2" styleClass="TexAlRight fontBold"
											footerText="Total Geral:" />
										<p:column styleClass="TexAlRight fontBold">
											<f:facet name="footer">
												<h:outputText
													value="#{resumoFolhaBean.totalTipoBeneficiarioQuantidade}" />
											</f:facet>
										</p:column>

										<p:column styleClass="TexAlRight fontBold">
											<f:facet name="footer">
												<h:outputText
													value="#{resumoFolhaBean.totalTipoBeneficiarioDescontos}">
													<f:convertNumber type="currency" currencySymbol="R$" />
												</h:outputText>
											</f:facet>
										</p:column>

										<p:column styleClass="TexAlRight fontBold">
											<f:facet name="footer">
												<h:outputText
													value="#{resumoFolhaBean.totalTipoBeneficiarioVencimentos}">
													<f:convertNumber type="currency" currencySymbol="R$" />
												</h:outputText>
											</f:facet>
										</p:column>
									</p:row>
								</p:columnGroup>

								<f:facet name="footer">
									<h:outputLabel
										value="Total de registros: #{resumoFolhaBean.resumoPorSituacaoBeneficiario.size()}" />
								</f:facet>

							</p:dataTable>
						</p:tab>
						<p:tab title="Por Cargo">
							<p:dataTable id="tabelaResumoProcessamentoPorCargo"
								value="#{resumoFolhaBean.resumoPorCargo}" var="resumo"
								emptyMessage="Nenhum registro encontrado." paginator="true"
								rows="10"
								paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown} {Exporters}"
								rowsPerPageTemplate="10,20,30,40,50,100,500"
								currentPageReportTemplate="página {currentPage} de {totalPages}"
								paginatorPosition="bottom">

								<f:facet name="header">
									<h:outputText value="Resumo da Folha por Cargos"
										styleClass="Fs16 white" />
								</f:facet>

								<f:facet name="{Exporters}">
									<h:commandLink style="float:right; padding:12px 6px"
										title="Exportar para o Excel">
										<p:graphicImage value="/resources/imagens/logoExcel.png"
											width="24" />
										<p:dataExporter type="xls"
											target="tabelaResumoProcessamentoPorCargo"
											fileName="resumoFolhaPorCargos" />
									</h:commandLink>
								</f:facet>

								<p:column headerText="Cargo">
									<h:outputText value="#{resumo[0]}" />
								</p:column>

								<p:column headerText="Tipo" sortBy="#{resumo[4]}"
									styleClass="TexAlRight">
									<h:outputText value="#{resumo[4]}" />
								</p:column>

								<p:column headerText="Quantidade" styleClass="TexAlRight">
									<h:outputText value="#{resumo[1]}" />
								</p:column>

								<p:column headerText="Total Descontos" styleClass="TexAlRight">
									<h:outputText value="#{resumo[2]}">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>

								<p:column headerText="Total Vencimentos" styleClass="TexAlRight">
									<h:outputText value="#{resumo[3]}">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>

								<p:columnGroup type="footer">
									<p:row>
										<p:column colspan="2" styleClass="TexAlRight fontBold"
											footerText="Total Geral:" />
										<p:column styleClass="TexAlRight fontBold">
											<f:facet name="footer">
												<h:outputText
													value="#{resumoFolhaBean.totalCargoQuantidade}" />
											</f:facet>
										</p:column>

										<p:column styleClass="TexAlRight fontBold">
											<f:facet name="footer">
												<h:outputText
													value="#{resumoFolhaBean.totalCargoDescontos}">
													<f:convertNumber type="currency" currencySymbol="R$" />
												</h:outputText>
											</f:facet>
										</p:column>

										<p:column styleClass="TexAlRight fontBold">
											<f:facet name="footer">
												<h:outputText
													value="#{resumoFolhaBean.totalCargoVencimentos}">
													<f:convertNumber type="currency" currencySymbol="R$" />
												</h:outputText>
											</f:facet>
										</p:column>
									</p:row>
								</p:columnGroup>

								<f:facet name="footer">
									<h:outputLabel
										value="Total de registros: #{resumoFolhaBean.resumoPorCargo.size()}" />
								</f:facet>

							</p:dataTable>
						</p:tab>
						<p:tab title="Por Verba">
							<p:dataTable id="tabelaResumoProcessamentoPorVerba"
								value="#{resumoFolhaBean.resumoPorVerba}" var="resumo"
								emptyMessage="Nenhum registro encontrado." paginator="true"
								rows="20"
								paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown} {Exporters}"
								rowsPerPageTemplate="20,30,40,50,100,500"
								currentPageReportTemplate="página {currentPage} de {totalPages}"
								paginatorPosition="bottom">

								<f:facet name="header">
									<h:outputText value="Resumo da Folha por Verbas"
										styleClass="Fs16 white" />
								</f:facet>

								<f:facet name="{Exporters}">
									<h:commandLink style="float:right; padding:12px 6px"
										title="Exportar para o Excel">
										<p:graphicImage value="/resources/imagens/logoExcel.png"
											width="24" />
										<p:dataExporter type="xls"
											target="tabelaResumoProcessamentoPorVerba"
											fileName="resumoFolhaPorVerbas" />
									</h:commandLink>
								</f:facet>


								<p:column headerText="Verba">
									<h:outputText value="#{resumo[0]}" />
								</p:column>

								<p:column headerText="Tipo" sortBy="#{resumo[3]}"
									styleClass="TexAlRight">
									<h:outputText value="#{resumo[3]}" />
								</p:column>

								<p:column headerText="Total Descontos" styleClass="TexAlRight">
									<h:outputText value="#{resumo[1]}">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>

								<p:column headerText="Total Vencimentos" styleClass="TexAlRight">
									<h:outputText value="#{resumo[2]}">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>

								<p:columnGroup type="footer">
									<p:row>
										<p:column colspan="2" styleClass="TexAlRight fontBold"
											footerText="Total Geral:" />
										<p:column styleClass="TexAlRight fontBold">
											<f:facet name="footer">
												<h:outputText
													value="#{resumoFolhaBean.totalVerbaDescontos}">
													<f:convertNumber type="currency" currencySymbol="R$" />
												</h:outputText>
											</f:facet>
										</p:column>
										<p:column styleClass="TexAlRight fontBold">
											<f:facet name="footer">
												<h:outputText
													value="#{resumoFolhaBean.totalVerbaVencimentos}">
													<f:convertNumber type="currency" currencySymbol="R$" />
												</h:outputText>
											</f:facet>
										</p:column>
									</p:row>
								</p:columnGroup>

								<f:facet name="footer">
									<h:outputLabel
										value="Total de registros: #{resumoFolhaBean.resumoPorVerba.size()}" />
								</f:facet>

							</p:dataTable>
						</p:tab>


						<p:tab title="Dados Anuais">

							<p:dataTable id="tabelaDadosFolhaAnual"
								value="#{resumoFolhaBean.dadosFolhaPagamentoAnual}" var="resumo"
								emptyMessage="Nenhum registro encontrado." paginator="true"
								rows="20"
								paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown} {Exporters}"
								rowsPerPageTemplate="20,30,40,50,100,500"
								currentPageReportTemplate="página {currentPage} de {totalPages}"
								paginatorPosition="bottom">

								<f:facet name="header">
									<h:outputText value="Resumo da Folha anual"
										styleClass="Fs16 white" />
								</f:facet>

								<f:facet name="{Exporters}">
									<h:commandLink style="float:right; padding:12px 6px"
										title="Exportar para o Excel">
										<p:graphicImage value="/resources/imagens/logoExcel.png"
											width="24" />
										<p:dataExporter type="xls" target="tabelaDadosFolhaAnual"
											fileName="resumoFolhaAnual" />
									</h:commandLink>
								</f:facet>

								<p:column headerText="Matrícula" footerText="Matrícula"
									styleClass="TexAlCenter" width="100">
									<h:outputText value="#{resumo[0]}" />
								</p:column>

								<p:column headerText="CPF" footerText="CPF"
									styleClass="TexAlCenter" width="100">
									<h:outputText value="#{resumo[1]}">
										<f:converter converterId="converter.CpfConverter" />
									</h:outputText>
								</p:column>

								<p:column headerText="Nome" footerText="Nome" width="250">
									<h:outputText value="#{resumo[2]}" />
								</p:column>

								<p:column headerText="Sexo" footerText="Sexo"
									styleClass="TexAlCenter" width="30">
									<h:outputText value="#{resumo[3]}" />
								</p:column>

								<p:column headerText="Data de Nascimento"
									footerText="Data de Nascimento" styleClass="TexAlCenter"
									width="80">
									<h:outputText value="#{resumo[4]}" />
								</p:column>

								<p:column headerText="Cargo" footerText="Cargo" width="200">
									<h:outputText value="#{resumo[5]}" />
								</p:column>

								<p:column headerText="Data Admissão/Inicio Pensão"
									footerText="Data Admissão/Inicio Pensão"
									styleClass="TexAlCenter" width="90">
									<h:outputText value="#{resumo[6]}" />
								</p:column>

								<p:column headerText="Situacão Funcional"
									footerText="Situacão Funcional" width="100">
									<h:outputText value="#{resumo[7]}" />
								</p:column>

								<p:column headerText="Data da Última Situacão Funcional"
									footerText="Data da Última Situacão Funcional" width="80">
									<h:outputText value="#{resumo[8]}" />
								</p:column>

								<p:column headerText="Tipo de Pensão"
									footerText="Tipo de Pensão" width="100">
									<h:outputText value="#{resumo[9]}" />
								</p:column>

								<p:column headerText="Ano" footerText="Ano" width="40">
									<h:outputText value="#{resumo[10]}" />
								</p:column>

								<p:column headerText="Janeiro" footerText="Janeiro" width="100">
									<h:outputText value="#{resumo[11]}" style="float: right;">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>

								<p:column headerText="Fevereiro" footerText="Fevereiro"
									width="100">
									<h:outputText value="#{resumo[12]}" style="float: right;">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>

								<p:column headerText="Março" footerText="Março" width="100">
									<h:outputText value="#{resumo[13]}" style="float: right;">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>

								<p:column headerText="Abril" footerText="Abril" width="100">
									<h:outputText value="#{resumo[14]}" style="float: right;">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>

								<p:column headerText="Maio" footerText="Maio" width="100">
									<h:outputText value="#{resumo[15]}" style="float: right;">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>

								<p:column headerText="Junho" footerText="Junho" width="100">
									<h:outputText value="#{resumo[16]}" style="float: right;">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>

								<p:column headerText="Julho" footerText="Julho" width="100">
									<h:outputText value="#{resumo[17]}" style="float: right;">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>

								<p:column headerText="Agosto" footerText="Agosto" width="100">
									<h:outputText value="#{resumo[18]}" style="float: right;">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>

								<p:column headerText="Setembro" footerText="Setembro"
									width="100">
									<h:outputText value="#{resumo[19]}" style="float: right;">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>

								<p:column headerText="Outubro" footerText="Outubro" width="100">
									<h:outputText value="#{resumo[20]}" style="float: right;">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>

								<p:column headerText="Novembro" footerText="Novembro"
									width="100">
									<h:outputText value="#{resumo[21]}" style="float: right;">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>

								<p:column headerText="Dezembro" footerText="Dezembro"
									width="100">
									<h:outputText value="#{resumo[22]}" style="float: right;">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>

								<p:column headerText="13º" footerText="Total" width="100">
									<h:outputText value="#{resumo[23]}" style="float: right;">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>

								<p:column headerText="Total" footerText="Total" width="100">
									<h:outputText value="#{resumo[24]}" style="float: right;">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>

								<p:columnGroup type="footer">
									<p:row>
										<p:column colspan="11" styleClass="TexAlRight fontBold"
											footerText="Total Geral:" />
										<p:column styleClass="TexAlRight fontBold">
											<f:facet name="footer">
												<h:outputText
													value="#{resumoFolhaBean.totalJaneiro}">
													<f:convertNumber type="currency" currencySymbol="R$" />
												</h:outputText>
											</f:facet>
										</p:column>
										<p:column styleClass="TexAlRight fontBold">
											<f:facet name="footer">
												<h:outputText
													value="#{resumoFolhaBean.totalFevereiro}">
													<f:convertNumber type="currency" currencySymbol="R$" />
												</h:outputText>
											</f:facet>
										</p:column>
										<p:column styleClass="TexAlRight fontBold">
											<f:facet name="footer">
												<h:outputText
													value="#{resumoFolhaBean.totalMarco}">
													<f:convertNumber type="currency" currencySymbol="R$" />
												</h:outputText>
											</f:facet>
										</p:column>
										<p:column styleClass="TexAlRight fontBold">
											<f:facet name="footer">
												<h:outputText
													value="#{resumoFolhaBean.totalAbril}">
													<f:convertNumber type="currency" currencySymbol="R$" />
												</h:outputText>
											</f:facet>
										</p:column>
										<p:column styleClass="TexAlRight fontBold">
											<f:facet name="footer">
												<h:outputText
													value="#{resumoFolhaBean.totalMaio}">
													<f:convertNumber type="currency" currencySymbol="R$" />
												</h:outputText>
											</f:facet>
										</p:column>
										<p:column styleClass="TexAlRight fontBold">
											<f:facet name="footer">
												<h:outputText
													value="#{resumoFolhaBean.totalJulho}">
													<f:convertNumber type="currency" currencySymbol="R$" />
												</h:outputText>
											</f:facet>
										</p:column>
										<p:column styleClass="TexAlRight fontBold">
											<f:facet name="footer">
												<h:outputText
													value="#{resumoFolhaBean.totalJulho}">
													<f:convertNumber type="currency" currencySymbol="R$" />
												</h:outputText>
											</f:facet>
										</p:column>
										<p:column styleClass="TexAlRight fontBold">
											<f:facet name="footer">
												<h:outputText
													value="#{resumoFolhaBean.totalAgosto}">
													<f:convertNumber type="currency" currencySymbol="R$" />
												</h:outputText>
											</f:facet>
										</p:column>
										<p:column styleClass="TexAlRight fontBold">
											<f:facet name="footer">
												<h:outputText
													value="#{resumoFolhaBean.totalSetembro}">
													<f:convertNumber type="currency" currencySymbol="R$" />
												</h:outputText>
											</f:facet>
										</p:column>
										<p:column styleClass="TexAlRight fontBold">
											<f:facet name="footer">
												<h:outputText
													value="#{resumoFolhaBean.totalOutubro}">
													<f:convertNumber type="currency" currencySymbol="R$" />
												</h:outputText>
											</f:facet>
										</p:column>
										<p:column styleClass="TexAlRight fontBold">
											<f:facet name="footer">
												<h:outputText
													value="#{resumoFolhaBean.totalNovembro}">
													<f:convertNumber type="currency" currencySymbol="R$" />
												</h:outputText>
											</f:facet>
										</p:column>
										<p:column styleClass="TexAlRight fontBold">
											<f:facet name="footer">
												<h:outputText
													value="#{resumoFolhaBean.totalDezembro}">
													<f:convertNumber type="currency" currencySymbol="R$" />
												</h:outputText>
											</f:facet>
										</p:column>
										<p:column styleClass="TexAlRight fontBold">
											<f:facet name="footer">
												<h:outputText
													value="#{resumoFolhaBean.totalDecimo}">
													<f:convertNumber type="currency" currencySymbol="R$" />
												</h:outputText>
											</f:facet>
										</p:column>
										<p:column styleClass="TexAlRight fontBold">
											<f:facet name="footer">
												<h:outputText
													value="#{resumoFolhaBean.totalGeral}">
													<f:convertNumber type="currency" currencySymbol="R$" />
												</h:outputText>
											</f:facet>
										</p:column>
									</p:row>
								</p:columnGroup>
								<f:facet name="footer">
									<h:outputLabel
										value="Total de registros: #{resumoFolhaBean.dadosFolhaPagamentoAnual.size()}" />
								</f:facet>

							</p:dataTable>

						</p:tab>

					</p:tabView>

				</p:fieldset>
			</div>
		</h:form>




	</ui:define>
</ui:composition>