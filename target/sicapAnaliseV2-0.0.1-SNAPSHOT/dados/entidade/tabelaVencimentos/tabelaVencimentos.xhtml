<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:p="http://primefaces.org/ui"
	xmlns:pe="http://primefaces.org/ui/extensions"
	xmlns:fn="http://java.sun.com/jsp/jstl/functions"
	template="/resources/template/template.xhtml">

	<ui:define name="content">
		<!-- 		<p:fieldset legend="Lista de Tabelas de Vencimentos"> -->
		<h:form prependId="false">
			<p:fieldset legend="Dados para a consulta por tabelas de vencimentos">


				<div class="ui-g">
					<div class="ui-g-12 ui-md-6 ui-lg-4">
						<h:panelGroup>
							<h:outputText value="Cargo: " styleClass="FontBold" />
							<p:selectOneMenu value="#{tabelaVencimentosBean.cargo.id}"
								filter="true" filterMatchMode="contains" >
								<f:selectItem itemLabel="Todas" itemValue="#{0}" />
								<f:selectItems value="#{tabelaVencimentosBean.listaCargo}"
									var="cargo"
									itemLabel="#{cargo.codigo} - #{cargo.nome} (Remessa: #{cargo.remessaEventual.id})"
									itemValue="#{cargo.id}" />
							</p:selectOneMenu>
						</h:panelGroup>
					</div>
				</div>
				<div class="ui-g">
					<div class="ui-g-12 ui-md-6 ui-lg-4">
						<h:panelGroup>
							<h:outputText value="Nome: " styleClass="FontBold" />
							<p:inputText value="#{tabelaVencimentosBean.nome}"
								style="width: 100%;" />
						</h:panelGroup>
					</div>
				</div>
				<div class="ui-g">
					<div class="ui-g-12">
						<p:commandButton value="Pesquisar" style="width: auto;"
						icon="fa fa-fw fa-search white"
								actionListener="#{tabelaVencimentosBean.pesquisar()}"
								update="masterDetail" />
					</div>
				</div>

				
			</p:fieldset>
			<div class="EmptyBox10"></div>

			<p:fieldset legend="Resultados da consulta">
				<pe:masterDetail id="masterDetail"
					level="#{tabelaVencimentosBean.currentLevel}"
					showAllBreadcrumbItems="true">

					<f:facet name="header">
						<p:messages showDetail="false" showSummary="true" />
					</f:facet>

					<pe:masterDetailLevel id="detailInicio" level="1">
						<f:facet name="label">
							<h:outputText value="Início" />
						</f:facet>

						<p:dataTable id="tabelaTabelaVencimentos" var="tabelaVencimento"
							widgetVar="tabelaTabelaVencimentos"
							value="#{tabelaVencimentosBean.listaTabelaVencimentos}"
							emptyMessage="Nenhuma tabela de vencimento encontrada." rows="10"
							paginator="true" paginatorAlwaysVisible="false"
							paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
							rowsPerPageTemplate="5,10,15,20"
							currentPageReportTemplate="{currentPage} de {totalPages}"
							paginatorPosition="bottom">
							
<!-- 							<f:facet name="header"> -->
<!-- 								<h:outputText value="Tabelas de Vencimentos" /> -->
<!-- 							</f:facet> -->

							<p:column headerText="Código" width="15%">
								<h:outputText value="#{tabelaVencimento.codigo}" />
							</p:column>

							<p:column headerText="Nome" width="15%">
								<p:commandLink value="#{tabelaVencimento.nome}" process="@this"
									immediate="true">
									<pe:selectDetailLevel
										listener="#{tabelaVencimentosBean.buscaDadosTabelaVencimento(tabelaVencimento)}" />
								</p:commandLink>
							</p:column>

							<f:facet name="footer">
								<h:outputLabel
									value="Total de tabelas de vencimentos: #{tabelaVencimentosBean.listaTabelaVencimentos.size()}" />
							</f:facet>

						</p:dataTable>
					</pe:masterDetailLevel>

					<pe:masterDetailLevel id="detailDadosTabela" level="2"
						contextVar="dados">
						<f:facet name="label">
							<h:outputText value="Dados da Tabela de Vencimentos" />
						</f:facet>
						<!-- 						<p:fieldset> -->
						<!-- 							<p:panel header="Tabela"> -->
						<h:panelGrid columns="2">
							<h:outputText value="Código: " styleClass="FontBold" />
							<h:outputText
								value="#{tabelaVencimentosBean.tabelaVencimentos.codigo}" />

							<h:outputText value="Nome: " styleClass="FontBold" />
							<h:outputText
								value="#{tabelaVencimentosBean.tabelaVencimentos.nome}" />

						</h:panelGrid>

						<p:spacer />

						<p:fieldset legend="Níveis Salariais">
						<p:dataTable id="dtNiveis" var="niveis" widgetVar="dtNiveis"
							value="#{tabelaVencimentosBean.tabelaVencimentos.listaNiveis}"
							emptyMessage="Nenhum nível encontrado." rows="10"
							paginator="true"
							paginatorAlwaysVisible="false"
							paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
							rowsPerPageTemplate="10,20,30,40,50"
							currentPageReportTemplate="página {currentPage} de {totalPages}"
							paginatorPosition="bottom">

<!-- 							<f:facet name="header"> -->
<!-- 								<h:outputText value="Níveis Salariais" /> -->
<!-- 							</f:facet> -->

							<p:column headerText="Código" width="15%">
								<h:outputText value="#{niveis.codigo}" />
							</p:column>

							<p:column headerText="Referência" width="15%">
								<h:outputText value="#{niveis.referencia}" />
							</p:column>

							<p:column headerText="Valor" width="15%">
								<h:outputText value="#{niveis.valor}">
									<f:convertNumber currencySymbol="R$" type="currency" />
								</h:outputText>
							</p:column>

							<f:facet name="footer">
								<h:outputLabel
									value="Total de níveis: #{tabelaVencimentosBean.tabelaVencimentos.listaNiveis.size()}" />
							</f:facet>

						</p:dataTable>
														</p:fieldset>

						<p:spacer />

						<p:fieldset legend="Cargos">
							<p:dataTable id="dtCargo" var="cargo" widgetVar="dtCargo"
								value="#{tabelaVencimentosBean.tabelaVencimentos.listaCargos}"
								emptyMessage="Nenhum cargo encontrado." rows="10"
								paginator="true"
								paginatorAlwaysVisible="false"
								paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
								rowsPerPageTemplate="10,20,30,40,50"
								currentPageReportTemplate="página {currentPage} de {totalPages}"
								paginatorPosition="bottom">
								
<!-- 														<f:facet name="header"> -->
<!-- 								<h:outputText value="Cargos" /> -->
<!-- 							</f:facet> -->

								<p:column headerText="Código" width="15%">
									<h:outputText value="#{cargo.cargo.codigo}" />
								</p:column>

								<p:column headerText="Nome" width="15%">
									<h:outputText value="#{cargo.cargo.nome}" />
								</p:column>

								<p:column headerText="Escolaridade" width="15%">
									<h:outputText value="#{cargo.cargo.escolaridade.descricao}" />
								</p:column>

								<p:column headerText="Tipo" width="15%">
									<h:outputText value="#{cargo.cargo.tipo.descricao}" />
								</p:column>

								<f:facet name="footer">
									<h:outputLabel
										value="Total de cargos: #{tabelaVencimentosBean.tabelaVencimentos.listaCargos.size()}" />
								</f:facet>
							</p:dataTable>
						</p:fieldset>

						<!-- 							</p:panel> -->
						<h:panelGrid columns="2">
							<p:commandButton value="Voltar" style="margin-top: 10px;"
								icon="ui-icon-arrowthick-1-w" process="@this" immediate="true">
								<pe:selectDetailLevel step="-1" />
							</p:commandButton>
						</h:panelGrid>
						<!-- 						</p:fieldset> -->
					</pe:masterDetailLevel>
				</pe:masterDetail>
			</p:fieldset>
		</h:form>
		<!-- 		</p:fieldset> -->
	</ui:define>
</ui:composition>