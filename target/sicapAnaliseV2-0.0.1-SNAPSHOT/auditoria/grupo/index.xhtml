<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:f="http://xmlns.jcp.org/jsf/core"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:p="http://primefaces.org/ui"
	xmlns:pe="http://primefaces.org/ui/extensions"
	template="/resources/template/template.xhtml">

	<ui:define name="content">


		<h:form>
			<p:fieldset legend="Dados para a consulta por grupos de trabalho">


				<div class="ui-g">
					<div class="ui-g-12 ui-md-6 ui-lg-4">
						<h:outputText value="Nome " styleClass="FontBold" />
						<p:inputText id="nome" value="#{grupoBean.grupo.nome}"
							required="true" requiredMessage="Informe o nome do grupo" />
						<p:message for="nome" display="text" />
					</div>
					<div class="ui-g-12 ui-md-4 ui-lg-3">
						<h:outputText value="Perfil " styleClass="FontBold" />

						<p:selectOneMenu id="perfil" value="#{grupoBean.grupo.perfil}"
							required="true" requiredMessage="Informe o perfil do grupo">
							<f:selectItem itemLabel="Selecione o Perfil" itemValue="#{null}" />
							<f:selectItems value="#{grupoBean.listaPerfis}" var="perfil"
								itemLabel="#{perfil.descricao}" itemValue="#{perfil}" />
						</p:selectOneMenu>

						<p:message for="perfil" display="text" />
					</div>
				</div>
				<div class="ui-g">
					<div class="ui-g-12">
						<p:commandButton value="Cadastrar" action="#{grupoBean.salvar}"
							style="width:auto;" update="@form :formMD:tblGrupos"
							icon="fa fa-fw fa-plus white" />

					</div>
				</div>

			</p:fieldset>
		</h:form>

		<div class="EmptyBox10" />

		<h:form id="formMD" prependId="false">

			<p:fieldset legend="Lista dos grupos de trabalho">

				<p:confirmDialog global="true" showEffect="fade" hideEffect="fade">
					<p:commandButton value="Sim" type="button"
						styleClass="ui-confirmdialog-yes" icon="fa fa-fw fa-check white" />
					<p:commandButton value="Não" type="button"
						styleClass="ui-confirmdialog-no RedButton"
						icon="fa fa-fw fa-times white" />
				</p:confirmDialog>

				<pe:masterDetail id="masterDetail" level="#{grupoBean.currentLevel}"
					showAllBreadcrumbItems="true">

					<f:facet name="header">
						<p:messages showDetail="false" showSummary="true" />
					</f:facet>

					<pe:masterDetailLevel id="detailGrupo" level="1">

						<f:facet name="label">
							<h:outputText value="Início" />
						</f:facet>

						<p:commandButton id="btnExcluirGrupo" styleClass="RedButton"
							style="width:auto;" icon="fa fa-fw fa-trash white"
							value="Excluir Grupo(s)" update="@form"
							action="#{grupoBean.excluirGrupo()}">
							<p:confirm header="Excluir Grupo"
								message="Você tem certeza que deseja continuar com a EXCLUSÃO do(s) Grupo(s) selecionado(s)?"
								icon="fa fa-exclamation-triangle yellow" />
						</p:commandButton>

						<div class="EmptyBox10" />

						<p:dataTable id="tblGrupos" var="grupo"
							emptyMessage="Nenhum Grupo Encontrado" rowKey="#{grupo.id}"
							selection="#{grupoBean.gruposSelecionado}"
							value="#{grupoBean.grupos}" tableStyle="table-layout: auto">

							<p:column selectionMode="multiple"
								style="width:25px;text-align:center;" />
							<p:column headerText="Grupo">
								<p:commandLink update=":formMD" value="#{grupo.nome}">
									<pe:selectDetailLevel contextValue="#{grupo}" />
									<f:setPropertyActionListener value="#{grupo}"
										target="#{grupoBean.grupoSelecionado}" />
								</p:commandLink>
							</p:column>
							<p:column headerText="Perfil" styleClass="TexAlCenter"
								width="12%">
								<h:outputText value="#{grupo.perfil.descricao}" />
							</p:column>
							<p:column headerText="Qtd. Usuários" styleClass="TexAlCenter"
								width="10%">
								<h:outputText
									value="#{grupo.getListaUsuarioGrupoAtivo().size()}" />
							</p:column>

							<p:column style="width:32px;text-align:center">
								<p:commandButton style="width:auto;"
									update="dlgSelecionarUsuario"
									oncomplete="PF('dlgSelecionarUsuarioVar').show()"
									icon="fa fa-fw fa-user-plus white" title="Incluir Usuário(s)">
									<f:setPropertyActionListener value="#{grupo}"
										target="#{grupoBean.grupoSelecionado}" />
									<f:setPropertyActionListener value="INCLUIR_USUARIO"
										target="#{usuarioInternoBean.operacao}" />
									<f:setPropertyActionListener value="#{grupo.id}"
										target="#{usuarioInternoBean.grupoId}" />
								</p:commandButton>

							</p:column>

						</p:dataTable>

					</pe:masterDetailLevel>
					<pe:masterDetailLevel id="detailUsuario" level="2"
						contextVar="grupo">
						<f:facet name="label">
							<h:outputText value="Usuários" />
						</f:facet>

						<h:panelGrid columns="3">
							<!-- 							<p:commandButton value="Voltar" icon="ui-icon-arrowthick-1-w" -->
							<!-- 								process="@this" immediate="true"> -->
							<!-- 								<pe:selectDetailLevel step="-1" /> -->
							<!-- 							</p:commandButton> -->
							<p:commandButton style="width:auto;"
								update="dlgSelecionarUsuario"
								oncomplete="PF('dlgSelecionarUsuarioVar').show()"
								icon="fa fa-fw fa-user-plus white" value="Incluir Usuário(s)">

								<f:setPropertyActionListener value="INCLUIR_USUARIO"
									target="#{usuarioInternoBean.operacao}" />
								<f:setPropertyActionListener
									value="#{grupoBean.grupoSelecionado.id}"
									target="#{usuarioInternoBean.grupoId}" />
							</p:commandButton>
							<p:commandButton id="btnExcluirUsuario" styleClass="RedButton"
								icon="fa fa-fw fa-trash white" value="Excluir Usuário(s)"
								update="tblUsuariosGrupo"
								action="#{grupoBean.excluirUsuarioGrupo()}">
								<p:confirm header="Excluir Usuário"
									message="Você tem certeza que deseja continuar com a EXCLUSÃO do(s) Usuário(s) selecionado(s)?"
									icon="fa fa-exclamation-triangle yellow" />
							</p:commandButton>
						</h:panelGrid>



						<div class="EmptyBox10" />
						<p:dataTable id="tblUsuariosGrupo" var="usuarioGrupo"
							rowKey="#{usuarioGrupo.id}"
							emptyMessage="Nenhum Usuário Encontrado"
							selection="#{grupoBean.usuariosGrupoSelecionado}"
							value="#{grupoBean.usuariosGrupo}"
							tableStyle="table-layout: auto">

							<f:facet name="header">
						       #{grupoBean.grupoSelecionado.nome}
						    </f:facet>
							<p:column selectionMode="multiple"
								style="width:25px;text-align:center;" />
							<p:column headerText="Nome">
								<h:outputText value="#{usuarioGrupo.usuario.nome.toUpperCase()}" />
							</p:column>
							<p:column headerText="Cargo">
								<h:outputText
									value="#{usuarioGrupo.usuario.cargoNome.toUpperCase()}" />
							</p:column>
							<p:column headerText="Setor">
								<h:outputText
									value="#{usuarioGrupo.usuario.setorNome.toUpperCase()}" />
							</p:column>
						</p:dataTable>

						<div class="EmptyBox10" />
						<h:panelGrid columns="2">
							<p:commandButton value="Voltar" style="margin-top: 10px;"
								icon="ui-icon-arrowthick-1-w" process="@this" immediate="true">
								<pe:selectDetailLevel step="-1" />
							</p:commandButton>
						</h:panelGrid>

					</pe:masterDetailLevel>
				</pe:masterDetail>
			</p:fieldset>
		</h:form>

		<p:dialog id="dlgSelecionarUsuario"
			widgetVar="dlgSelecionarUsuarioVar" modal="true" header="Usuários"
			showEffect="fade" hideEffect="fade" resizable="false"
			closeOnEscape="true" width="75%" height="60%" position="top"
			style="margin-top: 25px;">

			<ui:include src="usuarios.xhtml">
			</ui:include>

		</p:dialog>

	</ui:define>
</ui:composition>