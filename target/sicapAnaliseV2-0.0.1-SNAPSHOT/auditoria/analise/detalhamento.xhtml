
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:f="http://xmlns.jcp.org/jsf/core"
	xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:p="http://primefaces.org/ui">
	<h:form>
		<p:dataTable id="tblDetail" var="detalhamentoAcumulacao"
			tableStyle="table-layout: auto"
			value="#{detalhamentoAcumulacaoBean.acumulacao.listaDetalhamentoAcumulacao}"
			emptyMessage="Nenhum acumulação encontrada." rows="20"
			paginator="true" paginatorAlwaysVisible="false"
			paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
			rowsPerPageTemplate="20,30,40,50,100,300,500, 1000"
			currentPageReportTemplate="página {currentPage} de {totalPages}"
			paginatorPosition="bottom">

			<f:facet name="header">
				<h:outputText value="#{detalhamentoAcumulacaoBean.acumulacao.cpf}">
					<f:converter converterId="converter.CpfConverter" />
				</h:outputText>
				<h:outputText
					value=" - #{detalhamentoAcumulacaoBean.acumulacao.nome}" />
			</f:facet>


			<p:column headerText="Entidade" width="25%">
				<h:outputText value="#{detalhamentoAcumulacao.entidade.nome}" />
			</p:column>
			<p:column headerText="Município" styleClass="TexAlCenter">
				<h:outputText value="#{detalhamentoAcumulacao.municipio.nome}" />
			</p:column>

			<p:column headerText="Matrícula" styleClass="TexAlCenter">
				<h:outputText
					value="#{detalhamentoAcumulacao.beneficiario.matricula}" />
			</p:column>

			<p:column headerText="Cargo" styleClass="TexAlCenter">
				<h:outputText value="#{detalhamentoAcumulacao.cargoNome}" />
			</p:column>

			<p:column headerText="Cargo Referência" styleClass="TexAlCenter">
				<h:outputText
					value="#{detalhamentoAcumulacao.cargo.referenciaSub.descricao}" />
			</p:column>
			<p:column headerText="Tipo de Cargo" styleClass="TexAlCenter">
				<h:outputText value="#{detalhamentoAcumulacao.cargo.tipo.descricao}" />
			</p:column>

			<p:column headerText="Carga Horária" styleClass="TexAlCenter">
				<h:outputText
					value="#{detalhamentoAcumulacao.cargo.cargaHorariaMensal}" />
			</p:column>

			<f:facet name="footer">
				<h:outputLabel
					value="Total de acumulações: #{detalhamentoAcumulacaoBean.acumulacao.listaDetalhamentoAcumulacao.size()}" />
			</f:facet>

		</p:dataTable>
	</h:form>
</ui:composition>
