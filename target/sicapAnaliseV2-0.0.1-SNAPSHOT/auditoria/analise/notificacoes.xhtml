<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:f="http://xmlns.jcp.org/jsf/core"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:p="http://primefaces.org/ui"
	xmlns:pe="http://primefaces.org/ui/extensions"
	template="/resources/template/template.xhtml">
	<ui:define name="content">
		<style>
.vencida td {
	color: #d9534f !important;
}

#tblNotifEmit\:filtroAssunto\:filter {
	display: block;
	width: 100% !important;
}
</style>
		<p:fieldset legend="Notificações Emitidas">
			<h:form prependId="false">
				<!-- 				<div class="EmptyBox5" /> -->
				<p:dataTable value="#{notificacaoEmitidaBean.notificacoes}" id="tblNotifEmit"
					var="notificacao" rows="10" paginator="true"
					tableStyle="table-layout: auto" reflow="true"
					rowStyleClass="#{notificacao.situacaoNotificacao()}"
					paginatorPosition="bottom" paginatorAlwaysVisible="false"
					paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
					rowsPerPageTemplate="10,20,30,40,50"
					currentPageReportTemplate="página {currentPage} de {totalPages}"
					emptyMessage="Nenhuma notificação encontrada">

					<p:column headerText="Cód. Acumulação" styleClass="TexAlCenter">
						<h:outputText value="#{notificacao.idAcumulacao}" />
					</p:column>

					<p:column headerText="Data Solicitação"
						sortBy="#{notificacao.dataSolicitacao}" styleClass="TexAlCenter">
						<h:outputText value="#{notificacao.dataSolicitacao.toLocalDate()}">
							<f:converter converterId="converter.DateConverter" />
						</h:outputText>
					</p:column>
					<p:column headerText="Data Resposta"
						sortBy="#{notificacao.dataResposta}" styleClass="TexAlCenter">
						<h:outputText value="#{notificacao.dataResposta}">
							<f:converter converterId="converter.DateTimeConverter" />
						</h:outputText>
					</p:column>
					<p:column headerText="Prazo Resposta" styleClass="TexAlCenter">
						<h:outputText
							value="#{notificacao.dataPrazoResposta.toLocalDate()}">
							<f:converter converterId="converter.DateConverter" />
						</h:outputText>
					</p:column>
					<p:column headerText="Entidade" width="18%">
						<h:outputText value="#{notificacao.nomeEntidade}" />
					</p:column>
					<p:column headerText="Assunto" filterBy="#{notificacao.assunto}" id="filtroAssunto"
						filterMatchMode="contains">
						<h:outputText value="#{notificacao.assunto}" />
					</p:column>
					<p:column headerText="Mensagem">
						<h:outputText value="#{notificacao.mensagem}" />
					</p:column>
					<p:column headerText="Situação Beneficiário"
						styleClass="TexAlCenter">
						<h:outputText value="#{notificacao.situacaoBeneficiario}" />
					</p:column>


					<f:facet name="footer">
						<h:outputText
							value="Total de Notificações: #{notificacaoEmitidaBean.notificacoes.size()}" />
					</f:facet>
				</p:dataTable>
				<p:spacer />
<!-- 				<p:fieldset legend="Legenda"> -->
					<h:panelGrid columns="8">
						<div class="box corVermelho"></div>
						<h:outputText value="Prazo Vencido" styleClass="FontBold" />
					</h:panelGrid>
<!-- 				</p:fieldset> -->
				
			</h:form>
		</p:fieldset>
	</ui:define>
</ui:composition>
