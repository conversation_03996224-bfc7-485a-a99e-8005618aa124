<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:f="http://xmlns.jcp.org/jsf/core"
	xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:p="http://primefaces.org/ui">


<ui:composition template="/resources/template/template.xhtml">

	<ui:define name="content">
		<style>
.ui-growl-image-info {
	background-image:
		url("#{resource['primefaces-sentinel:images/warn-blue.svg']}")
		!important; /* test url */
}

.ui-growl-image-info+div.ui-growl-message {
	border-color: #cee4f5 !important;
	color: #63bce2 !important;
}

.ui-growl-item {
	border-color: #c4c9cc !important;
}
</style>
		<p:fieldset legend="Acumulações Atribuídas para Análise">
			<div class="EmptyBox5" />
			<h:form id="formDist">
				<p:commandButton icon="fa fa-fw fa-search white" style="width:auto;"
					update="@form :dlgInitAnlAcm" value="Iniciar Análise"
					rendered="#{loginBean.perfilAuditoria}"
					actionListener="#{distribuicaoAcumulacaoBean.temAcumulacaoSelecionada()}">
					<f:setPropertyActionListener
						value="#{distribuicaoAcumulacaoBean.retornaAcumulacoesSelecionadas()}"
						target="#{acumulacaoBean.acumulacoesSelecionadas}"></f:setPropertyActionListener>
				</p:commandButton>
				<div class="EmptyBox10" />

				<style type="text/css">
.emanalise td {
	color: #fca752 !important;
}
}
</style>

<p:messages id="messages" globalOnly="true" showDetail="true"
				showSummary="true" />
				
				<p:dataTable id="tblDist" widgetVar="tblDistVar" rows="15"
				reflow="true"
					paginator="true" paginatorPosition="bottom"
					paginatorAlwaysVisible="false"
					disabledSelection="#{distribuicaoAcumulacao.dataSaida != null || distribuicaoAcumulacao.acumulacao.situacao == 'EA'}"
					rowStyleClass="#{distribuicaoAcumulacao.acumulacao.situacao eq 'EA' and distribuicaoAcumulacao.dataSaida eq null ?  'emanalise' : null}"
					emptyMessage="Nenhuma acumulação atribuída"
					selection="#{distribuicaoAcumulacaoBean.listaDistribuicaoAcumulacaoSelecionada}"
					rowKey="#{distribuicaoAcumulacao.id}"
					value="#{distribuicaoAcumulacaoBean.listaDistribuicaoAcumulacao}"
					var="distribuicaoAcumulacao"
					sortBy="#{distribuicaoAcumulacao.dataEntrada}"
					sortOrder="descending">

<!-- 					<f:facet name="header"> -->
<!-- 						<h:outputText value="Acumulações Atribuídas" /> -->
<!-- 					</f:facet> -->

					<p:column selectionMode="multiple"
						style="width:30px;text-align:center;">

					</p:column>


					<p:column headerText="Cód. Acumulação" styleClass="TexAlCenter"
						width="8%">
						<h:outputText value="#{distribuicaoAcumulacao.acumulacao.id}" />
					</p:column>


					<p:column headerText="CPF" styleClass="TexAlCenter" width="8%">
						<h:outputText value="#{distribuicaoAcumulacao.acumulacao.cpf}">
							<f:converter converterId="converter.CpfConverter" />
						</h:outputText>
					</p:column>
					<p:column headerText="Nome">
						<h:outputText value="#{distribuicaoAcumulacao.acumulacao.nome}" />
					</p:column>
					<p:column headerText="Auditor Responsável">
						<h:outputText
							value="#{distribuicaoAcumulacao.usuarioAuditor.nome}" />
					</p:column>

					<p:column headerText="Data do Recebimento"
						sortBy="#{distribuicaoAcumulacao.dataEntrada}"
						styleClass="TexAlCenter" width="10%">
						<h:outputText value="#{distribuicaoAcumulacao.dataEntrada}">
							<f:converter converterId="converter.DateTimeConverter" />
						</h:outputText>
					</p:column>
					<p:column headerText="Data da Saída" styleClass="TexAlCenter"
						width="10%">
						<h:outputText value="#{distribuicaoAcumulacao.dataSaida}">
							<f:converter converterId="converter.DateTimeConverter" />
						</h:outputText>
					</p:column>
					<p:column headerText="Despacho">
						<h:outputText value="#{distribuicaoAcumulacao.despacho}" />
					</p:column>
					<f:facet name="footer">
						<h:outputText
							value="Total de Acumulações: #{distribuicaoAcumulacaoBean.qtdDistribuicaoAcumulacaoAtivas()}" />
					</f:facet>
				</p:dataTable>

				<p:spacer />
<!-- 				<p:fieldset legend="Legenda"> -->
					<h:panelGrid columns="8">
						<div class="box corLaranja"></div>
						<h:outputText value="Em Análise" styleClass="FontBold" />
					</h:panelGrid>
<!-- 				</p:fieldset> -->
			</h:form>
		</p:fieldset>
		<p:dialog id="dlgInitAnlAcm" closeOnEscape="true"
			widgetVar="dlgInitAnlAcmVar" showEffect="fade" modal="true"
			header="Iniciar Análise da(s) Acumulacão(ões)" width="70%"
			position="top" style="margin: 25px 0px 25px;"
			fitViewport="true"
			visible="#{not empty distribuicaoAcumulacaoBean.listaDistribuicaoAcumulacaoSelecionada}"
			hideEffect="fade" resizable="false">
			<ui:include src="analisar.xhtml" />
		</p:dialog>
	</ui:define>
</ui:composition>

</html>