<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:p="http://primefaces.org/ui"
	xmlns:pe="http://primefaces.org/ui/extensions"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:c="http://java.sun.com/jsp/jstl/core">

<ui:composition template="/resources/templates/template.xhtml">

	<ui:define name="scripts">
		<script type="text/javascript">
				var tempoSessao = 1500;
				var timer = setInterval(function() {
					if( tempoSessao === 0 ) {
						document.getElementById('session-countdown').innerHTML = "00min:00s";
			    		clearInterval( timer );			    	
			  		}
					var segundos = tempoSessao % 60;
					var minutos = parseInt(tempoSessao / 60);
					segundos = (segundos + "").replace(/^(\d)$/, '0$1');
					minutos = (minutos + "").replace(/^(\d)$/, '0$1');
			   		document.getElementById('session-countdown').innerHTML = minutos + "min:" + segundos + "s";   
			   		tempoSessao--;
				}, 1000);
		</script>
	</ui:define>

	<ui:define name="menubar"/>	
	
	<ui:define name="content">
		<h:form id="FrmEntidades" prependId="false" acceptcharset="ISO-8859-1">
			<pe:codeMirror></pe:codeMirror>
		</h:form>
	</ui:define>

</ui:composition>

</html>