<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  
  <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
  </properties>
  
  <groupId>sicapAnaliseV2</groupId>
  <artifactId>sicapAnaliseV2</artifactId>
  <version>0.0.1-SNAPSHOT</version>
  <packaging>war</packaging>
  
	<repositories>
    <repository>
      <id>jaspersoft-third-party</id>
      <name>Jaspersoft Third Party Artifacts</name>
      <url>https://jaspersoft.jfrog.io/jaspersoft/third-party-ce-artifacts/</url>
    </repository>
    <repository>
      <id>jr-ce-releases</id>
      <name>JasperReports CE Releases</name>
      <url>https://jaspersoft.jfrog.io/jaspersoft/jr-ce-releases/</url>
    </repository>
    <repository>
      <id>jboss-public-repository-group</id>
      <name>JBoss Public Repository Group</name>
      <url>https://repository.jboss.org/nexus/content/groups/public/</url>
      <layout>default</layout>
      <releases>
        <enabled>true</enabled>
        <updatePolicy>never</updatePolicy>
      </releases>
      <snapshots>
        <enabled>true</enabled>
        <updatePolicy>never</updatePolicy>
      </snapshots>
    </repository>
    <repository>
        <id>central</id>
        <name>Maven Central Repository</name>
        <url>https://repo.maven.apache.org/maven2</url>
        <layout>default</layout>
        <snapshots>
            <enabled>false</enabled>
        </snapshots>
    </repository>
  </repositories>

  <pluginRepositories>
    <pluginRepository>
      <id>jboss-public-repository-group</id>
      <name>JBoss Public Repository Group</name>
      <url>https://repository.jboss.org/nexus/content/groups/public/</url>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
    </pluginRepository>
      <pluginRepository>
          <id>central</id>
          <name>Maven Central Repository</name>
          <url>https://repo.maven.apache.org/maven2</url>
          <layout>default</layout>
          <snapshots>
              <enabled>false</enabled>
          </snapshots>
          <releases>
              <updatePolicy>never</updatePolicy>
          </releases>
      </pluginRepository>
  </pluginRepositories>
  
  <dependencies>
    <dependency>
      <groupId>com.fasterxml</groupId>
      <artifactId>classmate</artifactId>
      <version>0.8.0</version>
    </dependency>
    <dependency>
      <groupId>commons-beanutils</groupId>
      <artifactId>commons-beanutils</artifactId>
      <version>1.9.0</version>
    </dependency>
    <dependency>
      <groupId>commons-collections</groupId>
      <artifactId>commons-collections</artifactId>
      <version>3.2.2</version>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-collections4</artifactId>
      <version>4.1</version>
    </dependency>
    <dependency>
      <groupId>commons-digester</groupId>
      <artifactId>commons-digester</artifactId>
      <version>2.1</version>
    </dependency>
    <dependency>
      <groupId>commons-fileupload</groupId>
      <artifactId>commons-fileupload</artifactId>
      <version>1.3</version>
    </dependency>
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
      <version>2.4</version>
    </dependency>
    <dependency>
      <groupId>commons-lang</groupId>
      <artifactId>commons-lang</artifactId>
      <version>2.6</version>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>3.5</version>
    </dependency>
    <dependency>
      <groupId>commons-logging</groupId>
      <artifactId>commons-logging</artifactId>
      <version>1.1.1</version>
    </dependency>
    <dependency>
      <groupId>com.google.code.gson</groupId>
      <artifactId>gson</artifactId>
      <version>2.3.1</version>
    </dependency>
    <!-- Hibernate is provided by WildFly - removed to avoid conflicts -->
    <!-- Adding only annotations for compilation -->
    <dependency>
      <groupId>org.hibernate</groupId>
      <artifactId>hibernate-core</artifactId>
      <version>5.4.10.Final</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>net.sf.jasperreports</groupId>
      <artifactId>jasperreports</artifactId>
      <version>6.16.0</version>
    </dependency>
    <dependency>
      <groupId>org.jfree</groupId>
      <artifactId>jcommon</artifactId>
      <version>1.0.23</version>
    </dependency>
    <dependency>
      <groupId>org.glassfish.jersey.core</groupId>
      <artifactId>jersey-client</artifactId>
      <version>2.25.1</version>
    </dependency>
    <dependency>
      <groupId>org.jfree</groupId>
      <artifactId>jfreechart</artifactId>
      <version>1.0.19</version>
    </dependency>
    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi</artifactId>
      <version>3.17</version>
    </dependency>
    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi-ooxml</artifactId>
      <version>3.17</version>
    </dependency>
    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi-ooxml-schemas</artifactId>
      <version>3.17</version>
    </dependency>
    <dependency>
      <groupId>org.primefaces</groupId>
      <artifactId>primefaces</artifactId>
      <version>7.0</version>
    </dependency>
    <dependency>
      <groupId>org.primefaces.extensions</groupId>
      <artifactId>primefaces-extensions</artifactId>
      <version>7.0</version>
    </dependency>
    <dependency>
      <groupId>org.primefaces.extensions</groupId>
      <artifactId>resources-ckeditor</artifactId>
      <version>3.2.0</version>
    </dependency>
    <dependency>
      <groupId>org.primefaces.extensions</groupId>
      <artifactId>resources-codemirror</artifactId>
      <version>3.2.0</version>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>1.7.12</version>
    </dependency>
    <dependency>
      <groupId>uaihebert.com</groupId>
      <artifactId>uaiCriteria</artifactId>
      <version>4.0.0</version>
      <exclusions>
        <exclusion>
          <groupId>org.hibernate.javax.persistence</groupId>
          <artifactId>hibernate-jpa-2.1-api</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.xmlbeans</groupId>
      <artifactId>xmlbeans</artifactId>
      <version>2.6.0</version>
    </dependency>
    <dependency>
      <groupId>com.sun.activation</groupId>
      <artifactId>jakarta.activation</artifactId>
      <version>1.2.2</version>
    </dependency>
    <dependency>
      <groupId>org.jboss.spec.javax.annotation</groupId>
      <artifactId>jboss-annotations-api_1.3_spec</artifactId>
      <version>2.0.1.Final</version>
    </dependency>
    <dependency>
      <groupId>org.jboss.spec.javax.ejb</groupId>
      <artifactId>jboss-ejb-api_3.2_spec</artifactId>
      <version>2.0.0.Final</version>
    </dependency>
    <dependency>
      <groupId>org.jboss.spec.javax.el</groupId>
      <artifactId>jboss-el-api_3.0_spec</artifactId>
      <version>2.0.0.Final</version>
    </dependency>
    <dependency>
      <groupId>javax.enterprise</groupId>
      <artifactId>cdi-api</artifactId>
      <version>2.0</version>
    </dependency>
    <dependency>
      <groupId>org.jboss.spec.javax.faces</groupId>
      <artifactId>jboss-jsf-api_2.3_spec</artifactId>
      <version>3.0.0.SP04</version>
    </dependency>
    <dependency>
      <groupId>javax.inject</groupId>
      <artifactId>javax.inject</artifactId>
      <version>1</version>
    </dependency>
    <dependency>
      <groupId>org.jboss.spec.javax.interceptor</groupId>
      <artifactId>jboss-interceptors-api_1.2_spec</artifactId>
      <version>2.0.0.Final</version>
    </dependency>
    <dependency>
      <groupId>org.jboss.spec.javax.jms</groupId>
      <artifactId>jboss-jms-api_2.0_spec</artifactId>
      <version>2.0.0.Final</version>
    </dependency>
    <dependency>
      <groupId>com.sun.mail</groupId>
      <artifactId>jakarta.mail</artifactId>
      <version>1.6.5</version>
    </dependency>
    <dependency>
      <groupId>org.jboss.spec.javax.management.j2ee</groupId>
      <artifactId>jboss-j2eemgmt-api_1.1_spec</artifactId>
      <version>2.0.0.Final</version>
    </dependency>
    <dependency>
      <groupId>org.jboss.openjdk-orb</groupId>
      <artifactId>openjdk-orb</artifactId>
      <version>8.1.5.Final</version>
    </dependency>
    <dependency>
      <groupId>jakarta.persistence</groupId>
      <artifactId>jakarta.persistence-api</artifactId>
      <version>2.2.3</version>
    </dependency>
    <dependency>
      <groupId>org.jboss.spec.javax.resource</groupId>
      <artifactId>jboss-connector-api_1.7_spec</artifactId>
      <version>2.0.0.Final</version>
    </dependency>
    <dependency>
      <groupId>org.jboss.spec.javax.security.auth.message</groupId>
      <artifactId>jboss-jaspi-api_1.1_spec</artifactId>
      <version>2.0.1.Final</version>
    </dependency>
    <dependency>
      <groupId>org.jboss.spec.javax.security.jacc</groupId>
      <artifactId>jboss-jacc-api_1.5_spec</artifactId>
      <version>2.0.0.Final</version>
    </dependency>
    <dependency>
      <groupId>org.jboss.spec.javax.servlet</groupId>
      <artifactId>jboss-servlet-api_4.0_spec</artifactId>
      <version>2.0.0.Final</version>
    </dependency>
    <dependency>
      <groupId>org.jboss.spec.javax.servlet.jsp</groupId>
      <artifactId>jboss-jsp-api_2.3_spec</artifactId>
      <version>2.0.0.Final</version>
    </dependency>
	<dependency>
  <groupId>org.apache.taglibs</groupId>
  <artifactId>taglibs-standard-spec</artifactId>
  <version>1.2.5</version>
</dependency>
	<dependency>
  		<groupId>org.apache.taglibs</groupId>
  		<artifactId>taglibs-standard-compat</artifactId>
  		<version>1.2.5</version>
	</dependency>
	<dependency>
  		<groupId>org.apache.taglibs</groupId>
  		<artifactId>taglibs-standard-impl</artifactId>
  		<version>1.2.5</version>
	</dependency>
    <dependency>
      <groupId>org.jboss.spec.javax.transaction</groupId>
      <artifactId>jboss-transaction-api_1.3_spec</artifactId>
      <version>2.0.0.Final</version>
    </dependency>
    <dependency>
      <groupId>jakarta.validation</groupId>
      <artifactId>jakarta.validation-api</artifactId>
      <version>2.0.2</version>
    </dependency>
    <dependency>
      <groupId>org.jboss.spec.javax.ws.rs</groupId>
      <artifactId>jboss-jaxrs-api_2.1_spec</artifactId>
      <version>2.0.1.Final</version>
    </dependency>
    <dependency>
      <groupId>wsdl4j</groupId>
      <artifactId>wsdl4j</artifactId>
      <version>1.6.3</version>
    </dependency>
    <dependency>
      <groupId>org.jboss.spec.javax.xml.bind</groupId>
      <artifactId>jboss-jaxb-api_2.3_spec</artifactId>
      <version>2.0.1.Final</version>
    </dependency>
    <dependency>
      <groupId>org.jboss.spec.javax.xml.rpc</groupId>
      <artifactId>jboss-jaxrpc-api_1.1_spec</artifactId>
      <version>2.0.0.Final</version>
    </dependency>
    <dependency>
      <groupId>org.jboss.spec.javax.xml.soap</groupId>
      <artifactId>jboss-saaj-api_1.4_spec</artifactId>
      <version>1.0.2.Final</version>
    </dependency>
    <dependency>
      <groupId>org.jboss.spec.javax.xml.ws</groupId>
      <artifactId>jboss-jaxws-api_2.3_spec</artifactId>
      <version>2.0.0.Final</version>
    </dependency>
    <!-- Hibernate Validator is provided by WildFly - removed to avoid conflicts -->
    <dependency>
      <groupId>org.picketbox</groupId>
      <artifactId>picketbox-commons</artifactId>
      <version>1.0.0.final</version>
    </dependency>
	<dependency>
	    <groupId>org.picketbox</groupId>
	    <artifactId>picketbox-infinispan</artifactId>
	    <version>5.0.3.Final</version>
	    <exclusions>
	      <exclusion>
	        <groupId>org.hibernate</groupId>
	        <artifactId>hibernate-core</artifactId>
	      </exclusion>
	      <exclusion>
	        <groupId>org.hibernate</groupId>
	        <artifactId>hibernate-annotations</artifactId>
	      </exclusion>
	      <exclusion>
	        <groupId>org.hibernate</groupId>
	        <artifactId>hibernate-entitymanager</artifactId>
	      </exclusion>
	      <exclusion>
	        <groupId>org.hibernate</groupId>
	        <artifactId>hibernate</artifactId>
	      </exclusion>
	      <exclusion>
	        <groupId>org.hibernate</groupId>
	        <artifactId>hibernate-commons-annotations</artifactId>
	      </exclusion>
	      <exclusion>
	        <groupId>org.hibernate.javax.persistence</groupId>
	        <artifactId>hibernate-jpa-2.0-api</artifactId>
	      </exclusion>
	      <exclusion>
	        <groupId>javax.persistence</groupId>
	        <artifactId>persistence-api</artifactId>
	      </exclusion>
	    </exclusions>
	</dependency>
    <dependency>
      <groupId>org.picketbox</groupId>
      <artifactId>picketbox-commons</artifactId>
      <version>1.0.0.final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.core</groupId>
      <artifactId>wildfly-controller-client</artifactId>
      <version>15.0.1.Final</version>
    </dependency>
    <dependency>
      <groupId>org.jboss</groupId>
      <artifactId>jboss-dmr</artifactId>
      <version>1.5.1.Final</version>
    </dependency>
    <dependency>
      <groupId>org.jboss.logging</groupId>
      <artifactId>jboss-logging</artifactId>
      <version>3.4.1.Final</version>
    </dependency>
    <dependency>
      <groupId>org.jboss.resteasy</groupId>
      <artifactId>resteasy-jaxb-provider</artifactId>
      <version>3.15.1.Final</version>
    </dependency>
    <dependency>
      <groupId>org.jboss.resteasy</groupId>
      <artifactId>resteasy-client</artifactId>
      <version>3.15.1.Final</version>
    </dependency>
    <dependency>
      <groupId>org.jboss.resteasy</groupId>
      <artifactId>resteasy-jaxrs</artifactId>
      <version>3.15.1.Final</version>
    </dependency>
    <dependency>
      <groupId>org.jboss.resteasy</groupId>
      <artifactId>resteasy-multipart-provider</artifactId>
      <version>3.15.1.Final</version>
    </dependency>
    <dependency>
      <groupId>org.jboss.ejb3</groupId>
      <artifactId>jboss-ejb3-ext-api</artifactId>
      <version>2.3.0.Final</version>
    </dependency>
    <dependency>
      <groupId>org.jboss.spec.javax.batch</groupId>
      <artifactId>jboss-batch-api_1.0_spec</artifactId>
      <version>2.0.0.Final</version>
    </dependency>
    <dependency>
      <groupId>org.jboss.spec.javax.enterprise.concurrent</groupId>
      <artifactId>jboss-concurrency-api_1.0_spec</artifactId>
      <version>2.0.0.Final</version>
    </dependency>
    <dependency>
      <groupId>org.jboss.spec.javax.websocket</groupId>
      <artifactId>jboss-websocket-api_1.1_spec</artifactId>
      <version>2.0.0.Final</version>
    </dependency>
    <dependency>
      <groupId>jakarta.json</groupId>
      <artifactId>jakarta.json-api</artifactId>
      <version>1.1.6</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.common</groupId>
      <artifactId>wildfly-common</artifactId>
      <version>1.5.4.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-sasl-localuser</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-x500-cert-util</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-sasl-deprecated</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-auth-server-deprecated</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-http-basic</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-permission</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-http-bearer</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-realm-token</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-realm</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-sasl-gssapi</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-realm-ldap</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-http-digest</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-x500</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-sasl-gs2</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-http-util</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-password-impl</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-http-deprecated</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-jacc</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-sasl-oauth2</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-sasl-plain</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-http-cert</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-sasl-anonymous</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-http-spnego</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-ssl</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-sasl-external</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-sasl-scram</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-sasl</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-x500-cert</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-http-external</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-base</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-x500-principal</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-credential-source-impl</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-auth-server-http</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-auth-server</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-x500-cert-acme</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-security-manager-action</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-http-sso</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-http-form</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-http</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-client</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-credential-source-deprecated</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-provider-util</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-mechanism-gssapi</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-digest</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-realm-jdbc</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-audit</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-credential</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-auth</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-mechanism-http</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-x500-deprecated</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-mechanism</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-mechanism-digest</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-sasl-auth-util</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-json-util</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-asn1</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-credential-store</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-security-manager</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-sasl-otp</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-auth-util</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-jaspi</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-keystore</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-auth-server-sasl</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-util</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-mechanism-oauth2</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-sasl-entity</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-encryption</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-mechanism-scram</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-sasl-digest</artifactId>
      <version>1.15.3.Final</version>
    </dependency>
  </dependencies>
  <build>
    <resources>
      <resource>
        <directory>src/main/java</directory>
        <excludes>
          <exclude>**/*.java</exclude>
        </excludes>
      </resource>
    </resources>
    <plugins>
      <plugin>
        <artifactId>maven-war-plugin</artifactId>
        <version>3.2.3</version>
      </plugin>
      <plugin>
	    	<groupId>org.apache.maven.plugins</groupId>
	    	<artifactId>maven-war-plugin</artifactId>
	    	<version>3.3.2</version> 
	    	<configuration>
        		<archive>
            		<manifest>
                		<addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                		<addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
            		</manifest>
            		<manifestEntries>
                		<Built-By>SeuNome</Built-By>
               	 		<Custom-Entry>ValorCustomizado</Custom-Entry>
            		</manifestEntries>
        		</archive>
    		</configuration>
		</plugin>
		<plugin>
			<artifactId>maven-compiler-plugin</artifactId>
			<version>3.0</version>
			<configuration>
				<source>1.8</source>
				<target>1.8</target>
			</configuration>
		</plugin>
    </plugins>
  </build>
</project>