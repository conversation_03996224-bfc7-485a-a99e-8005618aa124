<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg">
<metadata></metadata>
<defs>
<font id="titillium_webregular" horiz-adv-x="1146" >
<font-face units-per-em="2048" ascent="1638" descent="-410" />
<missing-glyph horiz-adv-x="450" />
<glyph horiz-adv-x="0" />
<glyph unicode="&#xd;" horiz-adv-x="681" />
<glyph unicode=" "  horiz-adv-x="450" />
<glyph unicode="&#x09;" horiz-adv-x="450" />
<glyph unicode="&#xa0;" horiz-adv-x="450" />
<glyph unicode="!" horiz-adv-x="505" d="M170 0v229h168v-229h-168zM172 1417h162l-10 -950h-142z" />
<glyph unicode="&#x22;" horiz-adv-x="763" d="M135 1417h154l-8 -454h-138zM475 1417h154l-8 -454h-138z" />
<glyph unicode="#" d="M49 356v131h227v381h-227v131h227v365h140v-365h315v365h139v-365h228v-131h-228v-381h228v-131h-228v-356h-139v356h-315v-356h-140v356h-227zM416 487h315v381h-315v-381z" />
<glyph unicode="$" d="M141 1010q0 362 434 362q31 0 48 -2l34 264h105l-35 -270l256 -31l-12 -127q-141 16 -260 27l-62 -477q199 -47 281 -121t82 -238q0 -416 -432 -415h-31l-29 -236q-104 6 -104 14l28 228l-292 37l16 125q158 -23 293 -31l67 512q-209 49 -298 126t-89 253zM293 1018 q0 -111 55 -158t199 -82l59 461h-20q-293 0 -293 -221zM567 115h4q289 0 289 272q0 98 -51 145.5t-178 77.5z" />
<glyph unicode="%" d="M57 1090q0 282 225.5 282t225.5 -282q0 -145 -59.5 -217t-166 -72t-166 71.5t-59.5 217.5zM182 1090q0 -184 101.5 -184.5t101.5 184.5q0 92 -23.5 135t-78 43t-78 -43t-23.5 -135zM317 -8l420 1403l99 -33l-420 -1399zM641 270q0 281 224.5 281t224.5 -283 q0 -143 -59.5 -215.5t-166 -72.5t-165 72.5t-58.5 217.5zM764 270q0 -186 100 -186q57 0 80 45t23 139t-22.5 136t-78 42t-79 -42t-23.5 -134z" />
<glyph unicode="&#x26;" horiz-adv-x="1409" d="M86 396.5q0 195.5 75 286.5t242 146q-72 82 -94 145.5t-22 160.5q0 137 94 219t266 82t261 -82t89 -226.5t-71.5 -227.5t-243.5 -163l311 -307q45 131 58 332l149 -2q-27 -256 -94 -426l264 -250l-92 -102l-248 231q-141 -233 -428 -233t-401.5 110.5t-114.5 306z M242 414q0 -295 336 -295q252 0 346 184l-432 430q-135 -43 -192.5 -110.5t-57.5 -208.5zM440 1110q0 -125 92 -223l60 -58q141 61 196.5 121t55.5 164q0 188 -202 188.5t-202 -192.5z" />
<glyph unicode="'" horiz-adv-x="425" d="M137 1417h154l-10 -454h-138z" />
<glyph unicode="(" horiz-adv-x="540" d="M104 616q0 197 53.5 427.5t106.5 361.5l53 131h148q-72 -188 -133.5 -465.5t-61.5 -453t49.5 -393.5t98.5 -349l47 -131h-148q-213 479 -213 872z" />
<glyph unicode=")" horiz-adv-x="540" d="M76 -256q70 164 132 431t62 442.5t-48 404.5t-97 371l-49 143h147q136 -305 191 -653q23 -143 22.5 -303t-53.5 -378t-107 -337l-53 -121h-147z" />
<glyph unicode="*" horiz-adv-x="860" d="M111 938l221 162l-217 157l63 86l217 -159l84 258l101 -33l-82 -260h266v-102h-270l84 -256l-101 -31l-84 258l-219 -162z" />
<glyph unicode="+" d="M113 444v140h387v399h143v-399h391v-140h-391v-403h-143v403h-387z" />
<glyph unicode="," horiz-adv-x="456" d="M70 -252l94 471h170l-137 -471h-127z" />
<glyph unicode="-" horiz-adv-x="897" d="M139 492v143h621v-143h-621z" />
<glyph unicode="." horiz-adv-x="442" d="M137 0v233h168v-233h-168z" />
<glyph unicode="/" horiz-adv-x="843" d="M66 20l577 1430l137 -51l-577 -1428z" />
<glyph unicode="0" d="M80 667q0 386 121.5 546.5t369.5 160.5q168 0 273 -63q221 -131 221 -656q0 -371 -123 -523t-368.5 -152t-369.5 150.5t-124 536.5zM244 653q0 -303 79.5 -418.5t249.5 -115.5t250 116.5t80 433t-77 440.5t-253 124q-117 0 -184 -49q-145 -100 -145 -531z" />
<glyph unicode="1" d="M219 1065l430 287h148v-1352h-158v1176l-348 -230z" />
<glyph unicode="2" d="M141 0v133l396 416q102 106 149 161.5t85 131.5t38 151q0 131 -71.5 184.5t-223.5 53.5q-133 0 -297 -35l-53 -10l-12 131q195 55 401.5 55t312 -87t105.5 -275q0 -143 -63.5 -250t-219.5 -256l-360 -367h678v-137h-865z" />
<glyph unicode="3" d="M127 35l14 129q221 -45 414 -45q293 2 293 262q0 244 -279 254h-272v135h272q86 0 166 68.5t80 181.5t-63.5 163t-212.5 50q-164 0 -324 -31l-53 -10l-15 125q193 57 413 57t318.5 -84t98.5 -268q0 -94 -25.5 -136t-41 -64.5t-40.5 -43.5q-45 -35 -71 -49l-23 -14 q119 -43 176.5 -109.5t57.5 -218.5q0 -207 -105.5 -307t-333.5 -100q-180 0 -381 40z" />
<glyph unicode="4" d="M82 264v123l385 965h172l-391 -949h489v422h158v-422h172v-139h-172v-264h-158v264h-655z" />
<glyph unicode="5" d="M129 41l18 125q236 -45 397 -45t242.5 83t81.5 232.5t-74.5 212t-211.5 62.5q-74 0 -155 -20.5t-124 -41.5l-43 -20l-108 20l36 703h789v-144h-658l-36 -440q158 84 317 84q430 0 430 -399q0 -229 -118.5 -350t-333.5 -121q-172 0 -381 45z" />
<glyph unicode="6" d="M102 682q0 354 138.5 522t390.5 168q166 0 315 -24l55 -11l-14 -129q-184 25 -358 25t-268.5 -121t-94.5 -346l51 20q51 18 136.5 39t150.5 21q455 0 455 -420q0 -215 -124 -330.5t-349 -115.5q-483 0 -484 702zM264 627q2 -233 79 -370.5t234.5 -137.5t237.5 78.5 t80 223t-79 214.5t-222 70q-70 0 -153 -19.5t-130 -38.5z" />
<glyph unicode="7" d="M158 1208v144h839v-207l-520 -1165l-149 47l510 1128v53h-680z" />
<glyph unicode="8" d="M66 344q0 145 56 221t177 142q-111 55 -161 125.5t-50 191.5q0 172 128 261t353.5 89t357.5 -89t132 -263q0 -127 -51.5 -193.5t-173.5 -121.5q123 -55 184 -130t61 -210q0 -387 -506.5 -387t-506.5 364zM233 371q0 -248 339 -248t339 260q0 109 -60 161t-179 87h-215 q-113 -31 -168.5 -91t-55.5 -169zM256 1006q0 -90 49 -145.5t152 -92.5h215q111 35 165 90.5t54 145.5q0 227 -317.5 227t-317.5 -225z" />
<glyph unicode="9" d="M84 924q0 207 127 327.5t342 120.5q248 0 368.5 -183t120.5 -538.5t-136 -513t-400 -157.5q-154 0 -318 28l-53 8l15 127q184 -24 356 -24q371 0 373 471q-205 -78 -353 -78q-442 0 -442 412zM248 924q0 -273 291 -273q125 0 288 56l54 18q-6 508 -328 508 q-143 0 -224 -83t-81 -226z" />
<glyph unicode=":" horiz-adv-x="444" d="M137 0v233h168v-233h-168zM137 659v234h168v-234h-168z" />
<glyph unicode=";" horiz-adv-x="507" d="M98 -252l92 471h172l-137 -471h-127zM180 659v234h166v-234h-166z" />
<glyph unicode="&#x3c;" d="M125 446v136l852 415v-159l-694 -322l694 -328v-161z" />
<glyph unicode="=" d="M131 254v141h885v-141h-885zM131 633v141h885v-141h-885z" />
<glyph unicode="&#x3e;" d="M168 27v161l694 328l-694 322v159l852 -415v-136z" />
<glyph unicode="?" horiz-adv-x="915" d="M76 1376q207 61 342 62q211 0 312 -77t101 -251q0 -133 -35.5 -201.5t-145 -160t-150.5 -145.5t-41 -120v-71h-129q-27 55 -27 124.5t47 130t151.5 150.5t139.5 143.5t35 148.5t-65.5 142t-207.5 47q-90 0 -264 -36l-53 -11zM307 2v229h168v-229h-168z" />
<glyph unicode="@" horiz-adv-x="2000" d="M104 535q0 494 235.5 720t692.5 226t668 -210t211 -646v-27q0 -434 -137 -547q-49 -39 -99.5 -51t-115 -12t-110.5 12t-71 31q-49 37 -67 80q-201 -121 -357 -121q-76 0 -132 18.5t-113 69.5q-115 102 -115 399t93 427t323 130q88 0 198 -39l37 -14v33h154v-391 q0 -358 22 -426q10 -27 29.5 -49.5t42.5 -26.5t78 -4t93 26.5t64.5 135t26.5 319.5v29q0 379 -168 549t-558 170t-579.5 -194t-189.5 -629t180.5 -619.5t591.5 -184.5l293 21l6 -137q-193 -18 -299 -19q-238 0 -402.5 45t-285.5 154q-240 209 -240 752zM750 500 q0 -375 215 -375q88 0 299 94q-18 111 -19 354v285q-123 41 -211 41q-166 0 -225 -94t-59 -305z" />
<glyph unicode="A" horiz-adv-x="1220" d="M49 0l389 1417h344l389 -1417h-155l-103 365h-606l-102 -365h-156zM342 506h537l-215 776h-107z" />
<glyph unicode="B" horiz-adv-x="1261" d="M174 0v1417h514q211 0 316.5 -87t105.5 -273q0 -133 -46 -208t-136 -118q225 -78 225 -334q0 -213 -112.5 -305t-325.5 -92h-541zM332 139h377q139 0 211.5 58.5t72.5 205.5q0 78 -29.5 131.5t-78.5 75.5q-91 41 -187 41h-366v-512zM332 788h364q131 0 191.5 62.5 t60.5 188.5t-64.5 182.5t-201.5 56.5h-350v-490z" />
<glyph unicode="C" horiz-adv-x="1114" d="M121 706.5q0 188.5 21.5 316.5t77.5 226.5t158.5 143.5t266.5 45t385 -45l-6 -133q-202 36 -360 36h-9q-225 0 -297.5 -134t-72.5 -457q0 -162 13 -260.5t52 -177.5t114 -112.5t219 -33.5t341 37l6 -135q-211 -43 -379 -43t-271.5 46t-159.5 143t-77.5 223t-21.5 314.5z " />
<glyph unicode="D" horiz-adv-x="1320" d="M174 0v1417h506q346 0 459 -274q65 -156 65 -406v-8q0 -535 -266 -674q-106 -55 -258 -55h-506zM332 139h348q362 0 362 590q0 408 -182 508q-74 41 -180 41h-348v-1139z" />
<glyph unicode="E" horiz-adv-x="1132" d="M174 0v1417h862v-139h-704v-490h581v-137h-581v-512h704v-139h-862z" />
<glyph unicode="F" horiz-adv-x="1077" d="M174 0v1417h848v-139h-690v-553h583v-139h-583v-586h-158z" />
<glyph unicode="G" horiz-adv-x="1261" d="M117 707.5q0 371.5 119.5 551t425.5 179.5q190 0 395 -41l69 -13l-6 -131q-254 43 -440 43q-236 0 -317.5 -136t-81.5 -452.5t81.5 -452.5t315.5 -136q143 0 293 28v437h-236v141h391v-696q-258 -49 -460 -49q-309 0 -429 178t-120 549.5z" />
<glyph unicode="H" horiz-adv-x="1382" d="M174 0v1417h158v-635h721v635h155v-1417h-155v641h-721v-641h-158z" />
<glyph unicode="I" horiz-adv-x="503" d="M174 0v1417h158v-1417h-158z" />
<glyph unicode="J" horiz-adv-x="591" d="M37 -6q147 0 189 36t42 187v1200h156l2 -1222q0 -172 -57 -252q-47 -68 -187 -84q-55 -6 -145 -6v141z" />
<glyph unicode="K" horiz-adv-x="1165" d="M174 0v1417h158v-659l233 8l363 651h180l-406 -712l428 -705h-186l-377 627l-235 -9v-618h-158z" />
<glyph unicode="L" horiz-adv-x="980" d="M174 0v1417h158v-1276h622v-141h-780z" />
<glyph unicode="M" horiz-adv-x="1720" d="M174 0v1417h285l401 -1198l402 1198h284v-1417h-157v1249h-29l-412 -1198h-176l-412 1198h-28v-1249h-158z" />
<glyph unicode="N" horiz-adv-x="1384" d="M174 0v1417h295l543 -1278h43v1278h155v-1417h-288l-551 1278h-39v-1278h-158z" />
<glyph unicode="O" horiz-adv-x="1351" d="M117 701.5q0 373.5 122.5 555t436 181.5t437.5 -180.5t124 -559.5t-121 -548.5t-439.5 -169.5t-439 174t-120.5 547.5zM279 702.5q0 -311.5 81.5 -447.5t316 -136t315.5 133t81 446.5t-84 456.5t-314.5 143t-313 -142t-82.5 -453.5z" />
<glyph unicode="P" horiz-adv-x="1214" d="M174 0v1417h518q231 0 342 -112.5t111 -343.5q0 -484 -453 -484h-360v-477h-158zM332 616h358q293 0 293 345q0 164 -69.5 240.5t-223.5 76.5h-358v-662z" />
<glyph unicode="Q" horiz-adv-x="1351" d="M117 701.5q0 373.5 122.5 555t436 181.5t437.5 -180.5t124 -555.5q0 -260 -53.5 -416.5t-182.5 -232.5l175 -280l-148 -70l-180 295q-68 -18 -172 -18q-317 0 -438 174t-121 547.5zM279 702.5q0 -311.5 81.5 -447.5t316 -136t315.5 132t81 446.5t-84 457.5t-314.5 143 t-313 -142t-82.5 -453.5z" />
<glyph unicode="R" horiz-adv-x="1261" d="M174 0v1417h524q225 0 339 -104.5t114 -325.5q0 -328 -260 -412l272 -575h-174l-256 543h-401v-543h-158zM332 682h368q289 0 289 298t-291 298h-366v-596z" />
<glyph unicode="S" horiz-adv-x="1110" d="M98 1059q0 381 457 381q150 0 358 -31l70 -10l-14 -133q-285 34 -404 34q-307 0 -307 -231q0 -129 70.5 -177t286.5 -96t306.5 -125t90.5 -253q0 -436 -453 -436q-150 0 -375 34l-73 11l16 131q279 -37 424 -37q303 0 303 285q0 115 -66.5 165t-248.5 84 q-240 51 -340.5 132t-100.5 272z" />
<glyph unicode="T" horiz-adv-x="1077" d="M27 1276v141h1024v-141h-433v-1276h-155v1276h-436z" />
<glyph unicode="U" horiz-adv-x="1318" d="M164 430v987h158v-991q0 -307 331 -307q172 0 259 72.5t87 234.5v991h156v-987q0 -238 -126 -344t-371.5 -106t-369.5 106.5t-124 343.5z" />
<glyph unicode="V" horiz-adv-x="1191" d="M49 1417h164l326 -1278h114l326 1278h164l-371 -1417h-352z" />
<glyph unicode="W" horiz-adv-x="1814" d="M61 1417h164l242 -1278h59l289 1266h184l289 -1266h60l241 1278h162l-287 -1417h-288l-269 1210l-268 -1210h-289z" />
<glyph unicode="X" horiz-adv-x="1142" d="M39 0l438 694l-438 723h178l358 -606l361 606h170l-436 -719l436 -698h-178l-357 588l-362 -588h-170z" />
<glyph unicode="Y" horiz-adv-x="1101" d="M20 1417h179l352 -663l352 663h178l-450 -821v-596h-158v596z" />
<glyph unicode="Z" horiz-adv-x="1097" d="M88 -2v182l739 1053v45h-739v139h922v-184l-742 -1053v-41h742v-141h-922z" />
<glyph unicode="[" horiz-adv-x="673" d="M162 -252v1786h430v-137h-275v-1512h275v-137h-430z" />
<glyph unicode="\" horiz-adv-x="892" d="M61 1389l138 59l630 -1419l-137 -58z" />
<glyph unicode="]" horiz-adv-x="673" d="M82 -115h274v1512h-274v137h430v-1786h-430v137z" />
<glyph unicode="^" d="M119 653l379 699h137l391 -699h-164l-293 547l-286 -547h-164z" />
<glyph unicode="_" horiz-adv-x="1294" d="M209 -188h876v-136h-876v136z" />
<glyph unicode="`" horiz-adv-x="483" d="M-2 1384l53 132l443 -201l-43 -105z" />
<glyph unicode="a" horiz-adv-x="1026" d="M82 288.5q0 149.5 75.5 220.5t237.5 87l322 31v88q0 104 -45 149t-123 45q-164 0 -340 -20l-64 -6l-6 116q225 45 399.5 45t253 -79.5t78.5 -249.5v-525q6 -76 119 -90l-6 -120q-162 0 -244 81q-182 -81 -364 -81h-4q-141 0 -215 79.5t-74 229zM240 297q0 -188 155 -188 q139 0 275 47l47 16v338l-303 -29q-92 -8 -133 -53t-41 -131z" />
<glyph unicode="b" horiz-adv-x="1077" d="M147 2v1468h152v-497q156 71 301 71q207 0 290 -117.5t83 -412.5t-103.5 -414.5t-380.5 -119.5q-86 0 -284 16zM299 127q133 -10 188 -10q201 0 265.5 91t64.5 308t-51 304t-182 87q-119 0 -244 -45l-41 -14v-721z" />
<glyph unicode="c" horiz-adv-x="892" d="M104 517q0 292 98.5 409.5t327.5 117.5q68 0 230 -24l49 -6l-6 -125q-164 18 -242 18q-174 0 -236.5 -83t-62.5 -307t58.5 -312t242.5 -88l242 18l6 -127q-184 -28 -279 -28h-6q-240 0 -331 122.5t-91 414.5z" />
<glyph unicode="d" horiz-adv-x="1083" d="M102 482.5q0 297.5 99.5 429.5t328.5 132q119 0 252 -26v452h152v-1470h-152v70q-158 -90 -311 -90q-82 0 -143.5 20t-114.5 74q-111 111 -111 408.5zM260 504q0 -238 72 -320q33 -39 69.5 -53t95 -14t131 19.5t113.5 37.5l41 19v694q-126 24 -234 24h-9 q-156 0 -217.5 -99t-61.5 -308z" />
<glyph unicode="e" horiz-adv-x="1034" d="M102 506q0 538 426 538h2q207 0 309.5 -115.5t102.5 -363.5l-8 -116h-676q0 -170 61.5 -252t214 -82t310.5 12l59 6l4 -121q-226 -32 -389 -32h-10q-221 0 -313.5 128t-92.5 398zM256 571h532q0 188 -60 266t-196.5 78t-205 -81.5t-70.5 -262.5z" />
<glyph unicode="f" horiz-adv-x="677" d="M61 891v133h129v92q0 217 61.5 296t215.5 79l207 -14l-2 -127q-115 4 -189.5 4t-106.5 -49.5t-32 -190.5v-90h297v-133h-297v-891h-154v891h-129z" />
<glyph unicode="g" horiz-adv-x="1052" d="M102 -172q0 84 37 136t125 118q-57 39 -57 141q0 35 51 129l18 33q-162 74 -161 309q0 350 383 350q98 0 182 -20l31 -6l303 8v-131l-195 4q68 -68 68 -205q0 -193 -95.5 -269.5t-299.5 -76.5q-55 0 -101 8q-37 -90 -37 -116q0 -63 39 -79t235.5 -16t282.5 -61t86 -241 q0 -324 -454 -324q-246 0 -343.5 66.5t-97.5 242.5zM254 -160q0 -109 60.5 -150.5t223 -41.5t233.5 45t71 150.5t-51.5 136t-200.5 30.5l-221 10q-68 -49 -91.5 -86t-23.5 -94zM268 694q0 -121 54.5 -172t179.5 -51t178 51t53 173t-53 173.5t-178 51.5t-179.5 -52.5 t-54.5 -173.5z" />
<glyph unicode="h" horiz-adv-x="1099" d="M147 0v1470h154v-503q162 77 311 77h4q205 0 276 -110.5t71 -392.5v-541h-154v537q0 213 -42 291.5t-177 78.5q-129 0 -250 -47l-39 -14v-846h-154z" />
<glyph unicode="i" horiz-adv-x="448" d="M147 0v1024h154v-1024h-154zM147 1255v179h154v-179h-154z" />
<glyph unicode="j" horiz-adv-x="448" d="M-100 -338q160 92 205 157t45 208v997h151v-999q0 -188 -67.5 -282.5t-272.5 -199.5zM150 1255v179h151v-179h-151z" />
<glyph unicode="k" horiz-adv-x="980" d="M147 0v1470h154v-868l160 8l293 414h174l-334 -471l348 -553h-174l-305 475l-162 -6v-469h-154z" />
<glyph unicode="l" horiz-adv-x="473" d="M160 0v1470h153v-1470h-153z" />
<glyph unicode="m" horiz-adv-x="1712" d="M147 0v1024h152v-72q154 92 299 92q190 0 266 -104q68 41 175.5 72.5t189.5 31.5q205 0 275.5 -109.5t70.5 -393.5v-541h-154v537q0 213 -41 291.5t-174 78.5q-68 0 -139.5 -19.5t-110.5 -37.5l-38 -19q26 -66 26 -283v-16v-532h-153v528q0 221 -40 300t-176 79 q-66 0 -134 -19.5t-105 -37.5l-35 -19v-831h-154z" />
<glyph unicode="n" horiz-adv-x="1099" d="M147 0v1024h152v-72q166 92 317 92q205 0 276 -110.5t71 -392.5v-541h-152v537q0 213 -42 291.5t-179 78.5q-66 0 -138.5 -19.5t-111.5 -37.5l-39 -19v-831h-154z" />
<glyph unicode="o" horiz-adv-x="1081" d="M102 514q0 281 100.5 405.5t339 124.5t338 -124.5t99.5 -405.5t-94 -407.5t-344 -126.5t-344.5 126.5t-94.5 407.5zM260 517q0 -224 54.5 -314t227.5 -90t226 89t53 314t-59.5 310t-220 85t-221 -85t-60.5 -309z" />
<glyph unicode="p" horiz-adv-x="1079" d="M147 -455v1479h152v-74q156 94 307 94q195 0 283 -123.5t88 -408.5t-103.5 -408.5t-343.5 -123.5q-125 0 -229 22v-457h-154zM301 135q139 -22 215 -22h6q166 0 232.5 94t66.5 307t-60 303t-177 90q-61 0 -132 -20.5t-112 -40.5l-39 -21v-690z" />
<glyph unicode="q" horiz-adv-x="1077" d="M102 510q0 289 106.5 411.5t377.5 122.5l342 -20v-1479h-152v502q-144 -67 -289 -67q-207 0 -296 120.5t-89 409.5zM258 509q0 -210 56.5 -301t189.5 -91q119 0 235 45l37 14v723q-127 12 -188 12q-193 0 -261.5 -96t-68.5 -306z" />
<glyph unicode="r" horiz-adv-x="708" d="M147 0v1024h152v-139q178 123 375 162v-156q-86 -16 -179.5 -51t-142.5 -62l-51 -26v-752h-154z" />
<glyph unicode="s" horiz-adv-x="952" d="M92 751.5q0 149.5 105.5 221t263.5 71.5q125 0 313 -28l57 -10l-4 -132q-227 33 -358 33q-223 0 -223 -155q0 -72 51 -101.5t233.5 -61.5t258 -89.5t75.5 -215t-101 -231t-296 -73.5q-127 0 -299 28l-62 10l9 134q233 -35 352 -35t181.5 38t62.5 127t-53.5 120.5 t-233.5 60.5t-256 84t-76 204.5z" />
<glyph unicode="t" horiz-adv-x="718" d="M55 891v133h146v313h151v-313h326v-133h-326v-490q0 -176 25.5 -231t122.5 -55l182 12l10 -127q-137 -23 -209 -23q-160 0 -221 78t-61 297v539h-146z" />
<glyph unicode="u" horiz-adv-x="1087" d="M137 489v535h154v-532q0 -223 39 -299t176 -76q68 0 137.5 19.5t106.5 37.5l36 19v831h154v-1024h-154v72q-154 -92 -303 -92q-209 0 -277.5 108.5t-68.5 400.5z" />
<glyph unicode="v" horiz-adv-x="985" d="M51 1024h164l240 -891h75l248 891h158l-289 -1024h-307z" />
<glyph unicode="w" horiz-adv-x="1560" d="M63 1024h154l207 -891h33l243 871h160l244 -871h35l204 891h154l-244 -1024h-256l-217 801l-217 -801h-256z" />
<glyph unicode="x" horiz-adv-x="925" d="M45 0l322 512l-322 512h168l250 -401l250 401h168l-326 -508l324 -516h-168l-248 399l-250 -399h-168z" />
<glyph unicode="y" horiz-adv-x="989" d="M51 1024h154l256 -891h67l258 891h154l-426 -1479h-154l134 455h-152z" />
<glyph unicode="z" horiz-adv-x="931" d="M86 0v137l573 750h-573v137h760v-137l-574 -750h574v-137h-760z" />
<glyph unicode="{" horiz-adv-x="720" d="M49 575v125q117 27 168 81.5t51 142.5l-14 262q0 188 87 270t296 90l2 -133q-135 -8 -185.5 -64t-50.5 -171l15 -252q0 -131 -44 -190.5t-171 -96.5q125 -37 170 -99.5t45 -191.5l-15 -237q0 -115 50.5 -173.5t185.5 -66.5l-4 -133q-207 8 -294 91t-87 267l14 250 q0 88 -51 142.5t-168 86.5z" />
<glyph unicode="|" horiz-adv-x="471" d="M160 -455v1925h151v-1925h-151z" />
<glyph unicode="}" horiz-adv-x="720" d="M82 -129q135 8 185 66.5t50 173.5l-14 237q0 129 45 191.5t170 99.5q-127 37 -171 96.5t-44 190.5l14 252q0 115 -50 171t-185 64l2 133q209 -8 296 -90t87 -270l-14 -262q0 -88 51 -142.5t168 -81.5v-125q-117 -33 -168 -87t-51 -142l14 -250q0 -184 -87 -267t-294 -91z " />
<glyph unicode="~" d="M137 557q127 109 238 109q59 0 211.5 -62.5t191.5 -62.5q72 0 180 69l35 25l21 -125q-129 -111 -238 -111q-55 0 -209.5 63.5t-192.5 63.5t-91.5 -23.5t-87.5 -47.5l-35 -25z" />
<glyph unicode="&#xa1;" horiz-adv-x="466" d="M147 795v229h168v-229h-168zM152 -393l10 950h141l10 -950h-161z" />
<glyph unicode="&#xa2;" d="M207 503.5q0 227.5 89 333t273 115.5v242h133v-248q80 0 175 -18l32 -6l-6 -123q-162 14 -264 14q-152 0 -212 -68.5t-60 -236.5t60 -232.5t224 -64.5l254 14l6 -125q-123 -18 -209 -22v-244h-133v240q-190 10 -276 106t-86 323.5z" />
<glyph unicode="&#xa3;" d="M164 0v135h174v586h-137v133h137v113q0 240 68.5 322.5t234.5 82.5q106 0 227 -24l45 -11l-2 -125q-164 23 -259 23t-130 -57.5t-35 -221.5v-102h351v-133h-351v-586h316l158 33l26 -131l-168 -37h-655z" />
<glyph unicode="&#xa5;" d="M47 1352h176l352 -553l349 553h176l-350 -574h278v-133h-356l-17 -39v-139h375v-133h-375v-334h-157v334h-379v133h379v139l-17 39h-364v133h286z" />
<glyph unicode="&#xa8;" horiz-adv-x="483" d="M-25 1272v184h148v-184h-148zM385 1272v184h150v-184h-150z" />
<glyph unicode="&#xa9;" horiz-adv-x="1320" d="M121 889.5q0 236.5 153.5 397.5t387 161t386 -163t152.5 -398.5t-151.5 -396t-385 -160.5t-388 161.5t-154.5 398zM211 888.5q0 -196.5 128 -332.5t320.5 -136t321.5 136t129 332.5t-129 334t-320.5 137.5t-320.5 -137.5t-129 -334zM428 888q0 169 54.5 245.5t187.5 76.5 q76 0 137 -14l20 -6l-8 -109q-78 12 -148.5 12.5t-94 -46t-23.5 -156t25.5 -159.5t97.5 -50l143 14l8 -106q-75 -25 -159 -25q-133 0 -186.5 77t-53.5 246z" />
<glyph unicode="&#xab;" horiz-adv-x="1103" d="M92 446v123l387 293v-157l-254 -193l254 -215v-160zM578 446v123l389 293v-157l-256 -193l256 -215v-160z" />
<glyph unicode="&#xad;" horiz-adv-x="897" d="M139 492v143h621v-143h-621z" />
<glyph unicode="&#xae;" horiz-adv-x="1320" d="M121 889.5q0 236.5 153.5 397.5t387 161t386 -162t152.5 -398.5t-151.5 -397t-385 -160.5t-388 161.5t-154.5 398zM211 889.5q0 -195.5 129 -332.5t320.5 -137t320.5 136t129 332.5t-129 334t-320.5 137.5t-320.5 -137.5t-129 -333zM436 578v620h226q117 0 175 -45 t58 -135t-23.5 -134t-80.5 -71l112 -235h-125l-102 219h-121v-219h-119zM553 893h121q55 0 79.5 24.5t24.5 79.5q0 104 -127 105h-98v-209z" />
<glyph unicode="&#xb4;" horiz-adv-x="548" d="M53 1315l443 201l53 -132l-453 -174z" />
<glyph unicode="&#xb8;" horiz-adv-x="538" d="M86 -430l6 98q80 -6 135 -6q90 0 90 78q0 70 -90 70h-86v192h84v-102q121 0 174.5 -33t53.5 -127q0 -184 -191 -184q-94 0 -151 10z" />
<glyph unicode="&#xbb;" horiz-adv-x="1103" d="M137 137v160l256 215l-256 193v157l387 -293v-123zM623 137v160l256 215l-256 193v157l389 -293v-123z" />
<glyph unicode="&#xbf;" horiz-adv-x="903" d="M78 -84q0 133 36 201.5t145.5 160t150.5 145.5t41 120v71h129q27 -55 26.5 -124.5t-47.5 -130t-151.5 -150.5t-139.5 -143.5t-35 -148.5t66 -142t207 -47q109 0 264 34l53 13l11 -125q-207 -61 -342 -62q-211 0 -312.5 77t-101.5 251zM434 795v229h168v-229h-168z" />
<glyph unicode="&#xc0;" horiz-adv-x="1220" d="M49 0l389 1417h344l389 -1417h-155l-103 365h-606l-102 -365h-156zM334 1772l53 135l442 -205l-47 -109zM342 506h537l-215 776h-107z" />
<glyph unicode="&#xc1;" horiz-adv-x="1220" d="M49 0l389 1417h344l389 -1417h-155l-103 365h-606l-102 -365h-156zM342 506h537l-215 776h-107zM381 1702l442 205l54 -135l-449 -179z" />
<glyph unicode="&#xc2;" horiz-adv-x="1220" d="M49 0l389 1417h344l389 -1417h-155l-103 365h-606l-102 -365h-156zM266 1620l275 276h133l274 -276h-172l-168 164l-170 -164h-172zM342 506h537l-215 776h-107z" />
<glyph unicode="&#xc3;" horiz-adv-x="1220" d="M49 0l389 1417h344l389 -1417h-155l-103 365h-606l-102 -365h-156zM256 1772q39 45 92 81.5t100.5 36.5t172 -55t145.5 -55q41 0 119 71l24 23l37 -115q-96 -114 -180 -114q-43 0 -170 55t-154 55q-45 0 -125 -74l-26 -24zM342 506h537l-215 776h-107z" />
<glyph unicode="&#xc4;" horiz-adv-x="1220" d="M49 0l389 1417h344l389 -1417h-155l-103 365h-606l-102 -365h-156zM326 1655v184h149v-184h-149zM342 506h537l-215 776h-107zM743 1655v184h148v-184h-148z" />
<glyph unicode="&#xc5;" horiz-adv-x="1220" d="M49 0l377 1370q-61 57 -61 155.5t68.5 156t175 57.5t174 -57.5t67.5 -151.5t-57 -156l378 -1374h-155l-103 365h-606l-102 -365h-156zM342 506h537l-215 776h-107zM481 1524q0 -90 88 -107h76q90 16 90 107q0 51 -33.5 79.5t-93.5 28.5q-127 1 -127 -108z" />
<glyph unicode="&#xc6;" horiz-adv-x="1794" d="M41 0l459 1425h1198v-149h-707v-471h584v-150h-584v-505h707v-150h-862v358h-514l-119 -358h-162zM365 510h471l2 766h-224z" />
<glyph unicode="&#xc7;" horiz-adv-x="1114" d="M121 705q0 190 21.5 318t77.5 226.5t158.5 143.5t266.5 45t385 -45l-6 -133q-207 37 -369 36q-225 0 -297.5 -134t-72.5 -457q0 -162 13 -260.5t52 -177.5t114 -112.5t219 -33.5t341 37l6 -135q-188 -41 -371 -43v-80q121 0 174.5 -33t53.5 -127q0 -184 -191 -184 q-94 0 -151 10l-25 4l6 98q80 -6 136 -6q90 0 90 78q0 70 -90 70h-87v172q-264 18 -359 191t-95 532z" />
<glyph unicode="&#xc8;" horiz-adv-x="1132" d="M174 0v1417h862v-139h-704v-490h581v-137h-581v-512h704v-139h-862zM346 1772l53 135l443 -205l-47 -109z" />
<glyph unicode="&#xc9;" horiz-adv-x="1132" d="M174 0v1417h862v-139h-704v-490h581v-137h-581v-512h704v-139h-862zM360 1702l443 205l53 -135l-448 -179z" />
<glyph unicode="&#xca;" horiz-adv-x="1132" d="M174 0v1417h862v-139h-704v-490h581v-137h-581v-512h704v-139h-862zM260 1620l275 276h133l274 -276h-172l-168 164l-170 -164h-172z" />
<glyph unicode="&#xcb;" horiz-adv-x="1132" d="M174 0v1417h862v-139h-704v-490h581v-137h-581v-512h704v-139h-862zM322 1655v184h149v-184h-149zM739 1655v184h148v-184h-148z" />
<glyph unicode="&#xcc;" horiz-adv-x="503" d="M-41 1772l53 135l443 -205l-47 -109zM174 0v1417h158v-1417h-158z" />
<glyph unicode="&#xcd;" horiz-adv-x="503" d="M27 1702l442 205l53 -135l-448 -179zM174 0v1417h158v-1417h-158z" />
<glyph unicode="&#xce;" horiz-adv-x="503" d="M-96 1620l274 276h133l275 -276h-172l-168 164l-170 -164h-172zM174 0v1417h158v-1417h-158z" />
<glyph unicode="&#xcf;" horiz-adv-x="503" d="M-31 1655v184h150v-184h-150zM174 0v1417h158v-1417h-158zM387 1655v184h148v-184h-148z" />
<glyph unicode="&#xd1;" horiz-adv-x="1384" d="M174 0v1417h295l543 -1278h43v1278h155v-1417h-288l-551 1278h-39v-1278h-158zM342 1772q39 45 92 81.5t100.5 36.5t172 -55t145.5 -55q41 0 119 71l24 23l37 -115q-96 -114 -180 -114q-43 0 -170 55t-154 55q-45 0 -125 -74l-26 -24z" />
<glyph unicode="&#xd2;" horiz-adv-x="1351" d="M117 701.5q0 373.5 122.5 555t436 181.5t437.5 -180.5t124 -559.5t-121 -548.5t-439.5 -169.5t-439 174t-120.5 547.5zM279 702.5q0 -311.5 81.5 -447.5t316 -136t315.5 133t81 446.5t-84 456.5t-314.5 143t-313 -142t-82.5 -453.5zM412 1772l53 135l442 -205l-47 -109z " />
<glyph unicode="&#xd3;" horiz-adv-x="1351" d="M117 701.5q0 373.5 122.5 555t436 181.5t437.5 -180.5t124 -559.5t-121 -548.5t-439.5 -169.5t-439 174t-120.5 547.5zM279 702.5q0 -311.5 81.5 -447.5t316 -136t315.5 133t81 446.5t-84 456.5t-314.5 143t-313 -142t-82.5 -453.5zM406 1702l442 205l53 -135l-448 -179z " />
<glyph unicode="&#xd4;" horiz-adv-x="1351" d="M117 701.5q0 373.5 122.5 555t436 181.5t437.5 -180.5t124 -559.5t-121 -548.5t-439.5 -169.5t-439 174t-120.5 547.5zM279 702.5q0 -311.5 81.5 -447.5t316 -136t315.5 133t81 446.5t-84 456.5t-314.5 143t-313 -142t-82.5 -453.5zM336 1620l274 276h133l275 -276h-172 l-168 164l-170 -164h-172z" />
<glyph unicode="&#xd5;" horiz-adv-x="1351" d="M117 701.5q0 373.5 122.5 555t436 181.5t437.5 -180.5t124 -559.5t-121 -548.5t-439.5 -169.5t-439 174t-120.5 547.5zM279 702.5q0 -311.5 81.5 -447.5t316 -136t315.5 133t81 446.5t-84 456.5t-314.5 143t-313 -142t-82.5 -453.5zM326 1772q39 45 92 81.5t100 36.5 t172 -55t146 -55q41 0 118 71l25 23l37 -115q-96 -114 -180 -114q-43 0 -170 55t-154 55q-45 0 -125 -74l-27 -24z" />
<glyph unicode="&#xd6;" horiz-adv-x="1351" d="M117 701.5q0 373.5 122.5 555t436 181.5t437.5 -180.5t124 -559.5t-121 -548.5t-439.5 -169.5t-439 174t-120.5 547.5zM279 702.5q0 -311.5 81.5 -447.5t316 -136t315.5 133t81 446.5t-84 456.5t-314.5 143t-313 -142t-82.5 -453.5zM393 1655v184h150v-184h-150z M811 1655v184h147v-184h-147z" />
<glyph unicode="&#xd8;" horiz-adv-x="1351" d="M117 702q0 373 122.5 554.5t436.5 181.5q154 0 258 -43l115 241l118 -49l-120 -260q190 -160 190 -625q0 -383 -121 -552.5t-440 -169.5q-141 0 -246 34l-117 -252l-116 54l120 258q-109 82 -154.5 235.5t-45.5 392.5zM279 677q0 -337 106 -456l487 1039q-78 39 -196 38 q-232 0 -314.5 -142t-82.5 -479zM494 147q69 -28 182 -28q236 0 316.5 133t80.5 462.5t-98 461.5z" />
<glyph unicode="&#xd9;" horiz-adv-x="1318" d="M164 430v987h158v-991q0 -307 331 -307q172 0 259 72.5t87 234.5v991h156v-987q0 -238 -126 -344t-371.5 -106t-369.5 106.5t-124 343.5zM401 1772l54 135l442 -205l-47 -109z" />
<glyph unicode="&#xda;" horiz-adv-x="1318" d="M164 430v987h158v-991q0 -307 331 -307q172 0 259 72.5t87 234.5v991h156v-987q0 -238 -126 -344t-371.5 -106t-369.5 106.5t-124 343.5zM408 1702l442 205l53 -135l-448 -179z" />
<glyph unicode="&#xdb;" horiz-adv-x="1318" d="M164 430v987h158v-991q0 -307 331 -307q172 0 259 72.5t87 234.5v991h156v-987q0 -238 -126 -344t-371.5 -106t-369.5 106.5t-124 343.5zM317 1620l275 276h133l274 -276h-172l-168 164l-170 -164h-172z" />
<glyph unicode="&#xdc;" horiz-adv-x="1318" d="M164 430v987h158v-991q0 -307 331 -307q172 0 259 72.5t87 234.5v991h156v-987q0 -238 -126 -344t-371.5 -106t-369.5 106.5t-124 343.5zM377 1655v184h149v-184h-149zM795 1655v184h147v-184h-147z" />
<glyph unicode="&#xdd;" horiz-adv-x="1101" d="M20 1417h179l352 -663l352 663h178l-450 -821v-596h-158v596zM342 1702l442 205l54 -135l-449 -179z" />
<glyph unicode="&#xdf;" horiz-adv-x="1175" d="M147 0v1116q0 207 93.5 291t306.5 84t306 -69.5t93 -225.5q0 -109 -39 -167t-120.5 -94t-105.5 -54.5t-24 -50t34 -58.5t167 -92.5t189.5 -131t56.5 -186.5q0 -209 -93 -295.5t-319 -86.5q-102 0 -225 22l-43 8l6 129q174 -23 250 -22q147 0 205.5 54t58.5 153.5 t-43 144.5t-176 108.5t-180 112.5t-47 126t37.5 119t117.5 77t106.5 72.5t26.5 116.5t-52 116t-188 37t-190.5 -53.5t-54.5 -202.5v-1098h-154z" />
<glyph unicode="&#xe0;" horiz-adv-x="1026" d="M82 288.5q0 149.5 75.5 220.5t237.5 87l322 31v88q0 104 -45 149t-123 45q-164 0 -340 -20l-64 -6l-6 116q225 45 399.5 45t253 -79.5t78.5 -249.5v-525q6 -76 119 -90l-6 -120q-162 0 -244 81q-184 -82 -368 -81q-141 0 -215 79.5t-74 229zM240 297q0 -188 155 -188 q139 0 275 47l47 16v338l-303 -29q-92 -8 -133 -53t-41 -131zM248 1384l53 132l442 -201l-43 -105z" />
<glyph unicode="&#xe1;" horiz-adv-x="1026" d="M82 288.5q0 149.5 75.5 220.5t237.5 87l322 31v88q0 104 -45 149t-123 45q-164 0 -340 -20l-64 -6l-6 116q225 45 399.5 45t253 -79.5t78.5 -249.5v-525q6 -76 119 -90l-6 -120q-162 0 -244 81q-184 -82 -368 -81q-141 0 -215 79.5t-74 229zM240 297q0 -188 155 -188 q139 0 275 47l47 16v338l-303 -29q-92 -8 -133 -53t-41 -131zM242 1315l442 201l53 -132l-452 -174z" />
<glyph unicode="&#xe2;" horiz-adv-x="1026" d="M82 288.5q0 149.5 75.5 220.5t237.5 87l322 31v88q0 104 -45 149t-123 45q-164 0 -340 -20l-64 -6l-6 116q225 45 399.5 45t253 -79.5t78.5 -249.5v-525q6 -76 119 -90l-6 -120q-162 0 -244 81q-184 -82 -368 -81q-141 0 -215 79.5t-74 229zM195 1212l251 287h105 l254 -287h-154l-149 179l-154 -179h-153zM240 297q0 -188 155 -188q139 0 275 47l47 16v338l-303 -29q-92 -8 -133 -53t-41 -131z" />
<glyph unicode="&#xe3;" horiz-adv-x="1026" d="M82 288.5q0 149.5 75.5 220.5t237.5 87l322 31v88q0 104 -45 149t-123 45q-164 0 -340 -20l-64 -6l-6 116q225 45 399.5 45t253 -79.5t78.5 -249.5v-525q6 -76 119 -90l-6 -120q-162 0 -244 81q-184 -82 -368 -81q-141 0 -215 79.5t-74 229zM158 1366q102 106 186 107 q43 0 149.5 -47.5t129.5 -47.5q45 0 122 60l25 18l35 -98q-94 -105 -176 -105q-43 0 -148.5 48.5t-132.5 48.5q-47 0 -127 -62l-26 -20zM240 297q0 -188 155 -188q139 0 275 47l47 16v338l-303 -29q-92 -8 -133 -53t-41 -131z" />
<glyph unicode="&#xe4;" horiz-adv-x="1026" d="M82 288.5q0 149.5 75.5 220.5t237.5 87l322 31v88q0 104 -45 149t-123 45q-164 0 -340 -20l-64 -6l-6 116q225 45 399.5 45t253 -79.5t78.5 -249.5v-525q6 -76 119 -90l-6 -120q-162 0 -244 81q-184 -82 -368 -81q-141 0 -215 79.5t-74 229zM215 1272v184h147v-184h-147z M240 297q0 -188 155 -188q139 0 275 47l47 16v338l-303 -29q-92 -8 -133 -53t-41 -131zM625 1272v184h149v-184h-149z" />
<glyph unicode="&#xe5;" horiz-adv-x="1026" d="M82 288.5q0 149.5 75.5 220.5t237.5 87l322 31v88q0 104 -45 149t-123 45q-164 0 -340 -20l-64 -6l-6 116q225 45 399.5 45t253 -79.5t78.5 -249.5v-525q6 -76 119 -90l-6 -120q-162 0 -244 81q-184 -82 -368 -81q-141 0 -215 79.5t-74 229zM240 297q0 -188 155 -188 q139 0 275 47l47 16v338l-303 -29q-92 -8 -133 -53t-41 -131zM309 1321q0 90 61.5 151.5t150.5 61.5t150.5 -61.5t61.5 -151.5t-60.5 -151.5t-150.5 -61.5t-151.5 61.5t-61.5 151.5zM410 1320q0 -48 32.5 -82t79.5 -34t80 34t33 82t-33 82t-80 34t-79.5 -34t-32.5 -82z" />
<glyph unicode="&#xe6;" horiz-adv-x="1646" d="M82 299q0 158 84 220.5t287 82.5l264 25v84q0 186 -178 186q-135 0 -326 -16l-63 -4l-7 137q244 31 404 30q209 0 276 -149q98 150 312.5 149.5t316.5 -116t102 -363.5l-8 -119h-676q0 -168 61.5 -247.5t187.5 -79.5t335 14l59 4l5 -125q-231 -33 -415.5 -32.5 t-283.5 110.5l-65 -29q-178 -82 -394 -81q-137 0 -207.5 80.5t-70.5 238.5zM240 297q0 -184 145 -184q94 0 215 29.5t152 45.5q-39 104 -39 314l-299 -25q-90 -8 -132 -51t-42 -129zM870 571h531q0 182 -60.5 259t-196.5 77t-205 -80t-69 -256z" />
<glyph unicode="&#xe7;" horiz-adv-x="892" d="M104 532.5q0 276.5 98.5 394t327.5 117.5q68 0 230 -24l49 -6l-6 -125q-164 18 -242 18q-174 0 -236.5 -83t-62.5 -307t58.5 -312t242.5 -88l242 18l6 -127q-190 -29 -285 -28h-16v-80q121 0 174 -33t53 -127q0 -184 -190 -184q-94 0 -154 10l-22 4l6 98q80 -6 135 -6 q90 0 90 78q0 70 -90 70h-86v176q-180 20 -251 145t-71 401.5z" />
<glyph unicode="&#xe8;" horiz-adv-x="1034" d="M102 506q0 539 428 538q207 0 309.5 -115.5t102.5 -363.5l-8 -116h-676q0 -170 61.5 -252t214 -82t310.5 12l59 6l4 -121q-233 -33 -399 -32q-221 0 -313.5 128t-92.5 398zM248 1386l53 132l442 -201l-43 -105zM256 571h532q0 188 -60 266t-196.5 78t-205 -81.5 t-70.5 -262.5z" />
<glyph unicode="&#xe9;" horiz-adv-x="1034" d="M102 506q0 539 428 538q207 0 309.5 -115.5t102.5 -363.5l-8 -116h-676q0 -170 61.5 -252t214 -82t310.5 12l59 6l4 -121q-233 -33 -399 -32q-221 0 -313.5 128t-92.5 398zM256 571h532q0 188 -60 266t-196.5 78t-205 -81.5t-70.5 -262.5zM297 1315l442 201l54 -132 l-453 -174z" />
<glyph unicode="&#xea;" horiz-adv-x="1034" d="M102 506q0 539 428 538q207 0 309.5 -115.5t102.5 -363.5l-8 -116h-676q0 -170 61.5 -252t214 -82t310.5 12l59 6l4 -121q-233 -33 -399 -32q-221 0 -313.5 128t-92.5 398zM229 1212l252 287h105l254 -287h-154l-149 179l-154 -179h-154zM256 571h532q0 188 -60 266 t-196.5 78t-205 -81.5t-70.5 -262.5z" />
<glyph unicode="&#xeb;" horiz-adv-x="1034" d="M102 506q0 539 428 538q207 0 309.5 -115.5t102.5 -363.5l-8 -116h-676q0 -170 61.5 -252t214 -82t310.5 12l59 6l4 -121q-233 -33 -399 -32q-221 0 -313.5 128t-92.5 398zM246 1272v184h147v-184h-147zM256 571h532q0 188 -60 266t-196.5 78t-205 -81.5t-70.5 -262.5z M655 1272v184h150v-184h-150z" />
<glyph unicode="&#xec;" horiz-adv-x="448" d="M-123 1384l53 132l443 -201l-43 -105zM147 0v1024h154v-1024h-154z" />
<glyph unicode="&#xed;" horiz-adv-x="448" d="M78 1315l442 201l53 -132l-452 -174zM147 0v1024h154v-1024h-154z" />
<glyph unicode="&#xee;" horiz-adv-x="448" d="M-86 1212l252 287h104l254 -287h-153l-150 179l-153 -179h-154zM147 0v1024h154v-1024h-154z" />
<glyph unicode="&#xef;" horiz-adv-x="448" d="M-63 1272v184h147v-184h-147zM147 0v1024h154v-1024h-154zM346 1272v184h150v-184h-150z" />
<glyph unicode="&#xf1;" horiz-adv-x="1099" d="M147 0v1024h152v-72l45 23q47 25 126 47t146 22q205 0 276 -110.5t71 -392.5v-541h-152v537q0 213 -42 291.5t-184.5 78.5t-283.5 -76v-831h-154zM244 1366q102 106 186 107q43 0 149.5 -47.5t129.5 -47.5q45 0 122 60l25 18l35 -98q-94 -105 -176 -105 q-43 0 -148.5 48.5t-132.5 48.5q-47 0 -127 -62l-26 -20z" />
<glyph unicode="&#xf2;" horiz-adv-x="1081" d="M102 514q0 281 100.5 405.5t339 124.5t338 -124.5t99.5 -405.5t-94 -407.5t-344 -126.5t-344.5 126.5t-94.5 407.5zM260 517q0 -224 54.5 -314t227.5 -90t226 89t53 314t-59.5 310t-220 85t-221 -85t-60.5 -309zM289 1384l53 132l442 -201l-43 -105z" />
<glyph unicode="&#xf3;" horiz-adv-x="1081" d="M102 514q0 281 100.5 405.5t339 124.5t338 -124.5t99.5 -405.5t-94 -407.5t-344 -126.5t-344.5 126.5t-94.5 407.5zM260 517q0 -224 54.5 -314t227.5 -90t226 89t53 314t-59.5 310t-220 85t-221 -85t-60.5 -309zM315 1315l443 201l53 -132l-453 -174z" />
<glyph unicode="&#xf4;" horiz-adv-x="1081" d="M102 514q0 281 100.5 405.5t339 124.5t338 -124.5t99.5 -405.5t-94 -407.5t-344 -126.5t-344.5 126.5t-94.5 407.5zM231 1212l252 287h105l254 -287h-154l-149 179l-154 -179h-154zM260 517q0 -224 54.5 -314t227.5 -90t226 89t53 314t-59.5 310t-220 85t-221 -85 t-60.5 -309z" />
<glyph unicode="&#xf5;" horiz-adv-x="1081" d="M102 514q0 281 100.5 405.5t339 124.5t338 -124.5t99.5 -405.5t-94 -407.5t-344 -126.5t-344.5 126.5t-94.5 407.5zM215 1366q102 106 186 107q43 0 149.5 -47.5t129.5 -47.5q45 0 123 60l24 18l35 -98q-94 -105 -176 -105q-43 0 -148.5 48.5t-131.5 48.5q-47 0 -127 -62 l-27 -20zM260 517q0 -224 54.5 -314t227.5 -90t226 89t53 314t-59.5 310t-220 85t-221 -85t-60.5 -309z" />
<glyph unicode="&#xf6;" horiz-adv-x="1081" d="M102 514q0 281 100.5 405.5t339 124.5t338 -124.5t99.5 -405.5t-94 -407.5t-344 -126.5t-344.5 126.5t-94.5 407.5zM256 1272v184h147v-184h-147zM260 517q0 -224 54.5 -314t227.5 -90t226 89t53 314t-59.5 310t-220 85t-221 -85t-60.5 -309zM666 1272v184h149v-184h-149 z" />
<glyph unicode="&#xf8;" horiz-adv-x="1081" d="M102 524q0 270 100.5 395t338.5 125q80 0 151 -16l88 213l103 -39l-88 -213q184 -102 184 -465q0 -291 -94 -417.5t-344 -126.5q-84 0 -156 16l-88 -225l-102 36l90 224q-100 55 -141.5 174t-41.5 319zM260 558q0 -183 17.5 -267t68.5 -127l297 735q-43 12 -102 12 q-160 0 -220.5 -85t-60.5 -268zM436 123q37 -10 105 -10q174 0 227 89t53 331.5t-86 320.5z" />
<glyph unicode="&#xf9;" horiz-adv-x="1087" d="M137 489v535h154v-532q0 -223 39 -299t176 -76q68 0 137.5 19.5t106.5 37.5l36 19v831h154v-1024h-154v72q-154 -92 -303 -92q-209 0 -277.5 108.5t-68.5 400.5zM233 1384l54 132l442 -201l-43 -105z" />
<glyph unicode="&#xfa;" horiz-adv-x="1087" d="M137 489v535h154v-532q0 -223 39 -299t183 -76t273 76v831h154v-1024h-154v72l-43 -23q-43 -25 -118.5 -47t-141.5 -22q-209 0 -277.5 108.5t-68.5 400.5zM317 1315l443 201l53 -132l-453 -174z" />
<glyph unicode="&#xfb;" horiz-adv-x="1087" d="M137 489v535h154v-532q0 -223 39 -299t183 -76t273 76v831h154v-1024h-154v72l-43 -23q-43 -25 -118.5 -47t-141.5 -22q-209 0 -277.5 108.5t-68.5 400.5zM215 1212l252 287h104l254 -287h-153l-150 179l-153 -179h-154z" />
<glyph unicode="&#xfc;" horiz-adv-x="1087" d="M137 489v535h154v-532q0 -223 39 -299t183 -76t273 76v831h154v-1024h-154v72l-43 -23q-43 -25 -118.5 -47t-141.5 -22q-209 0 -277.5 108.5t-68.5 400.5zM270 1272v184h148v-184h-148zM680 1272v184h149v-184h-149z" />
<glyph unicode="&#xfd;" horiz-adv-x="989" d="M51 1024h154l256 -891h67l258 891h154l-426 -1479h-154l134 455h-152zM315 1315l443 201l53 -132l-453 -174z" />
<glyph unicode="&#xff;" horiz-adv-x="989" d="M51 1024h154l256 -891h67l258 891h154l-426 -1479h-154l134 455h-152zM213 1272v184h147v-184h-147zM623 1272v184h149v-184h-149z" />
<glyph unicode="&#x100;" horiz-adv-x="1220" d="M49 0l389 1417h344l389 -1417h-155l-103 365h-606l-102 -365h-156zM289 1663v119h637v-119h-637zM342 506h537l-215 776h-107z" />
<glyph unicode="&#x101;" horiz-adv-x="1026" d="M82 288.5q0 149.5 75.5 220.5t237.5 87l322 31v88q0 104 -45 149t-123 45q-164 0 -340 -20l-64 -6l-6 116q225 45 399.5 45t253 -79.5t78.5 -249.5v-525q6 -76 119 -90l-6 -120q-162 0 -244 81q-184 -82 -368 -81q-141 0 -215 79.5t-74 229zM229 1278v121h564v-121h-564z M240 297q0 -188 155 -188q139 0 275 47l47 16v338l-303 -29q-92 -8 -133 -53t-41 -131z" />
<glyph unicode="&#x102;" horiz-adv-x="1220" d="M49 0l389 1417h344l389 -1417h-155l-103 365h-606l-102 -365h-156zM285 1880h141q2 -68 54 -111t127 -43t126 43t53 111h144q-6 -121 -94 -194.5t-229.5 -73.5t-228.5 73.5t-93 194.5zM342 506h537l-215 776h-107z" />
<glyph unicode="&#x103;" horiz-adv-x="1030" d="M82 290.5q0 147.5 75.5 219.5t237.5 86l322 31v84q0 186 -178 186q-150 0 -392 -27l-6 134l66 10q197 31 340 30q164 0 243.5 -80.5t79.5 -252.5v-504q2 -80 117 -94l-4 -133q-168 0 -244 77l-51 -20q-51 -18 -145 -37.5t-183 -19.5q-133 0 -205.5 81.5t-72.5 229z M188 1501h158q4 -69 48 -111t123 -42t124 42t47 111h158q-6 -129 -93 -210t-235.5 -81t-235.5 81t-94 210zM240 297q0 -88 37.5 -136t107.5 -48q154 0 332 61v328l-303 -25q-174 -16 -174 -180z" />
<glyph unicode="&#x104;" horiz-adv-x="1220" d="M49 0l389 1417h344l389 -1417h-16q-143 -143 -143 -209q0 -39 23.5 -63.5t60.5 -24.5l102 12l14 -118q-92 -18 -167.5 -18.5t-129 47.5t-53.5 121t51.5 141.5t102.5 113.5l-103 363h-606l-102 -365h-156zM342 506h537l-215 776h-107z" />
<glyph unicode="&#x105;" horiz-adv-x="1024" d="M82 288.5q0 149.5 75.5 220.5t237.5 87l322 31v88q0 104 -45 149t-123 45q-164 0 -340 -20l-64 -6l-6 116q225 45 399.5 45t253 -79.5t78.5 -249.5v-525q6 -76 119 -90l-6 -120q-25 0 -70 4q-125 -131 -125 -193q0 -39 24 -63.5t60 -24.5l103 12l14 -118 q-92 -18 -168 -18.5t-129 47.5t-53 113.5t39 127.5q55 88 127 145q-33 16 -66 49q-184 -82 -368 -81q-141 0 -215 79.5t-74 229zM240 297q0 -188 155 -188q139 0 275 47l47 16v338l-303 -29q-92 -8 -133 -53t-41 -131z" />
<glyph unicode="&#x106;" horiz-adv-x="1114" d="M121 706.5q0 188.5 21.5 316.5t77.5 226.5t158.5 143.5t266.5 45t385 -45l-6 -133q-207 37 -369 36q-225 0 -297.5 -134t-72.5 -457q0 -162 13 -260.5t52 -177.5t114 -112.5t219 -33.5t341 37l6 -135q-211 -43 -379 -43t-271.5 46t-159.5 143t-77.5 223t-21.5 314.5z M371 1702l442 205l53 -135l-448 -179z" />
<glyph unicode="&#x107;" horiz-adv-x="892" d="M104 517q0 292 98.5 409.5t327.5 117.5q68 0 230 -24l49 -6l-6 -125q-164 18 -242 18q-174 0 -236.5 -83t-62.5 -307t58.5 -312t242.5 -88l242 18l6 -127q-190 -29 -285 -28q-240 0 -331 122.5t-91 414.5zM258 1315l442 201l54 -132l-453 -174z" />
<glyph unicode="&#x108;" horiz-adv-x="1114" d="M121 706.5q0 188.5 21.5 316.5t77.5 226.5t158.5 143.5t266.5 45t385 -45l-6 -133q-207 37 -369 36q-225 0 -297.5 -134t-72.5 -457q0 -162 13 -260.5t52 -177.5t114 -112.5t219 -33.5t341 37l6 -135q-211 -43 -379 -43t-271.5 46t-159.5 143t-77.5 223t-21.5 314.5z M295 1620l274 276h133l275 -276h-172l-168 164l-170 -164h-172z" />
<glyph unicode="&#x109;" horiz-adv-x="892" d="M104 517q0 292 98.5 409.5t327.5 117.5q68 0 230 -24l49 -6l-6 -125q-164 18 -242 18q-174 0 -236.5 -83t-62.5 -307t58.5 -312t242.5 -88l242 18l6 -127q-190 -29 -285 -28q-240 0 -331 122.5t-91 414.5zM170 1212l252 287h104l254 -287h-153l-150 179l-153 -179h-154z " />
<glyph unicode="&#x10a;" horiz-adv-x="1114" d="M121 706.5q0 188.5 21.5 316.5t77.5 226.5t158.5 143.5t266.5 45t385 -45l-6 -133q-207 37 -369 36q-225 0 -297.5 -134t-72.5 -457q0 -162 13 -260.5t52 -177.5t114 -112.5t219 -33.5t341 37l6 -135q-211 -43 -379 -43t-271.5 46t-159.5 143t-77.5 223t-21.5 314.5z M522 1634v178h152v-178h-152z" />
<glyph unicode="&#x10b;" horiz-adv-x="892" d="M104 517q0 292 98.5 409.5t327.5 117.5q68 0 230 -24l49 -6l-6 -125q-164 18 -242 18q-174 0 -236.5 -83t-62.5 -307t58.5 -312t242.5 -88l242 18l6 -127q-190 -29 -285 -28q-240 0 -331 122.5t-91 414.5zM438 1247v178h152v-178h-152z" />
<glyph unicode="&#x10c;" horiz-adv-x="1114" d="M121 706.5q0 188.5 21.5 316.5t77.5 226.5t158.5 143.5t266.5 45t385 -45l-6 -133q-207 37 -369 36q-225 0 -297.5 -134t-72.5 -457q0 -162 13 -260.5t52 -177.5t114 -112.5t219 -33.5t341 37l6 -135q-211 -43 -379 -43t-271.5 46t-159.5 143t-77.5 223t-21.5 314.5z M281 1896h172l170 -163l168 163h172l-275 -276h-133z" />
<glyph unicode="&#x10d;" horiz-adv-x="892" d="M104 517q0 292 98.5 409.5t327.5 117.5q68 0 230 -24l49 -6l-6 -125q-164 18 -242 18q-174 0 -236.5 -83t-62.5 -307t58.5 -312t242.5 -88l242 18l6 -127q-190 -29 -285 -28q-240 0 -331 122.5t-91 414.5zM186 1499h152l154 -178l151 178h154l-256 -287h-103z" />
<glyph unicode="&#x10e;" horiz-adv-x="1320" d="M174 0v1417h506q346 0 459 -274q66 -158 65 -414q0 -535 -266 -674q-106 -55 -258 -55h-506zM262 1896h172l170 -163l168 163h172l-274 -276h-133zM332 139h348q362 0 362 590q0 408 -182 508q-74 41 -180 41h-348v-1139z" />
<glyph unicode="&#x10f;" horiz-adv-x="1177" d="M102 482.5q0 297.5 99.5 429.5t328.5 132q119 0 252 -26v452h152v-1470h-152v70q-158 -90 -311 -90q-82 0 -143.5 20t-114.5 74q-111 111 -111 408.5zM260 504q0 -238 72 -320q33 -39 69.5 -53t95 -14t131 19.5t113.5 37.5l41 19v694q-131 25 -243 24q-156 0 -217.5 -99 t-61.5 -308zM1061 975l82 424h151l-100 -424h-133z" />
<glyph unicode="&#x110;" horiz-adv-x="1325" d="M41 637v149h139v639h506q289 0 406.5 -180t117.5 -510q0 -537 -268 -678q-104 -57 -256 -57h-506v637h-139zM336 150h350q361 -1 361 585q0 397 -183 500q-72 41 -178 41h-350v-490h323v-149h-323v-487z" />
<glyph unicode="&#x111;" horiz-adv-x="1083" d="M102 482.5q0 297.5 99.5 429.5t328.5 132q119 0 252 -26v256h-397v135h397v61h152v-61h47v-135h-47v-1274h-152v70q-158 -90 -311 -90q-82 0 -143.5 20t-114.5 74q-111 111 -111 408.5zM260 504q0 -238 72 -320q33 -39 69.5 -53t95 -14t131 19.5t113.5 37.5l41 19v694 q-131 25 -243 24q-156 0 -217.5 -99t-61.5 -308z" />
<glyph unicode="&#x112;" horiz-adv-x="1132" d="M174 0v1417h862v-139h-704v-490h581v-137h-581v-512h704v-139h-862zM283 1663v119h637v-119h-637z" />
<glyph unicode="&#x113;" horiz-adv-x="1034" d="M102 506q0 539 428 538q207 0 309.5 -115.5t102.5 -363.5l-8 -116h-676q0 -170 61.5 -252t214 -82t310.5 12l59 6l4 -121q-233 -33 -399 -32q-221 0 -313.5 128t-92.5 398zM252 1278v121h563v-121h-563zM256 571h532q0 188 -60 266t-196.5 78t-205 -81.5t-70.5 -262.5z " />
<glyph unicode="&#x114;" horiz-adv-x="1132" d="M174 0v1417h862v-139h-704v-490h581v-137h-581v-512h704v-139h-862zM301 1880h141q2 -68 54.5 -111t127 -43t126 43t53.5 111h143q-6 -121 -94 -194.5t-229.5 -73.5t-228.5 73.5t-93 194.5z" />
<glyph unicode="&#x115;" horiz-adv-x="1034" d="M102 506q0 539 428 538q207 0 309.5 -115.5t102.5 -363.5l-8 -116h-676q0 -170 61.5 -252t214 -82t310.5 12l59 6l4 -121q-233 -33 -399 -32q-221 0 -313.5 128t-92.5 398zM221 1487h125q4 -78 51 -125t128 -47t128 47t49 125h127q-6 -123 -88 -205t-216 -82t-216 82 t-88 205zM256 571h532q0 188 -60 266t-196.5 78t-205 -81.5t-70.5 -262.5z" />
<glyph unicode="&#x116;" horiz-adv-x="1132" d="M174 0v1417h862v-139h-704v-490h581v-137h-581v-512h704v-139h-862zM532 1640v179h152v-179h-152z" />
<glyph unicode="&#x117;" horiz-adv-x="1034" d="M102 506q0 539 428 538q207 0 309.5 -115.5t102.5 -363.5l-8 -116h-676q0 -170 61.5 -252t214 -82t310.5 12l59 6l4 -121q-233 -33 -399 -32q-221 0 -313.5 128t-92.5 398zM256 571h532q0 188 -60 266t-196.5 78t-205 -81.5t-70.5 -262.5zM444 1247v178h152v-178h-152z " />
<glyph unicode="&#x118;" horiz-adv-x="1132" d="M174 0v1417h862v-139h-704v-490h581v-137h-581v-512h704v-139h-59q-143 -143 -143 -209q0 -39 23.5 -63.5t60.5 -24.5l102 12l14 -118q-92 -18 -167.5 -18.5t-129 47.5t-53.5 121t50 140.5t102 112.5h-662z" />
<glyph unicode="&#x119;" horiz-adv-x="1036" d="M102 506q0 539 428 538q207 0 309.5 -115.5t102.5 -363.5l-8 -116h-676q0 -170 61.5 -252t206.5 -82q160 0 318 12l59 6l4 -121q-33 -4 -71 -10h12q-146 -143 -146 -211q0 -39 24 -63.5t60 -24.5l103 12l14 -118q-92 -18 -168 -18.5t-129 47.5t-53 139.5t139 224.5 q-106 -10 -184 -10q-221 0 -313.5 128t-92.5 398zM256 571h532q0 188 -60 266t-196.5 78t-205 -81.5t-70.5 -262.5z" />
<glyph unicode="&#x11a;" horiz-adv-x="1132" d="M174 0v1417h862v-139h-704v-490h581v-137h-581v-512h704v-139h-862zM254 1896h172l170 -163l168 163h172l-274 -276h-134z" />
<glyph unicode="&#x11b;" horiz-adv-x="1034" d="M102 506q0 539 428 538q207 0 309.5 -115.5t102.5 -363.5l-8 -116h-676q0 -170 61.5 -252t214 -82t310.5 12l59 6l4 -121q-233 -33 -399 -32q-221 0 -313.5 128t-92.5 398zM213 1499h152l153 -178l152 178h153l-256 -287h-102zM256 571h532q0 188 -60 266t-196.5 78 t-205 -81.5t-70.5 -262.5z" />
<glyph unicode="&#x11c;" horiz-adv-x="1261" d="M117 711.5q0 371.5 119.5 551t425.5 179.5q190 0 395 -41l69 -12l-6 -132q-254 43 -440 43q-236 0 -317.5 -136t-81.5 -452.5t81.5 -452.5t315.5 -136q143 0 293 29v436h-236v141h391v-696q-258 -49 -460 -49q-309 0 -429 178t-120 549.5zM319 1614l275 276h133l274 -276 h-172l-167 164l-170 -164h-173z" />
<glyph unicode="&#x11d;" horiz-adv-x="1052" d="M102 -172q0 84 37 136t125 118q-57 39 -57 141q0 35 51 129l18 33q-162 74 -161 309q0 350 383 350q98 0 182 -20l31 -6l303 8v-131l-195 4q68 -68 68 -205q0 -193 -95.5 -269.5t-299.5 -76.5q-55 0 -101 8q-37 -90 -37 -116q0 -63 39 -79t235.5 -16t282.5 -61t86 -240 q1 -325 -454 -325q-246 0 -343.5 66.5t-97.5 242.5zM231 1212l252 287h105l254 -287h-154l-149 179l-154 -179h-154zM254 -160q0 -109 60.5 -150.5t223 -41.5t233.5 45t71 150.5t-51.5 136t-200.5 30.5l-221 10q-68 -49 -91.5 -86t-23.5 -94zM268 694q0 -121 54.5 -172 t179.5 -51t178 51t53 173t-53 173.5t-178 51.5t-179.5 -52.5t-54.5 -173.5z" />
<glyph unicode="&#x11e;" horiz-adv-x="1261" d="M117 707.5q0 371.5 119.5 551t425.5 179.5q190 0 395 -41l69 -13l-6 -131q-254 43 -440 43q-236 0 -317.5 -136t-81.5 -452.5t81.5 -452.5t315.5 -136q143 0 293 28v437h-236v141h391v-696q-258 -49 -460 -49q-309 0 -429 178t-120 549.5zM350 1880h142q2 -68 54 -111 t127 -43t126 43t53 111h143q-6 -121 -94 -194.5t-229.5 -73.5t-228.5 73.5t-93 194.5z" />
<glyph unicode="&#x11f;" horiz-adv-x="1052" d="M102 -172q0 84 37 136t125 118q-57 39 -57 141q0 35 51 129l18 33q-162 74 -161 309q0 350 383 350q98 0 182 -20l31 -6l303 8v-131l-195 4q68 -68 68 -205q0 -193 -95.5 -269.5t-299.5 -76.5q-55 0 -101 8q-37 -90 -37 -116q0 -63 39 -79t235.5 -16t282.5 -61t86 -240 q1 -325 -454 -325q-246 0 -343.5 66.5t-97.5 242.5zM231 1487h125q4 -78 51.5 -125t128 -47t128 47t49.5 125h127q-6 -123 -88 -205t-216.5 -82t-216 82t-88.5 205zM254 -160q0 -109 60.5 -150.5t223 -41.5t233.5 45t71 150.5t-51.5 136t-200.5 30.5l-221 10 q-68 -49 -91.5 -86t-23.5 -94zM268 694q0 -121 54.5 -172t179.5 -51t178 51t53 173t-53 173.5t-178 51.5t-179.5 -52.5t-54.5 -173.5z" />
<glyph unicode="&#x120;" horiz-adv-x="1261" d="M117 707.5q0 371.5 119.5 551t425.5 179.5q190 0 395 -41l69 -13l-6 -131q-254 43 -440 43q-236 0 -317.5 -136t-81.5 -452.5t81.5 -452.5t315.5 -136q143 0 293 28v437h-236v141h391v-696q-258 -49 -460 -49q-309 0 -429 178t-120 549.5zM565 1640v179h152v-179h-152z " />
<glyph unicode="&#x121;" horiz-adv-x="1052" d="M102 -172q0 84 37 136t125 118q-57 39 -57 141q0 35 51 129l18 33q-162 74 -161 309q0 350 383 350q98 0 182 -20l31 -6l303 8v-131l-195 4q68 -68 68 -205q0 -193 -95.5 -269.5t-299.5 -76.5q-55 0 -101 8q-37 -90 -37 -116q0 -63 39 -79t235.5 -16t282.5 -61t86 -240 q1 -325 -454 -325q-246 0 -343.5 66.5t-97.5 242.5zM254 -160q0 -109 60.5 -150.5t223 -41.5t233.5 45t71 150.5t-51.5 136t-200.5 30.5l-221 10q-68 -49 -91.5 -86t-23.5 -94zM268 694q0 -121 54.5 -172t179.5 -51t178 51t53 173t-53 173.5t-178 51.5t-179.5 -52.5 t-54.5 -173.5zM459 1247v178h151v-178h-151z" />
<glyph unicode="&#x122;" horiz-adv-x="1261" d="M117 707.5q0 371.5 119.5 551t425.5 179.5q190 0 395 -41l69 -13l-6 -131q-254 43 -440 43q-236 0 -317.5 -136t-81.5 -452.5t81.5 -452.5t315.5 -136q143 0 293 28v437h-236v141h391v-696q-258 -49 -460 -49q-309 0 -429 178t-120 549.5zM532 -584l84 424h152l-102 -424 h-134z" />
<glyph unicode="&#x123;" horiz-adv-x="1052" d="M102 -172q0 84 37 136t125 118q-57 39 -57 141q0 35 51 129l18 33q-162 74 -161 309q0 350 383 350q98 0 182 -20l31 -6l303 8v-131l-195 4q68 -68 68 -205q0 -193 -95.5 -269.5t-299.5 -76.5q-55 0 -101 8q-37 -90 -37 -116q0 -63 39 -79t235.5 -16t282.5 -61t86 -240 q1 -325 -454 -325q-246 0 -343.5 66.5t-97.5 242.5zM254 -160q0 -109 60.5 -150.5t223 -41.5t233.5 45t71 150.5t-51.5 136t-200.5 30.5l-221 10q-68 -49 -91.5 -86t-23.5 -94zM268 694q0 -121 54.5 -172t179.5 -51t178 51t53 173t-53 173.5t-178 51.5t-179.5 -52.5 t-54.5 -173.5zM393 1206l103 424h133l-84 -424h-152z" />
<glyph unicode="&#x124;" horiz-adv-x="1382" d="M174 0v1417h158v-635h721v635h155v-1417h-155v641h-721v-641h-158zM352 1620l275 276h133l274 -276h-172l-168 164l-170 -164h-172z" />
<glyph unicode="&#x125;" horiz-adv-x="1099" d="M147 0v1470h154v-503q164 78 315 77q205 0 276 -110.5t71 -392.5v-541h-154v537q0 213 -42 291.5t-177 78.5q-129 0 -250 -47l-39 -14v-846h-154zM242 1585l252 287h104l254 -287h-154l-149 178l-154 -178h-153z" />
<glyph unicode="&#x126;" horiz-adv-x="1404" d="M31 1040v134h151v243h158v-243h721v243h156v-243h169v-134h-169v-1040h-156v641h-721v-641h-158v1040h-151zM340 782h721v258h-721v-258z" />
<glyph unicode="&#x127;" horiz-adv-x="1099" d="M20 1188v135h127v147h154v-147h315v-135h-315v-221q164 78 315 77q205 0 276 -110.5t71 -392.5v-541h-154v537q0 213 -42 291.5t-177 78.5q-129 0 -250 -47l-39 -14v-846h-154v1188h-127z" />
<glyph unicode="&#x128;" horiz-adv-x="503" d="M-102 1772q39 45 92 81.5t99 36.5t172 -55t149 -55q39 0 114 71l27 23l37 -115q-96 -114 -178 -114q-45 0 -172 55t-152 55q-47 0 -127 -74l-27 -24zM174 0v1417h158v-1417h-158z" />
<glyph unicode="&#x129;" horiz-adv-x="448" d="M-96 1366q102 106 186 107q43 0 149.5 -47.5t129.5 -47.5q45 0 123 60l24 18l35 -98q-94 -105 -176 -105q-43 0 -148.5 48.5t-132.5 48.5q-47 0 -127 -62l-26 -20zM147 0v1024h154v-1024h-154z" />
<glyph unicode="&#x12a;" horiz-adv-x="503" d="M-61 1663v119h636v-119h-636zM174 0v1417h158v-1417h-158z" />
<glyph unicode="&#x12b;" horiz-adv-x="448" d="M-57 1278v121h563v-121h-563zM147 0v1024h154v-1024h-154z" />
<glyph unicode="&#x12c;" horiz-adv-x="503" d="M-53 1880h141q2 -68 53.5 -111t127 -43t126.5 43t54 111h143q-6 -121 -94 -194.5t-229.5 -73.5t-228.5 73.5t-93 194.5zM174 0v1417h158v-1417h-158z" />
<glyph unicode="&#x12d;" horiz-adv-x="448" d="M-80 1487h125q4 -78 51 -125t128 -47t128 47t49 125h127q-6 -123 -88 -205t-216 -82t-216 82t-88 205zM147 0v1024h154v-1024h-154z" />
<glyph unicode="&#x12e;" horiz-adv-x="503" d="M29 -253q0 73 50 140.5t101 112.5h-6v1417h158v-1417q-156 -141 -156 -209q0 -39 23.5 -63.5t60.5 -24.5l105 12l14 -118q-92 -18 -168 -18.5t-129 47.5t-53 121z" />
<glyph unicode="&#x12f;" horiz-adv-x="448" d="M-2 -244q0 109 158 244h-9v1024h154v-1024q-156 -139 -156 -209q0 -39 24 -63.5t60 -24.5l105 12l14 -118q-92 -18 -168 -18.5t-129 47.5t-53 130zM147 1255v179h154v-179h-154z" />
<glyph unicode="&#x130;" horiz-adv-x="503" d="M174 0v1417h158v-1417h-158zM176 1640v179h152v-179h-152z" />
<glyph unicode="&#x131;" horiz-adv-x="448" d="M147 0v1024h154v-1024h-154z" />
<glyph unicode="&#x134;" horiz-adv-x="591" d="M4 1614l275 276h133l274 -276h-172l-168 164l-170 -164h-172zM37 -6q147 0 189 36t42 187v1200h156l2 -1222q0 -172 -57 -252q-47 -68 -187 -84q-55 -6 -145 -6v141z" />
<glyph unicode="&#x135;" horiz-adv-x="448" d="M-100 -338q158 92 204 158t46 207v997h151v-999q0 -188 -67.5 -282.5t-272.5 -199.5zM-84 1212l252 287h104l254 -287h-153l-150 179l-153 -179h-154z" />
<glyph unicode="&#x136;" horiz-adv-x="1163" d="M174 0v1425h158v-657l231 8l363 649h182l-406 -712l428 -713h-186l-377 627l-235 -9v-618h-158zM459 -584l82 424h151l-100 -424h-133z" />
<glyph unicode="&#x137;" horiz-adv-x="980" d="M147 0v1470h154v-868l160 8l293 414h174l-334 -471l348 -553h-174l-305 475l-162 -6v-469h-154zM205 -584l82 424h153l-102 -424h-133z" />
<glyph unicode="&#x139;" horiz-adv-x="980" d="M174 0v1425h158v-1273h622v-152h-780zM274 1702l443 205l53 -135l-448 -179z" />
<glyph unicode="&#x13a;" horiz-adv-x="473" d="M106 1733l443 200l53 -131l-452 -174zM160 0v1470h153v-1470h-153z" />
<glyph unicode="&#x13b;" horiz-adv-x="980" d="M174 0v1417h158v-1276h622v-141h-780zM410 -584l84 424h151l-102 -424h-133z" />
<glyph unicode="&#x13c;" horiz-adv-x="473" d="M68 -584l84 424h151l-102 -424h-133zM160 0v1470h153v-1470h-153z" />
<glyph unicode="&#x13d;" horiz-adv-x="1011" d="M174 0v1417h158v-1276h622v-141h-780zM750 946v479h149v-479h-149z" />
<glyph unicode="&#x13e;" horiz-adv-x="552" d="M160 0v1470h153v-1470h-153zM438 975l84 424h152l-101 -424h-135z" />
<glyph unicode="&#x141;" horiz-adv-x="989" d="M-8 582l192 135v700h158v-590l291 203l76 -104l-367 -258v-527h623v-141h-781v557l-116 -82z" />
<glyph unicode="&#x142;" horiz-adv-x="634" d="M20 582l213 149v739h154v-632l152 106l75 -106l-227 -160v-678h-154v571l-137 -96z" />
<glyph unicode="&#x143;" horiz-adv-x="1384" d="M174 0v1417h295l543 -1278h43v1278h155v-1417h-288l-551 1278h-39v-1278h-158zM451 1702l442 205l53 -135l-448 -179z" />
<glyph unicode="&#x144;" horiz-adv-x="1099" d="M147 0v1024h152v-72l45 23q47 25 126 47t146 22q205 0 276 -110.5t71 -392.5v-541h-152v537q0 213 -42 291.5t-184.5 78.5t-283.5 -76v-831h-154zM328 1315l442 201l53 -132l-452 -174z" />
<glyph unicode="&#x145;" horiz-adv-x="1384" d="M174 0v1417h295l543 -1278h43v1278h155v-1417h-288l-551 1278h-39v-1278h-158zM580 -584l84 424h151l-102 -424h-133z" />
<glyph unicode="&#x146;" horiz-adv-x="1099" d="M147 0v1024h152v-72q166 92 317 92q205 0 276 -110.5t71 -392.5v-541h-152v537q0 213 -42 291.5t-179 78.5q-66 0 -138.5 -19.5t-111.5 -37.5l-39 -19v-831h-154zM260 -584l84 424h152l-101 -424h-135z" />
<glyph unicode="&#x147;" horiz-adv-x="1384" d="M174 0v1417h295l543 -1278h43v1278h155v-1417h-288l-551 1278h-39v-1278h-158zM352 1890h172l170 -164l168 164h172l-274 -276h-133z" />
<glyph unicode="&#x148;" horiz-adv-x="1099" d="M147 0v1024h152v-72q166 92 317 92q205 0 276 -110.5t71 -392.5v-541h-152v537q0 213 -42 291.5t-179 78.5q-66 0 -138.5 -19.5t-111.5 -37.5l-39 -19v-831h-154zM244 1499h151l154 -178l151 178h154l-256 -287h-102z" />
<glyph unicode="&#x14a;" horiz-adv-x="1384" d="M174 0v1417h295l543 -1278h43v1278h155v-239l2 -1223q0 -172 -57 -254q-47 -66 -186 -82q-55 -6 -146 -6v141q147 0 189.5 36t42.5 187v23h-133l-551 1278h-39v-1278h-158z" />
<glyph unicode="&#x14b;" horiz-adv-x="1101" d="M147 0v1024h152v-68q178 88 324 88q197 0 269.5 -117.5t72.5 -394.5v-536q0 -190 -63.5 -282.5t-266.5 -198.5l-66 129q154 88 198 151.5t44 196.5v538q0 205 -42 290t-171 85q-59 0 -134 -18.5t-120 -38.5l-45 -19v-829h-152z" />
<glyph unicode="&#x14c;" horiz-adv-x="1351" d="M117 701.5q0 373.5 122.5 555t436 181.5t437.5 -180.5t124 -559.5t-121 -548.5t-439.5 -169.5t-439 174t-120.5 547.5zM279 702.5q0 -311.5 81.5 -447.5t316 -136t315.5 133t81 446.5t-84 456.5t-314.5 143t-313 -142t-82.5 -453.5zM356 1663v119h637v-119h-637z" />
<glyph unicode="&#x14d;" horiz-adv-x="1081" d="M102 514q0 281 100.5 405.5t339 124.5t338 -124.5t99.5 -405.5t-94 -407.5t-344 -126.5t-344.5 126.5t-94.5 407.5zM260 517q0 -224 54.5 -314t227.5 -90t226 89t53 314t-59.5 310t-220 85t-221 -85t-60.5 -309zM262 1303v120h563v-120h-563z" />
<glyph unicode="&#x14e;" horiz-adv-x="1351" d="M117 701.5q0 373.5 122.5 555t436 181.5t437.5 -180.5t124 -559.5t-121 -548.5t-439.5 -169.5t-439 174t-120.5 547.5zM279 702.5q0 -311.5 81.5 -447.5t316 -136t315.5 133t81 446.5t-84 456.5t-314.5 143t-313 -142t-82.5 -453.5zM356 1880h142q2 -68 53 -111t127 -43 t127 43t53 111h143q-6 -121 -94 -194.5t-229 -73.5t-228.5 73.5t-93.5 194.5z" />
<glyph unicode="&#x14f;" horiz-adv-x="1081" d="M102 514q0 281 100.5 405.5t339 124.5t338 -124.5t99.5 -405.5t-94 -407.5t-344 -126.5t-344.5 126.5t-94.5 407.5zM242 1487h125q4 -78 51 -125t128 -47t128 47t49 125h127q-6 -123 -88 -205t-216 -82t-216 82t-88 205zM260 517q0 -224 54.5 -314t227.5 -90t226 89 t53 314t-59.5 310t-220 85t-221 -85t-60.5 -309z" />
<glyph unicode="&#x150;" horiz-adv-x="1351" d="M117 701.5q0 373.5 122.5 555t436 181.5t437.5 -180.5t124 -559.5t-121 -548.5t-439.5 -169.5t-439 174t-120.5 547.5zM279 702.5q0 -311.5 81.5 -447.5t316 -136t315.5 133t81 446.5t-84 456.5t-314.5 143t-313 -142t-82.5 -453.5zM309 1608l291 270l111 -72l-297 -264z M713 1608l291 268l112 -72l-299 -264z" />
<glyph unicode="&#x151;" horiz-adv-x="1081" d="M102 514q0 281 100.5 405.5t339 124.5t338 -124.5t99.5 -405.5t-94 -407.5t-344 -126.5t-344.5 126.5t-94.5 407.5zM190 1278l297 283l111 -86l-301 -283zM260 517q0 -224 54.5 -314t227.5 -90t226 89t53 314t-59.5 310t-220 85t-221 -85t-60.5 -309zM586 1278l297 285 l110 -88l-303 -283z" />
<glyph unicode="&#x152;" horiz-adv-x="1894" d="M117 710q0 384 118.5 560t423.5 176q162 0 289 -21h850v-151h-694v-469h571v-152h-571v-501h694v-154h-846q-188 -18 -293 -18q-313 0 -427.5 173t-114.5 557zM281 709.5q0 -316.5 79.5 -446.5t319.5 -130q106 0 268 14v1127q-215 18 -272 18q-231 0 -313 -133 t-82 -449.5z" />
<glyph unicode="&#x153;" horiz-adv-x="1755" d="M102 515q0 282 101.5 405.5t339.5 123.5q139 0 226 -50t134 -165q92 215 348 215q207 0 309.5 -115.5t102.5 -363.5l-8 -119h-676q0 -168 61.5 -247.5t188.5 -79.5t334 14l59 4l4 -125q-231 -33 -397 -32q-244 0 -332 186q-45 -98 -129 -142t-225 -44 q-252 0 -346.5 126.5t-94.5 408.5zM260 519q0 -220 55.5 -311t226.5 -91t225 92t54 305t-64.5 302t-220 89t-216 -83t-60.5 -303zM977 571h532q0 182 -60 259t-198 77q-274 0 -274 -336z" />
<glyph unicode="&#x154;" horiz-adv-x="1261" d="M174 0v1417h524q225 0 339 -104.5t114 -325.5q0 -328 -260 -412l272 -575h-174l-256 543h-401v-543h-158zM332 682h368q289 0 289 298t-291 298h-366v-596zM383 1702l442 205l54 -135l-449 -179z" />
<glyph unicode="&#x155;" horiz-adv-x="708" d="M137 1315l443 201l53 -132l-453 -174zM147 0v1024h152v-139q178 123 375 162v-156q-86 -16 -179.5 -51t-142.5 -62l-51 -26v-752h-154z" />
<glyph unicode="&#x156;" horiz-adv-x="1261" d="M174 0v1417h524q225 0 339 -104.5t114 -325.5q0 -328 -260 -412l272 -575h-174l-256 543h-401v-543h-158zM332 682h368q289 0 289 298t-291 298h-366v-596zM502 -584l84 424h151l-102 -424h-133z" />
<glyph unicode="&#x157;" horiz-adv-x="708" d="M66 -584l81 424h152l-100 -424h-133zM147 0v1024h152v-139q178 123 375 162v-156q-86 -16 -179.5 -51t-142.5 -62l-51 -26v-752h-154z" />
<glyph unicode="&#x158;" horiz-adv-x="1261" d="M174 0v1417h524q225 0 339 -104.5t114 -325.5q0 -328 -260 -412l272 -575h-174l-256 543h-401v-543h-158zM274 1896h172l170 -163l168 163h172l-274 -276h-133zM332 682h368q289 0 289 298t-291 298h-366v-596z" />
<glyph unicode="&#x159;" horiz-adv-x="708" d="M53 1499h152l153 -178l152 178h154l-256 -287h-103zM147 0v1024h152v-139q178 123 375 162v-156q-86 -16 -179.5 -51t-142.5 -62l-51 -26v-752h-154z" />
<glyph unicode="&#x15a;" horiz-adv-x="1110" d="M98 1059q0 381 457 381q150 0 358 -31l70 -10l-14 -133q-285 34 -404 34q-307 0 -307 -231q0 -129 70.5 -177t286.5 -96t306.5 -125t90.5 -253q0 -436 -453 -436q-150 0 -375 34l-73 11l16 131q279 -37 424 -37q303 0 303 285q0 115 -66.5 165t-248.5 84 q-240 51 -340.5 132t-100.5 272zM334 1702l442 205l53 -135l-448 -179z" />
<glyph unicode="&#x15b;" horiz-adv-x="952" d="M92 751.5q0 149.5 105.5 221t263.5 71.5q125 0 313 -28l57 -10l-4 -132q-227 33 -358 33q-223 0 -223 -155q0 -72 51 -101.5t233.5 -61.5t258 -89.5t75.5 -215t-101 -231t-296 -73.5q-127 0 -299 28l-62 10l9 134q233 -35 352 -35t181.5 38t62.5 127t-53.5 120.5 t-233.5 60.5t-256 84t-76 204.5zM233 1315l443 201l53 -132l-453 -174z" />
<glyph unicode="&#x15c;" horiz-adv-x="1110" d="M98 1059q0 381 457 381q150 0 358 -31l70 -10l-14 -133q-285 34 -404 34q-307 0 -307 -231q0 -129 70.5 -177t286.5 -96t306.5 -125t90.5 -253q0 -436 -453 -436q-150 0 -375 34l-73 11l16 131q279 -37 424 -37q303 0 303 285q0 115 -66.5 165t-248.5 84 q-240 51 -340.5 132t-100.5 272zM221 1614l275 276h133l274 -276h-172l-168 164l-170 -164h-172z" />
<glyph unicode="&#x15d;" horiz-adv-x="952" d="M92 751.5q0 149.5 105.5 221t263.5 71.5q125 0 313 -28l57 -10l-4 -132q-227 33 -358 33q-223 0 -223 -155q0 -72 51 -101.5t233.5 -61.5t258 -89.5t75.5 -215t-101 -231t-296 -73.5q-127 0 -299 28l-62 10l9 134q233 -35 352 -35t181.5 38t62.5 127t-53.5 120.5 t-233.5 60.5t-256 84t-76 204.5zM174 1212l252 287h104l254 -287h-153l-150 179l-153 -179h-154z" />
<glyph unicode="&#x15e;" horiz-adv-x="1110" d="M98 1059q0 381 457 381q150 0 358 -31l70 -10l-14 -133q-285 35 -404 34q-307 0 -307 -231q0 -129 70.5 -177t286.5 -96t306.5 -125t90.5 -253q0 -408 -400 -434v-84q121 0 174.5 -33t53.5 -127q0 -184 -191 -184q-94 0 -153 10l-23 4l6 98q80 -6 135 -6q90 0 91 78 q0 70 -91 70h-86v172q-152 4 -352 34l-65 11l16 131q279 -37 424 -37q303 0 303 285q0 115 -66.5 165t-248.5 84q-240 51 -340.5 132t-100.5 272z" />
<glyph unicode="&#x15f;" horiz-adv-x="952" d="M92 751.5q0 149.5 105.5 221t263.5 71.5q125 0 313 -28l57 -10l-4 -132q-227 33 -358 33q-223 0 -223 -155q0 -72 51 -101.5t233.5 -61.5t258 -89.5t75.5 -212t-92 -228t-270 -79.5v-80q121 0 174 -33t53 -127q0 -184 -190 -184q-94 0 -154 10l-23 4l7 98q80 -6 135 -6 q90 0 90 78q0 70 -90 70h-86v172q-119 4 -262 28l-50 8l9 134q233 -35 352 -35t181.5 38t62.5 127t-53.5 120.5t-233.5 60.5t-256 84t-76 204.5z" />
<glyph unicode="&#x160;" horiz-adv-x="1110" d="M98 1059q0 381 457 381q150 0 358 -31l70 -10l-14 -133q-285 34 -404 34q-307 0 -307 -231q0 -129 70.5 -177t286.5 -96t306.5 -125t90.5 -253q0 -436 -453 -436q-150 0 -375 34l-73 11l16 131q279 -37 424 -37q303 0 303 285q0 115 -66.5 165t-248.5 84 q-240 51 -340.5 132t-100.5 272zM238 1896h172l170 -163l168 163h172l-275 -276h-133z" />
<glyph unicode="&#x161;" horiz-adv-x="952" d="M92 751.5q0 149.5 105.5 221t263.5 71.5q125 0 313 -28l57 -10l-4 -132q-227 33 -358 33q-223 0 -223 -155q0 -72 51 -101.5t233.5 -61.5t258 -89.5t75.5 -215t-101 -231t-296 -73.5q-127 0 -299 28l-62 10l9 134q233 -35 352 -35t181.5 38t62.5 127t-53.5 120.5 t-233.5 60.5t-256 84t-76 204.5zM190 1499h152l154 -178l151 178h154l-256 -287h-103z" />
<glyph unicode="&#x162;" horiz-adv-x="0" />
<glyph unicode="&#x163;" horiz-adv-x="718" d="M55 891v133h146v313h151v-313h326v-133h-326v-490q0 -176 25.5 -231t122.5 -55l182 12l10 -127q-137 -23 -209 -23q-160 0 -221 78t-61 297v539h-146zM174 -430l6 98q80 -6 135 -6q90 0 91 78q0 70 -91 70h-86v192h84v-102q121 0 174.5 -33t53.5 -127q0 -184 -191 -184 q-94 0 -153 10z" />
<glyph unicode="&#x164;" horiz-adv-x="1077" d="M27 1276v141h1024v-141h-433v-1276h-155v1276h-436zM199 1896h172l170 -163l168 163h172l-275 -276h-133z" />
<glyph unicode="&#x165;" horiz-adv-x="884" d="M55 885v139h146v313h153v-313h324v-139h-324v-477q0 -176 24.5 -230.5t123.5 -54.5l182 10l8 -133q-135 -23 -207 -23q-162 0 -223 79t-61 296v533h-146zM758 946v479h147v-479h-147z" />
<glyph unicode="&#x166;" horiz-adv-x="1083" d="M31 1276v141h1024v-141h-432v-526h335v-136h-335v-614h-156v614h-334v136h334v526h-436z" />
<glyph unicode="&#x167;" horiz-adv-x="724" d="M59 885v139h146v313h153v-313h324v-139h-324v-246h275v-121h-275v-110q0 -176 25 -230.5t123 -54.5l182 10l8 -133q-135 -23 -207 -23q-162 0 -223 79t-61 296v166h-113v121h113v246h-146z" />
<glyph unicode="&#x168;" horiz-adv-x="1318" d="M164 430v987h158v-991q0 -307 331 -307q172 0 259 72.5t87 234.5v991h156v-987q0 -238 -126 -344t-371.5 -106t-369.5 106.5t-124 343.5zM313 1772q39 45 92.5 81.5t100.5 36.5t172 -55t145 -55q41 0 119 71l25 23l37 -115q-97 -114 -181 -114q-43 0 -170 55t-153 55 q-45 0 -125 -74l-27 -24z" />
<glyph unicode="&#x169;" horiz-adv-x="1087" d="M137 489v535h154v-532q0 -223 39 -299t183 -76t273 76v831h154v-1024h-154v72l-43 -23q-43 -25 -118.5 -47t-141.5 -22q-209 0 -277.5 108.5t-68.5 400.5zM215 1366q102 106 186 107q43 0 149.5 -47.5t129.5 -47.5q45 0 123 60l24 18l35 -98q-94 -105 -176 -105 q-43 0 -148.5 48.5t-131.5 48.5q-47 0 -127 -62l-27 -20z" />
<glyph unicode="&#x16a;" horiz-adv-x="1318" d="M164 430v987h158v-991q0 -307 331 -307q172 0 259 72.5t87 234.5v991h156v-987q0 -238 -126 -344t-371.5 -106t-369.5 106.5t-124 343.5zM344 1663v119h637v-119h-637z" />
<glyph unicode="&#x16b;" horiz-adv-x="1087" d="M137 489v535h154v-532q0 -223 39 -299t183 -76t273 76v831h154v-1024h-154v72l-43 -23q-43 -25 -118.5 -47t-141.5 -22q-209 0 -277.5 108.5t-68.5 400.5zM258 1278v121h563v-121h-563z" />
<glyph unicode="&#x16c;" horiz-adv-x="1318" d="M164 430v987h158v-991q0 -307 331 -307q172 0 259 72.5t87 234.5v991h156v-987q0 -238 -126 -344t-371.5 -106t-369.5 106.5t-124 343.5zM342 1880h141q2 -68 54.5 -111t127 -43t126 43t53.5 111h143q-6 -121 -94 -194.5t-229.5 -73.5t-228.5 73.5t-93 194.5z" />
<glyph unicode="&#x16d;" horiz-adv-x="1087" d="M137 489v535h154v-532q0 -223 39 -299t183 -76t273 76v831h154v-1024h-154v72l-43 -23q-43 -25 -118.5 -47t-141.5 -22q-209 0 -277.5 108.5t-68.5 400.5zM227 1487h125q4 -78 51.5 -125t128 -47t128 47t49.5 125h127q-6 -123 -88 -205t-216.5 -82t-216.5 82t-88 205z " />
<glyph unicode="&#x16e;" horiz-adv-x="1318" d="M164 430v987h158v-991q0 -307 331 -307q172 0 259 72.5t87 234.5v991h156v-987q0 -238 -126 -344t-371.5 -106t-369.5 106.5t-124 343.5zM420 1731.5q0 101.5 68.5 159t175 57.5t174 -57.5t67.5 -159t-67.5 -158.5t-175 -57t-175 57t-67.5 158.5zM537 1733 q0 -51 33.5 -81t93 -30t93.5 29.5t34 81t-34 80t-93 28.5q-127 1 -127 -108z" />
<glyph unicode="&#x16f;" horiz-adv-x="1087" d="M137 489v535h154v-532q0 -223 39 -299t183 -76t273 76v831h154v-1024h-154v72l-43 -23q-43 -25 -118.5 -47t-141.5 -22q-209 0 -277.5 108.5t-68.5 400.5zM330 1321q0 90 61.5 151.5t150.5 61.5t150.5 -61.5t61.5 -151.5t-60.5 -151.5t-150.5 -61.5t-151.5 61.5 t-61.5 151.5zM430 1320q0 -48 33 -82t80 -34t79.5 34t32.5 82t-32.5 82t-79.5 34t-80 -34t-33 -82z" />
<glyph unicode="&#x170;" horiz-adv-x="1318" d="M164 430v987h158v-991q0 -307 331 -307q172 0 259 72.5t87 234.5v991h156v-987q0 -238 -126 -344t-371.5 -106t-369.5 106.5t-124 343.5zM317 1608l291 270l111 -72l-297 -264zM721 1608l291 268l112 -72l-299 -264z" />
<glyph unicode="&#x171;" horiz-adv-x="1087" d="M137 489v535h154v-532q0 -223 39 -299t183 -76t273 76v831h154v-1024h-154v72l-43 -23q-43 -25 -118.5 -47t-141.5 -22q-209 0 -277.5 108.5t-68.5 400.5zM221 1278l297 283l111 -86l-301 -283zM616 1278l297 285l111 -88l-303 -283z" />
<glyph unicode="&#x172;" horiz-adv-x="1320" d="M164 430v987h158v-991q0 -307 331 -307q172 0 259 72.5t87 234.5v991h156v-987q0 -360 -309 -430l-19 -18q-123 -131 -122 -191q0 -39 23.5 -63.5t59.5 -24.5l103 12l14 -118q-92 -18 -167.5 -18.5t-129 47.5t-53.5 136t129 218h-31q-242 0 -365.5 106.5t-123.5 343.5z " />
<glyph unicode="&#x173;" horiz-adv-x="1087" d="M137 489v535h154v-532q0 -223 39 -299t176 -76q61 0 131 19.5t108 37.5l41 19v831h154v-1024q-51 -39 -104.5 -107.5t-53.5 -104.5t23.5 -60.5t60.5 -24.5l105 12l14 -118q-92 -18 -168 -18.5t-129 47.5t-53 121t52 140.5t108 112.5h-9v66q-160 -86 -303 -86 q-209 0 -277.5 108.5t-68.5 400.5z" />
<glyph unicode="&#x174;" horiz-adv-x="1814" d="M61 1417h164l242 -1278h59l289 1266h184l289 -1266h60l241 1278h162l-287 -1417h-288l-269 1210l-268 -1210h-289zM567 1614l275 276h133l274 -276h-172l-168 164l-170 -164h-172z" />
<glyph unicode="&#x175;" horiz-adv-x="1560" d="M63 1024h154l207 -891h33l243 871h160l244 -871h35l204 891h154l-244 -1024h-256l-217 801l-217 -801h-256zM477 1212l252 287h105l253 -287h-153l-150 179l-153 -179h-154z" />
<glyph unicode="&#x176;" horiz-adv-x="1101" d="M20 1417h179l352 -663l352 663h178l-450 -821v-596h-158v596zM213 1620l274 276h134l274 -276h-172l-168 164l-170 -164h-172z" />
<glyph unicode="&#x177;" horiz-adv-x="989" d="M51 1024h154l256 -891h67l258 891h154l-426 -1479h-154l134 455h-152zM195 1212l251 287h105l254 -287h-154l-149 179l-154 -179h-153z" />
<glyph unicode="&#x178;" horiz-adv-x="1101" d="M20 1417h179l352 -663l352 663h178l-450 -821v-596h-158v596zM266 1655v184h150v-184h-150zM684 1655v184h147v-184h-147z" />
<glyph unicode="&#x179;" horiz-adv-x="1097" d="M88 -2v182l739 1053v45h-739v139h922v-184l-742 -1053v-41h742v-141h-922zM313 1702l443 205l53 -135l-449 -179z" />
<glyph unicode="&#x17a;" horiz-adv-x="931" d="M86 0v137l573 750h-573v137h760v-137l-574 -750h574v-137h-760zM231 1315l443 201l53 -132l-453 -174z" />
<glyph unicode="&#x17b;" horiz-adv-x="1097" d="M88 -2v182l739 1053v45h-739v139h922v-184l-742 -1053v-41h742v-141h-922zM471 1640v179h152v-179h-152z" />
<glyph unicode="&#x17c;" horiz-adv-x="931" d="M86 0v137l573 750h-573v137h760v-137l-574 -750h574v-137h-760zM389 1247v178h152v-178h-152z" />
<glyph unicode="&#x17d;" horiz-adv-x="1097" d="M88 -2v182l739 1053v45h-739v139h922v-184l-742 -1053v-41h742v-141h-922zM223 1896h172l170 -163l168 163h172l-274 -276h-133z" />
<glyph unicode="&#x17e;" horiz-adv-x="931" d="M86 0v137l573 750h-573v137h760v-137l-574 -750h574v-137h-760zM168 1499h151l154 -178l152 178h153l-256 -287h-102z" />
<glyph unicode="&#x192;" d="M80 -465l2 131q123 -10 194.5 -10t108.5 52t37 181v861h-129v131h129v110q0 201 56.5 310.5t219.5 109.5q57 0 172 -14l33 -4l-2 -129q-92 8 -174 8q-152 0 -152 -236v-155h297v-131h-297v-863q0 -207 -68.5 -287.5t-225.5 -80.5q-78 0 -170 12z" />
<glyph unicode="&#x1fa;" horiz-adv-x="1220" d="M49 0l377 1374q-59 59 -59 156.5t68.5 155t175 57.5t174 -57.5t67.5 -153.5t-59 -156l378 -1376h-155l-103 365h-606l-102 -365h-156zM342 506h537l-215 776h-107zM348 1892l443 205l53 -135l-449 -178zM483 1528q0 -51 34 -81t93.5 -30t93 30t33.5 81t-33.5 79.5 t-93.5 28.5q-127 1 -127 -108z" />
<glyph unicode="&#x1fb;" horiz-adv-x="1026" d="M82 288.5q0 149.5 75.5 220.5t237.5 87l322 31v88q0 104 -45 149t-123 45q-164 0 -340 -20l-64 -6l-6 116q225 45 399.5 45t253 -79.5t78.5 -249.5v-525q6 -76 119 -90l-6 -120q-162 0 -244 81q-184 -82 -368 -81q-141 0 -215 79.5t-74 229zM240 297q0 -188 155 -188 q139 0 275 47l47 16v338l-303 -29q-92 -8 -133 -53t-41 -131zM270 1688l443 200l53 -131l-453 -174zM291 1321q0 90 61.5 151.5t150.5 61.5t150.5 -61.5t61.5 -151.5t-60.5 -151.5t-150.5 -61.5t-151.5 61.5t-61.5 151.5zM391 1320q0 -48 33 -82t80 -34t79.5 34t32.5 82 t-32.5 82t-79.5 34t-80 -34t-33 -82z" />
<glyph unicode="&#x1fc;" horiz-adv-x="1794" d="M41 0l459 1425h1198v-149h-707v-471h584v-150h-584v-505h707v-150h-862v358h-514l-119 -358h-162zM365 510h471l2 766h-224zM846 1702l442 205l53 -135l-448 -179z" />
<glyph unicode="&#x1fd;" horiz-adv-x="1646" d="M82 299q0 158 84 220.5t287 82.5l264 25v84q0 186 -178 186q-135 0 -326 -16l-63 -4l-7 137q244 31 404 30q209 0 276 -149q98 150 312.5 149.5t316.5 -116t102 -363.5l-8 -119h-676q0 -168 61.5 -247.5t187.5 -79.5t335 14l59 4l5 -125q-231 -33 -415.5 -32.5 t-283.5 110.5l-65 -29q-178 -82 -394 -81q-137 0 -207.5 80.5t-70.5 238.5zM240 297q0 -184 145 -184q94 0 215 29.5t152 45.5q-39 104 -39 314l-299 -25q-90 -8 -132 -51t-42 -129zM594 1315l442 201l54 -132l-453 -174zM870 571h531q0 182 -60.5 259t-196.5 77t-205 -80 t-69 -256z" />
<glyph unicode="&#x1fe;" horiz-adv-x="1355" d="M119 702q0 373 123 554.5t436 181.5q154 0 258 -43l115 241l118 -49l-120 -260q190 -160 190 -625q0 -383 -121 -552.5t-440 -169.5q-141 0 -246 34l-117 -252l-116 54l120 258q-109 82 -154.5 235.5t-45.5 392.5zM281 677q0 -337 106 -456l487 1039q-78 39 -196 38 q-232 0 -314.5 -142t-82.5 -479zM428 1702l442 205l54 -135l-449 -179zM496 147q69 -28 182 -28q236 0 316.5 133t80.5 462.5t-98 461.5z" />
<glyph unicode="&#x1ff;" horiz-adv-x="1081" d="M102 524q0 270 100.5 395t338.5 125q80 0 151 -16l88 213l103 -39l-88 -213q184 -102 184 -465q0 -291 -94 -417.5t-344 -126.5q-84 0 -156 16l-88 -225l-102 36l90 224q-100 55 -141.5 174t-41.5 319zM260 558q0 -183 17.5 -267t68.5 -127l297 735q-43 12 -102 12 q-160 0 -220.5 -85t-60.5 -268zM295 1315l442 201l54 -132l-453 -174zM436 123q37 -10 105 -10q174 0 227 89t53 331.5t-86 320.5z" />
<glyph unicode="&#x218;" horiz-adv-x="1110" d="M98 1059q0 381 457 381q150 0 358 -31l70 -10l-14 -133q-285 34 -404 34q-307 0 -307 -231q0 -129 70.5 -177t286.5 -96t306.5 -125t90.5 -253q0 -436 -453 -436q-150 0 -375 34l-73 11l16 131q279 -37 424 -37q303 0 303 285q0 115 -66.5 165t-248.5 84 q-240 51 -340.5 132t-100.5 272zM428 -584l82 424h152l-101 -424h-133z" />
<glyph unicode="&#x219;" horiz-adv-x="952" d="M92 751.5q0 149.5 105.5 221t263.5 71.5q125 0 313 -28l57 -10l-4 -132q-227 33 -358 33q-223 0 -223 -155q0 -72 51 -101.5t233.5 -61.5t258 -89.5t75.5 -215t-101 -231t-296 -73.5q-127 0 -299 28l-62 10l9 134q233 -35 352 -35t181.5 38t62.5 127t-53.5 120.5 t-233.5 60.5t-256 84t-76 204.5zM227 -584l82 424h154l-103 -424h-133z" />
<glyph unicode="&#x21a;" horiz-adv-x="1077" d="M27 1276v141h1024v-141h-433v-1276h-155v1276h-436zM422 -584l82 424h153l-102 -424h-133z" />
<glyph unicode="&#x21b;" horiz-adv-x="718" d="M55 891v133h146v313h151v-313h326v-133h-326v-490q0 -176 25.5 -231t122.5 -55l182 12l10 -127q-137 -23 -209 -23q-160 0 -221 78t-61 297v539h-146zM207 -584l82 424h153l-102 -424h-133z" />
<glyph unicode="&#x2c6;" horiz-adv-x="483" d="M-39 1212l252 287h104l254 -287h-153l-150 179l-153 -179h-154z" />
<glyph unicode="&#x2da;" horiz-adv-x="483" d="M37 1321q0 90 61.5 151.5t150.5 61.5t150.5 -61.5t61.5 -151.5t-60.5 -151.5t-150.5 -61.5t-151.5 61.5t-61.5 151.5zM137 1320q0 -48 33 -82t80 -34t79.5 34t32.5 82t-32.5 82t-79.5 34t-80 -34t-33 -82z" />
<glyph unicode="&#x2dc;" horiz-adv-x="483" d="M-61 1366q102 106 186 107q43 0 149.5 -47.5t131.5 -47.5q43 0 118 60l27 18l35 -98q-94 -105 -176 -105q-43 0 -148.5 48.5t-132.5 48.5q-47 0 -127 -62l-27 -20z" />
<glyph unicode="&#x2000;" horiz-adv-x="1048" />
<glyph unicode="&#x2001;" horiz-adv-x="2097" />
<glyph unicode="&#x2002;" horiz-adv-x="1048" />
<glyph unicode="&#x2003;" horiz-adv-x="2097" />
<glyph unicode="&#x2004;" horiz-adv-x="699" />
<glyph unicode="&#x2005;" horiz-adv-x="524" />
<glyph unicode="&#x2006;" horiz-adv-x="349" />
<glyph unicode="&#x2007;" horiz-adv-x="349" />
<glyph unicode="&#x2008;" horiz-adv-x="262" />
<glyph unicode="&#x2009;" horiz-adv-x="419" />
<glyph unicode="&#x200a;" horiz-adv-x="116" />
<glyph unicode="&#x2010;" horiz-adv-x="897" d="M139 492v143h621v-143h-621z" />
<glyph unicode="&#x2011;" horiz-adv-x="897" d="M139 492v143h621v-143h-621z" />
<glyph unicode="&#x2012;" horiz-adv-x="897" d="M139 492v143h621v-143h-621z" />
<glyph unicode="&#x2013;" horiz-adv-x="1292" d="M135 481v135h1024v-135h-1024z" />
<glyph unicode="&#x2014;" horiz-adv-x="2316" d="M135 481v135h2048v-135h-2048z" />
<glyph unicode="&#x2018;" horiz-adv-x="448" d="M104 1001l132 449h120l-90 -449h-162z" />
<glyph unicode="&#x2019;" horiz-adv-x="442" d="M109 1001l90 449h161l-131 -449h-120z" />
<glyph unicode="&#x201a;" horiz-adv-x="446" d="M139 -43l27 88h24l-18 -88h-33z" />
<glyph unicode="&#x201c;" horiz-adv-x="763" d="M104 1001l132 449h120l-90 -449h-162zM418 1001l131 449h121l-90 -449h-162z" />
<glyph unicode="&#x201d;" horiz-adv-x="772" d="M109 1004l90 448h161l-131 -448h-120zM436 1004l90 448h162l-131 -448h-121z" />
<glyph unicode="&#x201e;" horiz-adv-x="735" d="M45 -219l131 448h121l-90 -448h-162zM356 -219l131 448h121l-90 -448h-162z" />
<glyph unicode="&#x2022;" horiz-adv-x="968" d="M227 217v586h512v-586h-512z" />
<glyph unicode="&#x2026;" horiz-adv-x="1484" d="M137 0v233h168v-233h-168zM659 0v233h166v-233h-166zM1180 0v233h168v-233h-168z" />
<glyph unicode="&#x202f;" horiz-adv-x="419" />
<glyph unicode="&#x2039;" horiz-adv-x="616" d="M92 446v123l387 293v-157l-254 -193l254 -215v-160z" />
<glyph unicode="&#x203a;" horiz-adv-x="616" d="M137 145v160l254 215l-254 193v157l387 -292v-123z" />
<glyph unicode="&#x205f;" horiz-adv-x="524" />
<glyph unicode="&#x20ac;" d="M61 455v125h123q-2 29 -2 95t2 99h-123v125h132q25 244 134 358.5t351 114.5q172 0 373 -43l-7 -127q-186 35 -354 35t-244.5 -80t-95.5 -258h586v-125h-594q-2 -33 -2 -102v-92h596v-125h-588q18 -178 95 -259t247 -81t354 37l7 -129q-203 -43 -373 -43 q-242 0 -351.5 116.5t-136.5 358.5h-129z" />
<glyph unicode="&#x2122;" horiz-adv-x="1386" d="M186 1188v102h410v-102h-135v-508h-113v508h-162zM659 678v612h148l135 -426l145 426h144v-612h-107v459l-135 -428h-88l-135 428v-459h-107z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1024" d="M0 0v1024h1024v-1024h-1024z" />
<hkern u1="&#x22;" u2="&#x129;" k="-6" />
<hkern u1="&#x22;" u2="&#xef;" k="-14" />
<hkern u1="&#x22;" u2="&#xee;" k="-10" />
<hkern u1="&#x22;" u2="&#xec;" k="-35" />
<hkern u1="&#x22;" u2="&#xc6;" k="109" />
<hkern u1="&#x22;" u2="&#x40;" k="25" />
<hkern u1="&#x22;" u2="&#x2f;" k="129" />
<hkern u1="&#x22;" u2="&#x26;" k="57" />
<hkern u1="&#x26;" u2="&#x201d;" k="98" />
<hkern u1="&#x26;" u2="&#x2019;" k="98" />
<hkern u1="&#x26;" u2="&#x21b;" k="14" />
<hkern u1="&#x26;" u2="&#x21a;" k="84" />
<hkern u1="&#x26;" u2="&#x1fe;" k="14" />
<hkern u1="&#x26;" u2="&#x178;" k="113" />
<hkern u1="&#x26;" u2="&#x177;" k="27" />
<hkern u1="&#x26;" u2="&#x176;" k="113" />
<hkern u1="&#x26;" u2="&#x175;" k="20" />
<hkern u1="&#x26;" u2="&#x174;" k="41" />
<hkern u1="&#x26;" u2="&#x172;" k="12" />
<hkern u1="&#x26;" u2="&#x170;" k="12" />
<hkern u1="&#x26;" u2="&#x16e;" k="12" />
<hkern u1="&#x26;" u2="&#x16c;" k="12" />
<hkern u1="&#x26;" u2="&#x16a;" k="12" />
<hkern u1="&#x26;" u2="&#x168;" k="12" />
<hkern u1="&#x26;" u2="&#x167;" k="14" />
<hkern u1="&#x26;" u2="&#x166;" k="84" />
<hkern u1="&#x26;" u2="&#x165;" k="14" />
<hkern u1="&#x26;" u2="&#x164;" k="84" />
<hkern u1="&#x26;" u2="&#x152;" k="14" />
<hkern u1="&#x26;" u2="&#x150;" k="14" />
<hkern u1="&#x26;" u2="&#x14e;" k="14" />
<hkern u1="&#x26;" u2="&#x14c;" k="14" />
<hkern u1="&#x26;" u2="&#x122;" k="14" />
<hkern u1="&#x26;" u2="&#x120;" k="14" />
<hkern u1="&#x26;" u2="&#x11e;" k="14" />
<hkern u1="&#x26;" u2="&#x11c;" k="14" />
<hkern u1="&#x26;" u2="&#x10c;" k="14" />
<hkern u1="&#x26;" u2="&#x10a;" k="14" />
<hkern u1="&#x26;" u2="&#x108;" k="14" />
<hkern u1="&#x26;" u2="&#x106;" k="14" />
<hkern u1="&#x26;" u2="&#xff;" k="27" />
<hkern u1="&#x26;" u2="&#xfd;" k="27" />
<hkern u1="&#x26;" u2="&#xdd;" k="113" />
<hkern u1="&#x26;" u2="&#xdc;" k="12" />
<hkern u1="&#x26;" u2="&#xdb;" k="12" />
<hkern u1="&#x26;" u2="&#xda;" k="12" />
<hkern u1="&#x26;" u2="&#xd9;" k="12" />
<hkern u1="&#x26;" u2="&#xd8;" k="14" />
<hkern u1="&#x26;" u2="&#xd6;" k="14" />
<hkern u1="&#x26;" u2="&#xd5;" k="14" />
<hkern u1="&#x26;" u2="&#xd4;" k="14" />
<hkern u1="&#x26;" u2="&#xd3;" k="14" />
<hkern u1="&#x26;" u2="&#xd2;" k="14" />
<hkern u1="&#x26;" u2="&#xc7;" k="14" />
<hkern u1="&#x26;" u2="y" k="27" />
<hkern u1="&#x26;" u2="w" k="20" />
<hkern u1="&#x26;" u2="v" k="27" />
<hkern u1="&#x26;" u2="t" k="14" />
<hkern u1="&#x26;" u2="Y" k="113" />
<hkern u1="&#x26;" u2="W" k="41" />
<hkern u1="&#x26;" u2="V" k="68" />
<hkern u1="&#x26;" u2="U" k="12" />
<hkern u1="&#x26;" u2="T" k="84" />
<hkern u1="&#x26;" u2="Q" k="14" />
<hkern u1="&#x26;" u2="O" k="14" />
<hkern u1="&#x26;" u2="G" k="14" />
<hkern u1="&#x26;" u2="C" k="14" />
<hkern u1="&#x26;" u2="&#x27;" k="100" />
<hkern u1="&#x26;" u2="&#x22;" k="100" />
<hkern u1="&#x27;" u2="&#x129;" k="-6" />
<hkern u1="&#x27;" u2="&#xef;" k="-14" />
<hkern u1="&#x27;" u2="&#xee;" k="-10" />
<hkern u1="&#x27;" u2="&#xec;" k="-35" />
<hkern u1="&#x27;" u2="&#xc6;" k="109" />
<hkern u1="&#x27;" u2="&#x40;" k="25" />
<hkern u1="&#x27;" u2="&#x2f;" k="129" />
<hkern u1="&#x27;" u2="&#x26;" k="57" />
<hkern u1="&#x28;" u2="&#x219;" k="8" />
<hkern u1="&#x28;" u2="&#x1ff;" k="37" />
<hkern u1="&#x28;" u2="&#x1fe;" k="27" />
<hkern u1="&#x28;" u2="&#x1fd;" k="10" />
<hkern u1="&#x28;" u2="&#x1fb;" k="10" />
<hkern u1="&#x28;" u2="&#x177;" k="8" />
<hkern u1="&#x28;" u2="&#x175;" k="12" />
<hkern u1="&#x28;" u2="&#x173;" k="29" />
<hkern u1="&#x28;" u2="&#x171;" k="29" />
<hkern u1="&#x28;" u2="&#x16f;" k="29" />
<hkern u1="&#x28;" u2="&#x16d;" k="29" />
<hkern u1="&#x28;" u2="&#x16b;" k="29" />
<hkern u1="&#x28;" u2="&#x169;" k="29" />
<hkern u1="&#x28;" u2="&#x161;" k="8" />
<hkern u1="&#x28;" u2="&#x15f;" k="8" />
<hkern u1="&#x28;" u2="&#x15d;" k="8" />
<hkern u1="&#x28;" u2="&#x15b;" k="8" />
<hkern u1="&#x28;" u2="&#x159;" k="8" />
<hkern u1="&#x28;" u2="&#x157;" k="8" />
<hkern u1="&#x28;" u2="&#x155;" k="8" />
<hkern u1="&#x28;" u2="&#x153;" k="37" />
<hkern u1="&#x28;" u2="&#x152;" k="27" />
<hkern u1="&#x28;" u2="&#x151;" k="37" />
<hkern u1="&#x28;" u2="&#x150;" k="27" />
<hkern u1="&#x28;" u2="&#x14f;" k="37" />
<hkern u1="&#x28;" u2="&#x14e;" k="27" />
<hkern u1="&#x28;" u2="&#x14d;" k="37" />
<hkern u1="&#x28;" u2="&#x14c;" k="27" />
<hkern u1="&#x28;" u2="&#x14b;" k="8" />
<hkern u1="&#x28;" u2="&#x148;" k="8" />
<hkern u1="&#x28;" u2="&#x146;" k="8" />
<hkern u1="&#x28;" u2="&#x144;" k="8" />
<hkern u1="&#x28;" u2="&#x135;" k="-18" />
<hkern u1="&#x28;" u2="&#x12d;" k="-37" />
<hkern u1="&#x28;" u2="&#x129;" k="-16" />
<hkern u1="&#x28;" u2="&#x122;" k="27" />
<hkern u1="&#x28;" u2="&#x120;" k="27" />
<hkern u1="&#x28;" u2="&#x11e;" k="27" />
<hkern u1="&#x28;" u2="&#x11c;" k="27" />
<hkern u1="&#x28;" u2="&#x11b;" k="37" />
<hkern u1="&#x28;" u2="&#x119;" k="37" />
<hkern u1="&#x28;" u2="&#x117;" k="37" />
<hkern u1="&#x28;" u2="&#x115;" k="37" />
<hkern u1="&#x28;" u2="&#x113;" k="37" />
<hkern u1="&#x28;" u2="&#x111;" k="37" />
<hkern u1="&#x28;" u2="&#x10f;" k="37" />
<hkern u1="&#x28;" u2="&#x10d;" k="37" />
<hkern u1="&#x28;" u2="&#x10c;" k="25" />
<hkern u1="&#x28;" u2="&#x10b;" k="37" />
<hkern u1="&#x28;" u2="&#x10a;" k="25" />
<hkern u1="&#x28;" u2="&#x109;" k="37" />
<hkern u1="&#x28;" u2="&#x108;" k="25" />
<hkern u1="&#x28;" u2="&#x107;" k="37" />
<hkern u1="&#x28;" u2="&#x106;" k="25" />
<hkern u1="&#x28;" u2="&#x105;" k="10" />
<hkern u1="&#x28;" u2="&#x103;" k="10" />
<hkern u1="&#x28;" u2="&#x101;" k="10" />
<hkern u1="&#x28;" u2="&#xff;" k="8" />
<hkern u1="&#x28;" u2="&#xfd;" k="8" />
<hkern u1="&#x28;" u2="&#xfc;" k="29" />
<hkern u1="&#x28;" u2="&#xfb;" k="29" />
<hkern u1="&#x28;" u2="&#xfa;" k="29" />
<hkern u1="&#x28;" u2="&#xf9;" k="29" />
<hkern u1="&#x28;" u2="&#xf8;" k="37" />
<hkern u1="&#x28;" u2="&#xf6;" k="37" />
<hkern u1="&#x28;" u2="&#xf5;" k="37" />
<hkern u1="&#x28;" u2="&#xf4;" k="37" />
<hkern u1="&#x28;" u2="&#xf3;" k="37" />
<hkern u1="&#x28;" u2="&#xf2;" k="37" />
<hkern u1="&#x28;" u2="&#xf1;" k="8" />
<hkern u1="&#x28;" u2="&#xef;" k="-31" />
<hkern u1="&#x28;" u2="&#xec;" k="-45" />
<hkern u1="&#x28;" u2="&#xeb;" k="37" />
<hkern u1="&#x28;" u2="&#xea;" k="37" />
<hkern u1="&#x28;" u2="&#xe9;" k="37" />
<hkern u1="&#x28;" u2="&#xe8;" k="37" />
<hkern u1="&#x28;" u2="&#xe7;" k="37" />
<hkern u1="&#x28;" u2="&#xe6;" k="10" />
<hkern u1="&#x28;" u2="&#xe5;" k="10" />
<hkern u1="&#x28;" u2="&#xe4;" k="10" />
<hkern u1="&#x28;" u2="&#xe3;" k="10" />
<hkern u1="&#x28;" u2="&#xe2;" k="10" />
<hkern u1="&#x28;" u2="&#xe1;" k="10" />
<hkern u1="&#x28;" u2="&#xe0;" k="10" />
<hkern u1="&#x28;" u2="&#xd8;" k="27" />
<hkern u1="&#x28;" u2="&#xd6;" k="27" />
<hkern u1="&#x28;" u2="&#xd5;" k="27" />
<hkern u1="&#x28;" u2="&#xd4;" k="27" />
<hkern u1="&#x28;" u2="&#xd3;" k="27" />
<hkern u1="&#x28;" u2="&#xd2;" k="27" />
<hkern u1="&#x28;" u2="&#xc7;" k="25" />
<hkern u1="&#x28;" u2="&#x7b;" k="23" />
<hkern u1="&#x28;" u2="y" k="8" />
<hkern u1="&#x28;" u2="w" k="12" />
<hkern u1="&#x28;" u2="v" k="8" />
<hkern u1="&#x28;" u2="u" k="29" />
<hkern u1="&#x28;" u2="s" k="8" />
<hkern u1="&#x28;" u2="r" k="8" />
<hkern u1="&#x28;" u2="q" k="37" />
<hkern u1="&#x28;" u2="p" k="8" />
<hkern u1="&#x28;" u2="o" k="37" />
<hkern u1="&#x28;" u2="n" k="8" />
<hkern u1="&#x28;" u2="m" k="8" />
<hkern u1="&#x28;" u2="j" k="-18" />
<hkern u1="&#x28;" u2="f" k="8" />
<hkern u1="&#x28;" u2="e" k="37" />
<hkern u1="&#x28;" u2="d" k="37" />
<hkern u1="&#x28;" u2="c" k="37" />
<hkern u1="&#x28;" u2="a" k="10" />
<hkern u1="&#x28;" u2="Q" k="27" />
<hkern u1="&#x28;" u2="O" k="27" />
<hkern u1="&#x28;" u2="G" k="27" />
<hkern u1="&#x28;" u2="C" k="25" />
<hkern u1="&#x29;" u2="&#x7d;" k="16" />
<hkern u1="&#x29;" u2="]" k="16" />
<hkern u1="&#x2a;" u2="&#x21a;" k="-8" />
<hkern u1="&#x2a;" u2="&#x219;" k="23" />
<hkern u1="&#x2a;" u2="&#x1ff;" k="35" />
<hkern u1="&#x2a;" u2="&#x1fc;" k="80" />
<hkern u1="&#x2a;" u2="&#x1fa;" k="80" />
<hkern u1="&#x2a;" u2="&#x17d;" k="23" />
<hkern u1="&#x2a;" u2="&#x17b;" k="23" />
<hkern u1="&#x2a;" u2="&#x179;" k="23" />
<hkern u1="&#x2a;" u2="&#x167;" k="-16" />
<hkern u1="&#x2a;" u2="&#x166;" k="-8" />
<hkern u1="&#x2a;" u2="&#x165;" k="-14" />
<hkern u1="&#x2a;" u2="&#x164;" k="-8" />
<hkern u1="&#x2a;" u2="&#x161;" k="23" />
<hkern u1="&#x2a;" u2="&#x15f;" k="23" />
<hkern u1="&#x2a;" u2="&#x15d;" k="23" />
<hkern u1="&#x2a;" u2="&#x15b;" k="23" />
<hkern u1="&#x2a;" u2="&#x153;" k="35" />
<hkern u1="&#x2a;" u2="&#x151;" k="35" />
<hkern u1="&#x2a;" u2="&#x14f;" k="35" />
<hkern u1="&#x2a;" u2="&#x14d;" k="35" />
<hkern u1="&#x2a;" u2="&#x135;" k="-37" />
<hkern u1="&#x2a;" u2="&#x134;" k="43" />
<hkern u1="&#x2a;" u2="&#x129;" k="-20" />
<hkern u1="&#x2a;" u2="&#x123;" k="29" />
<hkern u1="&#x2a;" u2="&#x121;" k="29" />
<hkern u1="&#x2a;" u2="&#x11f;" k="29" />
<hkern u1="&#x2a;" u2="&#x11d;" k="29" />
<hkern u1="&#x2a;" u2="&#x11b;" k="35" />
<hkern u1="&#x2a;" u2="&#x119;" k="35" />
<hkern u1="&#x2a;" u2="&#x117;" k="35" />
<hkern u1="&#x2a;" u2="&#x115;" k="35" />
<hkern u1="&#x2a;" u2="&#x113;" k="35" />
<hkern u1="&#x2a;" u2="&#x111;" k="41" />
<hkern u1="&#x2a;" u2="&#x10f;" k="41" />
<hkern u1="&#x2a;" u2="&#x10d;" k="35" />
<hkern u1="&#x2a;" u2="&#x10b;" k="35" />
<hkern u1="&#x2a;" u2="&#x109;" k="35" />
<hkern u1="&#x2a;" u2="&#x107;" k="35" />
<hkern u1="&#x2a;" u2="&#x104;" k="80" />
<hkern u1="&#x2a;" u2="&#x102;" k="80" />
<hkern u1="&#x2a;" u2="&#x100;" k="80" />
<hkern u1="&#x2a;" u2="&#xf8;" k="35" />
<hkern u1="&#x2a;" u2="&#xf6;" k="35" />
<hkern u1="&#x2a;" u2="&#xf5;" k="35" />
<hkern u1="&#x2a;" u2="&#xf4;" k="35" />
<hkern u1="&#x2a;" u2="&#xf3;" k="35" />
<hkern u1="&#x2a;" u2="&#xf2;" k="35" />
<hkern u1="&#x2a;" u2="&#xef;" k="-33" />
<hkern u1="&#x2a;" u2="&#xee;" k="-43" />
<hkern u1="&#x2a;" u2="&#xec;" k="-20" />
<hkern u1="&#x2a;" u2="&#xeb;" k="35" />
<hkern u1="&#x2a;" u2="&#xea;" k="35" />
<hkern u1="&#x2a;" u2="&#xe9;" k="35" />
<hkern u1="&#x2a;" u2="&#xe8;" k="35" />
<hkern u1="&#x2a;" u2="&#xe7;" k="35" />
<hkern u1="&#x2a;" u2="&#xc6;" k="98" />
<hkern u1="&#x2a;" u2="&#xc5;" k="80" />
<hkern u1="&#x2a;" u2="&#xc4;" k="80" />
<hkern u1="&#x2a;" u2="&#xc3;" k="80" />
<hkern u1="&#x2a;" u2="&#xc2;" k="80" />
<hkern u1="&#x2a;" u2="&#xc1;" k="80" />
<hkern u1="&#x2a;" u2="&#xc0;" k="80" />
<hkern u1="&#x2a;" u2="s" k="23" />
<hkern u1="&#x2a;" u2="q" k="41" />
<hkern u1="&#x2a;" u2="o" k="35" />
<hkern u1="&#x2a;" u2="g" k="29" />
<hkern u1="&#x2a;" u2="e" k="35" />
<hkern u1="&#x2a;" u2="d" k="41" />
<hkern u1="&#x2a;" u2="c" k="35" />
<hkern u1="&#x2a;" u2="Z" k="23" />
<hkern u1="&#x2a;" u2="T" k="-8" />
<hkern u1="&#x2a;" u2="J" k="43" />
<hkern u1="&#x2a;" u2="A" k="80" />
<hkern u1="&#x2c;" u2="v" k="72" />
<hkern u1="&#x2c;" u2="f" k="20" />
<hkern u1="&#x2c;" u2="V" k="109" />
<hkern u1="&#x2d;" u2="&#xc6;" k="35" />
<hkern u1="&#x2d;" u2="x" k="63" />
<hkern u1="&#x2d;" u2="v" k="27" />
<hkern u1="&#x2d;" u2="f" k="25" />
<hkern u1="&#x2d;" u2="X" k="80" />
<hkern u1="&#x2d;" u2="V" k="63" />
<hkern u1="&#x2e;" u2="v" k="72" />
<hkern u1="&#x2e;" u2="f" k="20" />
<hkern u1="&#x2e;" u2="V" k="109" />
<hkern u1="&#x2f;" u2="&#x219;" k="53" />
<hkern u1="&#x2f;" u2="&#x1ff;" k="68" />
<hkern u1="&#x2f;" u2="&#x1fe;" k="12" />
<hkern u1="&#x2f;" u2="&#x1fd;" k="43" />
<hkern u1="&#x2f;" u2="&#x1fc;" k="88" />
<hkern u1="&#x2f;" u2="&#x1fb;" k="43" />
<hkern u1="&#x2f;" u2="&#x1fa;" k="88" />
<hkern u1="&#x2f;" u2="&#x17e;" k="14" />
<hkern u1="&#x2f;" u2="&#x17c;" k="14" />
<hkern u1="&#x2f;" u2="&#x17a;" k="14" />
<hkern u1="&#x2f;" u2="&#x177;" k="12" />
<hkern u1="&#x2f;" u2="&#x175;" k="10" />
<hkern u1="&#x2f;" u2="&#x173;" k="37" />
<hkern u1="&#x2f;" u2="&#x171;" k="37" />
<hkern u1="&#x2f;" u2="&#x16f;" k="37" />
<hkern u1="&#x2f;" u2="&#x16d;" k="37" />
<hkern u1="&#x2f;" u2="&#x16b;" k="37" />
<hkern u1="&#x2f;" u2="&#x169;" k="37" />
<hkern u1="&#x2f;" u2="&#x161;" k="53" />
<hkern u1="&#x2f;" u2="&#x15f;" k="53" />
<hkern u1="&#x2f;" u2="&#x15d;" k="53" />
<hkern u1="&#x2f;" u2="&#x15b;" k="53" />
<hkern u1="&#x2f;" u2="&#x159;" k="41" />
<hkern u1="&#x2f;" u2="&#x157;" k="41" />
<hkern u1="&#x2f;" u2="&#x155;" k="41" />
<hkern u1="&#x2f;" u2="&#x153;" k="68" />
<hkern u1="&#x2f;" u2="&#x152;" k="12" />
<hkern u1="&#x2f;" u2="&#x151;" k="68" />
<hkern u1="&#x2f;" u2="&#x150;" k="12" />
<hkern u1="&#x2f;" u2="&#x14f;" k="68" />
<hkern u1="&#x2f;" u2="&#x14e;" k="12" />
<hkern u1="&#x2f;" u2="&#x14d;" k="68" />
<hkern u1="&#x2f;" u2="&#x14c;" k="12" />
<hkern u1="&#x2f;" u2="&#x14b;" k="41" />
<hkern u1="&#x2f;" u2="&#x148;" k="41" />
<hkern u1="&#x2f;" u2="&#x146;" k="41" />
<hkern u1="&#x2f;" u2="&#x144;" k="41" />
<hkern u1="&#x2f;" u2="&#x134;" k="39" />
<hkern u1="&#x2f;" u2="&#x12d;" k="-51" />
<hkern u1="&#x2f;" u2="&#x12b;" k="-39" />
<hkern u1="&#x2f;" u2="&#x129;" k="-72" />
<hkern u1="&#x2f;" u2="&#x123;" k="66" />
<hkern u1="&#x2f;" u2="&#x122;" k="12" />
<hkern u1="&#x2f;" u2="&#x121;" k="66" />
<hkern u1="&#x2f;" u2="&#x120;" k="12" />
<hkern u1="&#x2f;" u2="&#x11f;" k="66" />
<hkern u1="&#x2f;" u2="&#x11e;" k="12" />
<hkern u1="&#x2f;" u2="&#x11d;" k="66" />
<hkern u1="&#x2f;" u2="&#x11c;" k="12" />
<hkern u1="&#x2f;" u2="&#x11b;" k="68" />
<hkern u1="&#x2f;" u2="&#x119;" k="68" />
<hkern u1="&#x2f;" u2="&#x117;" k="68" />
<hkern u1="&#x2f;" u2="&#x115;" k="68" />
<hkern u1="&#x2f;" u2="&#x113;" k="68" />
<hkern u1="&#x2f;" u2="&#x111;" k="70" />
<hkern u1="&#x2f;" u2="&#x10f;" k="70" />
<hkern u1="&#x2f;" u2="&#x10d;" k="68" />
<hkern u1="&#x2f;" u2="&#x10c;" k="10" />
<hkern u1="&#x2f;" u2="&#x10b;" k="68" />
<hkern u1="&#x2f;" u2="&#x10a;" k="10" />
<hkern u1="&#x2f;" u2="&#x109;" k="68" />
<hkern u1="&#x2f;" u2="&#x108;" k="10" />
<hkern u1="&#x2f;" u2="&#x107;" k="68" />
<hkern u1="&#x2f;" u2="&#x106;" k="10" />
<hkern u1="&#x2f;" u2="&#x105;" k="43" />
<hkern u1="&#x2f;" u2="&#x104;" k="88" />
<hkern u1="&#x2f;" u2="&#x103;" k="43" />
<hkern u1="&#x2f;" u2="&#x102;" k="88" />
<hkern u1="&#x2f;" u2="&#x101;" k="43" />
<hkern u1="&#x2f;" u2="&#x100;" k="88" />
<hkern u1="&#x2f;" u2="&#xff;" k="12" />
<hkern u1="&#x2f;" u2="&#xfd;" k="12" />
<hkern u1="&#x2f;" u2="&#xfc;" k="37" />
<hkern u1="&#x2f;" u2="&#xfb;" k="37" />
<hkern u1="&#x2f;" u2="&#xfa;" k="37" />
<hkern u1="&#x2f;" u2="&#xf9;" k="37" />
<hkern u1="&#x2f;" u2="&#xf8;" k="68" />
<hkern u1="&#x2f;" u2="&#xf6;" k="68" />
<hkern u1="&#x2f;" u2="&#xf5;" k="68" />
<hkern u1="&#x2f;" u2="&#xf4;" k="68" />
<hkern u1="&#x2f;" u2="&#xf3;" k="68" />
<hkern u1="&#x2f;" u2="&#xf2;" k="68" />
<hkern u1="&#x2f;" u2="&#xf1;" k="41" />
<hkern u1="&#x2f;" u2="&#xef;" k="-51" />
<hkern u1="&#x2f;" u2="&#xec;" k="-84" />
<hkern u1="&#x2f;" u2="&#xeb;" k="68" />
<hkern u1="&#x2f;" u2="&#xea;" k="68" />
<hkern u1="&#x2f;" u2="&#xe9;" k="68" />
<hkern u1="&#x2f;" u2="&#xe8;" k="68" />
<hkern u1="&#x2f;" u2="&#xe7;" k="68" />
<hkern u1="&#x2f;" u2="&#xe6;" k="43" />
<hkern u1="&#x2f;" u2="&#xe5;" k="43" />
<hkern u1="&#x2f;" u2="&#xe4;" k="43" />
<hkern u1="&#x2f;" u2="&#xe3;" k="43" />
<hkern u1="&#x2f;" u2="&#xe2;" k="43" />
<hkern u1="&#x2f;" u2="&#xe1;" k="43" />
<hkern u1="&#x2f;" u2="&#xe0;" k="43" />
<hkern u1="&#x2f;" u2="&#xd8;" k="12" />
<hkern u1="&#x2f;" u2="&#xd6;" k="12" />
<hkern u1="&#x2f;" u2="&#xd5;" k="12" />
<hkern u1="&#x2f;" u2="&#xd4;" k="12" />
<hkern u1="&#x2f;" u2="&#xd3;" k="12" />
<hkern u1="&#x2f;" u2="&#xd2;" k="12" />
<hkern u1="&#x2f;" u2="&#xc7;" k="10" />
<hkern u1="&#x2f;" u2="&#xc6;" k="106" />
<hkern u1="&#x2f;" u2="&#xc5;" k="88" />
<hkern u1="&#x2f;" u2="&#xc4;" k="88" />
<hkern u1="&#x2f;" u2="&#xc3;" k="88" />
<hkern u1="&#x2f;" u2="&#xc2;" k="88" />
<hkern u1="&#x2f;" u2="&#xc1;" k="88" />
<hkern u1="&#x2f;" u2="&#xc0;" k="88" />
<hkern u1="&#x2f;" u2="z" k="14" />
<hkern u1="&#x2f;" u2="y" k="12" />
<hkern u1="&#x2f;" u2="w" k="10" />
<hkern u1="&#x2f;" u2="v" k="10" />
<hkern u1="&#x2f;" u2="u" k="37" />
<hkern u1="&#x2f;" u2="s" k="53" />
<hkern u1="&#x2f;" u2="r" k="41" />
<hkern u1="&#x2f;" u2="q" k="70" />
<hkern u1="&#x2f;" u2="p" k="41" />
<hkern u1="&#x2f;" u2="o" k="68" />
<hkern u1="&#x2f;" u2="n" k="41" />
<hkern u1="&#x2f;" u2="m" k="41" />
<hkern u1="&#x2f;" u2="g" k="66" />
<hkern u1="&#x2f;" u2="e" k="68" />
<hkern u1="&#x2f;" u2="d" k="70" />
<hkern u1="&#x2f;" u2="c" k="68" />
<hkern u1="&#x2f;" u2="a" k="43" />
<hkern u1="&#x2f;" u2="Q" k="12" />
<hkern u1="&#x2f;" u2="O" k="12" />
<hkern u1="&#x2f;" u2="J" k="39" />
<hkern u1="&#x2f;" u2="G" k="12" />
<hkern u1="&#x2f;" u2="C" k="10" />
<hkern u1="&#x2f;" u2="A" k="88" />
<hkern u1="&#x2f;" u2="&#x2f;" k="575" />
<hkern u1="&#x3a;" u2="V" k="29" />
<hkern u1="&#x3b;" u2="V" k="29" />
<hkern u1="&#x40;" u2="&#x21a;" k="33" />
<hkern u1="&#x40;" u2="&#x178;" k="59" />
<hkern u1="&#x40;" u2="&#x176;" k="59" />
<hkern u1="&#x40;" u2="&#x166;" k="33" />
<hkern u1="&#x40;" u2="&#x164;" k="33" />
<hkern u1="&#x40;" u2="&#x134;" k="16" />
<hkern u1="&#x40;" u2="&#xdd;" k="59" />
<hkern u1="&#x40;" u2="&#xc6;" k="12" />
<hkern u1="&#x40;" u2="Y" k="59" />
<hkern u1="&#x40;" u2="V" k="16" />
<hkern u1="&#x40;" u2="T" k="33" />
<hkern u1="&#x40;" u2="J" k="16" />
<hkern u1="A" u2="&#x2122;" k="84" />
<hkern u1="A" u2="&#xae;" k="53" />
<hkern u1="A" u2="&#x7d;" k="41" />
<hkern u1="A" u2="v" k="37" />
<hkern u1="A" u2="f" k="14" />
<hkern u1="A" u2="]" k="47" />
<hkern u1="A" u2="\" k="96" />
<hkern u1="A" u2="V" k="57" />
<hkern u1="A" u2="&#x3f;" k="51" />
<hkern u1="A" u2="&#x2a;" k="74" />
<hkern u1="B" u2="&#x21a;" k="33" />
<hkern u1="B" u2="&#x1fc;" k="20" />
<hkern u1="B" u2="&#x1fa;" k="20" />
<hkern u1="B" u2="&#x178;" k="51" />
<hkern u1="B" u2="&#x177;" k="8" />
<hkern u1="B" u2="&#x176;" k="51" />
<hkern u1="B" u2="&#x175;" k="8" />
<hkern u1="B" u2="&#x174;" k="6" />
<hkern u1="B" u2="&#x166;" k="33" />
<hkern u1="B" u2="&#x164;" k="33" />
<hkern u1="B" u2="&#x134;" k="33" />
<hkern u1="B" u2="&#x123;" k="18" />
<hkern u1="B" u2="&#x121;" k="18" />
<hkern u1="B" u2="&#x11f;" k="18" />
<hkern u1="B" u2="&#x11d;" k="18" />
<hkern u1="B" u2="&#x104;" k="20" />
<hkern u1="B" u2="&#x102;" k="20" />
<hkern u1="B" u2="&#x100;" k="20" />
<hkern u1="B" u2="&#xff;" k="8" />
<hkern u1="B" u2="&#xfd;" k="8" />
<hkern u1="B" u2="&#xdd;" k="51" />
<hkern u1="B" u2="&#xc6;" k="27" />
<hkern u1="B" u2="&#xc5;" k="20" />
<hkern u1="B" u2="&#xc4;" k="20" />
<hkern u1="B" u2="&#xc3;" k="20" />
<hkern u1="B" u2="&#xc2;" k="20" />
<hkern u1="B" u2="&#xc1;" k="20" />
<hkern u1="B" u2="&#xc0;" k="20" />
<hkern u1="B" u2="&#x7d;" k="41" />
<hkern u1="B" u2="y" k="8" />
<hkern u1="B" u2="x" k="10" />
<hkern u1="B" u2="w" k="8" />
<hkern u1="B" u2="v" k="8" />
<hkern u1="B" u2="g" k="18" />
<hkern u1="B" u2="]" k="55" />
<hkern u1="B" u2="\" k="12" />
<hkern u1="B" u2="Y" k="51" />
<hkern u1="B" u2="X" k="27" />
<hkern u1="B" u2="W" k="6" />
<hkern u1="B" u2="V" k="23" />
<hkern u1="B" u2="T" k="33" />
<hkern u1="B" u2="J" k="33" />
<hkern u1="B" u2="A" k="20" />
<hkern u1="B" u2="&#x3f;" k="14" />
<hkern u1="C" u2="&#x135;" k="-16" />
<hkern u1="C" u2="&#x12d;" k="-31" />
<hkern u1="C" u2="&#x12b;" k="-18" />
<hkern u1="C" u2="&#x129;" k="-59" />
<hkern u1="C" u2="&#xef;" k="-29" />
<hkern u1="C" u2="&#xee;" k="-25" />
<hkern u1="C" u2="&#xec;" k="-68" />
<hkern u1="C" u2="&#xae;" k="27" />
<hkern u1="C" u2="v" k="33" />
<hkern u1="C" u2="f" k="12" />
<hkern u1="D" u2="&#xc6;" k="35" />
<hkern u1="D" u2="&#x7d;" k="53" />
<hkern u1="D" u2="x" k="6" />
<hkern u1="D" u2="]" k="59" />
<hkern u1="D" u2="\" k="14" />
<hkern u1="D" u2="X" k="43" />
<hkern u1="D" u2="V" k="25" />
<hkern u1="D" u2="&#x3f;" k="18" />
<hkern u1="D" u2="&#x2f;" k="12" />
<hkern u1="D" u2="&#x29;" k="25" />
<hkern u1="E" u2="&#x135;" k="-16" />
<hkern u1="E" u2="&#x12d;" k="-20" />
<hkern u1="E" u2="&#x12b;" k="-14" />
<hkern u1="E" u2="&#x129;" k="-51" />
<hkern u1="E" u2="&#xef;" k="-27" />
<hkern u1="E" u2="&#xee;" k="-25" />
<hkern u1="E" u2="&#xec;" k="-74" />
<hkern u1="E" u2="v" k="23" />
<hkern u1="E" u2="f" k="6" />
<hkern u1="F" u2="&#x2026;" k="147" />
<hkern u1="F" u2="&#x201e;" k="147" />
<hkern u1="F" u2="&#x201a;" k="147" />
<hkern u1="F" u2="&#x2014;" k="8" />
<hkern u1="F" u2="&#x2013;" k="8" />
<hkern u1="F" u2="&#x21b;" k="14" />
<hkern u1="F" u2="&#x219;" k="35" />
<hkern u1="F" u2="&#x218;" k="20" />
<hkern u1="F" u2="&#x1ff;" k="37" />
<hkern u1="F" u2="&#x1fe;" k="20" />
<hkern u1="F" u2="&#x1fd;" k="68" />
<hkern u1="F" u2="&#x1fc;" k="70" />
<hkern u1="F" u2="&#x1fb;" k="68" />
<hkern u1="F" u2="&#x1fa;" k="70" />
<hkern u1="F" u2="&#x17e;" k="41" />
<hkern u1="F" u2="&#x17c;" k="41" />
<hkern u1="F" u2="&#x17a;" k="41" />
<hkern u1="F" u2="&#x177;" k="27" />
<hkern u1="F" u2="&#x175;" k="29" />
<hkern u1="F" u2="&#x173;" k="37" />
<hkern u1="F" u2="&#x171;" k="37" />
<hkern u1="F" u2="&#x16f;" k="37" />
<hkern u1="F" u2="&#x16d;" k="37" />
<hkern u1="F" u2="&#x16b;" k="37" />
<hkern u1="F" u2="&#x169;" k="37" />
<hkern u1="F" u2="&#x167;" k="14" />
<hkern u1="F" u2="&#x165;" k="14" />
<hkern u1="F" u2="&#x161;" k="35" />
<hkern u1="F" u2="&#x160;" k="20" />
<hkern u1="F" u2="&#x15f;" k="35" />
<hkern u1="F" u2="&#x15e;" k="20" />
<hkern u1="F" u2="&#x15d;" k="35" />
<hkern u1="F" u2="&#x15c;" k="20" />
<hkern u1="F" u2="&#x15b;" k="35" />
<hkern u1="F" u2="&#x15a;" k="20" />
<hkern u1="F" u2="&#x159;" k="43" />
<hkern u1="F" u2="&#x157;" k="43" />
<hkern u1="F" u2="&#x155;" k="43" />
<hkern u1="F" u2="&#x153;" k="37" />
<hkern u1="F" u2="&#x152;" k="20" />
<hkern u1="F" u2="&#x151;" k="37" />
<hkern u1="F" u2="&#x150;" k="20" />
<hkern u1="F" u2="&#x14f;" k="37" />
<hkern u1="F" u2="&#x14e;" k="20" />
<hkern u1="F" u2="&#x14d;" k="37" />
<hkern u1="F" u2="&#x14c;" k="20" />
<hkern u1="F" u2="&#x14b;" k="43" />
<hkern u1="F" u2="&#x148;" k="43" />
<hkern u1="F" u2="&#x146;" k="43" />
<hkern u1="F" u2="&#x144;" k="43" />
<hkern u1="F" u2="&#x135;" k="-33" />
<hkern u1="F" u2="&#x134;" k="43" />
<hkern u1="F" u2="&#x131;" k="43" />
<hkern u1="F" u2="&#x12d;" k="-59" />
<hkern u1="F" u2="&#x12b;" k="-51" />
<hkern u1="F" u2="&#x129;" k="-90" />
<hkern u1="F" u2="&#x123;" k="43" />
<hkern u1="F" u2="&#x122;" k="20" />
<hkern u1="F" u2="&#x121;" k="43" />
<hkern u1="F" u2="&#x120;" k="20" />
<hkern u1="F" u2="&#x11f;" k="43" />
<hkern u1="F" u2="&#x11e;" k="20" />
<hkern u1="F" u2="&#x11d;" k="43" />
<hkern u1="F" u2="&#x11c;" k="20" />
<hkern u1="F" u2="&#x11b;" k="37" />
<hkern u1="F" u2="&#x119;" k="37" />
<hkern u1="F" u2="&#x117;" k="37" />
<hkern u1="F" u2="&#x115;" k="37" />
<hkern u1="F" u2="&#x113;" k="37" />
<hkern u1="F" u2="&#x111;" k="39" />
<hkern u1="F" u2="&#x10f;" k="39" />
<hkern u1="F" u2="&#x10d;" k="37" />
<hkern u1="F" u2="&#x10c;" k="20" />
<hkern u1="F" u2="&#x10b;" k="37" />
<hkern u1="F" u2="&#x10a;" k="20" />
<hkern u1="F" u2="&#x109;" k="37" />
<hkern u1="F" u2="&#x108;" k="20" />
<hkern u1="F" u2="&#x107;" k="37" />
<hkern u1="F" u2="&#x106;" k="20" />
<hkern u1="F" u2="&#x105;" k="68" />
<hkern u1="F" u2="&#x104;" k="70" />
<hkern u1="F" u2="&#x103;" k="68" />
<hkern u1="F" u2="&#x102;" k="70" />
<hkern u1="F" u2="&#x101;" k="68" />
<hkern u1="F" u2="&#x100;" k="70" />
<hkern u1="F" u2="&#xff;" k="27" />
<hkern u1="F" u2="&#xfd;" k="27" />
<hkern u1="F" u2="&#xfc;" k="37" />
<hkern u1="F" u2="&#xfb;" k="37" />
<hkern u1="F" u2="&#xfa;" k="37" />
<hkern u1="F" u2="&#xf9;" k="37" />
<hkern u1="F" u2="&#xf8;" k="37" />
<hkern u1="F" u2="&#xf6;" k="37" />
<hkern u1="F" u2="&#xf5;" k="37" />
<hkern u1="F" u2="&#xf4;" k="37" />
<hkern u1="F" u2="&#xf3;" k="37" />
<hkern u1="F" u2="&#xf2;" k="37" />
<hkern u1="F" u2="&#xf1;" k="43" />
<hkern u1="F" u2="&#xef;" k="-57" />
<hkern u1="F" u2="&#xee;" k="-37" />
<hkern u1="F" u2="&#xec;" k="-113" />
<hkern u1="F" u2="&#xeb;" k="37" />
<hkern u1="F" u2="&#xea;" k="37" />
<hkern u1="F" u2="&#xe9;" k="37" />
<hkern u1="F" u2="&#xe8;" k="37" />
<hkern u1="F" u2="&#xe7;" k="37" />
<hkern u1="F" u2="&#xe6;" k="68" />
<hkern u1="F" u2="&#xe5;" k="68" />
<hkern u1="F" u2="&#xe4;" k="68" />
<hkern u1="F" u2="&#xe3;" k="68" />
<hkern u1="F" u2="&#xe2;" k="68" />
<hkern u1="F" u2="&#xe1;" k="68" />
<hkern u1="F" u2="&#xe0;" k="68" />
<hkern u1="F" u2="&#xd8;" k="20" />
<hkern u1="F" u2="&#xd6;" k="20" />
<hkern u1="F" u2="&#xd5;" k="20" />
<hkern u1="F" u2="&#xd4;" k="20" />
<hkern u1="F" u2="&#xd3;" k="20" />
<hkern u1="F" u2="&#xd2;" k="20" />
<hkern u1="F" u2="&#xc7;" k="20" />
<hkern u1="F" u2="&#xc6;" k="94" />
<hkern u1="F" u2="&#xc5;" k="70" />
<hkern u1="F" u2="&#xc4;" k="70" />
<hkern u1="F" u2="&#xc3;" k="70" />
<hkern u1="F" u2="&#xc2;" k="70" />
<hkern u1="F" u2="&#xc1;" k="70" />
<hkern u1="F" u2="&#xc0;" k="70" />
<hkern u1="F" u2="z" k="41" />
<hkern u1="F" u2="y" k="27" />
<hkern u1="F" u2="x" k="47" />
<hkern u1="F" u2="w" k="29" />
<hkern u1="F" u2="v" k="23" />
<hkern u1="F" u2="u" k="37" />
<hkern u1="F" u2="t" k="14" />
<hkern u1="F" u2="s" k="35" />
<hkern u1="F" u2="r" k="43" />
<hkern u1="F" u2="q" k="39" />
<hkern u1="F" u2="p" k="43" />
<hkern u1="F" u2="o" k="37" />
<hkern u1="F" u2="n" k="43" />
<hkern u1="F" u2="m" k="43" />
<hkern u1="F" u2="g" k="43" />
<hkern u1="F" u2="f" k="14" />
<hkern u1="F" u2="e" k="37" />
<hkern u1="F" u2="d" k="39" />
<hkern u1="F" u2="c" k="37" />
<hkern u1="F" u2="a" k="68" />
<hkern u1="F" u2="X" k="8" />
<hkern u1="F" u2="S" k="20" />
<hkern u1="F" u2="Q" k="20" />
<hkern u1="F" u2="O" k="20" />
<hkern u1="F" u2="J" k="43" />
<hkern u1="F" u2="G" k="20" />
<hkern u1="F" u2="C" k="20" />
<hkern u1="F" u2="A" k="70" />
<hkern u1="F" u2="&#x2f;" k="63" />
<hkern u1="F" u2="&#x2e;" k="147" />
<hkern u1="F" u2="&#x2d;" k="8" />
<hkern u1="F" u2="&#x2c;" k="147" />
<hkern u1="G" u2="&#xef;" k="-12" />
<hkern u1="G" u2="&#xee;" k="-8" />
<hkern u1="G" u2="&#xec;" k="-31" />
<hkern u1="G" u2="v" k="14" />
<hkern u1="G" u2="f" k="12" />
<hkern u1="G" u2="\" k="8" />
<hkern u1="G" u2="V" k="18" />
<hkern u1="H" u2="&#xec;" k="-6" />
<hkern u1="I" u2="&#xec;" k="-6" />
<hkern u1="J" u2="&#xec;" k="-8" />
<hkern u1="K" u2="&#x12d;" k="-59" />
<hkern u1="K" u2="&#x12b;" k="-27" />
<hkern u1="K" u2="&#x129;" k="-63" />
<hkern u1="K" u2="&#xef;" k="-55" />
<hkern u1="K" u2="&#xec;" k="-100" />
<hkern u1="K" u2="&#xae;" k="23" />
<hkern u1="K" u2="v" k="49" />
<hkern u1="K" u2="f" k="8" />
<hkern u1="L" u2="&#x2122;" k="184" />
<hkern u1="L" u2="&#xae;" k="172" />
<hkern u1="L" u2="&#x7d;" k="23" />
<hkern u1="L" u2="v" k="92" />
<hkern u1="L" u2="f" k="6" />
<hkern u1="L" u2="]" k="29" />
<hkern u1="L" u2="\" k="145" />
<hkern u1="L" u2="V" k="121" />
<hkern u1="L" u2="&#x3f;" k="27" />
<hkern u1="L" u2="&#x2a;" k="182" />
<hkern u1="M" u2="&#xec;" k="-6" />
<hkern u1="N" u2="&#xec;" k="-6" />
<hkern u1="O" u2="&#xc6;" k="31" />
<hkern u1="O" u2="&#x7d;" k="51" />
<hkern u1="O" u2="]" k="57" />
<hkern u1="O" u2="\" k="16" />
<hkern u1="O" u2="X" k="39" />
<hkern u1="O" u2="V" k="25" />
<hkern u1="O" u2="&#x3f;" k="14" />
<hkern u1="O" u2="&#x2f;" k="12" />
<hkern u1="O" u2="&#x29;" k="10" />
<hkern u1="P" u2="&#x2039;" k="18" />
<hkern u1="P" u2="&#x2026;" k="166" />
<hkern u1="P" u2="&#x201e;" k="166" />
<hkern u1="P" u2="&#x201a;" k="166" />
<hkern u1="P" u2="&#x2014;" k="18" />
<hkern u1="P" u2="&#x2013;" k="18" />
<hkern u1="P" u2="&#x1ff;" k="8" />
<hkern u1="P" u2="&#x1fd;" k="10" />
<hkern u1="P" u2="&#x1fc;" k="63" />
<hkern u1="P" u2="&#x1fb;" k="10" />
<hkern u1="P" u2="&#x1fa;" k="63" />
<hkern u1="P" u2="&#x17d;" k="14" />
<hkern u1="P" u2="&#x17b;" k="14" />
<hkern u1="P" u2="&#x179;" k="14" />
<hkern u1="P" u2="&#x178;" k="41" />
<hkern u1="P" u2="&#x176;" k="41" />
<hkern u1="P" u2="&#x153;" k="8" />
<hkern u1="P" u2="&#x151;" k="8" />
<hkern u1="P" u2="&#x14f;" k="8" />
<hkern u1="P" u2="&#x14d;" k="8" />
<hkern u1="P" u2="&#x135;" k="-16" />
<hkern u1="P" u2="&#x134;" k="53" />
<hkern u1="P" u2="&#x123;" k="8" />
<hkern u1="P" u2="&#x121;" k="8" />
<hkern u1="P" u2="&#x11f;" k="8" />
<hkern u1="P" u2="&#x11d;" k="8" />
<hkern u1="P" u2="&#x11b;" k="8" />
<hkern u1="P" u2="&#x119;" k="8" />
<hkern u1="P" u2="&#x117;" k="8" />
<hkern u1="P" u2="&#x115;" k="8" />
<hkern u1="P" u2="&#x113;" k="8" />
<hkern u1="P" u2="&#x111;" k="10" />
<hkern u1="P" u2="&#x10f;" k="10" />
<hkern u1="P" u2="&#x10d;" k="8" />
<hkern u1="P" u2="&#x10b;" k="8" />
<hkern u1="P" u2="&#x109;" k="8" />
<hkern u1="P" u2="&#x107;" k="8" />
<hkern u1="P" u2="&#x105;" k="10" />
<hkern u1="P" u2="&#x104;" k="63" />
<hkern u1="P" u2="&#x103;" k="10" />
<hkern u1="P" u2="&#x102;" k="63" />
<hkern u1="P" u2="&#x101;" k="10" />
<hkern u1="P" u2="&#x100;" k="63" />
<hkern u1="P" u2="&#xf8;" k="8" />
<hkern u1="P" u2="&#xf6;" k="8" />
<hkern u1="P" u2="&#xf5;" k="8" />
<hkern u1="P" u2="&#xf4;" k="8" />
<hkern u1="P" u2="&#xf3;" k="8" />
<hkern u1="P" u2="&#xf2;" k="8" />
<hkern u1="P" u2="&#xef;" k="-6" />
<hkern u1="P" u2="&#xee;" k="-12" />
<hkern u1="P" u2="&#xec;" k="-8" />
<hkern u1="P" u2="&#xeb;" k="8" />
<hkern u1="P" u2="&#xea;" k="8" />
<hkern u1="P" u2="&#xe9;" k="8" />
<hkern u1="P" u2="&#xe8;" k="8" />
<hkern u1="P" u2="&#xe7;" k="8" />
<hkern u1="P" u2="&#xe6;" k="10" />
<hkern u1="P" u2="&#xe5;" k="10" />
<hkern u1="P" u2="&#xe4;" k="10" />
<hkern u1="P" u2="&#xe3;" k="10" />
<hkern u1="P" u2="&#xe2;" k="10" />
<hkern u1="P" u2="&#xe1;" k="10" />
<hkern u1="P" u2="&#xe0;" k="10" />
<hkern u1="P" u2="&#xdd;" k="41" />
<hkern u1="P" u2="&#xc6;" k="74" />
<hkern u1="P" u2="&#xc5;" k="63" />
<hkern u1="P" u2="&#xc4;" k="63" />
<hkern u1="P" u2="&#xc3;" k="63" />
<hkern u1="P" u2="&#xc2;" k="63" />
<hkern u1="P" u2="&#xc1;" k="63" />
<hkern u1="P" u2="&#xc0;" k="63" />
<hkern u1="P" u2="&#xab;" k="18" />
<hkern u1="P" u2="&#x7d;" k="43" />
<hkern u1="P" u2="q" k="10" />
<hkern u1="P" u2="o" k="8" />
<hkern u1="P" u2="g" k="8" />
<hkern u1="P" u2="e" k="8" />
<hkern u1="P" u2="d" k="10" />
<hkern u1="P" u2="c" k="8" />
<hkern u1="P" u2="a" k="10" />
<hkern u1="P" u2="]" k="47" />
<hkern u1="P" u2="\" k="10" />
<hkern u1="P" u2="Z" k="14" />
<hkern u1="P" u2="Y" k="41" />
<hkern u1="P" u2="X" k="35" />
<hkern u1="P" u2="V" k="12" />
<hkern u1="P" u2="J" k="53" />
<hkern u1="P" u2="A" k="63" />
<hkern u1="P" u2="&#x2f;" k="74" />
<hkern u1="P" u2="&#x2e;" k="166" />
<hkern u1="P" u2="&#x2d;" k="18" />
<hkern u1="P" u2="&#x2c;" k="166" />
<hkern u1="P" u2="&#x29;" k="8" />
<hkern u1="Q" u2="&#xc6;" k="31" />
<hkern u1="Q" u2="&#x7d;" k="51" />
<hkern u1="Q" u2="]" k="57" />
<hkern u1="Q" u2="\" k="16" />
<hkern u1="Q" u2="X" k="39" />
<hkern u1="Q" u2="V" k="25" />
<hkern u1="Q" u2="&#x3f;" k="14" />
<hkern u1="Q" u2="&#x2f;" k="12" />
<hkern u1="Q" u2="&#x29;" k="10" />
<hkern u1="R" u2="&#xc6;" k="16" />
<hkern u1="R" u2="&#x7d;" k="31" />
<hkern u1="R" u2="]" k="35" />
<hkern u1="R" u2="\" k="12" />
<hkern u1="R" u2="V" k="20" />
<hkern u1="S" u2="&#x129;" k="-8" />
<hkern u1="S" u2="&#xef;" k="-16" />
<hkern u1="S" u2="&#xee;" k="-6" />
<hkern u1="S" u2="&#xec;" k="-41" />
<hkern u1="S" u2="&#xc6;" k="25" />
<hkern u1="S" u2="x" k="18" />
<hkern u1="S" u2="v" k="20" />
<hkern u1="S" u2="f" k="16" />
<hkern u1="S" u2="X" k="10" />
<hkern u1="S" u2="V" k="18" />
<hkern u1="T" u2="&#x16d;" k="170" />
<hkern u1="T" u2="&#x169;" k="166" />
<hkern u1="T" u2="&#x15d;" k="170" />
<hkern u1="T" u2="&#x159;" k="100" />
<hkern u1="T" u2="&#x155;" k="141" />
<hkern u1="T" u2="&#x151;" k="164" />
<hkern u1="T" u2="&#x135;" k="-39" />
<hkern u1="T" u2="&#x131;" k="176" />
<hkern u1="T" u2="&#x12d;" k="-88" />
<hkern u1="T" u2="&#x12b;" k="-78" />
<hkern u1="T" u2="&#x129;" k="-119" />
<hkern u1="T" u2="&#x11f;" k="193" />
<hkern u1="T" u2="&#x109;" k="166" />
<hkern u1="T" u2="&#xef;" k="-84" />
<hkern u1="T" u2="&#xee;" k="-45" />
<hkern u1="T" u2="&#xec;" k="-141" />
<hkern u1="T" u2="&#xe4;" k="168" />
<hkern u1="T" u2="&#xe3;" k="135" />
<hkern u1="T" u2="&#xdf;" k="8" />
<hkern u1="T" u2="&#xc6;" k="111" />
<hkern u1="T" u2="&#xae;" k="16" />
<hkern u1="T" u2="x" k="147" />
<hkern u1="T" u2="v" k="137" />
<hkern u1="T" u2="f" k="29" />
<hkern u1="T" u2="&#x40;" k="53" />
<hkern u1="T" u2="&#x2f;" k="104" />
<hkern u1="T" u2="&#x26;" k="43" />
<hkern u1="U" u2="&#xec;" k="-12" />
<hkern u1="U" u2="&#xc6;" k="18" />
<hkern u1="U" u2="&#x2f;" k="12" />
<hkern u1="V" u2="&#x203a;" k="39" />
<hkern u1="V" u2="&#x2039;" k="57" />
<hkern u1="V" u2="&#x2026;" k="109" />
<hkern u1="V" u2="&#x201e;" k="109" />
<hkern u1="V" u2="&#x201a;" k="109" />
<hkern u1="V" u2="&#x2014;" k="63" />
<hkern u1="V" u2="&#x2013;" k="63" />
<hkern u1="V" u2="&#x219;" k="51" />
<hkern u1="V" u2="&#x218;" k="16" />
<hkern u1="V" u2="&#x1ff;" k="66" />
<hkern u1="V" u2="&#x1fe;" k="25" />
<hkern u1="V" u2="&#x1fd;" k="55" />
<hkern u1="V" u2="&#x1fc;" k="57" />
<hkern u1="V" u2="&#x1fb;" k="55" />
<hkern u1="V" u2="&#x1fa;" k="57" />
<hkern u1="V" u2="&#x17e;" k="25" />
<hkern u1="V" u2="&#x17c;" k="25" />
<hkern u1="V" u2="&#x17a;" k="25" />
<hkern u1="V" u2="&#x177;" k="16" />
<hkern u1="V" u2="&#x175;" k="20" />
<hkern u1="V" u2="&#x173;" k="47" />
<hkern u1="V" u2="&#x171;" k="47" />
<hkern u1="V" u2="&#x16f;" k="47" />
<hkern u1="V" u2="&#x16d;" k="47" />
<hkern u1="V" u2="&#x16b;" k="47" />
<hkern u1="V" u2="&#x169;" k="47" />
<hkern u1="V" u2="&#x167;" k="8" />
<hkern u1="V" u2="&#x161;" k="51" />
<hkern u1="V" u2="&#x160;" k="16" />
<hkern u1="V" u2="&#x15f;" k="51" />
<hkern u1="V" u2="&#x15e;" k="16" />
<hkern u1="V" u2="&#x15d;" k="51" />
<hkern u1="V" u2="&#x15c;" k="16" />
<hkern u1="V" u2="&#x15b;" k="51" />
<hkern u1="V" u2="&#x15a;" k="16" />
<hkern u1="V" u2="&#x159;" k="45" />
<hkern u1="V" u2="&#x157;" k="53" />
<hkern u1="V" u2="&#x155;" k="45" />
<hkern u1="V" u2="&#x153;" k="66" />
<hkern u1="V" u2="&#x152;" k="25" />
<hkern u1="V" u2="&#x151;" k="66" />
<hkern u1="V" u2="&#x150;" k="25" />
<hkern u1="V" u2="&#x14f;" k="66" />
<hkern u1="V" u2="&#x14e;" k="25" />
<hkern u1="V" u2="&#x14d;" k="66" />
<hkern u1="V" u2="&#x14c;" k="25" />
<hkern u1="V" u2="&#x14b;" k="53" />
<hkern u1="V" u2="&#x148;" k="53" />
<hkern u1="V" u2="&#x146;" k="53" />
<hkern u1="V" u2="&#x144;" k="53" />
<hkern u1="V" u2="&#x135;" k="-31" />
<hkern u1="V" u2="&#x134;" k="55" />
<hkern u1="V" u2="&#x131;" k="53" />
<hkern u1="V" u2="&#x12d;" k="-66" />
<hkern u1="V" u2="&#x12b;" k="-47" />
<hkern u1="V" u2="&#x129;" k="-82" />
<hkern u1="V" u2="&#x123;" k="76" />
<hkern u1="V" u2="&#x122;" k="25" />
<hkern u1="V" u2="&#x121;" k="76" />
<hkern u1="V" u2="&#x120;" k="25" />
<hkern u1="V" u2="&#x11f;" k="76" />
<hkern u1="V" u2="&#x11e;" k="25" />
<hkern u1="V" u2="&#x11d;" k="76" />
<hkern u1="V" u2="&#x11c;" k="25" />
<hkern u1="V" u2="&#x11b;" k="66" />
<hkern u1="V" u2="&#x119;" k="66" />
<hkern u1="V" u2="&#x117;" k="66" />
<hkern u1="V" u2="&#x115;" k="66" />
<hkern u1="V" u2="&#x113;" k="66" />
<hkern u1="V" u2="&#x111;" k="66" />
<hkern u1="V" u2="&#x10f;" k="66" />
<hkern u1="V" u2="&#x10d;" k="66" />
<hkern u1="V" u2="&#x10c;" k="23" />
<hkern u1="V" u2="&#x10b;" k="66" />
<hkern u1="V" u2="&#x10a;" k="23" />
<hkern u1="V" u2="&#x109;" k="66" />
<hkern u1="V" u2="&#x108;" k="23" />
<hkern u1="V" u2="&#x107;" k="66" />
<hkern u1="V" u2="&#x106;" k="23" />
<hkern u1="V" u2="&#x105;" k="55" />
<hkern u1="V" u2="&#x104;" k="57" />
<hkern u1="V" u2="&#x103;" k="55" />
<hkern u1="V" u2="&#x102;" k="57" />
<hkern u1="V" u2="&#x101;" k="55" />
<hkern u1="V" u2="&#x100;" k="57" />
<hkern u1="V" u2="&#xff;" k="16" />
<hkern u1="V" u2="&#xfd;" k="16" />
<hkern u1="V" u2="&#xfc;" k="47" />
<hkern u1="V" u2="&#xfb;" k="47" />
<hkern u1="V" u2="&#xfa;" k="47" />
<hkern u1="V" u2="&#xf9;" k="47" />
<hkern u1="V" u2="&#xf8;" k="66" />
<hkern u1="V" u2="&#xf6;" k="66" />
<hkern u1="V" u2="&#xf5;" k="66" />
<hkern u1="V" u2="&#xf4;" k="66" />
<hkern u1="V" u2="&#xf3;" k="66" />
<hkern u1="V" u2="&#xf2;" k="66" />
<hkern u1="V" u2="&#xf1;" k="53" />
<hkern u1="V" u2="&#xef;" k="-61" />
<hkern u1="V" u2="&#xee;" k="-25" />
<hkern u1="V" u2="&#xec;" k="-113" />
<hkern u1="V" u2="&#xeb;" k="66" />
<hkern u1="V" u2="&#xea;" k="66" />
<hkern u1="V" u2="&#xe9;" k="66" />
<hkern u1="V" u2="&#xe8;" k="66" />
<hkern u1="V" u2="&#xe7;" k="66" />
<hkern u1="V" u2="&#xe6;" k="55" />
<hkern u1="V" u2="&#xe5;" k="55" />
<hkern u1="V" u2="&#xe4;" k="55" />
<hkern u1="V" u2="&#xe3;" k="55" />
<hkern u1="V" u2="&#xe2;" k="55" />
<hkern u1="V" u2="&#xe1;" k="55" />
<hkern u1="V" u2="&#xe0;" k="55" />
<hkern u1="V" u2="&#xd8;" k="25" />
<hkern u1="V" u2="&#xd6;" k="25" />
<hkern u1="V" u2="&#xd5;" k="25" />
<hkern u1="V" u2="&#xd4;" k="25" />
<hkern u1="V" u2="&#xd3;" k="25" />
<hkern u1="V" u2="&#xd2;" k="25" />
<hkern u1="V" u2="&#xc7;" k="23" />
<hkern u1="V" u2="&#xc6;" k="66" />
<hkern u1="V" u2="&#xc5;" k="57" />
<hkern u1="V" u2="&#xc4;" k="57" />
<hkern u1="V" u2="&#xc3;" k="57" />
<hkern u1="V" u2="&#xc2;" k="57" />
<hkern u1="V" u2="&#xc1;" k="57" />
<hkern u1="V" u2="&#xc0;" k="57" />
<hkern u1="V" u2="&#xbb;" k="39" />
<hkern u1="V" u2="&#xae;" k="14" />
<hkern u1="V" u2="&#xab;" k="57" />
<hkern u1="V" u2="z" k="25" />
<hkern u1="V" u2="y" k="16" />
<hkern u1="V" u2="x" k="16" />
<hkern u1="V" u2="w" k="20" />
<hkern u1="V" u2="v" k="16" />
<hkern u1="V" u2="u" k="47" />
<hkern u1="V" u2="s" k="51" />
<hkern u1="V" u2="r" k="53" />
<hkern u1="V" u2="q" k="66" />
<hkern u1="V" u2="p" k="53" />
<hkern u1="V" u2="o" k="66" />
<hkern u1="V" u2="n" k="53" />
<hkern u1="V" u2="m" k="53" />
<hkern u1="V" u2="g" k="76" />
<hkern u1="V" u2="f" k="6" />
<hkern u1="V" u2="e" k="66" />
<hkern u1="V" u2="d" k="66" />
<hkern u1="V" u2="c" k="66" />
<hkern u1="V" u2="a" k="55" />
<hkern u1="V" u2="S" k="16" />
<hkern u1="V" u2="Q" k="25" />
<hkern u1="V" u2="O" k="25" />
<hkern u1="V" u2="J" k="55" />
<hkern u1="V" u2="G" k="25" />
<hkern u1="V" u2="C" k="23" />
<hkern u1="V" u2="A" k="57" />
<hkern u1="V" u2="&#x40;" k="29" />
<hkern u1="V" u2="&#x3b;" k="29" />
<hkern u1="V" u2="&#x3a;" k="29" />
<hkern u1="V" u2="&#x2f;" k="84" />
<hkern u1="V" u2="&#x2e;" k="109" />
<hkern u1="V" u2="&#x2d;" k="63" />
<hkern u1="V" u2="&#x2c;" k="109" />
<hkern u1="V" u2="&#x26;" k="39" />
<hkern u1="W" u2="&#x135;" k="-31" />
<hkern u1="W" u2="&#x131;" k="31" />
<hkern u1="W" u2="&#x12d;" k="-53" />
<hkern u1="W" u2="&#x12b;" k="-37" />
<hkern u1="W" u2="&#x129;" k="-72" />
<hkern u1="W" u2="&#xef;" k="-43" />
<hkern u1="W" u2="&#xee;" k="-25" />
<hkern u1="W" u2="&#xec;" k="-98" />
<hkern u1="W" u2="&#xc6;" k="55" />
<hkern u1="W" u2="&#x2f;" k="55" />
<hkern u1="W" u2="&#x26;" k="12" />
<hkern u1="X" u2="&#x2039;" k="47" />
<hkern u1="X" u2="&#x2014;" k="78" />
<hkern u1="X" u2="&#x2013;" k="78" />
<hkern u1="X" u2="&#x21b;" k="18" />
<hkern u1="X" u2="&#x1ff;" k="43" />
<hkern u1="X" u2="&#x1fe;" k="39" />
<hkern u1="X" u2="&#x177;" k="55" />
<hkern u1="X" u2="&#x175;" k="51" />
<hkern u1="X" u2="&#x173;" k="33" />
<hkern u1="X" u2="&#x171;" k="33" />
<hkern u1="X" u2="&#x16f;" k="33" />
<hkern u1="X" u2="&#x16d;" k="33" />
<hkern u1="X" u2="&#x16b;" k="33" />
<hkern u1="X" u2="&#x169;" k="33" />
<hkern u1="X" u2="&#x167;" k="18" />
<hkern u1="X" u2="&#x165;" k="18" />
<hkern u1="X" u2="&#x153;" k="43" />
<hkern u1="X" u2="&#x152;" k="39" />
<hkern u1="X" u2="&#x151;" k="43" />
<hkern u1="X" u2="&#x150;" k="39" />
<hkern u1="X" u2="&#x14f;" k="43" />
<hkern u1="X" u2="&#x14e;" k="39" />
<hkern u1="X" u2="&#x14d;" k="43" />
<hkern u1="X" u2="&#x14c;" k="39" />
<hkern u1="X" u2="&#x135;" k="-6" />
<hkern u1="X" u2="&#x12d;" k="-78" />
<hkern u1="X" u2="&#x12b;" k="-47" />
<hkern u1="X" u2="&#x129;" k="-78" />
<hkern u1="X" u2="&#x123;" k="33" />
<hkern u1="X" u2="&#x122;" k="39" />
<hkern u1="X" u2="&#x121;" k="33" />
<hkern u1="X" u2="&#x120;" k="39" />
<hkern u1="X" u2="&#x11f;" k="33" />
<hkern u1="X" u2="&#x11e;" k="39" />
<hkern u1="X" u2="&#x11d;" k="33" />
<hkern u1="X" u2="&#x11c;" k="39" />
<hkern u1="X" u2="&#x11b;" k="43" />
<hkern u1="X" u2="&#x119;" k="43" />
<hkern u1="X" u2="&#x117;" k="43" />
<hkern u1="X" u2="&#x115;" k="43" />
<hkern u1="X" u2="&#x113;" k="43" />
<hkern u1="X" u2="&#x111;" k="37" />
<hkern u1="X" u2="&#x10f;" k="37" />
<hkern u1="X" u2="&#x10d;" k="43" />
<hkern u1="X" u2="&#x10c;" k="37" />
<hkern u1="X" u2="&#x10b;" k="43" />
<hkern u1="X" u2="&#x10a;" k="37" />
<hkern u1="X" u2="&#x109;" k="43" />
<hkern u1="X" u2="&#x108;" k="37" />
<hkern u1="X" u2="&#x107;" k="43" />
<hkern u1="X" u2="&#x106;" k="37" />
<hkern u1="X" u2="&#xff;" k="55" />
<hkern u1="X" u2="&#xfd;" k="55" />
<hkern u1="X" u2="&#xfc;" k="33" />
<hkern u1="X" u2="&#xfb;" k="33" />
<hkern u1="X" u2="&#xfa;" k="33" />
<hkern u1="X" u2="&#xf9;" k="33" />
<hkern u1="X" u2="&#xf8;" k="43" />
<hkern u1="X" u2="&#xf6;" k="43" />
<hkern u1="X" u2="&#xf5;" k="43" />
<hkern u1="X" u2="&#xf4;" k="43" />
<hkern u1="X" u2="&#xf3;" k="43" />
<hkern u1="X" u2="&#xf2;" k="43" />
<hkern u1="X" u2="&#xef;" k="-74" />
<hkern u1="X" u2="&#xee;" k="-10" />
<hkern u1="X" u2="&#xec;" k="-115" />
<hkern u1="X" u2="&#xeb;" k="43" />
<hkern u1="X" u2="&#xea;" k="43" />
<hkern u1="X" u2="&#xe9;" k="43" />
<hkern u1="X" u2="&#xe8;" k="43" />
<hkern u1="X" u2="&#xe7;" k="43" />
<hkern u1="X" u2="&#xd8;" k="39" />
<hkern u1="X" u2="&#xd6;" k="39" />
<hkern u1="X" u2="&#xd5;" k="39" />
<hkern u1="X" u2="&#xd4;" k="39" />
<hkern u1="X" u2="&#xd3;" k="39" />
<hkern u1="X" u2="&#xd2;" k="39" />
<hkern u1="X" u2="&#xc7;" k="37" />
<hkern u1="X" u2="&#xae;" k="18" />
<hkern u1="X" u2="&#xab;" k="47" />
<hkern u1="X" u2="y" k="55" />
<hkern u1="X" u2="w" k="51" />
<hkern u1="X" u2="v" k="53" />
<hkern u1="X" u2="u" k="33" />
<hkern u1="X" u2="t" k="18" />
<hkern u1="X" u2="q" k="37" />
<hkern u1="X" u2="o" k="43" />
<hkern u1="X" u2="g" k="33" />
<hkern u1="X" u2="f" k="8" />
<hkern u1="X" u2="e" k="43" />
<hkern u1="X" u2="d" k="37" />
<hkern u1="X" u2="c" k="43" />
<hkern u1="X" u2="Q" k="39" />
<hkern u1="X" u2="O" k="39" />
<hkern u1="X" u2="G" k="39" />
<hkern u1="X" u2="C" k="37" />
<hkern u1="X" u2="&#x2d;" k="78" />
<hkern u1="Y" u2="&#x159;" k="80" />
<hkern u1="Y" u2="&#x155;" k="94" />
<hkern u1="Y" u2="&#x151;" k="127" />
<hkern u1="Y" u2="&#x142;" k="12" />
<hkern u1="Y" u2="&#x135;" k="-12" />
<hkern u1="Y" u2="&#x131;" k="125" />
<hkern u1="Y" u2="&#x12d;" k="-94" />
<hkern u1="Y" u2="&#x12b;" k="-63" />
<hkern u1="Y" u2="&#x129;" k="-92" />
<hkern u1="Y" u2="&#x103;" k="127" />
<hkern u1="Y" u2="&#xff;" k="70" />
<hkern u1="Y" u2="&#xef;" k="-90" />
<hkern u1="Y" u2="&#xee;" k="-16" />
<hkern u1="Y" u2="&#xec;" k="-131" />
<hkern u1="Y" u2="&#xeb;" k="137" />
<hkern u1="Y" u2="&#xe4;" k="117" />
<hkern u1="Y" u2="&#xe3;" k="109" />
<hkern u1="Y" u2="&#xdf;" k="12" />
<hkern u1="Y" u2="&#xc6;" k="106" />
<hkern u1="Y" u2="&#xae;" k="45" />
<hkern u1="Y" u2="x" k="82" />
<hkern u1="Y" u2="v" k="84" />
<hkern u1="Y" u2="f" k="37" />
<hkern u1="Y" u2="&#x40;" k="74" />
<hkern u1="Y" u2="&#x2f;" k="131" />
<hkern u1="Y" u2="&#x26;" k="76" />
<hkern u1="Z" u2="&#x135;" k="-31" />
<hkern u1="Z" u2="&#x12d;" k="-23" />
<hkern u1="Z" u2="&#x12b;" k="-20" />
<hkern u1="Z" u2="&#x129;" k="-57" />
<hkern u1="Z" u2="&#xef;" k="-25" />
<hkern u1="Z" u2="&#xee;" k="-31" />
<hkern u1="Z" u2="&#xec;" k="-80" />
<hkern u1="Z" u2="&#xae;" k="14" />
<hkern u1="Z" u2="v" k="25" />
<hkern u1="Z" u2="f" k="6" />
<hkern u1="[" u2="&#x21b;" k="51" />
<hkern u1="[" u2="&#x219;" k="51" />
<hkern u1="[" u2="&#x218;" k="23" />
<hkern u1="[" u2="&#x1ff;" k="78" />
<hkern u1="[" u2="&#x1fe;" k="57" />
<hkern u1="[" u2="&#x1fd;" k="63" />
<hkern u1="[" u2="&#x1fc;" k="47" />
<hkern u1="[" u2="&#x1fb;" k="63" />
<hkern u1="[" u2="&#x1fa;" k="47" />
<hkern u1="[" u2="&#x17e;" k="41" />
<hkern u1="[" u2="&#x17c;" k="41" />
<hkern u1="[" u2="&#x17a;" k="41" />
<hkern u1="[" u2="&#x177;" k="61" />
<hkern u1="[" u2="&#x175;" k="68" />
<hkern u1="[" u2="&#x173;" k="70" />
<hkern u1="[" u2="&#x171;" k="70" />
<hkern u1="[" u2="&#x16f;" k="70" />
<hkern u1="[" u2="&#x16d;" k="70" />
<hkern u1="[" u2="&#x16b;" k="70" />
<hkern u1="[" u2="&#x169;" k="70" />
<hkern u1="[" u2="&#x167;" k="51" />
<hkern u1="[" u2="&#x165;" k="51" />
<hkern u1="[" u2="&#x161;" k="51" />
<hkern u1="[" u2="&#x160;" k="23" />
<hkern u1="[" u2="&#x15f;" k="51" />
<hkern u1="[" u2="&#x15e;" k="23" />
<hkern u1="[" u2="&#x15d;" k="51" />
<hkern u1="[" u2="&#x15c;" k="23" />
<hkern u1="[" u2="&#x15b;" k="51" />
<hkern u1="[" u2="&#x15a;" k="23" />
<hkern u1="[" u2="&#x159;" k="51" />
<hkern u1="[" u2="&#x157;" k="51" />
<hkern u1="[" u2="&#x155;" k="51" />
<hkern u1="[" u2="&#x153;" k="78" />
<hkern u1="[" u2="&#x152;" k="57" />
<hkern u1="[" u2="&#x151;" k="78" />
<hkern u1="[" u2="&#x150;" k="57" />
<hkern u1="[" u2="&#x14f;" k="78" />
<hkern u1="[" u2="&#x14e;" k="57" />
<hkern u1="[" u2="&#x14d;" k="78" />
<hkern u1="[" u2="&#x14c;" k="57" />
<hkern u1="[" u2="&#x14b;" k="51" />
<hkern u1="[" u2="&#x148;" k="51" />
<hkern u1="[" u2="&#x146;" k="51" />
<hkern u1="[" u2="&#x144;" k="51" />
<hkern u1="[" u2="&#x135;" k="-8" />
<hkern u1="[" u2="&#x12d;" k="-49" />
<hkern u1="[" u2="&#x129;" k="-23" />
<hkern u1="[" u2="&#x122;" k="57" />
<hkern u1="[" u2="&#x120;" k="57" />
<hkern u1="[" u2="&#x11e;" k="57" />
<hkern u1="[" u2="&#x11c;" k="57" />
<hkern u1="[" u2="&#x11b;" k="78" />
<hkern u1="[" u2="&#x119;" k="78" />
<hkern u1="[" u2="&#x117;" k="78" />
<hkern u1="[" u2="&#x115;" k="78" />
<hkern u1="[" u2="&#x113;" k="78" />
<hkern u1="[" u2="&#x111;" k="76" />
<hkern u1="[" u2="&#x10f;" k="76" />
<hkern u1="[" u2="&#x10d;" k="78" />
<hkern u1="[" u2="&#x10c;" k="53" />
<hkern u1="[" u2="&#x10b;" k="78" />
<hkern u1="[" u2="&#x10a;" k="53" />
<hkern u1="[" u2="&#x109;" k="78" />
<hkern u1="[" u2="&#x108;" k="53" />
<hkern u1="[" u2="&#x107;" k="78" />
<hkern u1="[" u2="&#x106;" k="53" />
<hkern u1="[" u2="&#x105;" k="63" />
<hkern u1="[" u2="&#x104;" k="47" />
<hkern u1="[" u2="&#x103;" k="63" />
<hkern u1="[" u2="&#x102;" k="47" />
<hkern u1="[" u2="&#x101;" k="63" />
<hkern u1="[" u2="&#x100;" k="47" />
<hkern u1="[" u2="&#xff;" k="61" />
<hkern u1="[" u2="&#xfd;" k="61" />
<hkern u1="[" u2="&#xfc;" k="70" />
<hkern u1="[" u2="&#xfb;" k="70" />
<hkern u1="[" u2="&#xfa;" k="70" />
<hkern u1="[" u2="&#xf9;" k="70" />
<hkern u1="[" u2="&#xf8;" k="78" />
<hkern u1="[" u2="&#xf6;" k="78" />
<hkern u1="[" u2="&#xf5;" k="78" />
<hkern u1="[" u2="&#xf4;" k="78" />
<hkern u1="[" u2="&#xf3;" k="78" />
<hkern u1="[" u2="&#xf2;" k="78" />
<hkern u1="[" u2="&#xf1;" k="51" />
<hkern u1="[" u2="&#xef;" k="-31" />
<hkern u1="[" u2="&#xec;" k="-80" />
<hkern u1="[" u2="&#xeb;" k="78" />
<hkern u1="[" u2="&#xea;" k="78" />
<hkern u1="[" u2="&#xe9;" k="78" />
<hkern u1="[" u2="&#xe8;" k="78" />
<hkern u1="[" u2="&#xe7;" k="78" />
<hkern u1="[" u2="&#xe6;" k="63" />
<hkern u1="[" u2="&#xe5;" k="63" />
<hkern u1="[" u2="&#xe4;" k="63" />
<hkern u1="[" u2="&#xe3;" k="63" />
<hkern u1="[" u2="&#xe2;" k="63" />
<hkern u1="[" u2="&#xe1;" k="63" />
<hkern u1="[" u2="&#xe0;" k="63" />
<hkern u1="[" u2="&#xd8;" k="57" />
<hkern u1="[" u2="&#xd6;" k="57" />
<hkern u1="[" u2="&#xd5;" k="57" />
<hkern u1="[" u2="&#xd4;" k="57" />
<hkern u1="[" u2="&#xd3;" k="57" />
<hkern u1="[" u2="&#xd2;" k="57" />
<hkern u1="[" u2="&#xc7;" k="53" />
<hkern u1="[" u2="&#xc6;" k="47" />
<hkern u1="[" u2="&#xc5;" k="47" />
<hkern u1="[" u2="&#xc4;" k="47" />
<hkern u1="[" u2="&#xc3;" k="47" />
<hkern u1="[" u2="&#xc2;" k="47" />
<hkern u1="[" u2="&#xc1;" k="47" />
<hkern u1="[" u2="&#xc0;" k="47" />
<hkern u1="[" u2="&#x7b;" k="43" />
<hkern u1="[" u2="z" k="41" />
<hkern u1="[" u2="y" k="61" />
<hkern u1="[" u2="x" k="37" />
<hkern u1="[" u2="w" k="68" />
<hkern u1="[" u2="v" k="66" />
<hkern u1="[" u2="u" k="70" />
<hkern u1="[" u2="t" k="51" />
<hkern u1="[" u2="s" k="51" />
<hkern u1="[" u2="r" k="51" />
<hkern u1="[" u2="q" k="76" />
<hkern u1="[" u2="p" k="51" />
<hkern u1="[" u2="o" k="78" />
<hkern u1="[" u2="n" k="51" />
<hkern u1="[" u2="m" k="51" />
<hkern u1="[" u2="j" k="-8" />
<hkern u1="[" u2="f" k="31" />
<hkern u1="[" u2="e" k="78" />
<hkern u1="[" u2="d" k="76" />
<hkern u1="[" u2="c" k="78" />
<hkern u1="[" u2="a" k="63" />
<hkern u1="[" u2="S" k="23" />
<hkern u1="[" u2="Q" k="57" />
<hkern u1="[" u2="O" k="57" />
<hkern u1="[" u2="G" k="57" />
<hkern u1="[" u2="C" k="53" />
<hkern u1="[" u2="A" k="47" />
<hkern u1="[" u2="&#x28;" k="16" />
<hkern u1="\" u2="&#x201d;" k="139" />
<hkern u1="\" u2="&#x2019;" k="139" />
<hkern u1="\" u2="&#x21b;" k="25" />
<hkern u1="\" u2="&#x21a;" k="117" />
<hkern u1="\" u2="&#x218;" k="8" />
<hkern u1="\" u2="&#x1ff;" k="8" />
<hkern u1="\" u2="&#x1fe;" k="16" />
<hkern u1="\" u2="&#x178;" k="139" />
<hkern u1="\" u2="&#x177;" k="47" />
<hkern u1="\" u2="&#x176;" k="139" />
<hkern u1="\" u2="&#x175;" k="37" />
<hkern u1="\" u2="&#x174;" k="61" />
<hkern u1="\" u2="&#x172;" k="18" />
<hkern u1="\" u2="&#x170;" k="18" />
<hkern u1="\" u2="&#x16e;" k="18" />
<hkern u1="\" u2="&#x16c;" k="18" />
<hkern u1="\" u2="&#x16a;" k="18" />
<hkern u1="\" u2="&#x168;" k="18" />
<hkern u1="\" u2="&#x167;" k="25" />
<hkern u1="\" u2="&#x166;" k="117" />
<hkern u1="\" u2="&#x165;" k="25" />
<hkern u1="\" u2="&#x164;" k="117" />
<hkern u1="\" u2="&#x160;" k="8" />
<hkern u1="\" u2="&#x15e;" k="8" />
<hkern u1="\" u2="&#x15c;" k="8" />
<hkern u1="\" u2="&#x15a;" k="8" />
<hkern u1="\" u2="&#x153;" k="8" />
<hkern u1="\" u2="&#x152;" k="16" />
<hkern u1="\" u2="&#x151;" k="8" />
<hkern u1="\" u2="&#x150;" k="16" />
<hkern u1="\" u2="&#x14f;" k="8" />
<hkern u1="\" u2="&#x14e;" k="16" />
<hkern u1="\" u2="&#x14d;" k="8" />
<hkern u1="\" u2="&#x14c;" k="16" />
<hkern u1="\" u2="&#x122;" k="16" />
<hkern u1="\" u2="&#x120;" k="16" />
<hkern u1="\" u2="&#x11e;" k="16" />
<hkern u1="\" u2="&#x11c;" k="16" />
<hkern u1="\" u2="&#x11b;" k="8" />
<hkern u1="\" u2="&#x119;" k="8" />
<hkern u1="\" u2="&#x117;" k="8" />
<hkern u1="\" u2="&#x115;" k="8" />
<hkern u1="\" u2="&#x113;" k="8" />
<hkern u1="\" u2="&#x10d;" k="8" />
<hkern u1="\" u2="&#x10c;" k="16" />
<hkern u1="\" u2="&#x10b;" k="8" />
<hkern u1="\" u2="&#x10a;" k="16" />
<hkern u1="\" u2="&#x109;" k="8" />
<hkern u1="\" u2="&#x108;" k="16" />
<hkern u1="\" u2="&#x107;" k="8" />
<hkern u1="\" u2="&#x106;" k="16" />
<hkern u1="\" u2="&#xff;" k="47" />
<hkern u1="\" u2="&#xfd;" k="47" />
<hkern u1="\" u2="&#xf8;" k="8" />
<hkern u1="\" u2="&#xf6;" k="8" />
<hkern u1="\" u2="&#xf5;" k="8" />
<hkern u1="\" u2="&#xf4;" k="8" />
<hkern u1="\" u2="&#xf3;" k="8" />
<hkern u1="\" u2="&#xf2;" k="8" />
<hkern u1="\" u2="&#xeb;" k="8" />
<hkern u1="\" u2="&#xea;" k="8" />
<hkern u1="\" u2="&#xe9;" k="8" />
<hkern u1="\" u2="&#xe8;" k="8" />
<hkern u1="\" u2="&#xe7;" k="8" />
<hkern u1="\" u2="&#xdd;" k="139" />
<hkern u1="\" u2="&#xdc;" k="18" />
<hkern u1="\" u2="&#xdb;" k="18" />
<hkern u1="\" u2="&#xda;" k="18" />
<hkern u1="\" u2="&#xd9;" k="18" />
<hkern u1="\" u2="&#xd8;" k="16" />
<hkern u1="\" u2="&#xd6;" k="16" />
<hkern u1="\" u2="&#xd5;" k="16" />
<hkern u1="\" u2="&#xd4;" k="16" />
<hkern u1="\" u2="&#xd3;" k="16" />
<hkern u1="\" u2="&#xd2;" k="16" />
<hkern u1="\" u2="&#xc7;" k="16" />
<hkern u1="\" u2="y" k="47" />
<hkern u1="\" u2="w" k="37" />
<hkern u1="\" u2="v" k="47" />
<hkern u1="\" u2="t" k="25" />
<hkern u1="\" u2="o" k="8" />
<hkern u1="\" u2="f" k="10" />
<hkern u1="\" u2="e" k="8" />
<hkern u1="\" u2="c" k="8" />
<hkern u1="\" u2="Y" k="139" />
<hkern u1="\" u2="W" k="61" />
<hkern u1="\" u2="V" k="90" />
<hkern u1="\" u2="U" k="18" />
<hkern u1="\" u2="T" k="117" />
<hkern u1="\" u2="S" k="8" />
<hkern u1="\" u2="Q" k="16" />
<hkern u1="\" u2="O" k="16" />
<hkern u1="\" u2="G" k="16" />
<hkern u1="\" u2="C" k="16" />
<hkern u1="\" u2="&#x27;" k="141" />
<hkern u1="\" u2="&#x22;" k="141" />
<hkern u1="a" u2="&#x2122;" k="25" />
<hkern u1="a" u2="&#x7d;" k="18" />
<hkern u1="a" u2="v" k="10" />
<hkern u1="a" u2="]" k="23" />
<hkern u1="a" u2="\" k="72" />
<hkern u1="a" u2="V" k="51" />
<hkern u1="a" u2="&#x3f;" k="31" />
<hkern u1="a" u2="&#x2a;" k="6" />
<hkern u1="b" u2="&#x2122;" k="35" />
<hkern u1="b" u2="&#xc6;" k="14" />
<hkern u1="b" u2="&#x7d;" k="68" />
<hkern u1="b" u2="x" k="25" />
<hkern u1="b" u2="v" k="16" />
<hkern u1="b" u2="]" k="76" />
<hkern u1="b" u2="\" k="74" />
<hkern u1="b" u2="X" k="41" />
<hkern u1="b" u2="V" k="63" />
<hkern u1="b" u2="&#x3f;" k="51" />
<hkern u1="b" u2="&#x2a;" k="16" />
<hkern u1="b" u2="&#x29;" k="37" />
<hkern u1="c" u2="&#x7d;" k="33" />
<hkern u1="c" u2="]" k="39" />
<hkern u1="c" u2="\" k="20" />
<hkern u1="c" u2="V" k="25" />
<hkern u1="c" u2="&#x3f;" k="18" />
<hkern u1="d" u2="&#xef;" k="-6" />
<hkern u1="d" u2="&#xec;" k="-14" />
<hkern u1="e" u2="&#x2122;" k="29" />
<hkern u1="e" u2="&#xc6;" k="10" />
<hkern u1="e" u2="&#x7d;" k="55" />
<hkern u1="e" u2="x" k="8" />
<hkern u1="e" u2="v" k="16" />
<hkern u1="e" u2="]" k="51" />
<hkern u1="e" u2="\" k="70" />
<hkern u1="e" u2="V" k="63" />
<hkern u1="e" u2="&#x3f;" k="41" />
<hkern u1="e" u2="&#x29;" k="10" />
<hkern u1="f" u2="&#x203a;" k="45" />
<hkern u1="f" u2="&#x2039;" k="70" />
<hkern u1="f" u2="&#x2026;" k="84" />
<hkern u1="f" u2="&#x201e;" k="84" />
<hkern u1="f" u2="&#x201a;" k="84" />
<hkern u1="f" u2="&#x2014;" k="86" />
<hkern u1="f" u2="&#x2013;" k="86" />
<hkern u1="f" u2="&#x21a;" k="63" />
<hkern u1="f" u2="&#x1ff;" k="18" />
<hkern u1="f" u2="&#x1fc;" k="61" />
<hkern u1="f" u2="&#x1fa;" k="61" />
<hkern u1="f" u2="&#x17d;" k="23" />
<hkern u1="f" u2="&#x17b;" k="23" />
<hkern u1="f" u2="&#x179;" k="23" />
<hkern u1="f" u2="&#x178;" k="20" />
<hkern u1="f" u2="&#x176;" k="20" />
<hkern u1="f" u2="&#x166;" k="63" />
<hkern u1="f" u2="&#x164;" k="63" />
<hkern u1="f" u2="&#x153;" k="18" />
<hkern u1="f" u2="&#x151;" k="18" />
<hkern u1="f" u2="&#x14f;" k="18" />
<hkern u1="f" u2="&#x14d;" k="18" />
<hkern u1="f" u2="&#x135;" k="-31" />
<hkern u1="f" u2="&#x134;" k="51" />
<hkern u1="f" u2="&#x12d;" k="-115" />
<hkern u1="f" u2="&#x12b;" k="-20" />
<hkern u1="f" u2="&#x129;" k="-70" />
<hkern u1="f" u2="&#x123;" k="10" />
<hkern u1="f" u2="&#x121;" k="10" />
<hkern u1="f" u2="&#x11f;" k="10" />
<hkern u1="f" u2="&#x11d;" k="10" />
<hkern u1="f" u2="&#x11b;" k="18" />
<hkern u1="f" u2="&#x119;" k="18" />
<hkern u1="f" u2="&#x117;" k="18" />
<hkern u1="f" u2="&#x115;" k="18" />
<hkern u1="f" u2="&#x113;" k="18" />
<hkern u1="f" u2="&#x111;" k="20" />
<hkern u1="f" u2="&#x10f;" k="20" />
<hkern u1="f" u2="&#x10d;" k="18" />
<hkern u1="f" u2="&#x10b;" k="18" />
<hkern u1="f" u2="&#x109;" k="18" />
<hkern u1="f" u2="&#x107;" k="18" />
<hkern u1="f" u2="&#x104;" k="61" />
<hkern u1="f" u2="&#x102;" k="61" />
<hkern u1="f" u2="&#x100;" k="61" />
<hkern u1="f" u2="&#xf8;" k="18" />
<hkern u1="f" u2="&#xf6;" k="18" />
<hkern u1="f" u2="&#xf5;" k="18" />
<hkern u1="f" u2="&#xf4;" k="18" />
<hkern u1="f" u2="&#xf3;" k="18" />
<hkern u1="f" u2="&#xf2;" k="18" />
<hkern u1="f" u2="&#xef;" k="-49" />
<hkern u1="f" u2="&#xee;" k="-37" />
<hkern u1="f" u2="&#xec;" k="-158" />
<hkern u1="f" u2="&#xeb;" k="18" />
<hkern u1="f" u2="&#xea;" k="18" />
<hkern u1="f" u2="&#xe9;" k="18" />
<hkern u1="f" u2="&#xe8;" k="18" />
<hkern u1="f" u2="&#xe7;" k="18" />
<hkern u1="f" u2="&#xdd;" k="20" />
<hkern u1="f" u2="&#xc6;" k="70" />
<hkern u1="f" u2="&#xc5;" k="61" />
<hkern u1="f" u2="&#xc4;" k="61" />
<hkern u1="f" u2="&#xc3;" k="61" />
<hkern u1="f" u2="&#xc2;" k="61" />
<hkern u1="f" u2="&#xc1;" k="61" />
<hkern u1="f" u2="&#xc0;" k="61" />
<hkern u1="f" u2="&#xbb;" k="45" />
<hkern u1="f" u2="&#xab;" k="70" />
<hkern u1="f" u2="q" k="20" />
<hkern u1="f" u2="o" k="18" />
<hkern u1="f" u2="g" k="10" />
<hkern u1="f" u2="e" k="18" />
<hkern u1="f" u2="d" k="20" />
<hkern u1="f" u2="c" k="18" />
<hkern u1="f" u2="Z" k="23" />
<hkern u1="f" u2="Y" k="20" />
<hkern u1="f" u2="X" k="27" />
<hkern u1="f" u2="T" k="63" />
<hkern u1="f" u2="J" k="51" />
<hkern u1="f" u2="A" k="61" />
<hkern u1="f" u2="&#x2f;" k="55" />
<hkern u1="f" u2="&#x2e;" k="84" />
<hkern u1="f" u2="&#x2d;" k="86" />
<hkern u1="f" u2="&#x2c;" k="84" />
<hkern u1="f" u2="&#x26;" k="18" />
<hkern u1="g" u2="&#x135;" k="-39" />
<hkern u1="g" u2="j" k="-39" />
<hkern u1="g" u2="\" k="12" />
<hkern u1="h" u2="&#x2122;" k="33" />
<hkern u1="h" u2="&#x7d;" k="49" />
<hkern u1="h" u2="v" k="10" />
<hkern u1="h" u2="]" k="53" />
<hkern u1="h" u2="\" k="74" />
<hkern u1="h" u2="V" k="59" />
<hkern u1="h" u2="&#x3f;" k="45" />
<hkern u1="h" u2="&#x2a;" k="6" />
<hkern u1="h" u2="&#x29;" k="8" />
<hkern u1="i" u2="&#xef;" k="-6" />
<hkern u1="i" u2="&#xec;" k="-14" />
<hkern u1="j" u2="&#xef;" k="-6" />
<hkern u1="j" u2="&#xec;" k="-14" />
<hkern u1="k" u2="&#x7d;" k="27" />
<hkern u1="k" u2="]" k="35" />
<hkern u1="k" u2="\" k="14" />
<hkern u1="k" u2="V" k="18" />
<hkern u1="k" u2="&#x3f;" k="14" />
<hkern u1="l" u2="&#xec;" k="-10" />
<hkern u1="m" u2="&#x2122;" k="33" />
<hkern u1="m" u2="&#x7d;" k="49" />
<hkern u1="m" u2="v" k="10" />
<hkern u1="m" u2="]" k="53" />
<hkern u1="m" u2="\" k="74" />
<hkern u1="m" u2="V" k="59" />
<hkern u1="m" u2="&#x3f;" k="45" />
<hkern u1="m" u2="&#x2a;" k="6" />
<hkern u1="m" u2="&#x29;" k="8" />
<hkern u1="n" u2="&#x2122;" k="33" />
<hkern u1="n" u2="&#x7d;" k="49" />
<hkern u1="n" u2="v" k="10" />
<hkern u1="n" u2="]" k="53" />
<hkern u1="n" u2="\" k="74" />
<hkern u1="n" u2="V" k="59" />
<hkern u1="n" u2="&#x3f;" k="45" />
<hkern u1="n" u2="&#x2a;" k="6" />
<hkern u1="n" u2="&#x29;" k="8" />
<hkern u1="o" u2="&#x2122;" k="31" />
<hkern u1="o" u2="&#xc6;" k="14" />
<hkern u1="o" u2="&#x7d;" k="68" />
<hkern u1="o" u2="x" k="25" />
<hkern u1="o" u2="v" k="18" />
<hkern u1="o" u2="]" k="78" />
<hkern u1="o" u2="\" k="76" />
<hkern u1="o" u2="X" k="43" />
<hkern u1="o" u2="V" k="66" />
<hkern u1="o" u2="&#x3f;" k="47" />
<hkern u1="o" u2="&#x2a;" k="6" />
<hkern u1="o" u2="&#x29;" k="37" />
<hkern u1="p" u2="&#x2122;" k="35" />
<hkern u1="p" u2="&#xc6;" k="14" />
<hkern u1="p" u2="&#x7d;" k="68" />
<hkern u1="p" u2="x" k="25" />
<hkern u1="p" u2="v" k="16" />
<hkern u1="p" u2="]" k="76" />
<hkern u1="p" u2="\" k="74" />
<hkern u1="p" u2="X" k="41" />
<hkern u1="p" u2="V" k="63" />
<hkern u1="p" u2="&#x3f;" k="51" />
<hkern u1="p" u2="&#x2a;" k="16" />
<hkern u1="p" u2="&#x29;" k="37" />
<hkern u1="q" u2="&#x2122;" k="23" />
<hkern u1="q" u2="&#x7d;" k="47" />
<hkern u1="q" u2="]" k="51" />
<hkern u1="q" u2="\" k="47" />
<hkern u1="q" u2="V" k="53" />
<hkern u1="q" u2="&#x3f;" k="27" />
<hkern u1="q" u2="&#x29;" k="8" />
<hkern u1="r" u2="&#xc6;" k="86" />
<hkern u1="r" u2="&#x7d;" k="47" />
<hkern u1="r" u2="]" k="59" />
<hkern u1="r" u2="\" k="8" />
<hkern u1="r" u2="X" k="55" />
<hkern u1="r" u2="&#x2f;" k="70" />
<hkern u1="r" u2="&#x29;" k="8" />
<hkern u1="r" u2="&#x26;" k="20" />
<hkern u1="s" u2="&#x2122;" k="25" />
<hkern u1="s" u2="&#xc6;" k="10" />
<hkern u1="s" u2="&#x7d;" k="55" />
<hkern u1="s" u2="x" k="6" />
<hkern u1="s" u2="v" k="14" />
<hkern u1="s" u2="]" k="66" />
<hkern u1="s" u2="\" k="45" />
<hkern u1="s" u2="X" k="6" />
<hkern u1="s" u2="V" k="45" />
<hkern u1="s" u2="&#x3f;" k="27" />
<hkern u1="s" u2="&#x29;" k="10" />
<hkern u1="t" u2="&#x7d;" k="16" />
<hkern u1="t" u2="]" k="23" />
<hkern u1="t" u2="\" k="14" />
<hkern u1="u" u2="&#x2122;" k="23" />
<hkern u1="u" u2="&#x7d;" k="47" />
<hkern u1="u" u2="]" k="51" />
<hkern u1="u" u2="\" k="47" />
<hkern u1="u" u2="V" k="53" />
<hkern u1="u" u2="&#x3f;" k="27" />
<hkern u1="u" u2="&#x29;" k="8" />
<hkern u1="v" u2="&#x2039;" k="25" />
<hkern u1="v" u2="&#x2026;" k="72" />
<hkern u1="v" u2="&#x201e;" k="72" />
<hkern u1="v" u2="&#x201a;" k="72" />
<hkern u1="v" u2="&#x2014;" k="27" />
<hkern u1="v" u2="&#x2013;" k="27" />
<hkern u1="v" u2="&#x21a;" k="137" />
<hkern u1="v" u2="&#x219;" k="12" />
<hkern u1="v" u2="&#x1ff;" k="18" />
<hkern u1="v" u2="&#x1fd;" k="16" />
<hkern u1="v" u2="&#x1fc;" k="37" />
<hkern u1="v" u2="&#x1fb;" k="16" />
<hkern u1="v" u2="&#x1fa;" k="37" />
<hkern u1="v" u2="&#x17d;" k="31" />
<hkern u1="v" u2="&#x17b;" k="31" />
<hkern u1="v" u2="&#x179;" k="31" />
<hkern u1="v" u2="&#x178;" k="84" />
<hkern u1="v" u2="&#x176;" k="84" />
<hkern u1="v" u2="&#x166;" k="137" />
<hkern u1="v" u2="&#x164;" k="137" />
<hkern u1="v" u2="&#x161;" k="12" />
<hkern u1="v" u2="&#x15f;" k="12" />
<hkern u1="v" u2="&#x15d;" k="12" />
<hkern u1="v" u2="&#x15b;" k="12" />
<hkern u1="v" u2="&#x153;" k="18" />
<hkern u1="v" u2="&#x151;" k="18" />
<hkern u1="v" u2="&#x14f;" k="18" />
<hkern u1="v" u2="&#x14d;" k="18" />
<hkern u1="v" u2="&#x134;" k="53" />
<hkern u1="v" u2="&#x123;" k="18" />
<hkern u1="v" u2="&#x121;" k="18" />
<hkern u1="v" u2="&#x11f;" k="18" />
<hkern u1="v" u2="&#x11d;" k="18" />
<hkern u1="v" u2="&#x11b;" k="18" />
<hkern u1="v" u2="&#x119;" k="18" />
<hkern u1="v" u2="&#x117;" k="18" />
<hkern u1="v" u2="&#x115;" k="18" />
<hkern u1="v" u2="&#x113;" k="18" />
<hkern u1="v" u2="&#x111;" k="16" />
<hkern u1="v" u2="&#x10f;" k="16" />
<hkern u1="v" u2="&#x10d;" k="18" />
<hkern u1="v" u2="&#x10b;" k="18" />
<hkern u1="v" u2="&#x109;" k="18" />
<hkern u1="v" u2="&#x107;" k="18" />
<hkern u1="v" u2="&#x105;" k="16" />
<hkern u1="v" u2="&#x104;" k="37" />
<hkern u1="v" u2="&#x103;" k="16" />
<hkern u1="v" u2="&#x102;" k="37" />
<hkern u1="v" u2="&#x101;" k="16" />
<hkern u1="v" u2="&#x100;" k="37" />
<hkern u1="v" u2="&#xf8;" k="18" />
<hkern u1="v" u2="&#xf6;" k="18" />
<hkern u1="v" u2="&#xf5;" k="18" />
<hkern u1="v" u2="&#xf4;" k="18" />
<hkern u1="v" u2="&#xf3;" k="18" />
<hkern u1="v" u2="&#xf2;" k="18" />
<hkern u1="v" u2="&#xeb;" k="18" />
<hkern u1="v" u2="&#xea;" k="18" />
<hkern u1="v" u2="&#xe9;" k="18" />
<hkern u1="v" u2="&#xe8;" k="18" />
<hkern u1="v" u2="&#xe7;" k="18" />
<hkern u1="v" u2="&#xe6;" k="16" />
<hkern u1="v" u2="&#xe5;" k="16" />
<hkern u1="v" u2="&#xe4;" k="16" />
<hkern u1="v" u2="&#xe3;" k="16" />
<hkern u1="v" u2="&#xe2;" k="16" />
<hkern u1="v" u2="&#xe1;" k="16" />
<hkern u1="v" u2="&#xe0;" k="16" />
<hkern u1="v" u2="&#xdd;" k="84" />
<hkern u1="v" u2="&#xc6;" k="43" />
<hkern u1="v" u2="&#xc5;" k="37" />
<hkern u1="v" u2="&#xc4;" k="37" />
<hkern u1="v" u2="&#xc3;" k="37" />
<hkern u1="v" u2="&#xc2;" k="37" />
<hkern u1="v" u2="&#xc1;" k="37" />
<hkern u1="v" u2="&#xc0;" k="37" />
<hkern u1="v" u2="&#xab;" k="25" />
<hkern u1="v" u2="&#x7d;" k="55" />
<hkern u1="v" u2="s" k="12" />
<hkern u1="v" u2="q" k="16" />
<hkern u1="v" u2="o" k="18" />
<hkern u1="v" u2="g" k="18" />
<hkern u1="v" u2="e" k="18" />
<hkern u1="v" u2="d" k="16" />
<hkern u1="v" u2="c" k="18" />
<hkern u1="v" u2="a" k="16" />
<hkern u1="v" u2="]" k="66" />
<hkern u1="v" u2="\" k="14" />
<hkern u1="v" u2="Z" k="31" />
<hkern u1="v" u2="Y" k="84" />
<hkern u1="v" u2="X" k="53" />
<hkern u1="v" u2="V" k="16" />
<hkern u1="v" u2="T" k="137" />
<hkern u1="v" u2="J" k="53" />
<hkern u1="v" u2="A" k="37" />
<hkern u1="v" u2="&#x3f;" k="18" />
<hkern u1="v" u2="&#x2f;" k="41" />
<hkern u1="v" u2="&#x2e;" k="72" />
<hkern u1="v" u2="&#x2d;" k="27" />
<hkern u1="v" u2="&#x2c;" k="72" />
<hkern u1="v" u2="&#x29;" k="8" />
<hkern u1="w" u2="&#xc6;" k="37" />
<hkern u1="w" u2="&#x7d;" k="57" />
<hkern u1="w" u2="]" k="68" />
<hkern u1="w" u2="\" k="14" />
<hkern u1="w" u2="X" k="51" />
<hkern u1="w" u2="V" k="20" />
<hkern u1="w" u2="&#x3f;" k="20" />
<hkern u1="w" u2="&#x2f;" k="35" />
<hkern u1="w" u2="&#x29;" k="12" />
<hkern u1="x" u2="&#x2039;" k="53" />
<hkern u1="x" u2="&#x2014;" k="66" />
<hkern u1="x" u2="&#x2013;" k="66" />
<hkern u1="x" u2="&#x21a;" k="147" />
<hkern u1="x" u2="&#x1ff;" k="25" />
<hkern u1="x" u2="&#x1fd;" k="8" />
<hkern u1="x" u2="&#x1fb;" k="8" />
<hkern u1="x" u2="&#x178;" k="80" />
<hkern u1="x" u2="&#x176;" k="80" />
<hkern u1="x" u2="&#x166;" k="147" />
<hkern u1="x" u2="&#x164;" k="147" />
<hkern u1="x" u2="&#x153;" k="25" />
<hkern u1="x" u2="&#x151;" k="25" />
<hkern u1="x" u2="&#x14f;" k="25" />
<hkern u1="x" u2="&#x14d;" k="25" />
<hkern u1="x" u2="&#x134;" k="6" />
<hkern u1="x" u2="&#x123;" k="20" />
<hkern u1="x" u2="&#x121;" k="20" />
<hkern u1="x" u2="&#x11f;" k="20" />
<hkern u1="x" u2="&#x11d;" k="20" />
<hkern u1="x" u2="&#x11b;" k="25" />
<hkern u1="x" u2="&#x119;" k="25" />
<hkern u1="x" u2="&#x117;" k="25" />
<hkern u1="x" u2="&#x115;" k="25" />
<hkern u1="x" u2="&#x113;" k="25" />
<hkern u1="x" u2="&#x111;" k="27" />
<hkern u1="x" u2="&#x10f;" k="27" />
<hkern u1="x" u2="&#x10d;" k="25" />
<hkern u1="x" u2="&#x10b;" k="25" />
<hkern u1="x" u2="&#x109;" k="25" />
<hkern u1="x" u2="&#x107;" k="25" />
<hkern u1="x" u2="&#x105;" k="8" />
<hkern u1="x" u2="&#x103;" k="8" />
<hkern u1="x" u2="&#x101;" k="8" />
<hkern u1="x" u2="&#xf8;" k="25" />
<hkern u1="x" u2="&#xf6;" k="25" />
<hkern u1="x" u2="&#xf5;" k="25" />
<hkern u1="x" u2="&#xf4;" k="25" />
<hkern u1="x" u2="&#xf3;" k="25" />
<hkern u1="x" u2="&#xf2;" k="25" />
<hkern u1="x" u2="&#xeb;" k="25" />
<hkern u1="x" u2="&#xea;" k="25" />
<hkern u1="x" u2="&#xe9;" k="25" />
<hkern u1="x" u2="&#xe8;" k="25" />
<hkern u1="x" u2="&#xe7;" k="25" />
<hkern u1="x" u2="&#xe6;" k="8" />
<hkern u1="x" u2="&#xe5;" k="8" />
<hkern u1="x" u2="&#xe4;" k="8" />
<hkern u1="x" u2="&#xe3;" k="8" />
<hkern u1="x" u2="&#xe2;" k="8" />
<hkern u1="x" u2="&#xe1;" k="8" />
<hkern u1="x" u2="&#xe0;" k="8" />
<hkern u1="x" u2="&#xdd;" k="80" />
<hkern u1="x" u2="&#xab;" k="53" />
<hkern u1="x" u2="&#x7d;" k="33" />
<hkern u1="x" u2="q" k="27" />
<hkern u1="x" u2="o" k="25" />
<hkern u1="x" u2="g" k="20" />
<hkern u1="x" u2="e" k="25" />
<hkern u1="x" u2="d" k="27" />
<hkern u1="x" u2="c" k="25" />
<hkern u1="x" u2="a" k="8" />
<hkern u1="x" u2="]" k="39" />
<hkern u1="x" u2="\" k="12" />
<hkern u1="x" u2="Y" k="80" />
<hkern u1="x" u2="V" k="14" />
<hkern u1="x" u2="T" k="147" />
<hkern u1="x" u2="J" k="6" />
<hkern u1="x" u2="&#x2d;" k="66" />
<hkern u1="y" u2="&#xc6;" k="43" />
<hkern u1="y" u2="&#x7d;" k="49" />
<hkern u1="y" u2="]" k="59" />
<hkern u1="y" u2="\" k="14" />
<hkern u1="y" u2="X" k="53" />
<hkern u1="y" u2="V" k="16" />
<hkern u1="y" u2="&#x3f;" k="18" />
<hkern u1="y" u2="&#x2f;" k="43" />
<hkern u1="z" u2="&#x2122;" k="10" />
<hkern u1="z" u2="&#x7d;" k="35" />
<hkern u1="z" u2="]" k="41" />
<hkern u1="z" u2="\" k="20" />
<hkern u1="z" u2="V" k="27" />
<hkern u1="z" u2="&#x3f;" k="16" />
<hkern u1="&#x7b;" u2="&#x21b;" k="37" />
<hkern u1="&#x7b;" u2="&#x219;" k="45" />
<hkern u1="&#x7b;" u2="&#x218;" k="23" />
<hkern u1="&#x7b;" u2="&#x1ff;" k="68" />
<hkern u1="&#x7b;" u2="&#x1fe;" k="51" />
<hkern u1="&#x7b;" u2="&#x1fd;" k="55" />
<hkern u1="&#x7b;" u2="&#x1fc;" k="41" />
<hkern u1="&#x7b;" u2="&#x1fb;" k="55" />
<hkern u1="&#x7b;" u2="&#x1fa;" k="41" />
<hkern u1="&#x7b;" u2="&#x17e;" k="35" />
<hkern u1="&#x7b;" u2="&#x17c;" k="35" />
<hkern u1="&#x7b;" u2="&#x17a;" k="35" />
<hkern u1="&#x7b;" u2="&#x177;" k="51" />
<hkern u1="&#x7b;" u2="&#x175;" k="57" />
<hkern u1="&#x7b;" u2="&#x173;" k="61" />
<hkern u1="&#x7b;" u2="&#x171;" k="61" />
<hkern u1="&#x7b;" u2="&#x16f;" k="61" />
<hkern u1="&#x7b;" u2="&#x16d;" k="61" />
<hkern u1="&#x7b;" u2="&#x16b;" k="61" />
<hkern u1="&#x7b;" u2="&#x169;" k="61" />
<hkern u1="&#x7b;" u2="&#x167;" k="37" />
<hkern u1="&#x7b;" u2="&#x165;" k="37" />
<hkern u1="&#x7b;" u2="&#x161;" k="45" />
<hkern u1="&#x7b;" u2="&#x160;" k="23" />
<hkern u1="&#x7b;" u2="&#x15f;" k="45" />
<hkern u1="&#x7b;" u2="&#x15e;" k="23" />
<hkern u1="&#x7b;" u2="&#x15d;" k="45" />
<hkern u1="&#x7b;" u2="&#x15c;" k="23" />
<hkern u1="&#x7b;" u2="&#x15b;" k="45" />
<hkern u1="&#x7b;" u2="&#x15a;" k="23" />
<hkern u1="&#x7b;" u2="&#x159;" k="47" />
<hkern u1="&#x7b;" u2="&#x157;" k="47" />
<hkern u1="&#x7b;" u2="&#x155;" k="47" />
<hkern u1="&#x7b;" u2="&#x153;" k="68" />
<hkern u1="&#x7b;" u2="&#x152;" k="51" />
<hkern u1="&#x7b;" u2="&#x151;" k="68" />
<hkern u1="&#x7b;" u2="&#x150;" k="51" />
<hkern u1="&#x7b;" u2="&#x14f;" k="68" />
<hkern u1="&#x7b;" u2="&#x14e;" k="51" />
<hkern u1="&#x7b;" u2="&#x14d;" k="68" />
<hkern u1="&#x7b;" u2="&#x14c;" k="51" />
<hkern u1="&#x7b;" u2="&#x14b;" k="47" />
<hkern u1="&#x7b;" u2="&#x148;" k="47" />
<hkern u1="&#x7b;" u2="&#x146;" k="47" />
<hkern u1="&#x7b;" u2="&#x144;" k="47" />
<hkern u1="&#x7b;" u2="&#x135;" k="-16" />
<hkern u1="&#x7b;" u2="&#x12d;" k="-51" />
<hkern u1="&#x7b;" u2="&#x129;" k="-23" />
<hkern u1="&#x7b;" u2="&#x122;" k="51" />
<hkern u1="&#x7b;" u2="&#x120;" k="51" />
<hkern u1="&#x7b;" u2="&#x11e;" k="51" />
<hkern u1="&#x7b;" u2="&#x11c;" k="51" />
<hkern u1="&#x7b;" u2="&#x11b;" k="68" />
<hkern u1="&#x7b;" u2="&#x119;" k="68" />
<hkern u1="&#x7b;" u2="&#x117;" k="68" />
<hkern u1="&#x7b;" u2="&#x115;" k="68" />
<hkern u1="&#x7b;" u2="&#x113;" k="68" />
<hkern u1="&#x7b;" u2="&#x111;" k="68" />
<hkern u1="&#x7b;" u2="&#x10f;" k="68" />
<hkern u1="&#x7b;" u2="&#x10d;" k="68" />
<hkern u1="&#x7b;" u2="&#x10c;" k="49" />
<hkern u1="&#x7b;" u2="&#x10b;" k="68" />
<hkern u1="&#x7b;" u2="&#x10a;" k="49" />
<hkern u1="&#x7b;" u2="&#x109;" k="68" />
<hkern u1="&#x7b;" u2="&#x108;" k="49" />
<hkern u1="&#x7b;" u2="&#x107;" k="68" />
<hkern u1="&#x7b;" u2="&#x106;" k="49" />
<hkern u1="&#x7b;" u2="&#x105;" k="55" />
<hkern u1="&#x7b;" u2="&#x104;" k="41" />
<hkern u1="&#x7b;" u2="&#x103;" k="55" />
<hkern u1="&#x7b;" u2="&#x102;" k="41" />
<hkern u1="&#x7b;" u2="&#x101;" k="55" />
<hkern u1="&#x7b;" u2="&#x100;" k="41" />
<hkern u1="&#x7b;" u2="&#xff;" k="51" />
<hkern u1="&#x7b;" u2="&#xfd;" k="51" />
<hkern u1="&#x7b;" u2="&#xfc;" k="61" />
<hkern u1="&#x7b;" u2="&#xfb;" k="61" />
<hkern u1="&#x7b;" u2="&#xfa;" k="61" />
<hkern u1="&#x7b;" u2="&#xf9;" k="61" />
<hkern u1="&#x7b;" u2="&#xf8;" k="68" />
<hkern u1="&#x7b;" u2="&#xf6;" k="68" />
<hkern u1="&#x7b;" u2="&#xf5;" k="68" />
<hkern u1="&#x7b;" u2="&#xf4;" k="68" />
<hkern u1="&#x7b;" u2="&#xf3;" k="68" />
<hkern u1="&#x7b;" u2="&#xf2;" k="68" />
<hkern u1="&#x7b;" u2="&#xf1;" k="47" />
<hkern u1="&#x7b;" u2="&#xef;" k="-31" />
<hkern u1="&#x7b;" u2="&#xec;" k="-78" />
<hkern u1="&#x7b;" u2="&#xeb;" k="68" />
<hkern u1="&#x7b;" u2="&#xea;" k="68" />
<hkern u1="&#x7b;" u2="&#xe9;" k="68" />
<hkern u1="&#x7b;" u2="&#xe8;" k="68" />
<hkern u1="&#x7b;" u2="&#xe7;" k="68" />
<hkern u1="&#x7b;" u2="&#xe6;" k="55" />
<hkern u1="&#x7b;" u2="&#xe5;" k="55" />
<hkern u1="&#x7b;" u2="&#xe4;" k="55" />
<hkern u1="&#x7b;" u2="&#xe3;" k="55" />
<hkern u1="&#x7b;" u2="&#xe2;" k="55" />
<hkern u1="&#x7b;" u2="&#xe1;" k="55" />
<hkern u1="&#x7b;" u2="&#xe0;" k="55" />
<hkern u1="&#x7b;" u2="&#xd8;" k="51" />
<hkern u1="&#x7b;" u2="&#xd6;" k="51" />
<hkern u1="&#x7b;" u2="&#xd5;" k="51" />
<hkern u1="&#x7b;" u2="&#xd4;" k="51" />
<hkern u1="&#x7b;" u2="&#xd3;" k="51" />
<hkern u1="&#x7b;" u2="&#xd2;" k="51" />
<hkern u1="&#x7b;" u2="&#xc7;" k="49" />
<hkern u1="&#x7b;" u2="&#xc6;" k="43" />
<hkern u1="&#x7b;" u2="&#xc5;" k="41" />
<hkern u1="&#x7b;" u2="&#xc4;" k="41" />
<hkern u1="&#x7b;" u2="&#xc3;" k="41" />
<hkern u1="&#x7b;" u2="&#xc2;" k="41" />
<hkern u1="&#x7b;" u2="&#xc1;" k="41" />
<hkern u1="&#x7b;" u2="&#xc0;" k="41" />
<hkern u1="&#x7b;" u2="&#x7b;" k="39" />
<hkern u1="&#x7b;" u2="z" k="35" />
<hkern u1="&#x7b;" u2="y" k="51" />
<hkern u1="&#x7b;" u2="x" k="33" />
<hkern u1="&#x7b;" u2="w" k="57" />
<hkern u1="&#x7b;" u2="v" k="53" />
<hkern u1="&#x7b;" u2="u" k="61" />
<hkern u1="&#x7b;" u2="t" k="37" />
<hkern u1="&#x7b;" u2="s" k="45" />
<hkern u1="&#x7b;" u2="r" k="47" />
<hkern u1="&#x7b;" u2="q" k="68" />
<hkern u1="&#x7b;" u2="p" k="47" />
<hkern u1="&#x7b;" u2="o" k="68" />
<hkern u1="&#x7b;" u2="n" k="47" />
<hkern u1="&#x7b;" u2="m" k="47" />
<hkern u1="&#x7b;" u2="j" k="-16" />
<hkern u1="&#x7b;" u2="f" k="23" />
<hkern u1="&#x7b;" u2="e" k="68" />
<hkern u1="&#x7b;" u2="d" k="68" />
<hkern u1="&#x7b;" u2="c" k="68" />
<hkern u1="&#x7b;" u2="a" k="55" />
<hkern u1="&#x7b;" u2="S" k="23" />
<hkern u1="&#x7b;" u2="Q" k="51" />
<hkern u1="&#x7b;" u2="O" k="51" />
<hkern u1="&#x7b;" u2="G" k="51" />
<hkern u1="&#x7b;" u2="C" k="49" />
<hkern u1="&#x7b;" u2="A" k="41" />
<hkern u1="&#x7b;" u2="&#x28;" k="16" />
<hkern u1="&#x7c;" u2="&#xec;" k="-12" />
<hkern u1="&#x7d;" u2="&#x7d;" k="39" />
<hkern u1="&#x7d;" u2="]" k="43" />
<hkern u1="&#x7d;" u2="&#x29;" k="23" />
<hkern u1="&#xa1;" u2="&#x21a;" k="98" />
<hkern u1="&#xa1;" u2="&#x178;" k="66" />
<hkern u1="&#xa1;" u2="&#x176;" k="66" />
<hkern u1="&#xa1;" u2="&#x166;" k="98" />
<hkern u1="&#xa1;" u2="&#x164;" k="98" />
<hkern u1="&#xa1;" u2="&#xdd;" k="66" />
<hkern u1="&#xa1;" u2="Y" k="66" />
<hkern u1="&#xa1;" u2="V" k="16" />
<hkern u1="&#xa1;" u2="T" k="98" />
<hkern u1="&#xab;" u2="V" k="39" />
<hkern u1="&#xae;" u2="&#x21a;" k="18" />
<hkern u1="&#xae;" u2="&#x1fc;" k="55" />
<hkern u1="&#xae;" u2="&#x1fa;" k="55" />
<hkern u1="&#xae;" u2="&#x17d;" k="27" />
<hkern u1="&#xae;" u2="&#x17b;" k="27" />
<hkern u1="&#xae;" u2="&#x179;" k="27" />
<hkern u1="&#xae;" u2="&#x178;" k="47" />
<hkern u1="&#xae;" u2="&#x176;" k="47" />
<hkern u1="&#xae;" u2="&#x166;" k="18" />
<hkern u1="&#xae;" u2="&#x164;" k="18" />
<hkern u1="&#xae;" u2="&#x134;" k="49" />
<hkern u1="&#xae;" u2="&#x104;" k="55" />
<hkern u1="&#xae;" u2="&#x102;" k="55" />
<hkern u1="&#xae;" u2="&#x100;" k="55" />
<hkern u1="&#xae;" u2="&#xdd;" k="47" />
<hkern u1="&#xae;" u2="&#xc6;" k="68" />
<hkern u1="&#xae;" u2="&#xc5;" k="55" />
<hkern u1="&#xae;" u2="&#xc4;" k="55" />
<hkern u1="&#xae;" u2="&#xc3;" k="55" />
<hkern u1="&#xae;" u2="&#xc2;" k="55" />
<hkern u1="&#xae;" u2="&#xc1;" k="55" />
<hkern u1="&#xae;" u2="&#xc0;" k="55" />
<hkern u1="&#xae;" u2="Z" k="27" />
<hkern u1="&#xae;" u2="Y" k="47" />
<hkern u1="&#xae;" u2="X" k="20" />
<hkern u1="&#xae;" u2="V" k="14" />
<hkern u1="&#xae;" u2="T" k="18" />
<hkern u1="&#xae;" u2="J" k="49" />
<hkern u1="&#xae;" u2="A" k="55" />
<hkern u1="&#xbb;" u2="&#x141;" k="-6" />
<hkern u1="&#xbb;" u2="&#xc6;" k="14" />
<hkern u1="&#xbb;" u2="x" k="53" />
<hkern u1="&#xbb;" u2="v" k="16" />
<hkern u1="&#xbb;" u2="f" k="8" />
<hkern u1="&#xbb;" u2="X" k="49" />
<hkern u1="&#xbb;" u2="V" k="57" />
<hkern u1="&#xbf;" u2="&#x21b;" k="57" />
<hkern u1="&#xbf;" u2="&#x21a;" k="162" />
<hkern u1="&#xbf;" u2="&#x219;" k="66" />
<hkern u1="&#xbf;" u2="&#x218;" k="57" />
<hkern u1="&#xbf;" u2="&#x1ff;" k="68" />
<hkern u1="&#xbf;" u2="&#x1fe;" k="57" />
<hkern u1="&#xbf;" u2="&#x1fd;" k="70" />
<hkern u1="&#xbf;" u2="&#x1fc;" k="78" />
<hkern u1="&#xbf;" u2="&#x1fb;" k="70" />
<hkern u1="&#xbf;" u2="&#x1fa;" k="78" />
<hkern u1="&#xbf;" u2="&#x17e;" k="59" />
<hkern u1="&#xbf;" u2="&#x17d;" k="76" />
<hkern u1="&#xbf;" u2="&#x17c;" k="59" />
<hkern u1="&#xbf;" u2="&#x17b;" k="76" />
<hkern u1="&#xbf;" u2="&#x17a;" k="59" />
<hkern u1="&#xbf;" u2="&#x179;" k="76" />
<hkern u1="&#xbf;" u2="&#x178;" k="135" />
<hkern u1="&#xbf;" u2="&#x177;" k="61" />
<hkern u1="&#xbf;" u2="&#x176;" k="135" />
<hkern u1="&#xbf;" u2="&#x175;" k="66" />
<hkern u1="&#xbf;" u2="&#x174;" k="76" />
<hkern u1="&#xbf;" u2="&#x173;" k="63" />
<hkern u1="&#xbf;" u2="&#x172;" k="59" />
<hkern u1="&#xbf;" u2="&#x171;" k="63" />
<hkern u1="&#xbf;" u2="&#x170;" k="59" />
<hkern u1="&#xbf;" u2="&#x16f;" k="63" />
<hkern u1="&#xbf;" u2="&#x16e;" k="59" />
<hkern u1="&#xbf;" u2="&#x16d;" k="63" />
<hkern u1="&#xbf;" u2="&#x16c;" k="59" />
<hkern u1="&#xbf;" u2="&#x16b;" k="63" />
<hkern u1="&#xbf;" u2="&#x16a;" k="59" />
<hkern u1="&#xbf;" u2="&#x169;" k="63" />
<hkern u1="&#xbf;" u2="&#x168;" k="59" />
<hkern u1="&#xbf;" u2="&#x167;" k="57" />
<hkern u1="&#xbf;" u2="&#x166;" k="162" />
<hkern u1="&#xbf;" u2="&#x165;" k="57" />
<hkern u1="&#xbf;" u2="&#x164;" k="162" />
<hkern u1="&#xbf;" u2="&#x161;" k="66" />
<hkern u1="&#xbf;" u2="&#x160;" k="57" />
<hkern u1="&#xbf;" u2="&#x15f;" k="66" />
<hkern u1="&#xbf;" u2="&#x15e;" k="57" />
<hkern u1="&#xbf;" u2="&#x15d;" k="66" />
<hkern u1="&#xbf;" u2="&#x15c;" k="57" />
<hkern u1="&#xbf;" u2="&#x15b;" k="66" />
<hkern u1="&#xbf;" u2="&#x15a;" k="57" />
<hkern u1="&#xbf;" u2="&#x159;" k="63" />
<hkern u1="&#xbf;" u2="&#x158;" k="57" />
<hkern u1="&#xbf;" u2="&#x157;" k="63" />
<hkern u1="&#xbf;" u2="&#x156;" k="57" />
<hkern u1="&#xbf;" u2="&#x155;" k="63" />
<hkern u1="&#xbf;" u2="&#x154;" k="57" />
<hkern u1="&#xbf;" u2="&#x153;" k="68" />
<hkern u1="&#xbf;" u2="&#x152;" k="57" />
<hkern u1="&#xbf;" u2="&#x151;" k="68" />
<hkern u1="&#xbf;" u2="&#x150;" k="57" />
<hkern u1="&#xbf;" u2="&#x14f;" k="68" />
<hkern u1="&#xbf;" u2="&#x14e;" k="57" />
<hkern u1="&#xbf;" u2="&#x14d;" k="68" />
<hkern u1="&#xbf;" u2="&#x14c;" k="57" />
<hkern u1="&#xbf;" u2="&#x14b;" k="63" />
<hkern u1="&#xbf;" u2="&#x14a;" k="57" />
<hkern u1="&#xbf;" u2="&#x148;" k="63" />
<hkern u1="&#xbf;" u2="&#x147;" k="57" />
<hkern u1="&#xbf;" u2="&#x146;" k="63" />
<hkern u1="&#xbf;" u2="&#x145;" k="57" />
<hkern u1="&#xbf;" u2="&#x144;" k="63" />
<hkern u1="&#xbf;" u2="&#x143;" k="57" />
<hkern u1="&#xbf;" u2="&#x142;" k="63" />
<hkern u1="&#xbf;" u2="&#x141;" k="57" />
<hkern u1="&#xbf;" u2="&#x13e;" k="63" />
<hkern u1="&#xbf;" u2="&#x13d;" k="57" />
<hkern u1="&#xbf;" u2="&#x13c;" k="63" />
<hkern u1="&#xbf;" u2="&#x13b;" k="57" />
<hkern u1="&#xbf;" u2="&#x13a;" k="63" />
<hkern u1="&#xbf;" u2="&#x139;" k="57" />
<hkern u1="&#xbf;" u2="&#x137;" k="63" />
<hkern u1="&#xbf;" u2="&#x136;" k="57" />
<hkern u1="&#xbf;" u2="&#x135;" k="63" />
<hkern u1="&#xbf;" u2="&#x134;" k="27" />
<hkern u1="&#xbf;" u2="&#x131;" k="63" />
<hkern u1="&#xbf;" u2="&#x130;" k="57" />
<hkern u1="&#xbf;" u2="&#x12f;" k="31" />
<hkern u1="&#xbf;" u2="&#x12e;" k="51" />
<hkern u1="&#xbf;" u2="&#x12d;" k="63" />
<hkern u1="&#xbf;" u2="&#x12c;" k="57" />
<hkern u1="&#xbf;" u2="&#x12b;" k="63" />
<hkern u1="&#xbf;" u2="&#x12a;" k="57" />
<hkern u1="&#xbf;" u2="&#x129;" k="63" />
<hkern u1="&#xbf;" u2="&#x128;" k="57" />
<hkern u1="&#xbf;" u2="&#x127;" k="63" />
<hkern u1="&#xbf;" u2="&#x126;" k="57" />
<hkern u1="&#xbf;" u2="&#x125;" k="63" />
<hkern u1="&#xbf;" u2="&#x124;" k="57" />
<hkern u1="&#xbf;" u2="&#x122;" k="57" />
<hkern u1="&#xbf;" u2="&#x120;" k="57" />
<hkern u1="&#xbf;" u2="&#x11e;" k="57" />
<hkern u1="&#xbf;" u2="&#x11c;" k="57" />
<hkern u1="&#xbf;" u2="&#x11b;" k="68" />
<hkern u1="&#xbf;" u2="&#x11a;" k="57" />
<hkern u1="&#xbf;" u2="&#x119;" k="68" />
<hkern u1="&#xbf;" u2="&#x118;" k="57" />
<hkern u1="&#xbf;" u2="&#x117;" k="68" />
<hkern u1="&#xbf;" u2="&#x116;" k="57" />
<hkern u1="&#xbf;" u2="&#x115;" k="68" />
<hkern u1="&#xbf;" u2="&#x114;" k="57" />
<hkern u1="&#xbf;" u2="&#x113;" k="68" />
<hkern u1="&#xbf;" u2="&#x112;" k="57" />
<hkern u1="&#xbf;" u2="&#x111;" k="68" />
<hkern u1="&#xbf;" u2="&#x110;" k="57" />
<hkern u1="&#xbf;" u2="&#x10f;" k="68" />
<hkern u1="&#xbf;" u2="&#x10e;" k="57" />
<hkern u1="&#xbf;" u2="&#x10d;" k="68" />
<hkern u1="&#xbf;" u2="&#x10c;" k="57" />
<hkern u1="&#xbf;" u2="&#x10b;" k="68" />
<hkern u1="&#xbf;" u2="&#x10a;" k="57" />
<hkern u1="&#xbf;" u2="&#x109;" k="68" />
<hkern u1="&#xbf;" u2="&#x108;" k="57" />
<hkern u1="&#xbf;" u2="&#x107;" k="68" />
<hkern u1="&#xbf;" u2="&#x106;" k="57" />
<hkern u1="&#xbf;" u2="&#x105;" k="70" />
<hkern u1="&#xbf;" u2="&#x104;" k="78" />
<hkern u1="&#xbf;" u2="&#x103;" k="70" />
<hkern u1="&#xbf;" u2="&#x102;" k="78" />
<hkern u1="&#xbf;" u2="&#x101;" k="70" />
<hkern u1="&#xbf;" u2="&#x100;" k="78" />
<hkern u1="&#xbf;" u2="&#xff;" k="61" />
<hkern u1="&#xbf;" u2="&#xfd;" k="61" />
<hkern u1="&#xbf;" u2="&#xfc;" k="63" />
<hkern u1="&#xbf;" u2="&#xfb;" k="63" />
<hkern u1="&#xbf;" u2="&#xfa;" k="63" />
<hkern u1="&#xbf;" u2="&#xf9;" k="63" />
<hkern u1="&#xbf;" u2="&#xf8;" k="68" />
<hkern u1="&#xbf;" u2="&#xf6;" k="68" />
<hkern u1="&#xbf;" u2="&#xf5;" k="68" />
<hkern u1="&#xbf;" u2="&#xf4;" k="68" />
<hkern u1="&#xbf;" u2="&#xf3;" k="68" />
<hkern u1="&#xbf;" u2="&#xf2;" k="68" />
<hkern u1="&#xbf;" u2="&#xf1;" k="63" />
<hkern u1="&#xbf;" u2="&#xef;" k="63" />
<hkern u1="&#xbf;" u2="&#xee;" k="63" />
<hkern u1="&#xbf;" u2="&#xed;" k="63" />
<hkern u1="&#xbf;" u2="&#xec;" k="63" />
<hkern u1="&#xbf;" u2="&#xeb;" k="68" />
<hkern u1="&#xbf;" u2="&#xea;" k="68" />
<hkern u1="&#xbf;" u2="&#xe9;" k="68" />
<hkern u1="&#xbf;" u2="&#xe8;" k="68" />
<hkern u1="&#xbf;" u2="&#xe7;" k="68" />
<hkern u1="&#xbf;" u2="&#xe6;" k="70" />
<hkern u1="&#xbf;" u2="&#xe5;" k="70" />
<hkern u1="&#xbf;" u2="&#xe4;" k="70" />
<hkern u1="&#xbf;" u2="&#xe3;" k="70" />
<hkern u1="&#xbf;" u2="&#xe2;" k="70" />
<hkern u1="&#xbf;" u2="&#xe1;" k="70" />
<hkern u1="&#xbf;" u2="&#xe0;" k="70" />
<hkern u1="&#xbf;" u2="&#xdf;" k="63" />
<hkern u1="&#xbf;" u2="&#xdd;" k="135" />
<hkern u1="&#xbf;" u2="&#xdc;" k="59" />
<hkern u1="&#xbf;" u2="&#xdb;" k="59" />
<hkern u1="&#xbf;" u2="&#xda;" k="59" />
<hkern u1="&#xbf;" u2="&#xd9;" k="59" />
<hkern u1="&#xbf;" u2="&#xd8;" k="57" />
<hkern u1="&#xbf;" u2="&#xd6;" k="57" />
<hkern u1="&#xbf;" u2="&#xd5;" k="57" />
<hkern u1="&#xbf;" u2="&#xd4;" k="57" />
<hkern u1="&#xbf;" u2="&#xd3;" k="57" />
<hkern u1="&#xbf;" u2="&#xd2;" k="57" />
<hkern u1="&#xbf;" u2="&#xd1;" k="57" />
<hkern u1="&#xbf;" u2="&#xcf;" k="57" />
<hkern u1="&#xbf;" u2="&#xce;" k="57" />
<hkern u1="&#xbf;" u2="&#xcd;" k="57" />
<hkern u1="&#xbf;" u2="&#xcc;" k="57" />
<hkern u1="&#xbf;" u2="&#xcb;" k="57" />
<hkern u1="&#xbf;" u2="&#xca;" k="57" />
<hkern u1="&#xbf;" u2="&#xc9;" k="57" />
<hkern u1="&#xbf;" u2="&#xc8;" k="57" />
<hkern u1="&#xbf;" u2="&#xc7;" k="57" />
<hkern u1="&#xbf;" u2="&#xc6;" k="82" />
<hkern u1="&#xbf;" u2="&#xc5;" k="78" />
<hkern u1="&#xbf;" u2="&#xc4;" k="78" />
<hkern u1="&#xbf;" u2="&#xc3;" k="78" />
<hkern u1="&#xbf;" u2="&#xc2;" k="78" />
<hkern u1="&#xbf;" u2="&#xc1;" k="78" />
<hkern u1="&#xbf;" u2="&#xc0;" k="78" />
<hkern u1="&#xbf;" u2="z" k="59" />
<hkern u1="&#xbf;" u2="y" k="61" />
<hkern u1="&#xbf;" u2="x" k="55" />
<hkern u1="&#xbf;" u2="w" k="66" />
<hkern u1="&#xbf;" u2="v" k="66" />
<hkern u1="&#xbf;" u2="u" k="63" />
<hkern u1="&#xbf;" u2="t" k="57" />
<hkern u1="&#xbf;" u2="s" k="66" />
<hkern u1="&#xbf;" u2="r" k="63" />
<hkern u1="&#xbf;" u2="q" k="68" />
<hkern u1="&#xbf;" u2="p" k="63" />
<hkern u1="&#xbf;" u2="o" k="68" />
<hkern u1="&#xbf;" u2="n" k="63" />
<hkern u1="&#xbf;" u2="m" k="63" />
<hkern u1="&#xbf;" u2="l" k="63" />
<hkern u1="&#xbf;" u2="k" k="63" />
<hkern u1="&#xbf;" u2="j" k="63" />
<hkern u1="&#xbf;" u2="i" k="63" />
<hkern u1="&#xbf;" u2="h" k="63" />
<hkern u1="&#xbf;" u2="f" k="55" />
<hkern u1="&#xbf;" u2="e" k="68" />
<hkern u1="&#xbf;" u2="d" k="68" />
<hkern u1="&#xbf;" u2="c" k="68" />
<hkern u1="&#xbf;" u2="b" k="63" />
<hkern u1="&#xbf;" u2="a" k="70" />
<hkern u1="&#xbf;" u2="Z" k="76" />
<hkern u1="&#xbf;" u2="Y" k="135" />
<hkern u1="&#xbf;" u2="X" k="76" />
<hkern u1="&#xbf;" u2="W" k="76" />
<hkern u1="&#xbf;" u2="V" k="90" />
<hkern u1="&#xbf;" u2="U" k="59" />
<hkern u1="&#xbf;" u2="T" k="162" />
<hkern u1="&#xbf;" u2="S" k="57" />
<hkern u1="&#xbf;" u2="R" k="57" />
<hkern u1="&#xbf;" u2="Q" k="57" />
<hkern u1="&#xbf;" u2="P" k="57" />
<hkern u1="&#xbf;" u2="O" k="57" />
<hkern u1="&#xbf;" u2="N" k="57" />
<hkern u1="&#xbf;" u2="M" k="57" />
<hkern u1="&#xbf;" u2="L" k="57" />
<hkern u1="&#xbf;" u2="K" k="57" />
<hkern u1="&#xbf;" u2="J" k="27" />
<hkern u1="&#xbf;" u2="I" k="57" />
<hkern u1="&#xbf;" u2="H" k="57" />
<hkern u1="&#xbf;" u2="G" k="57" />
<hkern u1="&#xbf;" u2="F" k="57" />
<hkern u1="&#xbf;" u2="E" k="57" />
<hkern u1="&#xbf;" u2="D" k="57" />
<hkern u1="&#xbf;" u2="C" k="57" />
<hkern u1="&#xbf;" u2="B" k="57" />
<hkern u1="&#xbf;" u2="A" k="78" />
<hkern u1="&#xc0;" u2="&#x2122;" k="84" />
<hkern u1="&#xc0;" u2="&#xae;" k="53" />
<hkern u1="&#xc0;" u2="&#x7d;" k="41" />
<hkern u1="&#xc0;" u2="v" k="37" />
<hkern u1="&#xc0;" u2="f" k="14" />
<hkern u1="&#xc0;" u2="]" k="47" />
<hkern u1="&#xc0;" u2="\" k="96" />
<hkern u1="&#xc0;" u2="V" k="57" />
<hkern u1="&#xc0;" u2="&#x3f;" k="51" />
<hkern u1="&#xc0;" u2="&#x2a;" k="74" />
<hkern u1="&#xc1;" u2="&#x2122;" k="84" />
<hkern u1="&#xc1;" u2="&#xae;" k="53" />
<hkern u1="&#xc1;" u2="&#x7d;" k="41" />
<hkern u1="&#xc1;" u2="v" k="37" />
<hkern u1="&#xc1;" u2="f" k="14" />
<hkern u1="&#xc1;" u2="]" k="47" />
<hkern u1="&#xc1;" u2="\" k="96" />
<hkern u1="&#xc1;" u2="V" k="57" />
<hkern u1="&#xc1;" u2="&#x3f;" k="51" />
<hkern u1="&#xc1;" u2="&#x2a;" k="74" />
<hkern u1="&#xc2;" u2="&#x2122;" k="84" />
<hkern u1="&#xc2;" u2="&#xae;" k="53" />
<hkern u1="&#xc2;" u2="&#x7d;" k="41" />
<hkern u1="&#xc2;" u2="v" k="37" />
<hkern u1="&#xc2;" u2="f" k="14" />
<hkern u1="&#xc2;" u2="]" k="47" />
<hkern u1="&#xc2;" u2="\" k="96" />
<hkern u1="&#xc2;" u2="V" k="57" />
<hkern u1="&#xc2;" u2="&#x3f;" k="51" />
<hkern u1="&#xc2;" u2="&#x2a;" k="74" />
<hkern u1="&#xc3;" u2="&#x2122;" k="84" />
<hkern u1="&#xc3;" u2="&#xae;" k="53" />
<hkern u1="&#xc3;" u2="&#x7d;" k="41" />
<hkern u1="&#xc3;" u2="v" k="37" />
<hkern u1="&#xc3;" u2="f" k="14" />
<hkern u1="&#xc3;" u2="]" k="47" />
<hkern u1="&#xc3;" u2="\" k="96" />
<hkern u1="&#xc3;" u2="V" k="57" />
<hkern u1="&#xc3;" u2="&#x3f;" k="51" />
<hkern u1="&#xc3;" u2="&#x2a;" k="74" />
<hkern u1="&#xc4;" u2="&#x2122;" k="84" />
<hkern u1="&#xc4;" u2="&#xae;" k="53" />
<hkern u1="&#xc4;" u2="&#x7d;" k="41" />
<hkern u1="&#xc4;" u2="v" k="37" />
<hkern u1="&#xc4;" u2="f" k="14" />
<hkern u1="&#xc4;" u2="]" k="47" />
<hkern u1="&#xc4;" u2="\" k="96" />
<hkern u1="&#xc4;" u2="V" k="57" />
<hkern u1="&#xc4;" u2="&#x3f;" k="51" />
<hkern u1="&#xc4;" u2="&#x2a;" k="74" />
<hkern u1="&#xc5;" u2="&#x2122;" k="84" />
<hkern u1="&#xc5;" u2="&#xae;" k="53" />
<hkern u1="&#xc5;" u2="&#x7d;" k="41" />
<hkern u1="&#xc5;" u2="v" k="37" />
<hkern u1="&#xc5;" u2="f" k="14" />
<hkern u1="&#xc5;" u2="]" k="47" />
<hkern u1="&#xc5;" u2="\" k="96" />
<hkern u1="&#xc5;" u2="V" k="57" />
<hkern u1="&#xc5;" u2="&#x3f;" k="51" />
<hkern u1="&#xc5;" u2="&#x2a;" k="74" />
<hkern u1="&#xc6;" u2="&#x135;" k="-16" />
<hkern u1="&#xc6;" u2="&#x12d;" k="-20" />
<hkern u1="&#xc6;" u2="&#x12b;" k="-14" />
<hkern u1="&#xc6;" u2="&#x129;" k="-51" />
<hkern u1="&#xc6;" u2="&#xef;" k="-27" />
<hkern u1="&#xc6;" u2="&#xee;" k="-25" />
<hkern u1="&#xc6;" u2="&#xec;" k="-74" />
<hkern u1="&#xc6;" u2="v" k="23" />
<hkern u1="&#xc6;" u2="f" k="6" />
<hkern u1="&#xc7;" u2="&#x135;" k="-16" />
<hkern u1="&#xc7;" u2="&#x12d;" k="-31" />
<hkern u1="&#xc7;" u2="&#x12b;" k="-18" />
<hkern u1="&#xc7;" u2="&#x129;" k="-59" />
<hkern u1="&#xc7;" u2="&#xef;" k="-29" />
<hkern u1="&#xc7;" u2="&#xee;" k="-25" />
<hkern u1="&#xc7;" u2="&#xec;" k="-68" />
<hkern u1="&#xc7;" u2="&#xae;" k="27" />
<hkern u1="&#xc7;" u2="v" k="33" />
<hkern u1="&#xc7;" u2="f" k="12" />
<hkern u1="&#xc8;" u2="&#x135;" k="-16" />
<hkern u1="&#xc8;" u2="&#x12d;" k="-20" />
<hkern u1="&#xc8;" u2="&#x12b;" k="-14" />
<hkern u1="&#xc8;" u2="&#x129;" k="-51" />
<hkern u1="&#xc8;" u2="&#xef;" k="-27" />
<hkern u1="&#xc8;" u2="&#xee;" k="-25" />
<hkern u1="&#xc8;" u2="&#xec;" k="-74" />
<hkern u1="&#xc8;" u2="v" k="23" />
<hkern u1="&#xc8;" u2="f" k="6" />
<hkern u1="&#xc9;" u2="&#x135;" k="-16" />
<hkern u1="&#xc9;" u2="&#x12d;" k="-20" />
<hkern u1="&#xc9;" u2="&#x12b;" k="-14" />
<hkern u1="&#xc9;" u2="&#x129;" k="-51" />
<hkern u1="&#xc9;" u2="&#xef;" k="-27" />
<hkern u1="&#xc9;" u2="&#xee;" k="-25" />
<hkern u1="&#xc9;" u2="&#xec;" k="-74" />
<hkern u1="&#xc9;" u2="v" k="23" />
<hkern u1="&#xc9;" u2="f" k="6" />
<hkern u1="&#xca;" u2="&#x135;" k="-16" />
<hkern u1="&#xca;" u2="&#x12d;" k="-20" />
<hkern u1="&#xca;" u2="&#x12b;" k="-14" />
<hkern u1="&#xca;" u2="&#x129;" k="-51" />
<hkern u1="&#xca;" u2="&#xef;" k="-27" />
<hkern u1="&#xca;" u2="&#xee;" k="-25" />
<hkern u1="&#xca;" u2="&#xec;" k="-74" />
<hkern u1="&#xca;" u2="v" k="23" />
<hkern u1="&#xca;" u2="f" k="6" />
<hkern u1="&#xcb;" u2="&#x135;" k="-16" />
<hkern u1="&#xcb;" u2="&#x12d;" k="-20" />
<hkern u1="&#xcb;" u2="&#x12b;" k="-14" />
<hkern u1="&#xcb;" u2="&#x129;" k="-51" />
<hkern u1="&#xcb;" u2="&#xef;" k="-27" />
<hkern u1="&#xcb;" u2="&#xee;" k="-25" />
<hkern u1="&#xcb;" u2="&#xec;" k="-74" />
<hkern u1="&#xcb;" u2="v" k="23" />
<hkern u1="&#xcb;" u2="f" k="6" />
<hkern u1="&#xcc;" u2="&#xec;" k="-6" />
<hkern u1="&#xcd;" u2="&#xec;" k="-6" />
<hkern u1="&#xce;" u2="&#xec;" k="-6" />
<hkern u1="&#xcf;" u2="&#xec;" k="-6" />
<hkern u1="&#xd1;" u2="&#xec;" k="-6" />
<hkern u1="&#xd2;" u2="&#xc6;" k="31" />
<hkern u1="&#xd2;" u2="&#x7d;" k="51" />
<hkern u1="&#xd2;" u2="]" k="57" />
<hkern u1="&#xd2;" u2="\" k="16" />
<hkern u1="&#xd2;" u2="X" k="39" />
<hkern u1="&#xd2;" u2="V" k="25" />
<hkern u1="&#xd2;" u2="&#x3f;" k="14" />
<hkern u1="&#xd2;" u2="&#x2f;" k="12" />
<hkern u1="&#xd2;" u2="&#x29;" k="10" />
<hkern u1="&#xd3;" u2="&#xc6;" k="31" />
<hkern u1="&#xd3;" u2="&#x7d;" k="51" />
<hkern u1="&#xd3;" u2="]" k="57" />
<hkern u1="&#xd3;" u2="\" k="16" />
<hkern u1="&#xd3;" u2="X" k="39" />
<hkern u1="&#xd3;" u2="V" k="25" />
<hkern u1="&#xd3;" u2="&#x3f;" k="14" />
<hkern u1="&#xd3;" u2="&#x2f;" k="12" />
<hkern u1="&#xd3;" u2="&#x29;" k="10" />
<hkern u1="&#xd4;" u2="&#xc6;" k="31" />
<hkern u1="&#xd4;" u2="&#x7d;" k="51" />
<hkern u1="&#xd4;" u2="]" k="57" />
<hkern u1="&#xd4;" u2="\" k="16" />
<hkern u1="&#xd4;" u2="X" k="39" />
<hkern u1="&#xd4;" u2="V" k="25" />
<hkern u1="&#xd4;" u2="&#x3f;" k="14" />
<hkern u1="&#xd4;" u2="&#x2f;" k="12" />
<hkern u1="&#xd4;" u2="&#x29;" k="10" />
<hkern u1="&#xd5;" u2="&#xc6;" k="31" />
<hkern u1="&#xd5;" u2="&#x7d;" k="51" />
<hkern u1="&#xd5;" u2="]" k="57" />
<hkern u1="&#xd5;" u2="\" k="16" />
<hkern u1="&#xd5;" u2="X" k="39" />
<hkern u1="&#xd5;" u2="V" k="25" />
<hkern u1="&#xd5;" u2="&#x3f;" k="14" />
<hkern u1="&#xd5;" u2="&#x2f;" k="12" />
<hkern u1="&#xd5;" u2="&#x29;" k="10" />
<hkern u1="&#xd6;" u2="&#xc6;" k="31" />
<hkern u1="&#xd6;" u2="&#x7d;" k="51" />
<hkern u1="&#xd6;" u2="]" k="57" />
<hkern u1="&#xd6;" u2="\" k="16" />
<hkern u1="&#xd6;" u2="X" k="39" />
<hkern u1="&#xd6;" u2="V" k="25" />
<hkern u1="&#xd6;" u2="&#x3f;" k="14" />
<hkern u1="&#xd6;" u2="&#x2f;" k="12" />
<hkern u1="&#xd6;" u2="&#x29;" k="10" />
<hkern u1="&#xd8;" u2="&#xc6;" k="31" />
<hkern u1="&#xd8;" u2="&#x7d;" k="51" />
<hkern u1="&#xd8;" u2="]" k="57" />
<hkern u1="&#xd8;" u2="\" k="16" />
<hkern u1="&#xd8;" u2="X" k="39" />
<hkern u1="&#xd8;" u2="V" k="25" />
<hkern u1="&#xd8;" u2="&#x3f;" k="14" />
<hkern u1="&#xd8;" u2="&#x2f;" k="12" />
<hkern u1="&#xd8;" u2="&#x29;" k="10" />
<hkern u1="&#xd9;" u2="&#xec;" k="-12" />
<hkern u1="&#xd9;" u2="&#xc6;" k="18" />
<hkern u1="&#xd9;" u2="&#x2f;" k="12" />
<hkern u1="&#xda;" u2="&#xec;" k="-12" />
<hkern u1="&#xda;" u2="&#xc6;" k="18" />
<hkern u1="&#xda;" u2="&#x2f;" k="12" />
<hkern u1="&#xdb;" u2="&#xec;" k="-12" />
<hkern u1="&#xdb;" u2="&#xc6;" k="18" />
<hkern u1="&#xdb;" u2="&#x2f;" k="12" />
<hkern u1="&#xdc;" u2="&#xec;" k="-12" />
<hkern u1="&#xdc;" u2="&#xc6;" k="18" />
<hkern u1="&#xdc;" u2="&#x2f;" k="12" />
<hkern u1="&#xdd;" u2="&#x159;" k="80" />
<hkern u1="&#xdd;" u2="&#x155;" k="94" />
<hkern u1="&#xdd;" u2="&#x151;" k="127" />
<hkern u1="&#xdd;" u2="&#x142;" k="12" />
<hkern u1="&#xdd;" u2="&#x135;" k="-12" />
<hkern u1="&#xdd;" u2="&#x131;" k="125" />
<hkern u1="&#xdd;" u2="&#x12d;" k="-94" />
<hkern u1="&#xdd;" u2="&#x12b;" k="-63" />
<hkern u1="&#xdd;" u2="&#x129;" k="-92" />
<hkern u1="&#xdd;" u2="&#x103;" k="127" />
<hkern u1="&#xdd;" u2="&#xff;" k="70" />
<hkern u1="&#xdd;" u2="&#xef;" k="-90" />
<hkern u1="&#xdd;" u2="&#xee;" k="-16" />
<hkern u1="&#xdd;" u2="&#xec;" k="-131" />
<hkern u1="&#xdd;" u2="&#xeb;" k="137" />
<hkern u1="&#xdd;" u2="&#xe4;" k="117" />
<hkern u1="&#xdd;" u2="&#xe3;" k="109" />
<hkern u1="&#xdd;" u2="&#xdf;" k="12" />
<hkern u1="&#xdd;" u2="&#xc6;" k="106" />
<hkern u1="&#xdd;" u2="&#xae;" k="45" />
<hkern u1="&#xdd;" u2="x" k="82" />
<hkern u1="&#xdd;" u2="v" k="84" />
<hkern u1="&#xdd;" u2="f" k="37" />
<hkern u1="&#xdd;" u2="&#x40;" k="74" />
<hkern u1="&#xdd;" u2="&#x2f;" k="131" />
<hkern u1="&#xdd;" u2="&#x26;" k="76" />
<hkern u1="&#xdf;" u2="&#x2122;" k="23" />
<hkern u1="&#xdf;" u2="&#x201d;" k="29" />
<hkern u1="&#xdf;" u2="&#x201c;" k="31" />
<hkern u1="&#xdf;" u2="&#x2019;" k="29" />
<hkern u1="&#xdf;" u2="&#x2018;" k="31" />
<hkern u1="&#xdf;" u2="&#x21b;" k="18" />
<hkern u1="&#xdf;" u2="&#x21a;" k="63" />
<hkern u1="&#xdf;" u2="&#x218;" k="10" />
<hkern u1="&#xdf;" u2="&#x178;" k="84" />
<hkern u1="&#xdf;" u2="&#x177;" k="35" />
<hkern u1="&#xdf;" u2="&#x176;" k="84" />
<hkern u1="&#xdf;" u2="&#x175;" k="23" />
<hkern u1="&#xdf;" u2="&#x174;" k="39" />
<hkern u1="&#xdf;" u2="&#x167;" k="18" />
<hkern u1="&#xdf;" u2="&#x166;" k="63" />
<hkern u1="&#xdf;" u2="&#x165;" k="18" />
<hkern u1="&#xdf;" u2="&#x164;" k="63" />
<hkern u1="&#xdf;" u2="&#x160;" k="10" />
<hkern u1="&#xdf;" u2="&#x15e;" k="10" />
<hkern u1="&#xdf;" u2="&#x15c;" k="10" />
<hkern u1="&#xdf;" u2="&#x15a;" k="10" />
<hkern u1="&#xdf;" u2="&#x134;" k="41" />
<hkern u1="&#xdf;" u2="&#x123;" k="10" />
<hkern u1="&#xdf;" u2="&#x121;" k="10" />
<hkern u1="&#xdf;" u2="&#x11f;" k="10" />
<hkern u1="&#xdf;" u2="&#x11d;" k="10" />
<hkern u1="&#xdf;" u2="&#xff;" k="35" />
<hkern u1="&#xdf;" u2="&#xfd;" k="35" />
<hkern u1="&#xdf;" u2="&#xdd;" k="84" />
<hkern u1="&#xdf;" u2="&#xc6;" k="6" />
<hkern u1="&#xdf;" u2="&#xae;" k="27" />
<hkern u1="&#xdf;" u2="&#x7d;" k="35" />
<hkern u1="&#xdf;" u2="y" k="35" />
<hkern u1="&#xdf;" u2="x" k="18" />
<hkern u1="&#xdf;" u2="w" k="23" />
<hkern u1="&#xdf;" u2="v" k="33" />
<hkern u1="&#xdf;" u2="t" k="18" />
<hkern u1="&#xdf;" u2="g" k="10" />
<hkern u1="&#xdf;" u2="f" k="10" />
<hkern u1="&#xdf;" u2="]" k="43" />
<hkern u1="&#xdf;" u2="\" k="39" />
<hkern u1="&#xdf;" u2="Y" k="84" />
<hkern u1="&#xdf;" u2="X" k="12" />
<hkern u1="&#xdf;" u2="W" k="39" />
<hkern u1="&#xdf;" u2="V" k="57" />
<hkern u1="&#xdf;" u2="T" k="63" />
<hkern u1="&#xdf;" u2="S" k="10" />
<hkern u1="&#xdf;" u2="J" k="41" />
<hkern u1="&#xdf;" u2="&#x3f;" k="18" />
<hkern u1="&#xdf;" u2="&#x2a;" k="31" />
<hkern u1="&#xdf;" u2="&#x27;" k="27" />
<hkern u1="&#xdf;" u2="&#x22;" k="27" />
<hkern u1="&#xe0;" u2="&#x2122;" k="25" />
<hkern u1="&#xe0;" u2="&#x7d;" k="18" />
<hkern u1="&#xe0;" u2="v" k="10" />
<hkern u1="&#xe0;" u2="]" k="23" />
<hkern u1="&#xe0;" u2="\" k="72" />
<hkern u1="&#xe0;" u2="V" k="51" />
<hkern u1="&#xe0;" u2="&#x3f;" k="31" />
<hkern u1="&#xe0;" u2="&#x2a;" k="6" />
<hkern u1="&#xe1;" u2="&#x2122;" k="25" />
<hkern u1="&#xe1;" u2="&#x7d;" k="18" />
<hkern u1="&#xe1;" u2="v" k="10" />
<hkern u1="&#xe1;" u2="]" k="23" />
<hkern u1="&#xe1;" u2="\" k="72" />
<hkern u1="&#xe1;" u2="V" k="51" />
<hkern u1="&#xe1;" u2="&#x3f;" k="31" />
<hkern u1="&#xe1;" u2="&#x2a;" k="6" />
<hkern u1="&#xe2;" u2="&#x2122;" k="25" />
<hkern u1="&#xe2;" u2="&#x7d;" k="18" />
<hkern u1="&#xe2;" u2="v" k="10" />
<hkern u1="&#xe2;" u2="]" k="23" />
<hkern u1="&#xe2;" u2="\" k="72" />
<hkern u1="&#xe2;" u2="V" k="51" />
<hkern u1="&#xe2;" u2="&#x3f;" k="31" />
<hkern u1="&#xe2;" u2="&#x2a;" k="6" />
<hkern u1="&#xe3;" u2="&#x2122;" k="25" />
<hkern u1="&#xe3;" u2="&#x7d;" k="18" />
<hkern u1="&#xe3;" u2="v" k="10" />
<hkern u1="&#xe3;" u2="]" k="23" />
<hkern u1="&#xe3;" u2="\" k="72" />
<hkern u1="&#xe3;" u2="V" k="51" />
<hkern u1="&#xe3;" u2="&#x3f;" k="31" />
<hkern u1="&#xe3;" u2="&#x2a;" k="6" />
<hkern u1="&#xe4;" u2="&#x2122;" k="25" />
<hkern u1="&#xe4;" u2="&#x7d;" k="18" />
<hkern u1="&#xe4;" u2="v" k="10" />
<hkern u1="&#xe4;" u2="]" k="23" />
<hkern u1="&#xe4;" u2="\" k="72" />
<hkern u1="&#xe4;" u2="V" k="51" />
<hkern u1="&#xe4;" u2="&#x3f;" k="31" />
<hkern u1="&#xe4;" u2="&#x2a;" k="6" />
<hkern u1="&#xe5;" u2="&#x2122;" k="25" />
<hkern u1="&#xe5;" u2="&#x7d;" k="18" />
<hkern u1="&#xe5;" u2="v" k="10" />
<hkern u1="&#xe5;" u2="]" k="23" />
<hkern u1="&#xe5;" u2="\" k="72" />
<hkern u1="&#xe5;" u2="V" k="51" />
<hkern u1="&#xe5;" u2="&#x3f;" k="31" />
<hkern u1="&#xe5;" u2="&#x2a;" k="6" />
<hkern u1="&#xe6;" u2="&#x2122;" k="29" />
<hkern u1="&#xe6;" u2="&#xc6;" k="10" />
<hkern u1="&#xe6;" u2="&#x7d;" k="55" />
<hkern u1="&#xe6;" u2="x" k="8" />
<hkern u1="&#xe6;" u2="v" k="16" />
<hkern u1="&#xe6;" u2="]" k="51" />
<hkern u1="&#xe6;" u2="\" k="70" />
<hkern u1="&#xe6;" u2="V" k="63" />
<hkern u1="&#xe6;" u2="&#x3f;" k="41" />
<hkern u1="&#xe6;" u2="&#x29;" k="10" />
<hkern u1="&#xe7;" u2="&#x7d;" k="33" />
<hkern u1="&#xe7;" u2="]" k="39" />
<hkern u1="&#xe7;" u2="\" k="20" />
<hkern u1="&#xe7;" u2="V" k="25" />
<hkern u1="&#xe7;" u2="&#x3f;" k="18" />
<hkern u1="&#xe8;" u2="&#x2122;" k="29" />
<hkern u1="&#xe8;" u2="&#xc6;" k="10" />
<hkern u1="&#xe8;" u2="&#x7d;" k="55" />
<hkern u1="&#xe8;" u2="x" k="8" />
<hkern u1="&#xe8;" u2="v" k="16" />
<hkern u1="&#xe8;" u2="]" k="51" />
<hkern u1="&#xe8;" u2="\" k="70" />
<hkern u1="&#xe8;" u2="V" k="63" />
<hkern u1="&#xe8;" u2="&#x3f;" k="41" />
<hkern u1="&#xe8;" u2="&#x29;" k="10" />
<hkern u1="&#xe9;" u2="&#x2122;" k="29" />
<hkern u1="&#xe9;" u2="&#xc6;" k="10" />
<hkern u1="&#xe9;" u2="&#x7d;" k="55" />
<hkern u1="&#xe9;" u2="x" k="8" />
<hkern u1="&#xe9;" u2="v" k="16" />
<hkern u1="&#xe9;" u2="]" k="51" />
<hkern u1="&#xe9;" u2="\" k="70" />
<hkern u1="&#xe9;" u2="V" k="63" />
<hkern u1="&#xe9;" u2="&#x3f;" k="41" />
<hkern u1="&#xe9;" u2="&#x29;" k="10" />
<hkern u1="&#xea;" u2="&#x2122;" k="29" />
<hkern u1="&#xea;" u2="&#xc6;" k="10" />
<hkern u1="&#xea;" u2="&#x7d;" k="55" />
<hkern u1="&#xea;" u2="x" k="8" />
<hkern u1="&#xea;" u2="v" k="16" />
<hkern u1="&#xea;" u2="]" k="51" />
<hkern u1="&#xea;" u2="\" k="70" />
<hkern u1="&#xea;" u2="V" k="63" />
<hkern u1="&#xea;" u2="&#x3f;" k="41" />
<hkern u1="&#xea;" u2="&#x29;" k="10" />
<hkern u1="&#xeb;" u2="&#x2122;" k="29" />
<hkern u1="&#xeb;" u2="&#xc6;" k="10" />
<hkern u1="&#xeb;" u2="&#x7d;" k="55" />
<hkern u1="&#xeb;" u2="x" k="8" />
<hkern u1="&#xeb;" u2="v" k="16" />
<hkern u1="&#xeb;" u2="]" k="51" />
<hkern u1="&#xeb;" u2="\" k="70" />
<hkern u1="&#xeb;" u2="V" k="63" />
<hkern u1="&#xeb;" u2="&#x3f;" k="41" />
<hkern u1="&#xeb;" u2="&#x29;" k="10" />
<hkern u1="&#xec;" u2="&#xef;" k="-6" />
<hkern u1="&#xec;" u2="&#xec;" k="-14" />
<hkern u1="&#xed;" u2="&#x2122;" k="-29" />
<hkern u1="&#xed;" u2="&#x201d;" k="-6" />
<hkern u1="&#xed;" u2="&#x2019;" k="-6" />
<hkern u1="&#xed;" u2="&#x165;" k="-10" />
<hkern u1="&#xed;" u2="&#x159;" k="-70" />
<hkern u1="&#xed;" u2="&#x142;" k="-14" />
<hkern u1="&#xed;" u2="&#x13e;" k="-14" />
<hkern u1="&#xed;" u2="&#x13c;" k="-14" />
<hkern u1="&#xed;" u2="&#x13a;" k="-14" />
<hkern u1="&#xed;" u2="&#x137;" k="-20" />
<hkern u1="&#xed;" u2="&#x135;" k="-20" />
<hkern u1="&#xed;" u2="&#x131;" k="-20" />
<hkern u1="&#xed;" u2="&#x12f;" k="-20" />
<hkern u1="&#xed;" u2="&#x12d;" k="-20" />
<hkern u1="&#xed;" u2="&#x12b;" k="-20" />
<hkern u1="&#xed;" u2="&#x129;" k="-20" />
<hkern u1="&#xed;" u2="&#x127;" k="-20" />
<hkern u1="&#xed;" u2="&#x125;" k="-20" />
<hkern u1="&#xed;" u2="&#xef;" k="-6" />
<hkern u1="&#xed;" u2="&#xee;" k="-20" />
<hkern u1="&#xed;" u2="&#xed;" k="-20" />
<hkern u1="&#xed;" u2="&#xec;" k="-14" />
<hkern u1="&#xed;" u2="&#xdf;" k="-20" />
<hkern u1="&#xed;" u2="&#x7d;" k="-82" />
<hkern u1="&#xed;" u2="&#x7c;" k="-16" />
<hkern u1="&#xed;" u2="l" k="-14" />
<hkern u1="&#xed;" u2="k" k="-20" />
<hkern u1="&#xed;" u2="j" k="-20" />
<hkern u1="&#xed;" u2="i" k="-20" />
<hkern u1="&#xed;" u2="h" k="-20" />
<hkern u1="&#xed;" u2="b" k="-20" />
<hkern u1="&#xed;" u2="]" k="-82" />
<hkern u1="&#xed;" u2="\" k="-90" />
<hkern u1="&#xed;" u2="&#x3f;" k="-76" />
<hkern u1="&#xed;" u2="&#x2a;" k="-41" />
<hkern u1="&#xed;" u2="&#x29;" k="-51" />
<hkern u1="&#xed;" u2="&#x27;" k="-35" />
<hkern u1="&#xed;" u2="&#x22;" k="-35" />
<hkern u1="&#xed;" u2="&#x21;" k="-16" />
<hkern u1="&#xee;" u2="&#x2122;" k="-6" />
<hkern u1="&#xee;" u2="&#xef;" k="-6" />
<hkern u1="&#xee;" u2="&#xec;" k="-14" />
<hkern u1="&#xee;" u2="&#x3f;" k="-20" />
<hkern u1="&#xee;" u2="&#x2a;" k="-39" />
<hkern u1="&#xef;" u2="&#x2122;" k="-8" />
<hkern u1="&#xef;" u2="&#xef;" k="-6" />
<hkern u1="&#xef;" u2="&#xec;" k="-14" />
<hkern u1="&#xef;" u2="&#x7d;" k="-23" />
<hkern u1="&#xef;" u2="]" k="-23" />
<hkern u1="&#xef;" u2="\" k="-37" />
<hkern u1="&#xef;" u2="&#x3f;" k="-29" />
<hkern u1="&#xef;" u2="&#x2a;" k="-37" />
<hkern u1="&#xef;" u2="&#x29;" k="-23" />
<hkern u1="&#xf1;" u2="&#x2122;" k="33" />
<hkern u1="&#xf1;" u2="&#x7d;" k="49" />
<hkern u1="&#xf1;" u2="v" k="10" />
<hkern u1="&#xf1;" u2="]" k="53" />
<hkern u1="&#xf1;" u2="\" k="74" />
<hkern u1="&#xf1;" u2="V" k="59" />
<hkern u1="&#xf1;" u2="&#x3f;" k="45" />
<hkern u1="&#xf1;" u2="&#x2a;" k="6" />
<hkern u1="&#xf1;" u2="&#x29;" k="8" />
<hkern u1="&#xf2;" u2="&#x2122;" k="31" />
<hkern u1="&#xf2;" u2="&#xc6;" k="14" />
<hkern u1="&#xf2;" u2="&#x7d;" k="68" />
<hkern u1="&#xf2;" u2="x" k="25" />
<hkern u1="&#xf2;" u2="v" k="18" />
<hkern u1="&#xf2;" u2="]" k="78" />
<hkern u1="&#xf2;" u2="\" k="76" />
<hkern u1="&#xf2;" u2="X" k="43" />
<hkern u1="&#xf2;" u2="V" k="66" />
<hkern u1="&#xf2;" u2="&#x3f;" k="47" />
<hkern u1="&#xf2;" u2="&#x2a;" k="6" />
<hkern u1="&#xf2;" u2="&#x29;" k="37" />
<hkern u1="&#xf3;" u2="&#x2122;" k="31" />
<hkern u1="&#xf3;" u2="&#xc6;" k="14" />
<hkern u1="&#xf3;" u2="&#x7d;" k="68" />
<hkern u1="&#xf3;" u2="x" k="25" />
<hkern u1="&#xf3;" u2="v" k="18" />
<hkern u1="&#xf3;" u2="]" k="78" />
<hkern u1="&#xf3;" u2="\" k="76" />
<hkern u1="&#xf3;" u2="X" k="43" />
<hkern u1="&#xf3;" u2="V" k="66" />
<hkern u1="&#xf3;" u2="&#x3f;" k="47" />
<hkern u1="&#xf3;" u2="&#x2a;" k="6" />
<hkern u1="&#xf3;" u2="&#x29;" k="37" />
<hkern u1="&#xf4;" u2="&#x2122;" k="31" />
<hkern u1="&#xf4;" u2="&#xc6;" k="14" />
<hkern u1="&#xf4;" u2="&#x7d;" k="68" />
<hkern u1="&#xf4;" u2="x" k="25" />
<hkern u1="&#xf4;" u2="v" k="18" />
<hkern u1="&#xf4;" u2="]" k="78" />
<hkern u1="&#xf4;" u2="\" k="76" />
<hkern u1="&#xf4;" u2="X" k="43" />
<hkern u1="&#xf4;" u2="V" k="66" />
<hkern u1="&#xf4;" u2="&#x3f;" k="47" />
<hkern u1="&#xf4;" u2="&#x2a;" k="6" />
<hkern u1="&#xf4;" u2="&#x29;" k="37" />
<hkern u1="&#xf5;" u2="&#x2122;" k="31" />
<hkern u1="&#xf5;" u2="&#xc6;" k="14" />
<hkern u1="&#xf5;" u2="&#x7d;" k="68" />
<hkern u1="&#xf5;" u2="x" k="25" />
<hkern u1="&#xf5;" u2="v" k="18" />
<hkern u1="&#xf5;" u2="]" k="78" />
<hkern u1="&#xf5;" u2="\" k="76" />
<hkern u1="&#xf5;" u2="X" k="43" />
<hkern u1="&#xf5;" u2="V" k="66" />
<hkern u1="&#xf5;" u2="&#x3f;" k="47" />
<hkern u1="&#xf5;" u2="&#x2a;" k="6" />
<hkern u1="&#xf5;" u2="&#x29;" k="37" />
<hkern u1="&#xf6;" u2="&#x2122;" k="31" />
<hkern u1="&#xf6;" u2="&#xc6;" k="14" />
<hkern u1="&#xf6;" u2="&#x7d;" k="68" />
<hkern u1="&#xf6;" u2="x" k="25" />
<hkern u1="&#xf6;" u2="v" k="18" />
<hkern u1="&#xf6;" u2="]" k="78" />
<hkern u1="&#xf6;" u2="\" k="76" />
<hkern u1="&#xf6;" u2="X" k="43" />
<hkern u1="&#xf6;" u2="V" k="66" />
<hkern u1="&#xf6;" u2="&#x3f;" k="47" />
<hkern u1="&#xf6;" u2="&#x2a;" k="6" />
<hkern u1="&#xf6;" u2="&#x29;" k="37" />
<hkern u1="&#xf8;" u2="&#x2122;" k="31" />
<hkern u1="&#xf8;" u2="&#xc6;" k="14" />
<hkern u1="&#xf8;" u2="&#x7d;" k="68" />
<hkern u1="&#xf8;" u2="x" k="25" />
<hkern u1="&#xf8;" u2="v" k="18" />
<hkern u1="&#xf8;" u2="]" k="78" />
<hkern u1="&#xf8;" u2="\" k="76" />
<hkern u1="&#xf8;" u2="X" k="43" />
<hkern u1="&#xf8;" u2="V" k="66" />
<hkern u1="&#xf8;" u2="&#x3f;" k="47" />
<hkern u1="&#xf8;" u2="&#x2a;" k="6" />
<hkern u1="&#xf8;" u2="&#x29;" k="37" />
<hkern u1="&#xf9;" u2="&#x2122;" k="23" />
<hkern u1="&#xf9;" u2="&#x7d;" k="47" />
<hkern u1="&#xf9;" u2="]" k="51" />
<hkern u1="&#xf9;" u2="\" k="47" />
<hkern u1="&#xf9;" u2="V" k="53" />
<hkern u1="&#xf9;" u2="&#x3f;" k="27" />
<hkern u1="&#xf9;" u2="&#x29;" k="8" />
<hkern u1="&#xfa;" u2="&#x2122;" k="23" />
<hkern u1="&#xfa;" u2="&#x7d;" k="47" />
<hkern u1="&#xfa;" u2="]" k="51" />
<hkern u1="&#xfa;" u2="\" k="47" />
<hkern u1="&#xfa;" u2="V" k="53" />
<hkern u1="&#xfa;" u2="&#x3f;" k="27" />
<hkern u1="&#xfa;" u2="&#x29;" k="8" />
<hkern u1="&#xfb;" u2="&#x2122;" k="23" />
<hkern u1="&#xfb;" u2="&#x7d;" k="47" />
<hkern u1="&#xfb;" u2="]" k="51" />
<hkern u1="&#xfb;" u2="\" k="47" />
<hkern u1="&#xfb;" u2="V" k="53" />
<hkern u1="&#xfb;" u2="&#x3f;" k="27" />
<hkern u1="&#xfb;" u2="&#x29;" k="8" />
<hkern u1="&#xfc;" u2="&#x2122;" k="23" />
<hkern u1="&#xfc;" u2="&#x7d;" k="47" />
<hkern u1="&#xfc;" u2="]" k="51" />
<hkern u1="&#xfc;" u2="\" k="47" />
<hkern u1="&#xfc;" u2="V" k="53" />
<hkern u1="&#xfc;" u2="&#x3f;" k="27" />
<hkern u1="&#xfc;" u2="&#x29;" k="8" />
<hkern u1="&#xfd;" u2="&#xc6;" k="43" />
<hkern u1="&#xfd;" u2="&#x7d;" k="49" />
<hkern u1="&#xfd;" u2="]" k="59" />
<hkern u1="&#xfd;" u2="\" k="14" />
<hkern u1="&#xfd;" u2="X" k="53" />
<hkern u1="&#xfd;" u2="V" k="16" />
<hkern u1="&#xfd;" u2="&#x3f;" k="18" />
<hkern u1="&#xfd;" u2="&#x2f;" k="43" />
<hkern u1="&#xff;" u2="&#xc6;" k="43" />
<hkern u1="&#xff;" u2="&#x7d;" k="49" />
<hkern u1="&#xff;" u2="]" k="59" />
<hkern u1="&#xff;" u2="\" k="14" />
<hkern u1="&#xff;" u2="X" k="53" />
<hkern u1="&#xff;" u2="V" k="16" />
<hkern u1="&#xff;" u2="&#x3f;" k="18" />
<hkern u1="&#xff;" u2="&#x2f;" k="43" />
<hkern u1="&#x100;" u2="&#x2122;" k="84" />
<hkern u1="&#x100;" u2="&#xae;" k="53" />
<hkern u1="&#x100;" u2="&#x7d;" k="41" />
<hkern u1="&#x100;" u2="v" k="37" />
<hkern u1="&#x100;" u2="f" k="14" />
<hkern u1="&#x100;" u2="]" k="47" />
<hkern u1="&#x100;" u2="\" k="96" />
<hkern u1="&#x100;" u2="V" k="57" />
<hkern u1="&#x100;" u2="&#x3f;" k="51" />
<hkern u1="&#x100;" u2="&#x2a;" k="74" />
<hkern u1="&#x101;" u2="&#x2122;" k="25" />
<hkern u1="&#x101;" u2="&#x7d;" k="18" />
<hkern u1="&#x101;" u2="v" k="10" />
<hkern u1="&#x101;" u2="]" k="23" />
<hkern u1="&#x101;" u2="\" k="72" />
<hkern u1="&#x101;" u2="V" k="51" />
<hkern u1="&#x101;" u2="&#x3f;" k="31" />
<hkern u1="&#x101;" u2="&#x2a;" k="6" />
<hkern u1="&#x102;" u2="&#x2122;" k="84" />
<hkern u1="&#x102;" u2="&#xae;" k="53" />
<hkern u1="&#x102;" u2="&#x7d;" k="41" />
<hkern u1="&#x102;" u2="v" k="37" />
<hkern u1="&#x102;" u2="f" k="14" />
<hkern u1="&#x102;" u2="]" k="47" />
<hkern u1="&#x102;" u2="\" k="96" />
<hkern u1="&#x102;" u2="V" k="57" />
<hkern u1="&#x102;" u2="&#x3f;" k="51" />
<hkern u1="&#x102;" u2="&#x2a;" k="74" />
<hkern u1="&#x103;" u2="&#x2122;" k="25" />
<hkern u1="&#x103;" u2="&#x7d;" k="18" />
<hkern u1="&#x103;" u2="v" k="10" />
<hkern u1="&#x103;" u2="]" k="23" />
<hkern u1="&#x103;" u2="\" k="72" />
<hkern u1="&#x103;" u2="V" k="51" />
<hkern u1="&#x103;" u2="&#x3f;" k="31" />
<hkern u1="&#x103;" u2="&#x2a;" k="6" />
<hkern u1="&#x104;" u2="&#x2122;" k="84" />
<hkern u1="&#x104;" u2="&#x201e;" k="-12" />
<hkern u1="&#x104;" u2="&#xae;" k="53" />
<hkern u1="&#x104;" u2="&#x7d;" k="41" />
<hkern u1="&#x104;" u2="v" k="37" />
<hkern u1="&#x104;" u2="j" k="-135" />
<hkern u1="&#x104;" u2="f" k="14" />
<hkern u1="&#x104;" u2="]" k="47" />
<hkern u1="&#x104;" u2="\" k="96" />
<hkern u1="&#x104;" u2="V" k="57" />
<hkern u1="&#x104;" u2="&#x3f;" k="51" />
<hkern u1="&#x104;" u2="&#x2a;" k="74" />
<hkern u1="&#x105;" u2="&#x2122;" k="25" />
<hkern u1="&#x105;" u2="&#x7d;" k="18" />
<hkern u1="&#x105;" u2="v" k="10" />
<hkern u1="&#x105;" u2="j" k="-104" />
<hkern u1="&#x105;" u2="]" k="23" />
<hkern u1="&#x105;" u2="\" k="72" />
<hkern u1="&#x105;" u2="V" k="51" />
<hkern u1="&#x105;" u2="&#x3f;" k="31" />
<hkern u1="&#x105;" u2="&#x2a;" k="6" />
<hkern u1="&#x106;" u2="&#x135;" k="-16" />
<hkern u1="&#x106;" u2="&#x12d;" k="-31" />
<hkern u1="&#x106;" u2="&#x12b;" k="-18" />
<hkern u1="&#x106;" u2="&#x129;" k="-59" />
<hkern u1="&#x106;" u2="&#xef;" k="-29" />
<hkern u1="&#x106;" u2="&#xee;" k="-25" />
<hkern u1="&#x106;" u2="&#xec;" k="-68" />
<hkern u1="&#x106;" u2="&#xae;" k="27" />
<hkern u1="&#x106;" u2="v" k="33" />
<hkern u1="&#x106;" u2="f" k="12" />
<hkern u1="&#x107;" u2="&#x7d;" k="33" />
<hkern u1="&#x107;" u2="]" k="39" />
<hkern u1="&#x107;" u2="\" k="20" />
<hkern u1="&#x107;" u2="V" k="25" />
<hkern u1="&#x107;" u2="&#x3f;" k="18" />
<hkern u1="&#x108;" u2="&#x135;" k="-16" />
<hkern u1="&#x108;" u2="&#x12d;" k="-31" />
<hkern u1="&#x108;" u2="&#x12b;" k="-18" />
<hkern u1="&#x108;" u2="&#x129;" k="-59" />
<hkern u1="&#x108;" u2="&#xef;" k="-29" />
<hkern u1="&#x108;" u2="&#xee;" k="-25" />
<hkern u1="&#x108;" u2="&#xec;" k="-68" />
<hkern u1="&#x108;" u2="&#xae;" k="27" />
<hkern u1="&#x108;" u2="v" k="33" />
<hkern u1="&#x108;" u2="f" k="12" />
<hkern u1="&#x109;" u2="&#x7d;" k="33" />
<hkern u1="&#x109;" u2="]" k="39" />
<hkern u1="&#x109;" u2="\" k="20" />
<hkern u1="&#x109;" u2="V" k="25" />
<hkern u1="&#x109;" u2="&#x3f;" k="18" />
<hkern u1="&#x10a;" u2="&#x135;" k="-16" />
<hkern u1="&#x10a;" u2="&#x12d;" k="-31" />
<hkern u1="&#x10a;" u2="&#x12b;" k="-18" />
<hkern u1="&#x10a;" u2="&#x129;" k="-59" />
<hkern u1="&#x10a;" u2="&#xef;" k="-29" />
<hkern u1="&#x10a;" u2="&#xee;" k="-25" />
<hkern u1="&#x10a;" u2="&#xec;" k="-68" />
<hkern u1="&#x10a;" u2="&#xae;" k="27" />
<hkern u1="&#x10a;" u2="v" k="33" />
<hkern u1="&#x10a;" u2="f" k="12" />
<hkern u1="&#x10b;" u2="&#x7d;" k="33" />
<hkern u1="&#x10b;" u2="]" k="39" />
<hkern u1="&#x10b;" u2="\" k="20" />
<hkern u1="&#x10b;" u2="V" k="25" />
<hkern u1="&#x10b;" u2="&#x3f;" k="18" />
<hkern u1="&#x10c;" u2="&#x135;" k="-16" />
<hkern u1="&#x10c;" u2="&#x12d;" k="-31" />
<hkern u1="&#x10c;" u2="&#x12b;" k="-18" />
<hkern u1="&#x10c;" u2="&#x129;" k="-59" />
<hkern u1="&#x10c;" u2="&#xef;" k="-29" />
<hkern u1="&#x10c;" u2="&#xee;" k="-25" />
<hkern u1="&#x10c;" u2="&#xec;" k="-68" />
<hkern u1="&#x10c;" u2="&#xae;" k="27" />
<hkern u1="&#x10c;" u2="v" k="33" />
<hkern u1="&#x10c;" u2="f" k="12" />
<hkern u1="&#x10d;" u2="&#x7d;" k="33" />
<hkern u1="&#x10d;" u2="]" k="39" />
<hkern u1="&#x10d;" u2="\" k="20" />
<hkern u1="&#x10d;" u2="V" k="25" />
<hkern u1="&#x10d;" u2="&#x3f;" k="18" />
<hkern u1="&#x10e;" u2="&#xc6;" k="35" />
<hkern u1="&#x10e;" u2="&#x7d;" k="53" />
<hkern u1="&#x10e;" u2="x" k="6" />
<hkern u1="&#x10e;" u2="]" k="59" />
<hkern u1="&#x10e;" u2="\" k="14" />
<hkern u1="&#x10e;" u2="X" k="43" />
<hkern u1="&#x10e;" u2="V" k="25" />
<hkern u1="&#x10e;" u2="&#x3f;" k="18" />
<hkern u1="&#x10e;" u2="&#x2f;" k="12" />
<hkern u1="&#x10e;" u2="&#x29;" k="25" />
<hkern u1="&#x10f;" u2="&#x2122;" k="-16" />
<hkern u1="&#x10f;" u2="&#x17e;" k="-27" />
<hkern u1="&#x10f;" u2="&#x161;" k="-14" />
<hkern u1="&#x10f;" u2="&#x10d;" k="-23" />
<hkern u1="&#x10f;" u2="&#xe1;" k="-8" />
<hkern u1="&#x10f;" u2="&#xdf;" k="-43" />
<hkern u1="&#x10f;" u2="&#x7d;" k="-45" />
<hkern u1="&#x10f;" u2="&#x7c;" k="-10" />
<hkern u1="&#x10f;" u2="x" k="-31" />
<hkern u1="&#x10f;" u2="v" k="-29" />
<hkern u1="&#x10f;" u2="f" k="-16" />
<hkern u1="&#x10f;" u2="]" k="-45" />
<hkern u1="&#x10f;" u2="\" k="-109" />
<hkern u1="&#x10f;" u2="&#x3f;" k="-94" />
<hkern u1="&#x10f;" u2="&#x2f;" k="29" />
<hkern u1="&#x10f;" u2="&#x2a;" k="-78" />
<hkern u1="&#x10f;" u2="&#x29;" k="-49" />
<hkern u1="&#x10f;" u2="&#x21;" k="-10" />
<hkern u1="&#x110;" u2="&#xc6;" k="35" />
<hkern u1="&#x110;" u2="&#x7d;" k="53" />
<hkern u1="&#x110;" u2="x" k="6" />
<hkern u1="&#x110;" u2="]" k="59" />
<hkern u1="&#x110;" u2="\" k="14" />
<hkern u1="&#x110;" u2="X" k="43" />
<hkern u1="&#x110;" u2="V" k="25" />
<hkern u1="&#x110;" u2="&#x3f;" k="18" />
<hkern u1="&#x110;" u2="&#x2f;" k="12" />
<hkern u1="&#x110;" u2="&#x29;" k="25" />
<hkern u1="&#x111;" u2="&#xef;" k="-6" />
<hkern u1="&#x111;" u2="&#xec;" k="-14" />
<hkern u1="&#x112;" u2="&#x135;" k="-16" />
<hkern u1="&#x112;" u2="&#x12d;" k="-20" />
<hkern u1="&#x112;" u2="&#x12b;" k="-14" />
<hkern u1="&#x112;" u2="&#x129;" k="-51" />
<hkern u1="&#x112;" u2="&#xef;" k="-27" />
<hkern u1="&#x112;" u2="&#xee;" k="-25" />
<hkern u1="&#x112;" u2="&#xec;" k="-74" />
<hkern u1="&#x112;" u2="v" k="23" />
<hkern u1="&#x112;" u2="f" k="6" />
<hkern u1="&#x113;" u2="&#x2122;" k="29" />
<hkern u1="&#x113;" u2="&#xc6;" k="10" />
<hkern u1="&#x113;" u2="&#x7d;" k="55" />
<hkern u1="&#x113;" u2="x" k="8" />
<hkern u1="&#x113;" u2="v" k="16" />
<hkern u1="&#x113;" u2="]" k="51" />
<hkern u1="&#x113;" u2="\" k="70" />
<hkern u1="&#x113;" u2="V" k="63" />
<hkern u1="&#x113;" u2="&#x3f;" k="41" />
<hkern u1="&#x113;" u2="&#x29;" k="10" />
<hkern u1="&#x114;" u2="&#x135;" k="-16" />
<hkern u1="&#x114;" u2="&#x12d;" k="-20" />
<hkern u1="&#x114;" u2="&#x12b;" k="-14" />
<hkern u1="&#x114;" u2="&#x129;" k="-51" />
<hkern u1="&#x114;" u2="&#xef;" k="-27" />
<hkern u1="&#x114;" u2="&#xee;" k="-25" />
<hkern u1="&#x114;" u2="&#xec;" k="-74" />
<hkern u1="&#x114;" u2="v" k="23" />
<hkern u1="&#x114;" u2="f" k="6" />
<hkern u1="&#x115;" u2="&#x2122;" k="29" />
<hkern u1="&#x115;" u2="&#xc6;" k="10" />
<hkern u1="&#x115;" u2="&#x7d;" k="55" />
<hkern u1="&#x115;" u2="x" k="8" />
<hkern u1="&#x115;" u2="v" k="16" />
<hkern u1="&#x115;" u2="]" k="51" />
<hkern u1="&#x115;" u2="\" k="70" />
<hkern u1="&#x115;" u2="V" k="63" />
<hkern u1="&#x115;" u2="&#x3f;" k="41" />
<hkern u1="&#x115;" u2="&#x29;" k="10" />
<hkern u1="&#x116;" u2="&#x135;" k="-16" />
<hkern u1="&#x116;" u2="&#x12d;" k="-20" />
<hkern u1="&#x116;" u2="&#x12b;" k="-14" />
<hkern u1="&#x116;" u2="&#x129;" k="-51" />
<hkern u1="&#x116;" u2="&#xef;" k="-27" />
<hkern u1="&#x116;" u2="&#xee;" k="-25" />
<hkern u1="&#x116;" u2="&#xec;" k="-74" />
<hkern u1="&#x116;" u2="v" k="23" />
<hkern u1="&#x116;" u2="f" k="6" />
<hkern u1="&#x117;" u2="&#x2122;" k="29" />
<hkern u1="&#x117;" u2="&#xc6;" k="10" />
<hkern u1="&#x117;" u2="&#x7d;" k="55" />
<hkern u1="&#x117;" u2="x" k="8" />
<hkern u1="&#x117;" u2="v" k="16" />
<hkern u1="&#x117;" u2="]" k="51" />
<hkern u1="&#x117;" u2="\" k="70" />
<hkern u1="&#x117;" u2="V" k="63" />
<hkern u1="&#x117;" u2="&#x3f;" k="41" />
<hkern u1="&#x117;" u2="&#x29;" k="10" />
<hkern u1="&#x118;" u2="&#x135;" k="-16" />
<hkern u1="&#x118;" u2="&#x12d;" k="-20" />
<hkern u1="&#x118;" u2="&#x12b;" k="-14" />
<hkern u1="&#x118;" u2="&#x129;" k="-51" />
<hkern u1="&#x118;" u2="&#xef;" k="-27" />
<hkern u1="&#x118;" u2="&#xee;" k="-25" />
<hkern u1="&#x118;" u2="&#xec;" k="-74" />
<hkern u1="&#x118;" u2="v" k="23" />
<hkern u1="&#x118;" u2="j" k="-45" />
<hkern u1="&#x118;" u2="f" k="6" />
<hkern u1="&#x119;" u2="&#x2122;" k="29" />
<hkern u1="&#x119;" u2="&#xc6;" k="10" />
<hkern u1="&#x119;" u2="&#x7d;" k="55" />
<hkern u1="&#x119;" u2="x" k="8" />
<hkern u1="&#x119;" u2="v" k="16" />
<hkern u1="&#x119;" u2="]" k="51" />
<hkern u1="&#x119;" u2="\" k="70" />
<hkern u1="&#x119;" u2="V" k="63" />
<hkern u1="&#x119;" u2="&#x3f;" k="41" />
<hkern u1="&#x119;" u2="&#x29;" k="10" />
<hkern u1="&#x11a;" u2="&#x135;" k="-16" />
<hkern u1="&#x11a;" u2="&#x12d;" k="-20" />
<hkern u1="&#x11a;" u2="&#x12b;" k="-14" />
<hkern u1="&#x11a;" u2="&#x129;" k="-51" />
<hkern u1="&#x11a;" u2="&#xef;" k="-27" />
<hkern u1="&#x11a;" u2="&#xee;" k="-25" />
<hkern u1="&#x11a;" u2="&#xec;" k="-74" />
<hkern u1="&#x11a;" u2="v" k="23" />
<hkern u1="&#x11a;" u2="f" k="6" />
<hkern u1="&#x11b;" u2="&#x2122;" k="29" />
<hkern u1="&#x11b;" u2="&#xc6;" k="10" />
<hkern u1="&#x11b;" u2="&#x7d;" k="55" />
<hkern u1="&#x11b;" u2="x" k="8" />
<hkern u1="&#x11b;" u2="v" k="16" />
<hkern u1="&#x11b;" u2="]" k="51" />
<hkern u1="&#x11b;" u2="\" k="70" />
<hkern u1="&#x11b;" u2="V" k="63" />
<hkern u1="&#x11b;" u2="&#x3f;" k="41" />
<hkern u1="&#x11b;" u2="&#x29;" k="10" />
<hkern u1="&#x11c;" u2="&#xef;" k="-12" />
<hkern u1="&#x11c;" u2="&#xee;" k="-8" />
<hkern u1="&#x11c;" u2="&#xec;" k="-31" />
<hkern u1="&#x11c;" u2="v" k="14" />
<hkern u1="&#x11c;" u2="f" k="12" />
<hkern u1="&#x11c;" u2="\" k="8" />
<hkern u1="&#x11c;" u2="V" k="18" />
<hkern u1="&#x11d;" u2="&#x135;" k="-39" />
<hkern u1="&#x11d;" u2="j" k="-39" />
<hkern u1="&#x11d;" u2="\" k="12" />
<hkern u1="&#x11e;" u2="&#xef;" k="-12" />
<hkern u1="&#x11e;" u2="&#xee;" k="-8" />
<hkern u1="&#x11e;" u2="&#xec;" k="-31" />
<hkern u1="&#x11e;" u2="v" k="14" />
<hkern u1="&#x11e;" u2="f" k="12" />
<hkern u1="&#x11e;" u2="\" k="8" />
<hkern u1="&#x11e;" u2="V" k="18" />
<hkern u1="&#x11f;" u2="&#x135;" k="-39" />
<hkern u1="&#x11f;" u2="j" k="-39" />
<hkern u1="&#x11f;" u2="\" k="12" />
<hkern u1="&#x120;" u2="&#xef;" k="-12" />
<hkern u1="&#x120;" u2="&#xee;" k="-8" />
<hkern u1="&#x120;" u2="&#xec;" k="-31" />
<hkern u1="&#x120;" u2="v" k="14" />
<hkern u1="&#x120;" u2="f" k="12" />
<hkern u1="&#x120;" u2="\" k="8" />
<hkern u1="&#x120;" u2="V" k="18" />
<hkern u1="&#x121;" u2="&#x135;" k="-39" />
<hkern u1="&#x121;" u2="j" k="-39" />
<hkern u1="&#x121;" u2="\" k="12" />
<hkern u1="&#x122;" u2="&#xef;" k="-12" />
<hkern u1="&#x122;" u2="&#xee;" k="-8" />
<hkern u1="&#x122;" u2="&#xec;" k="-31" />
<hkern u1="&#x122;" u2="v" k="14" />
<hkern u1="&#x122;" u2="f" k="12" />
<hkern u1="&#x122;" u2="\" k="8" />
<hkern u1="&#x122;" u2="V" k="18" />
<hkern u1="&#x123;" u2="&#x135;" k="-39" />
<hkern u1="&#x123;" u2="j" k="-39" />
<hkern u1="&#x123;" u2="\" k="12" />
<hkern u1="&#x124;" u2="&#xec;" k="-6" />
<hkern u1="&#x125;" u2="&#x2122;" k="33" />
<hkern u1="&#x125;" u2="&#x7d;" k="49" />
<hkern u1="&#x125;" u2="v" k="10" />
<hkern u1="&#x125;" u2="]" k="53" />
<hkern u1="&#x125;" u2="\" k="74" />
<hkern u1="&#x125;" u2="V" k="59" />
<hkern u1="&#x125;" u2="&#x3f;" k="45" />
<hkern u1="&#x125;" u2="&#x2a;" k="6" />
<hkern u1="&#x125;" u2="&#x29;" k="8" />
<hkern u1="&#x126;" u2="&#xec;" k="-6" />
<hkern u1="&#x126;" u2="&#x2a;" k="-18" />
<hkern u1="&#x127;" u2="&#x2122;" k="33" />
<hkern u1="&#x127;" u2="&#x7d;" k="49" />
<hkern u1="&#x127;" u2="v" k="10" />
<hkern u1="&#x127;" u2="]" k="53" />
<hkern u1="&#x127;" u2="\" k="74" />
<hkern u1="&#x127;" u2="V" k="59" />
<hkern u1="&#x127;" u2="&#x3f;" k="45" />
<hkern u1="&#x127;" u2="&#x2a;" k="6" />
<hkern u1="&#x127;" u2="&#x29;" k="8" />
<hkern u1="&#x128;" u2="&#xec;" k="-6" />
<hkern u1="&#x129;" u2="&#x2122;" k="-10" />
<hkern u1="&#x129;" u2="&#xef;" k="-6" />
<hkern u1="&#x129;" u2="&#xec;" k="-14" />
<hkern u1="&#x129;" u2="&#x7d;" k="-39" />
<hkern u1="&#x129;" u2="]" k="-39" />
<hkern u1="&#x129;" u2="\" k="-84" />
<hkern u1="&#x129;" u2="&#x3f;" k="-78" />
<hkern u1="&#x129;" u2="&#x2a;" k="-39" />
<hkern u1="&#x129;" u2="&#x29;" k="-23" />
<hkern u1="&#x129;" u2="&#x27;" k="-8" />
<hkern u1="&#x129;" u2="&#x22;" k="-8" />
<hkern u1="&#x12a;" u2="&#xec;" k="-6" />
<hkern u1="&#x12b;" u2="&#xef;" k="-6" />
<hkern u1="&#x12b;" u2="&#xec;" k="-14" />
<hkern u1="&#x12b;" u2="\" k="-41" />
<hkern u1="&#x12b;" u2="&#x3f;" k="-37" />
<hkern u1="&#x12b;" u2="&#x2a;" k="-18" />
<hkern u1="&#x12c;" u2="&#xec;" k="-6" />
<hkern u1="&#x12d;" u2="&#xef;" k="-6" />
<hkern u1="&#x12d;" u2="&#xec;" k="-14" />
<hkern u1="&#x12d;" u2="&#x7d;" k="-51" />
<hkern u1="&#x12d;" u2="]" k="-51" />
<hkern u1="&#x12d;" u2="\" k="-47" />
<hkern u1="&#x12d;" u2="&#x3f;" k="-35" />
<hkern u1="&#x12d;" u2="&#x2a;" k="-10" />
<hkern u1="&#x12d;" u2="&#x29;" k="-37" />
<hkern u1="&#x12e;" u2="&#xec;" k="-6" />
<hkern u1="&#x12e;" u2="j" k="-23" />
<hkern u1="&#x12f;" u2="&#xef;" k="-6" />
<hkern u1="&#x12f;" u2="&#xec;" k="-14" />
<hkern u1="&#x12f;" u2="j" k="-35" />
<hkern u1="&#x130;" u2="&#xec;" k="-6" />
<hkern u1="&#x131;" u2="&#xef;" k="-6" />
<hkern u1="&#x131;" u2="&#xec;" k="-14" />
<hkern u1="&#x134;" u2="&#xec;" k="-8" />
<hkern u1="&#x135;" u2="&#x2122;" k="-12" />
<hkern u1="&#x135;" u2="&#xef;" k="-6" />
<hkern u1="&#x135;" u2="&#xec;" k="-14" />
<hkern u1="&#x135;" u2="&#x3f;" k="-27" />
<hkern u1="&#x135;" u2="&#x2a;" k="-31" />
<hkern u1="&#x135;" u2="&#x27;" k="-6" />
<hkern u1="&#x135;" u2="&#x22;" k="-6" />
<hkern u1="&#x136;" u2="&#x12d;" k="-59" />
<hkern u1="&#x136;" u2="&#x12b;" k="-27" />
<hkern u1="&#x136;" u2="&#x129;" k="-63" />
<hkern u1="&#x136;" u2="&#xef;" k="-55" />
<hkern u1="&#x136;" u2="&#xec;" k="-100" />
<hkern u1="&#x136;" u2="&#xae;" k="23" />
<hkern u1="&#x136;" u2="v" k="49" />
<hkern u1="&#x136;" u2="f" k="8" />
<hkern u1="&#x137;" u2="&#x7d;" k="27" />
<hkern u1="&#x137;" u2="]" k="35" />
<hkern u1="&#x137;" u2="\" k="14" />
<hkern u1="&#x137;" u2="V" k="18" />
<hkern u1="&#x137;" u2="&#x3f;" k="14" />
<hkern u1="&#x139;" u2="&#x2122;" k="184" />
<hkern u1="&#x139;" u2="&#xae;" k="172" />
<hkern u1="&#x139;" u2="&#x7d;" k="23" />
<hkern u1="&#x139;" u2="v" k="92" />
<hkern u1="&#x139;" u2="f" k="6" />
<hkern u1="&#x139;" u2="]" k="29" />
<hkern u1="&#x139;" u2="\" k="145" />
<hkern u1="&#x139;" u2="V" k="121" />
<hkern u1="&#x139;" u2="&#x3f;" k="27" />
<hkern u1="&#x139;" u2="&#x2a;" k="182" />
<hkern u1="&#x13a;" u2="&#xec;" k="-10" />
<hkern u1="&#x13b;" u2="&#x2122;" k="184" />
<hkern u1="&#x13b;" u2="&#xae;" k="172" />
<hkern u1="&#x13b;" u2="&#x7d;" k="23" />
<hkern u1="&#x13b;" u2="v" k="92" />
<hkern u1="&#x13b;" u2="f" k="6" />
<hkern u1="&#x13b;" u2="]" k="29" />
<hkern u1="&#x13b;" u2="\" k="145" />
<hkern u1="&#x13b;" u2="V" k="121" />
<hkern u1="&#x13b;" u2="&#x3f;" k="27" />
<hkern u1="&#x13b;" u2="&#x2a;" k="182" />
<hkern u1="&#x13c;" u2="&#xec;" k="-10" />
<hkern u1="&#x13d;" u2="&#x2122;" k="170" />
<hkern u1="&#x13d;" u2="&#x201d;" k="162" />
<hkern u1="&#x13d;" u2="&#x201c;" k="158" />
<hkern u1="&#x13d;" u2="&#x2019;" k="162" />
<hkern u1="&#x13d;" u2="&#x2018;" k="158" />
<hkern u1="&#x13d;" u2="&#x21a;" k="82" />
<hkern u1="&#x13d;" u2="&#x178;" k="76" />
<hkern u1="&#x13d;" u2="&#x176;" k="76" />
<hkern u1="&#x13d;" u2="&#x174;" k="78" />
<hkern u1="&#x13d;" u2="&#x166;" k="82" />
<hkern u1="&#x13d;" u2="&#x164;" k="82" />
<hkern u1="&#x13d;" u2="&#xdd;" k="76" />
<hkern u1="&#x13d;" u2="&#xae;" k="164" />
<hkern u1="&#x13d;" u2="&#x7d;" k="23" />
<hkern u1="&#x13d;" u2="v" k="92" />
<hkern u1="&#x13d;" u2="f" k="6" />
<hkern u1="&#x13d;" u2="]" k="29" />
<hkern u1="&#x13d;" u2="\" k="119" />
<hkern u1="&#x13d;" u2="Y" k="76" />
<hkern u1="&#x13d;" u2="W" k="78" />
<hkern u1="&#x13d;" u2="V" k="92" />
<hkern u1="&#x13d;" u2="T" k="82" />
<hkern u1="&#x13d;" u2="&#x3f;" k="27" />
<hkern u1="&#x13d;" u2="&#x2a;" k="109" />
<hkern u1="&#x13d;" u2="&#x27;" k="174" />
<hkern u1="&#x13d;" u2="&#x22;" k="174" />
<hkern u1="&#x13e;" u2="&#x2122;" k="-16" />
<hkern u1="&#x13e;" u2="&#x17e;" k="-27" />
<hkern u1="&#x13e;" u2="&#x161;" k="-14" />
<hkern u1="&#x13e;" u2="&#x10d;" k="-23" />
<hkern u1="&#x13e;" u2="&#xe1;" k="-8" />
<hkern u1="&#x13e;" u2="&#xdf;" k="-45" />
<hkern u1="&#x13e;" u2="&#x7d;" k="-45" />
<hkern u1="&#x13e;" u2="&#x7c;" k="-10" />
<hkern u1="&#x13e;" u2="x" k="-31" />
<hkern u1="&#x13e;" u2="v" k="-29" />
<hkern u1="&#x13e;" u2="f" k="-16" />
<hkern u1="&#x13e;" u2="]" k="-45" />
<hkern u1="&#x13e;" u2="\" k="-109" />
<hkern u1="&#x13e;" u2="&#x3f;" k="-94" />
<hkern u1="&#x13e;" u2="&#x2f;" k="29" />
<hkern u1="&#x13e;" u2="&#x2a;" k="-78" />
<hkern u1="&#x13e;" u2="&#x29;" k="-49" />
<hkern u1="&#x13e;" u2="&#x21;" k="-10" />
<hkern u1="&#x141;" u2="&#x2122;" k="184" />
<hkern u1="&#x141;" u2="&#xae;" k="172" />
<hkern u1="&#x141;" u2="&#x7d;" k="23" />
<hkern u1="&#x141;" u2="v" k="92" />
<hkern u1="&#x141;" u2="f" k="6" />
<hkern u1="&#x141;" u2="]" k="29" />
<hkern u1="&#x141;" u2="\" k="145" />
<hkern u1="&#x141;" u2="V" k="121" />
<hkern u1="&#x141;" u2="&#x3f;" k="27" />
<hkern u1="&#x141;" u2="&#x2a;" k="182" />
<hkern u1="&#x142;" u2="&#xec;" k="-10" />
<hkern u1="&#x143;" u2="&#xec;" k="-6" />
<hkern u1="&#x144;" u2="&#x2122;" k="33" />
<hkern u1="&#x144;" u2="&#x7d;" k="49" />
<hkern u1="&#x144;" u2="v" k="10" />
<hkern u1="&#x144;" u2="]" k="53" />
<hkern u1="&#x144;" u2="\" k="74" />
<hkern u1="&#x144;" u2="V" k="59" />
<hkern u1="&#x144;" u2="&#x3f;" k="45" />
<hkern u1="&#x144;" u2="&#x2a;" k="6" />
<hkern u1="&#x144;" u2="&#x29;" k="8" />
<hkern u1="&#x145;" u2="&#xec;" k="-6" />
<hkern u1="&#x146;" u2="&#x2122;" k="33" />
<hkern u1="&#x146;" u2="&#x7d;" k="49" />
<hkern u1="&#x146;" u2="v" k="10" />
<hkern u1="&#x146;" u2="]" k="53" />
<hkern u1="&#x146;" u2="\" k="74" />
<hkern u1="&#x146;" u2="V" k="59" />
<hkern u1="&#x146;" u2="&#x3f;" k="45" />
<hkern u1="&#x146;" u2="&#x2a;" k="6" />
<hkern u1="&#x146;" u2="&#x29;" k="8" />
<hkern u1="&#x147;" u2="&#xec;" k="-6" />
<hkern u1="&#x148;" u2="&#x2122;" k="33" />
<hkern u1="&#x148;" u2="&#x7d;" k="49" />
<hkern u1="&#x148;" u2="v" k="10" />
<hkern u1="&#x148;" u2="]" k="53" />
<hkern u1="&#x148;" u2="\" k="74" />
<hkern u1="&#x148;" u2="V" k="59" />
<hkern u1="&#x148;" u2="&#x3f;" k="45" />
<hkern u1="&#x148;" u2="&#x2a;" k="6" />
<hkern u1="&#x148;" u2="&#x29;" k="8" />
<hkern u1="&#x14a;" u2="&#xec;" k="-6" />
<hkern u1="&#x14b;" u2="&#x2122;" k="33" />
<hkern u1="&#x14b;" u2="&#x7d;" k="49" />
<hkern u1="&#x14b;" u2="v" k="10" />
<hkern u1="&#x14b;" u2="]" k="53" />
<hkern u1="&#x14b;" u2="\" k="74" />
<hkern u1="&#x14b;" u2="V" k="59" />
<hkern u1="&#x14b;" u2="&#x3f;" k="45" />
<hkern u1="&#x14b;" u2="&#x2a;" k="6" />
<hkern u1="&#x14b;" u2="&#x29;" k="8" />
<hkern u1="&#x14c;" u2="&#xc6;" k="31" />
<hkern u1="&#x14c;" u2="&#x7d;" k="51" />
<hkern u1="&#x14c;" u2="]" k="57" />
<hkern u1="&#x14c;" u2="\" k="16" />
<hkern u1="&#x14c;" u2="X" k="39" />
<hkern u1="&#x14c;" u2="V" k="25" />
<hkern u1="&#x14c;" u2="&#x3f;" k="14" />
<hkern u1="&#x14c;" u2="&#x2f;" k="12" />
<hkern u1="&#x14c;" u2="&#x29;" k="10" />
<hkern u1="&#x14d;" u2="&#x2122;" k="31" />
<hkern u1="&#x14d;" u2="&#xc6;" k="14" />
<hkern u1="&#x14d;" u2="&#x7d;" k="68" />
<hkern u1="&#x14d;" u2="x" k="25" />
<hkern u1="&#x14d;" u2="v" k="18" />
<hkern u1="&#x14d;" u2="]" k="78" />
<hkern u1="&#x14d;" u2="\" k="76" />
<hkern u1="&#x14d;" u2="X" k="43" />
<hkern u1="&#x14d;" u2="V" k="66" />
<hkern u1="&#x14d;" u2="&#x3f;" k="47" />
<hkern u1="&#x14d;" u2="&#x2a;" k="6" />
<hkern u1="&#x14d;" u2="&#x29;" k="37" />
<hkern u1="&#x14e;" u2="&#xc6;" k="31" />
<hkern u1="&#x14e;" u2="&#x7d;" k="51" />
<hkern u1="&#x14e;" u2="]" k="57" />
<hkern u1="&#x14e;" u2="\" k="16" />
<hkern u1="&#x14e;" u2="X" k="39" />
<hkern u1="&#x14e;" u2="V" k="25" />
<hkern u1="&#x14e;" u2="&#x3f;" k="14" />
<hkern u1="&#x14e;" u2="&#x2f;" k="12" />
<hkern u1="&#x14e;" u2="&#x29;" k="10" />
<hkern u1="&#x14f;" u2="&#x2122;" k="31" />
<hkern u1="&#x14f;" u2="&#xc6;" k="14" />
<hkern u1="&#x14f;" u2="&#x7d;" k="68" />
<hkern u1="&#x14f;" u2="x" k="25" />
<hkern u1="&#x14f;" u2="v" k="18" />
<hkern u1="&#x14f;" u2="]" k="78" />
<hkern u1="&#x14f;" u2="\" k="76" />
<hkern u1="&#x14f;" u2="X" k="43" />
<hkern u1="&#x14f;" u2="V" k="66" />
<hkern u1="&#x14f;" u2="&#x3f;" k="47" />
<hkern u1="&#x14f;" u2="&#x2a;" k="6" />
<hkern u1="&#x14f;" u2="&#x29;" k="37" />
<hkern u1="&#x150;" u2="&#xc6;" k="31" />
<hkern u1="&#x150;" u2="&#x7d;" k="51" />
<hkern u1="&#x150;" u2="]" k="57" />
<hkern u1="&#x150;" u2="\" k="16" />
<hkern u1="&#x150;" u2="X" k="39" />
<hkern u1="&#x150;" u2="V" k="25" />
<hkern u1="&#x150;" u2="&#x3f;" k="14" />
<hkern u1="&#x150;" u2="&#x2f;" k="12" />
<hkern u1="&#x150;" u2="&#x29;" k="10" />
<hkern u1="&#x151;" u2="&#x2122;" k="31" />
<hkern u1="&#x151;" u2="&#xc6;" k="14" />
<hkern u1="&#x151;" u2="&#x7d;" k="68" />
<hkern u1="&#x151;" u2="x" k="25" />
<hkern u1="&#x151;" u2="v" k="18" />
<hkern u1="&#x151;" u2="]" k="78" />
<hkern u1="&#x151;" u2="\" k="76" />
<hkern u1="&#x151;" u2="X" k="43" />
<hkern u1="&#x151;" u2="V" k="66" />
<hkern u1="&#x151;" u2="&#x3f;" k="47" />
<hkern u1="&#x151;" u2="&#x2a;" k="6" />
<hkern u1="&#x151;" u2="&#x29;" k="37" />
<hkern u1="&#x152;" u2="&#x135;" k="-16" />
<hkern u1="&#x152;" u2="&#x12d;" k="-20" />
<hkern u1="&#x152;" u2="&#x12b;" k="-14" />
<hkern u1="&#x152;" u2="&#x129;" k="-51" />
<hkern u1="&#x152;" u2="&#xef;" k="-27" />
<hkern u1="&#x152;" u2="&#xee;" k="-25" />
<hkern u1="&#x152;" u2="&#xec;" k="-74" />
<hkern u1="&#x152;" u2="v" k="23" />
<hkern u1="&#x152;" u2="f" k="6" />
<hkern u1="&#x153;" u2="&#x2122;" k="29" />
<hkern u1="&#x153;" u2="&#xc6;" k="10" />
<hkern u1="&#x153;" u2="&#x7d;" k="55" />
<hkern u1="&#x153;" u2="x" k="8" />
<hkern u1="&#x153;" u2="v" k="16" />
<hkern u1="&#x153;" u2="]" k="51" />
<hkern u1="&#x153;" u2="\" k="70" />
<hkern u1="&#x153;" u2="V" k="63" />
<hkern u1="&#x153;" u2="&#x3f;" k="41" />
<hkern u1="&#x153;" u2="&#x29;" k="10" />
<hkern u1="&#x154;" u2="&#xc6;" k="16" />
<hkern u1="&#x154;" u2="&#x7d;" k="31" />
<hkern u1="&#x154;" u2="]" k="35" />
<hkern u1="&#x154;" u2="\" k="12" />
<hkern u1="&#x154;" u2="V" k="20" />
<hkern u1="&#x155;" u2="&#xc6;" k="86" />
<hkern u1="&#x155;" u2="&#x7d;" k="47" />
<hkern u1="&#x155;" u2="]" k="59" />
<hkern u1="&#x155;" u2="\" k="8" />
<hkern u1="&#x155;" u2="X" k="55" />
<hkern u1="&#x155;" u2="&#x2f;" k="70" />
<hkern u1="&#x155;" u2="&#x29;" k="8" />
<hkern u1="&#x155;" u2="&#x26;" k="20" />
<hkern u1="&#x156;" u2="&#xc6;" k="16" />
<hkern u1="&#x156;" u2="&#x7d;" k="31" />
<hkern u1="&#x156;" u2="]" k="35" />
<hkern u1="&#x156;" u2="\" k="12" />
<hkern u1="&#x156;" u2="V" k="20" />
<hkern u1="&#x157;" u2="&#xc6;" k="86" />
<hkern u1="&#x157;" u2="&#x7d;" k="47" />
<hkern u1="&#x157;" u2="]" k="59" />
<hkern u1="&#x157;" u2="\" k="8" />
<hkern u1="&#x157;" u2="X" k="55" />
<hkern u1="&#x157;" u2="&#x2f;" k="70" />
<hkern u1="&#x157;" u2="&#x29;" k="8" />
<hkern u1="&#x157;" u2="&#x26;" k="20" />
<hkern u1="&#x158;" u2="&#xc6;" k="16" />
<hkern u1="&#x158;" u2="&#x7d;" k="31" />
<hkern u1="&#x158;" u2="]" k="35" />
<hkern u1="&#x158;" u2="\" k="12" />
<hkern u1="&#x158;" u2="V" k="20" />
<hkern u1="&#x159;" u2="&#xc6;" k="86" />
<hkern u1="&#x159;" u2="&#x7d;" k="47" />
<hkern u1="&#x159;" u2="]" k="59" />
<hkern u1="&#x159;" u2="\" k="8" />
<hkern u1="&#x159;" u2="X" k="55" />
<hkern u1="&#x159;" u2="&#x2f;" k="70" />
<hkern u1="&#x159;" u2="&#x29;" k="8" />
<hkern u1="&#x159;" u2="&#x26;" k="20" />
<hkern u1="&#x15a;" u2="&#x129;" k="-8" />
<hkern u1="&#x15a;" u2="&#xef;" k="-16" />
<hkern u1="&#x15a;" u2="&#xee;" k="-6" />
<hkern u1="&#x15a;" u2="&#xec;" k="-41" />
<hkern u1="&#x15a;" u2="&#xc6;" k="25" />
<hkern u1="&#x15a;" u2="x" k="18" />
<hkern u1="&#x15a;" u2="v" k="20" />
<hkern u1="&#x15a;" u2="f" k="16" />
<hkern u1="&#x15a;" u2="X" k="10" />
<hkern u1="&#x15a;" u2="V" k="18" />
<hkern u1="&#x15b;" u2="&#x2122;" k="25" />
<hkern u1="&#x15b;" u2="&#xc6;" k="10" />
<hkern u1="&#x15b;" u2="&#x7d;" k="55" />
<hkern u1="&#x15b;" u2="x" k="6" />
<hkern u1="&#x15b;" u2="v" k="14" />
<hkern u1="&#x15b;" u2="]" k="66" />
<hkern u1="&#x15b;" u2="\" k="45" />
<hkern u1="&#x15b;" u2="X" k="6" />
<hkern u1="&#x15b;" u2="V" k="45" />
<hkern u1="&#x15b;" u2="&#x3f;" k="27" />
<hkern u1="&#x15b;" u2="&#x29;" k="10" />
<hkern u1="&#x15c;" u2="&#x129;" k="-8" />
<hkern u1="&#x15c;" u2="&#xef;" k="-16" />
<hkern u1="&#x15c;" u2="&#xee;" k="-6" />
<hkern u1="&#x15c;" u2="&#xec;" k="-41" />
<hkern u1="&#x15c;" u2="&#xc6;" k="25" />
<hkern u1="&#x15c;" u2="x" k="18" />
<hkern u1="&#x15c;" u2="v" k="20" />
<hkern u1="&#x15c;" u2="f" k="16" />
<hkern u1="&#x15c;" u2="X" k="10" />
<hkern u1="&#x15c;" u2="V" k="18" />
<hkern u1="&#x15d;" u2="&#x2122;" k="25" />
<hkern u1="&#x15d;" u2="&#xc6;" k="10" />
<hkern u1="&#x15d;" u2="&#x7d;" k="55" />
<hkern u1="&#x15d;" u2="x" k="6" />
<hkern u1="&#x15d;" u2="v" k="14" />
<hkern u1="&#x15d;" u2="]" k="66" />
<hkern u1="&#x15d;" u2="\" k="45" />
<hkern u1="&#x15d;" u2="X" k="6" />
<hkern u1="&#x15d;" u2="V" k="45" />
<hkern u1="&#x15d;" u2="&#x3f;" k="27" />
<hkern u1="&#x15d;" u2="&#x29;" k="10" />
<hkern u1="&#x15e;" u2="&#x129;" k="-8" />
<hkern u1="&#x15e;" u2="&#xef;" k="-16" />
<hkern u1="&#x15e;" u2="&#xee;" k="-6" />
<hkern u1="&#x15e;" u2="&#xec;" k="-41" />
<hkern u1="&#x15e;" u2="&#xc6;" k="25" />
<hkern u1="&#x15e;" u2="x" k="18" />
<hkern u1="&#x15e;" u2="v" k="20" />
<hkern u1="&#x15e;" u2="f" k="16" />
<hkern u1="&#x15e;" u2="X" k="10" />
<hkern u1="&#x15e;" u2="V" k="18" />
<hkern u1="&#x15f;" u2="&#x2122;" k="25" />
<hkern u1="&#x15f;" u2="&#xc6;" k="10" />
<hkern u1="&#x15f;" u2="&#x7d;" k="55" />
<hkern u1="&#x15f;" u2="x" k="6" />
<hkern u1="&#x15f;" u2="v" k="14" />
<hkern u1="&#x15f;" u2="]" k="66" />
<hkern u1="&#x15f;" u2="\" k="45" />
<hkern u1="&#x15f;" u2="X" k="6" />
<hkern u1="&#x15f;" u2="V" k="45" />
<hkern u1="&#x15f;" u2="&#x3f;" k="27" />
<hkern u1="&#x15f;" u2="&#x29;" k="10" />
<hkern u1="&#x160;" u2="&#x129;" k="-8" />
<hkern u1="&#x160;" u2="&#xef;" k="-16" />
<hkern u1="&#x160;" u2="&#xee;" k="-6" />
<hkern u1="&#x160;" u2="&#xec;" k="-41" />
<hkern u1="&#x160;" u2="&#xc6;" k="25" />
<hkern u1="&#x160;" u2="x" k="18" />
<hkern u1="&#x160;" u2="v" k="20" />
<hkern u1="&#x160;" u2="f" k="16" />
<hkern u1="&#x160;" u2="X" k="10" />
<hkern u1="&#x160;" u2="V" k="18" />
<hkern u1="&#x161;" u2="&#x2122;" k="25" />
<hkern u1="&#x161;" u2="&#xc6;" k="10" />
<hkern u1="&#x161;" u2="&#x7d;" k="55" />
<hkern u1="&#x161;" u2="x" k="6" />
<hkern u1="&#x161;" u2="v" k="14" />
<hkern u1="&#x161;" u2="]" k="66" />
<hkern u1="&#x161;" u2="\" k="45" />
<hkern u1="&#x161;" u2="X" k="6" />
<hkern u1="&#x161;" u2="V" k="45" />
<hkern u1="&#x161;" u2="&#x3f;" k="27" />
<hkern u1="&#x161;" u2="&#x29;" k="10" />
<hkern u1="&#x164;" u2="&#x16d;" k="170" />
<hkern u1="&#x164;" u2="&#x169;" k="166" />
<hkern u1="&#x164;" u2="&#x15d;" k="170" />
<hkern u1="&#x164;" u2="&#x159;" k="100" />
<hkern u1="&#x164;" u2="&#x155;" k="141" />
<hkern u1="&#x164;" u2="&#x151;" k="164" />
<hkern u1="&#x164;" u2="&#x135;" k="-39" />
<hkern u1="&#x164;" u2="&#x131;" k="176" />
<hkern u1="&#x164;" u2="&#x12d;" k="-88" />
<hkern u1="&#x164;" u2="&#x12b;" k="-78" />
<hkern u1="&#x164;" u2="&#x129;" k="-119" />
<hkern u1="&#x164;" u2="&#x11f;" k="193" />
<hkern u1="&#x164;" u2="&#x109;" k="166" />
<hkern u1="&#x164;" u2="&#xef;" k="-84" />
<hkern u1="&#x164;" u2="&#xee;" k="-45" />
<hkern u1="&#x164;" u2="&#xec;" k="-141" />
<hkern u1="&#x164;" u2="&#xe4;" k="168" />
<hkern u1="&#x164;" u2="&#xe3;" k="135" />
<hkern u1="&#x164;" u2="&#xdf;" k="8" />
<hkern u1="&#x164;" u2="&#xc6;" k="111" />
<hkern u1="&#x164;" u2="&#xae;" k="16" />
<hkern u1="&#x164;" u2="x" k="147" />
<hkern u1="&#x164;" u2="v" k="137" />
<hkern u1="&#x164;" u2="f" k="29" />
<hkern u1="&#x164;" u2="&#x40;" k="53" />
<hkern u1="&#x164;" u2="&#x2f;" k="104" />
<hkern u1="&#x164;" u2="&#x26;" k="43" />
<hkern u1="&#x165;" u2="&#x203a;" k="53" />
<hkern u1="&#x165;" u2="&#x2039;" k="119" />
<hkern u1="&#x165;" u2="&#x2026;" k="43" />
<hkern u1="&#x165;" u2="&#x201e;" k="43" />
<hkern u1="&#x165;" u2="&#x201c;" k="-6" />
<hkern u1="&#x165;" u2="&#x201a;" k="43" />
<hkern u1="&#x165;" u2="&#x2018;" k="-6" />
<hkern u1="&#x165;" u2="&#x2014;" k="139" />
<hkern u1="&#x165;" u2="&#x2013;" k="139" />
<hkern u1="&#x165;" u2="&#x21b;" k="-16" />
<hkern u1="&#x165;" u2="&#x1ff;" k="31" />
<hkern u1="&#x165;" u2="&#x177;" k="-20" />
<hkern u1="&#x165;" u2="&#x175;" k="-14" />
<hkern u1="&#x165;" u2="&#x167;" k="-16" />
<hkern u1="&#x165;" u2="&#x165;" k="-16" />
<hkern u1="&#x165;" u2="&#x153;" k="31" />
<hkern u1="&#x165;" u2="&#x151;" k="31" />
<hkern u1="&#x165;" u2="&#x14f;" k="31" />
<hkern u1="&#x165;" u2="&#x14d;" k="31" />
<hkern u1="&#x165;" u2="&#x142;" k="-6" />
<hkern u1="&#x165;" u2="&#x13e;" k="-6" />
<hkern u1="&#x165;" u2="&#x13c;" k="-6" />
<hkern u1="&#x165;" u2="&#x13a;" k="-6" />
<hkern u1="&#x165;" u2="&#x137;" k="-12" />
<hkern u1="&#x165;" u2="&#x135;" k="-12" />
<hkern u1="&#x165;" u2="&#x131;" k="-12" />
<hkern u1="&#x165;" u2="&#x12f;" k="-12" />
<hkern u1="&#x165;" u2="&#x12d;" k="-12" />
<hkern u1="&#x165;" u2="&#x12b;" k="-12" />
<hkern u1="&#x165;" u2="&#x129;" k="-12" />
<hkern u1="&#x165;" u2="&#x127;" k="-12" />
<hkern u1="&#x165;" u2="&#x125;" k="-12" />
<hkern u1="&#x165;" u2="&#x123;" k="18" />
<hkern u1="&#x165;" u2="&#x121;" k="18" />
<hkern u1="&#x165;" u2="&#x11f;" k="18" />
<hkern u1="&#x165;" u2="&#x11d;" k="18" />
<hkern u1="&#x165;" u2="&#x11b;" k="31" />
<hkern u1="&#x165;" u2="&#x119;" k="31" />
<hkern u1="&#x165;" u2="&#x117;" k="31" />
<hkern u1="&#x165;" u2="&#x115;" k="31" />
<hkern u1="&#x165;" u2="&#x113;" k="31" />
<hkern u1="&#x165;" u2="&#x111;" k="33" />
<hkern u1="&#x165;" u2="&#x10f;" k="33" />
<hkern u1="&#x165;" u2="&#x10d;" k="31" />
<hkern u1="&#x165;" u2="&#x10b;" k="31" />
<hkern u1="&#x165;" u2="&#x109;" k="31" />
<hkern u1="&#x165;" u2="&#x107;" k="31" />
<hkern u1="&#x165;" u2="&#xff;" k="-20" />
<hkern u1="&#x165;" u2="&#xfd;" k="-20" />
<hkern u1="&#x165;" u2="&#xf8;" k="31" />
<hkern u1="&#x165;" u2="&#xf6;" k="31" />
<hkern u1="&#x165;" u2="&#xf5;" k="31" />
<hkern u1="&#x165;" u2="&#xf4;" k="31" />
<hkern u1="&#x165;" u2="&#xf3;" k="31" />
<hkern u1="&#x165;" u2="&#xf2;" k="31" />
<hkern u1="&#x165;" u2="&#xef;" k="-12" />
<hkern u1="&#x165;" u2="&#xee;" k="-12" />
<hkern u1="&#x165;" u2="&#xed;" k="-12" />
<hkern u1="&#x165;" u2="&#xec;" k="-12" />
<hkern u1="&#x165;" u2="&#xeb;" k="31" />
<hkern u1="&#x165;" u2="&#xea;" k="31" />
<hkern u1="&#x165;" u2="&#xe9;" k="31" />
<hkern u1="&#x165;" u2="&#xe8;" k="31" />
<hkern u1="&#x165;" u2="&#xe7;" k="31" />
<hkern u1="&#x165;" u2="&#xe4;" k="-8" />
<hkern u1="&#x165;" u2="&#xdf;" k="-12" />
<hkern u1="&#x165;" u2="&#xbb;" k="53" />
<hkern u1="&#x165;" u2="&#xab;" k="119" />
<hkern u1="&#x165;" u2="&#x7d;" k="-6" />
<hkern u1="&#x165;" u2="y" k="-20" />
<hkern u1="&#x165;" u2="x" k="-23" />
<hkern u1="&#x165;" u2="w" k="-14" />
<hkern u1="&#x165;" u2="v" k="-23" />
<hkern u1="&#x165;" u2="t" k="-16" />
<hkern u1="&#x165;" u2="q" k="33" />
<hkern u1="&#x165;" u2="o" k="31" />
<hkern u1="&#x165;" u2="l" k="-6" />
<hkern u1="&#x165;" u2="k" k="-12" />
<hkern u1="&#x165;" u2="j" k="-12" />
<hkern u1="&#x165;" u2="i" k="-12" />
<hkern u1="&#x165;" u2="h" k="-12" />
<hkern u1="&#x165;" u2="g" k="18" />
<hkern u1="&#x165;" u2="f" k="-8" />
<hkern u1="&#x165;" u2="e" k="31" />
<hkern u1="&#x165;" u2="d" k="33" />
<hkern u1="&#x165;" u2="c" k="31" />
<hkern u1="&#x165;" u2="b" k="-12" />
<hkern u1="&#x165;" u2="]" k="-8" />
<hkern u1="&#x165;" u2="\" k="-10" />
<hkern u1="&#x165;" u2="&#x3f;" k="-12" />
<hkern u1="&#x165;" u2="&#x2f;" k="29" />
<hkern u1="&#x165;" u2="&#x2e;" k="43" />
<hkern u1="&#x165;" u2="&#x2d;" k="139" />
<hkern u1="&#x165;" u2="&#x2c;" k="43" />
<hkern u1="&#x165;" u2="&#x2a;" k="-25" />
<hkern u1="&#x166;" u2="&#x16d;" k="170" />
<hkern u1="&#x166;" u2="&#x169;" k="166" />
<hkern u1="&#x166;" u2="&#x15d;" k="170" />
<hkern u1="&#x166;" u2="&#x159;" k="100" />
<hkern u1="&#x166;" u2="&#x155;" k="141" />
<hkern u1="&#x166;" u2="&#x151;" k="164" />
<hkern u1="&#x166;" u2="&#x135;" k="-39" />
<hkern u1="&#x166;" u2="&#x131;" k="176" />
<hkern u1="&#x166;" u2="&#x12d;" k="-88" />
<hkern u1="&#x166;" u2="&#x12b;" k="-78" />
<hkern u1="&#x166;" u2="&#x129;" k="-119" />
<hkern u1="&#x166;" u2="&#x11f;" k="193" />
<hkern u1="&#x166;" u2="&#x109;" k="166" />
<hkern u1="&#x166;" u2="&#xef;" k="-84" />
<hkern u1="&#x166;" u2="&#xee;" k="-45" />
<hkern u1="&#x166;" u2="&#xec;" k="-141" />
<hkern u1="&#x166;" u2="&#xe4;" k="168" />
<hkern u1="&#x166;" u2="&#xe3;" k="135" />
<hkern u1="&#x166;" u2="&#xdf;" k="8" />
<hkern u1="&#x166;" u2="&#xc6;" k="111" />
<hkern u1="&#x166;" u2="&#xae;" k="16" />
<hkern u1="&#x166;" u2="x" k="147" />
<hkern u1="&#x166;" u2="v" k="137" />
<hkern u1="&#x166;" u2="f" k="29" />
<hkern u1="&#x166;" u2="&#x40;" k="53" />
<hkern u1="&#x166;" u2="&#x2f;" k="104" />
<hkern u1="&#x166;" u2="&#x26;" k="43" />
<hkern u1="&#x167;" u2="&#x7d;" k="16" />
<hkern u1="&#x167;" u2="]" k="23" />
<hkern u1="&#x167;" u2="\" k="14" />
<hkern u1="&#x168;" u2="&#xec;" k="-12" />
<hkern u1="&#x168;" u2="&#xc6;" k="18" />
<hkern u1="&#x168;" u2="&#x2f;" k="12" />
<hkern u1="&#x169;" u2="&#x2122;" k="23" />
<hkern u1="&#x169;" u2="&#x7d;" k="47" />
<hkern u1="&#x169;" u2="]" k="51" />
<hkern u1="&#x169;" u2="\" k="47" />
<hkern u1="&#x169;" u2="V" k="53" />
<hkern u1="&#x169;" u2="&#x3f;" k="27" />
<hkern u1="&#x169;" u2="&#x29;" k="8" />
<hkern u1="&#x16a;" u2="&#xec;" k="-12" />
<hkern u1="&#x16a;" u2="&#xc6;" k="18" />
<hkern u1="&#x16a;" u2="&#x2f;" k="12" />
<hkern u1="&#x16b;" u2="&#x2122;" k="23" />
<hkern u1="&#x16b;" u2="&#x7d;" k="47" />
<hkern u1="&#x16b;" u2="]" k="51" />
<hkern u1="&#x16b;" u2="\" k="47" />
<hkern u1="&#x16b;" u2="V" k="53" />
<hkern u1="&#x16b;" u2="&#x3f;" k="27" />
<hkern u1="&#x16b;" u2="&#x29;" k="8" />
<hkern u1="&#x16c;" u2="&#xec;" k="-12" />
<hkern u1="&#x16c;" u2="&#xc6;" k="18" />
<hkern u1="&#x16c;" u2="&#x2f;" k="12" />
<hkern u1="&#x16d;" u2="&#x2122;" k="23" />
<hkern u1="&#x16d;" u2="&#x7d;" k="47" />
<hkern u1="&#x16d;" u2="]" k="51" />
<hkern u1="&#x16d;" u2="\" k="47" />
<hkern u1="&#x16d;" u2="V" k="53" />
<hkern u1="&#x16d;" u2="&#x3f;" k="27" />
<hkern u1="&#x16d;" u2="&#x29;" k="8" />
<hkern u1="&#x16e;" u2="&#xec;" k="-12" />
<hkern u1="&#x16e;" u2="&#xc6;" k="18" />
<hkern u1="&#x16e;" u2="&#x2f;" k="12" />
<hkern u1="&#x16f;" u2="&#x2122;" k="23" />
<hkern u1="&#x16f;" u2="&#x7d;" k="47" />
<hkern u1="&#x16f;" u2="]" k="51" />
<hkern u1="&#x16f;" u2="\" k="47" />
<hkern u1="&#x16f;" u2="V" k="53" />
<hkern u1="&#x16f;" u2="&#x3f;" k="27" />
<hkern u1="&#x16f;" u2="&#x29;" k="8" />
<hkern u1="&#x170;" u2="&#xec;" k="-12" />
<hkern u1="&#x170;" u2="&#xc6;" k="18" />
<hkern u1="&#x170;" u2="&#x2f;" k="12" />
<hkern u1="&#x171;" u2="&#x2122;" k="23" />
<hkern u1="&#x171;" u2="&#x7d;" k="47" />
<hkern u1="&#x171;" u2="]" k="51" />
<hkern u1="&#x171;" u2="\" k="47" />
<hkern u1="&#x171;" u2="V" k="53" />
<hkern u1="&#x171;" u2="&#x3f;" k="27" />
<hkern u1="&#x171;" u2="&#x29;" k="8" />
<hkern u1="&#x172;" u2="&#xec;" k="-12" />
<hkern u1="&#x172;" u2="&#xc6;" k="18" />
<hkern u1="&#x172;" u2="&#x2f;" k="12" />
<hkern u1="&#x173;" u2="&#x2122;" k="23" />
<hkern u1="&#x173;" u2="&#x7d;" k="47" />
<hkern u1="&#x173;" u2="j" k="-37" />
<hkern u1="&#x173;" u2="]" k="51" />
<hkern u1="&#x173;" u2="\" k="47" />
<hkern u1="&#x173;" u2="V" k="53" />
<hkern u1="&#x173;" u2="&#x3f;" k="27" />
<hkern u1="&#x173;" u2="&#x29;" k="8" />
<hkern u1="&#x174;" u2="&#x135;" k="-31" />
<hkern u1="&#x174;" u2="&#x131;" k="31" />
<hkern u1="&#x174;" u2="&#x12d;" k="-53" />
<hkern u1="&#x174;" u2="&#x12b;" k="-37" />
<hkern u1="&#x174;" u2="&#x129;" k="-72" />
<hkern u1="&#x174;" u2="&#xef;" k="-43" />
<hkern u1="&#x174;" u2="&#xee;" k="-25" />
<hkern u1="&#x174;" u2="&#xec;" k="-98" />
<hkern u1="&#x174;" u2="&#xc6;" k="55" />
<hkern u1="&#x174;" u2="&#x2f;" k="55" />
<hkern u1="&#x174;" u2="&#x26;" k="12" />
<hkern u1="&#x175;" u2="&#xc6;" k="37" />
<hkern u1="&#x175;" u2="&#x7d;" k="57" />
<hkern u1="&#x175;" u2="]" k="68" />
<hkern u1="&#x175;" u2="\" k="14" />
<hkern u1="&#x175;" u2="X" k="51" />
<hkern u1="&#x175;" u2="V" k="20" />
<hkern u1="&#x175;" u2="&#x3f;" k="20" />
<hkern u1="&#x175;" u2="&#x2f;" k="35" />
<hkern u1="&#x175;" u2="&#x29;" k="12" />
<hkern u1="&#x176;" u2="&#x159;" k="80" />
<hkern u1="&#x176;" u2="&#x155;" k="94" />
<hkern u1="&#x176;" u2="&#x151;" k="127" />
<hkern u1="&#x176;" u2="&#x142;" k="12" />
<hkern u1="&#x176;" u2="&#x135;" k="-12" />
<hkern u1="&#x176;" u2="&#x131;" k="125" />
<hkern u1="&#x176;" u2="&#x12d;" k="-94" />
<hkern u1="&#x176;" u2="&#x12b;" k="-63" />
<hkern u1="&#x176;" u2="&#x129;" k="-92" />
<hkern u1="&#x176;" u2="&#x103;" k="127" />
<hkern u1="&#x176;" u2="&#xff;" k="70" />
<hkern u1="&#x176;" u2="&#xef;" k="-90" />
<hkern u1="&#x176;" u2="&#xee;" k="-16" />
<hkern u1="&#x176;" u2="&#xec;" k="-131" />
<hkern u1="&#x176;" u2="&#xeb;" k="137" />
<hkern u1="&#x176;" u2="&#xe4;" k="117" />
<hkern u1="&#x176;" u2="&#xe3;" k="109" />
<hkern u1="&#x176;" u2="&#xdf;" k="12" />
<hkern u1="&#x176;" u2="&#xc6;" k="106" />
<hkern u1="&#x176;" u2="&#xae;" k="45" />
<hkern u1="&#x176;" u2="x" k="82" />
<hkern u1="&#x176;" u2="v" k="84" />
<hkern u1="&#x176;" u2="f" k="37" />
<hkern u1="&#x176;" u2="&#x40;" k="74" />
<hkern u1="&#x176;" u2="&#x2f;" k="131" />
<hkern u1="&#x176;" u2="&#x26;" k="76" />
<hkern u1="&#x177;" u2="&#xc6;" k="43" />
<hkern u1="&#x177;" u2="&#x7d;" k="49" />
<hkern u1="&#x177;" u2="]" k="59" />
<hkern u1="&#x177;" u2="\" k="14" />
<hkern u1="&#x177;" u2="X" k="53" />
<hkern u1="&#x177;" u2="V" k="16" />
<hkern u1="&#x177;" u2="&#x3f;" k="18" />
<hkern u1="&#x177;" u2="&#x2f;" k="43" />
<hkern u1="&#x178;" u2="&#x159;" k="80" />
<hkern u1="&#x178;" u2="&#x155;" k="94" />
<hkern u1="&#x178;" u2="&#x151;" k="127" />
<hkern u1="&#x178;" u2="&#x142;" k="12" />
<hkern u1="&#x178;" u2="&#x135;" k="-12" />
<hkern u1="&#x178;" u2="&#x131;" k="125" />
<hkern u1="&#x178;" u2="&#x12d;" k="-94" />
<hkern u1="&#x178;" u2="&#x12b;" k="-63" />
<hkern u1="&#x178;" u2="&#x129;" k="-92" />
<hkern u1="&#x178;" u2="&#x103;" k="127" />
<hkern u1="&#x178;" u2="&#xff;" k="70" />
<hkern u1="&#x178;" u2="&#xef;" k="-90" />
<hkern u1="&#x178;" u2="&#xee;" k="-16" />
<hkern u1="&#x178;" u2="&#xec;" k="-131" />
<hkern u1="&#x178;" u2="&#xeb;" k="137" />
<hkern u1="&#x178;" u2="&#xe4;" k="117" />
<hkern u1="&#x178;" u2="&#xe3;" k="109" />
<hkern u1="&#x178;" u2="&#xdf;" k="12" />
<hkern u1="&#x178;" u2="&#xc6;" k="106" />
<hkern u1="&#x178;" u2="&#xae;" k="45" />
<hkern u1="&#x178;" u2="x" k="82" />
<hkern u1="&#x178;" u2="v" k="84" />
<hkern u1="&#x178;" u2="f" k="37" />
<hkern u1="&#x178;" u2="&#x40;" k="74" />
<hkern u1="&#x178;" u2="&#x2f;" k="131" />
<hkern u1="&#x178;" u2="&#x26;" k="76" />
<hkern u1="&#x179;" u2="&#x135;" k="-31" />
<hkern u1="&#x179;" u2="&#x12d;" k="-23" />
<hkern u1="&#x179;" u2="&#x12b;" k="-20" />
<hkern u1="&#x179;" u2="&#x129;" k="-57" />
<hkern u1="&#x179;" u2="&#xef;" k="-25" />
<hkern u1="&#x179;" u2="&#xee;" k="-31" />
<hkern u1="&#x179;" u2="&#xec;" k="-80" />
<hkern u1="&#x179;" u2="&#xae;" k="14" />
<hkern u1="&#x179;" u2="v" k="25" />
<hkern u1="&#x179;" u2="f" k="6" />
<hkern u1="&#x17a;" u2="&#x2122;" k="10" />
<hkern u1="&#x17a;" u2="&#x7d;" k="35" />
<hkern u1="&#x17a;" u2="]" k="41" />
<hkern u1="&#x17a;" u2="\" k="20" />
<hkern u1="&#x17a;" u2="V" k="27" />
<hkern u1="&#x17a;" u2="&#x3f;" k="16" />
<hkern u1="&#x17b;" u2="&#x135;" k="-31" />
<hkern u1="&#x17b;" u2="&#x12d;" k="-23" />
<hkern u1="&#x17b;" u2="&#x12b;" k="-20" />
<hkern u1="&#x17b;" u2="&#x129;" k="-57" />
<hkern u1="&#x17b;" u2="&#xef;" k="-25" />
<hkern u1="&#x17b;" u2="&#xee;" k="-31" />
<hkern u1="&#x17b;" u2="&#xec;" k="-80" />
<hkern u1="&#x17b;" u2="&#xae;" k="14" />
<hkern u1="&#x17b;" u2="v" k="25" />
<hkern u1="&#x17b;" u2="f" k="6" />
<hkern u1="&#x17c;" u2="&#x2122;" k="10" />
<hkern u1="&#x17c;" u2="&#x7d;" k="35" />
<hkern u1="&#x17c;" u2="]" k="41" />
<hkern u1="&#x17c;" u2="\" k="20" />
<hkern u1="&#x17c;" u2="V" k="27" />
<hkern u1="&#x17c;" u2="&#x3f;" k="16" />
<hkern u1="&#x17d;" u2="&#x135;" k="-31" />
<hkern u1="&#x17d;" u2="&#x12d;" k="-23" />
<hkern u1="&#x17d;" u2="&#x12b;" k="-20" />
<hkern u1="&#x17d;" u2="&#x129;" k="-57" />
<hkern u1="&#x17d;" u2="&#xef;" k="-25" />
<hkern u1="&#x17d;" u2="&#xee;" k="-31" />
<hkern u1="&#x17d;" u2="&#xec;" k="-80" />
<hkern u1="&#x17d;" u2="&#xae;" k="14" />
<hkern u1="&#x17d;" u2="v" k="25" />
<hkern u1="&#x17d;" u2="f" k="6" />
<hkern u1="&#x17e;" u2="&#x2122;" k="10" />
<hkern u1="&#x17e;" u2="&#x7d;" k="35" />
<hkern u1="&#x17e;" u2="]" k="41" />
<hkern u1="&#x17e;" u2="\" k="20" />
<hkern u1="&#x17e;" u2="V" k="27" />
<hkern u1="&#x17e;" u2="&#x3f;" k="16" />
<hkern u1="&#x1fa;" u2="&#x2122;" k="84" />
<hkern u1="&#x1fa;" u2="&#xae;" k="53" />
<hkern u1="&#x1fa;" u2="&#x7d;" k="41" />
<hkern u1="&#x1fa;" u2="v" k="37" />
<hkern u1="&#x1fa;" u2="f" k="14" />
<hkern u1="&#x1fa;" u2="]" k="47" />
<hkern u1="&#x1fa;" u2="\" k="96" />
<hkern u1="&#x1fa;" u2="V" k="57" />
<hkern u1="&#x1fa;" u2="&#x3f;" k="51" />
<hkern u1="&#x1fa;" u2="&#x2a;" k="74" />
<hkern u1="&#x1fb;" u2="&#x2122;" k="25" />
<hkern u1="&#x1fb;" u2="&#x7d;" k="18" />
<hkern u1="&#x1fb;" u2="v" k="10" />
<hkern u1="&#x1fb;" u2="]" k="23" />
<hkern u1="&#x1fb;" u2="\" k="72" />
<hkern u1="&#x1fb;" u2="V" k="51" />
<hkern u1="&#x1fb;" u2="&#x3f;" k="31" />
<hkern u1="&#x1fb;" u2="&#x2a;" k="6" />
<hkern u1="&#x1fc;" u2="&#x135;" k="-16" />
<hkern u1="&#x1fc;" u2="&#x12d;" k="-20" />
<hkern u1="&#x1fc;" u2="&#x12b;" k="-14" />
<hkern u1="&#x1fc;" u2="&#x129;" k="-51" />
<hkern u1="&#x1fc;" u2="&#xef;" k="-27" />
<hkern u1="&#x1fc;" u2="&#xee;" k="-25" />
<hkern u1="&#x1fc;" u2="&#xec;" k="-74" />
<hkern u1="&#x1fc;" u2="v" k="23" />
<hkern u1="&#x1fc;" u2="f" k="6" />
<hkern u1="&#x1fd;" u2="&#x2122;" k="29" />
<hkern u1="&#x1fd;" u2="&#xc6;" k="10" />
<hkern u1="&#x1fd;" u2="&#x7d;" k="55" />
<hkern u1="&#x1fd;" u2="x" k="8" />
<hkern u1="&#x1fd;" u2="v" k="16" />
<hkern u1="&#x1fd;" u2="]" k="51" />
<hkern u1="&#x1fd;" u2="\" k="70" />
<hkern u1="&#x1fd;" u2="V" k="63" />
<hkern u1="&#x1fd;" u2="&#x3f;" k="41" />
<hkern u1="&#x1fd;" u2="&#x29;" k="10" />
<hkern u1="&#x1fe;" u2="&#xc6;" k="31" />
<hkern u1="&#x1fe;" u2="&#x7d;" k="51" />
<hkern u1="&#x1fe;" u2="]" k="57" />
<hkern u1="&#x1fe;" u2="\" k="16" />
<hkern u1="&#x1fe;" u2="X" k="39" />
<hkern u1="&#x1fe;" u2="V" k="25" />
<hkern u1="&#x1fe;" u2="&#x3f;" k="14" />
<hkern u1="&#x1fe;" u2="&#x2f;" k="12" />
<hkern u1="&#x1fe;" u2="&#x29;" k="10" />
<hkern u1="&#x1ff;" u2="&#x2122;" k="31" />
<hkern u1="&#x1ff;" u2="&#xc6;" k="14" />
<hkern u1="&#x1ff;" u2="&#x7d;" k="68" />
<hkern u1="&#x1ff;" u2="x" k="25" />
<hkern u1="&#x1ff;" u2="v" k="18" />
<hkern u1="&#x1ff;" u2="]" k="78" />
<hkern u1="&#x1ff;" u2="\" k="76" />
<hkern u1="&#x1ff;" u2="X" k="43" />
<hkern u1="&#x1ff;" u2="V" k="66" />
<hkern u1="&#x1ff;" u2="&#x3f;" k="47" />
<hkern u1="&#x1ff;" u2="&#x2a;" k="6" />
<hkern u1="&#x1ff;" u2="&#x29;" k="37" />
<hkern u1="&#x218;" u2="&#x129;" k="-8" />
<hkern u1="&#x218;" u2="&#xef;" k="-16" />
<hkern u1="&#x218;" u2="&#xee;" k="-6" />
<hkern u1="&#x218;" u2="&#xec;" k="-41" />
<hkern u1="&#x218;" u2="&#xc6;" k="25" />
<hkern u1="&#x218;" u2="x" k="18" />
<hkern u1="&#x218;" u2="v" k="20" />
<hkern u1="&#x218;" u2="f" k="16" />
<hkern u1="&#x218;" u2="X" k="10" />
<hkern u1="&#x218;" u2="V" k="18" />
<hkern u1="&#x219;" u2="&#x2122;" k="25" />
<hkern u1="&#x219;" u2="&#xc6;" k="10" />
<hkern u1="&#x219;" u2="&#x7d;" k="55" />
<hkern u1="&#x219;" u2="x" k="6" />
<hkern u1="&#x219;" u2="v" k="14" />
<hkern u1="&#x219;" u2="]" k="66" />
<hkern u1="&#x219;" u2="\" k="45" />
<hkern u1="&#x219;" u2="X" k="6" />
<hkern u1="&#x219;" u2="V" k="45" />
<hkern u1="&#x219;" u2="&#x3f;" k="27" />
<hkern u1="&#x219;" u2="&#x29;" k="10" />
<hkern u1="&#x21a;" u2="&#x16d;" k="170" />
<hkern u1="&#x21a;" u2="&#x169;" k="166" />
<hkern u1="&#x21a;" u2="&#x15d;" k="170" />
<hkern u1="&#x21a;" u2="&#x159;" k="100" />
<hkern u1="&#x21a;" u2="&#x155;" k="141" />
<hkern u1="&#x21a;" u2="&#x151;" k="164" />
<hkern u1="&#x21a;" u2="&#x135;" k="-39" />
<hkern u1="&#x21a;" u2="&#x131;" k="176" />
<hkern u1="&#x21a;" u2="&#x12d;" k="-88" />
<hkern u1="&#x21a;" u2="&#x12b;" k="-78" />
<hkern u1="&#x21a;" u2="&#x129;" k="-119" />
<hkern u1="&#x21a;" u2="&#x11f;" k="193" />
<hkern u1="&#x21a;" u2="&#x109;" k="166" />
<hkern u1="&#x21a;" u2="&#xef;" k="-84" />
<hkern u1="&#x21a;" u2="&#xee;" k="-45" />
<hkern u1="&#x21a;" u2="&#xec;" k="-141" />
<hkern u1="&#x21a;" u2="&#xe4;" k="168" />
<hkern u1="&#x21a;" u2="&#xe3;" k="135" />
<hkern u1="&#x21a;" u2="&#xdf;" k="8" />
<hkern u1="&#x21a;" u2="&#xc6;" k="111" />
<hkern u1="&#x21a;" u2="&#xae;" k="16" />
<hkern u1="&#x21a;" u2="x" k="147" />
<hkern u1="&#x21a;" u2="v" k="137" />
<hkern u1="&#x21a;" u2="f" k="29" />
<hkern u1="&#x21a;" u2="&#x40;" k="53" />
<hkern u1="&#x21a;" u2="&#x2f;" k="104" />
<hkern u1="&#x21a;" u2="&#x26;" k="43" />
<hkern u1="&#x21b;" u2="&#x7d;" k="16" />
<hkern u1="&#x21b;" u2="]" k="23" />
<hkern u1="&#x21b;" u2="\" k="14" />
<hkern u1="&#x2013;" u2="&#xc6;" k="35" />
<hkern u1="&#x2013;" u2="x" k="63" />
<hkern u1="&#x2013;" u2="v" k="27" />
<hkern u1="&#x2013;" u2="f" k="25" />
<hkern u1="&#x2013;" u2="X" k="80" />
<hkern u1="&#x2013;" u2="V" k="63" />
<hkern u1="&#x2014;" u2="&#xc6;" k="35" />
<hkern u1="&#x2014;" u2="x" k="63" />
<hkern u1="&#x2014;" u2="v" k="27" />
<hkern u1="&#x2014;" u2="f" k="25" />
<hkern u1="&#x2014;" u2="X" k="80" />
<hkern u1="&#x2014;" u2="V" k="63" />
<hkern u1="&#x2018;" u2="&#x135;" k="-6" />
<hkern u1="&#x2018;" u2="&#x12d;" k="-25" />
<hkern u1="&#x2018;" u2="&#x129;" k="-37" />
<hkern u1="&#x2018;" u2="&#xef;" k="-29" />
<hkern u1="&#x2018;" u2="&#xee;" k="-12" />
<hkern u1="&#x2018;" u2="&#xec;" k="-63" />
<hkern u1="&#x2018;" u2="&#xc6;" k="121" />
<hkern u1="&#x2019;" u2="&#x12d;" k="-37" />
<hkern u1="&#x2019;" u2="&#x12b;" k="-10" />
<hkern u1="&#x2019;" u2="&#x129;" k="-45" />
<hkern u1="&#x2019;" u2="&#xef;" k="-37" />
<hkern u1="&#x2019;" u2="&#xee;" k="-8" />
<hkern u1="&#x2019;" u2="&#xec;" k="-72" />
<hkern u1="&#x2019;" u2="&#xc6;" k="125" />
<hkern u1="&#x2019;" u2="&#x40;" k="49" />
<hkern u1="&#x2019;" u2="&#x2f;" k="145" />
<hkern u1="&#x2019;" u2="&#x26;" k="63" />
<hkern u1="&#x201a;" u2="v" k="72" />
<hkern u1="&#x201a;" u2="f" k="20" />
<hkern u1="&#x201a;" u2="V" k="109" />
<hkern u1="&#x201c;" u2="&#x135;" k="-6" />
<hkern u1="&#x201c;" u2="&#x12d;" k="-25" />
<hkern u1="&#x201c;" u2="&#x129;" k="-37" />
<hkern u1="&#x201c;" u2="&#xef;" k="-29" />
<hkern u1="&#x201c;" u2="&#xee;" k="-12" />
<hkern u1="&#x201c;" u2="&#xec;" k="-63" />
<hkern u1="&#x201c;" u2="&#xc6;" k="121" />
<hkern u1="&#x201d;" u2="&#x135;" k="-6" />
<hkern u1="&#x201d;" u2="&#x12d;" k="-37" />
<hkern u1="&#x201d;" u2="&#x12b;" k="-10" />
<hkern u1="&#x201d;" u2="&#x129;" k="-45" />
<hkern u1="&#x201d;" u2="&#xef;" k="-37" />
<hkern u1="&#x201d;" u2="&#xee;" k="-8" />
<hkern u1="&#x201d;" u2="&#xec;" k="-72" />
<hkern u1="&#x201d;" u2="&#xc6;" k="125" />
<hkern u1="&#x201d;" u2="&#x40;" k="49" />
<hkern u1="&#x201d;" u2="&#x2f;" k="145" />
<hkern u1="&#x201d;" u2="&#x26;" k="63" />
<hkern u1="&#x201e;" u2="v" k="72" />
<hkern u1="&#x201e;" u2="f" k="20" />
<hkern u1="&#x201e;" u2="V" k="109" />
<hkern u1="&#x2039;" u2="V" k="39" />
<hkern u1="&#x203a;" u2="&#x141;" k="-6" />
<hkern u1="&#x203a;" u2="&#xc6;" k="14" />
<hkern u1="&#x203a;" u2="x" k="53" />
<hkern u1="&#x203a;" u2="v" k="16" />
<hkern u1="&#x203a;" u2="f" k="8" />
<hkern u1="&#x203a;" u2="X" k="49" />
<hkern u1="&#x203a;" u2="V" k="57" />
<hkern u1="&#x2122;" u2="&#x1fc;" k="57" />
<hkern u1="&#x2122;" u2="&#x1fa;" k="57" />
<hkern u1="&#x2122;" u2="&#x17d;" k="10" />
<hkern u1="&#x2122;" u2="&#x17b;" k="10" />
<hkern u1="&#x2122;" u2="&#x179;" k="10" />
<hkern u1="&#x2122;" u2="&#x135;" k="-35" />
<hkern u1="&#x2122;" u2="&#x134;" k="45" />
<hkern u1="&#x2122;" u2="&#x12d;" k="-12" />
<hkern u1="&#x2122;" u2="&#x129;" k="-25" />
<hkern u1="&#x2122;" u2="&#x104;" k="57" />
<hkern u1="&#x2122;" u2="&#x102;" k="57" />
<hkern u1="&#x2122;" u2="&#x100;" k="57" />
<hkern u1="&#x2122;" u2="&#xef;" k="-29" />
<hkern u1="&#x2122;" u2="&#xee;" k="-37" />
<hkern u1="&#x2122;" u2="&#xec;" k="-37" />
<hkern u1="&#x2122;" u2="&#xc6;" k="72" />
<hkern u1="&#x2122;" u2="&#xc5;" k="57" />
<hkern u1="&#x2122;" u2="&#xc4;" k="57" />
<hkern u1="&#x2122;" u2="&#xc3;" k="57" />
<hkern u1="&#x2122;" u2="&#xc2;" k="57" />
<hkern u1="&#x2122;" u2="&#xc1;" k="57" />
<hkern u1="&#x2122;" u2="&#xc0;" k="57" />
<hkern u1="&#x2122;" u2="Z" k="10" />
<hkern u1="&#x2122;" u2="J" k="45" />
<hkern u1="&#x2122;" u2="A" k="57" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	k="16" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="J,Jcircumflex" 	k="8" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="23" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="d,q,dcaron,dcroat" 	k="20" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="31" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="guillemotleft,guilsinglleft" 	k="31" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="hyphen,endash,emdash" 	k="63" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="20" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="t,tcaron,tbar,uni021B" 	k="10" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="w,wcircumflex" 	k="33" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="y,yacute,ydieresis,ycircumflex" 	k="31" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="guillemotright,guilsinglright" 	k="10" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron" 	k="8" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="23" />
<hkern g1="D,Dcaron,Dcroat" 	g2="J,Jcircumflex" 	k="41" />
<hkern g1="D,Dcaron,Dcroat" 	g2="T,Tcaron,Tbar,uni021A" 	k="47" />
<hkern g1="D,Dcaron,Dcroat" 	g2="W,Wcircumflex" 	k="6" />
<hkern g1="D,Dcaron,Dcroat" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="59" />
<hkern g1="D,Dcaron,Dcroat" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="29" />
<hkern g1="D,Dcaron,Dcroat" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="14" />
<hkern g1="D,Dcaron,Dcroat" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	k="6" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="d,q,dcaron,dcroat" 	k="18" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="29" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="guillemotleft,guilsinglleft" 	k="25" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="hyphen,endash,emdash" 	k="43" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="18" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="t,tcaron,tbar,uni021B" 	k="12" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="w,wcircumflex" 	k="27" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="y,yacute,ydieresis,ycircumflex" 	k="23" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="16" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent" 	g2="J,Jcircumflex" 	k="20" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="35" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="12" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent" 	g2="t,tcaron,tbar,uni021B" 	k="10" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent" 	g2="w,wcircumflex" 	k="14" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent" 	g2="y,yacute,ydieresis,ycircumflex" 	k="14" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng" 	g2="J,Jcircumflex" 	k="12" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng" 	g2="d,q,dcaron,dcroat" 	k="10" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="20" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="10" />
<hkern g1="J,Jcircumflex" 	g2="d,q,dcaron,dcroat" 	k="10" />
<hkern g1="J,Jcircumflex" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="16" />
<hkern g1="J,Jcircumflex" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="10" />
<hkern g1="K,Kcommaaccent" 	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	k="31" />
<hkern g1="K,Kcommaaccent" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="35" />
<hkern g1="K,Kcommaaccent" 	g2="d,q,dcaron,dcroat" 	k="29" />
<hkern g1="K,Kcommaaccent" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="25" />
<hkern g1="K,Kcommaaccent" 	g2="guillemotleft,guilsinglleft" 	k="45" />
<hkern g1="K,Kcommaaccent" 	g2="hyphen,endash,emdash" 	k="74" />
<hkern g1="K,Kcommaaccent" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="31" />
<hkern g1="K,Kcommaaccent" 	g2="t,tcaron,tbar,uni021B" 	k="23" />
<hkern g1="K,Kcommaaccent" 	g2="w,wcircumflex" 	k="51" />
<hkern g1="K,Kcommaaccent" 	g2="y,yacute,ydieresis,ycircumflex" 	k="51" />
<hkern g1="K,Kcommaaccent" 	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron" 	k="6" />
<hkern g1="K,Kcommaaccent" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="27" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	k="33" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="41" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="T,Tcaron,Tbar,uni021A" 	k="195" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="27" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="W,Wcircumflex" 	k="92" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="182" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="d,q,dcaron,dcroat" 	k="10" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="guillemotleft,guilsinglleft" 	k="80" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="hyphen,endash,emdash" 	k="150" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="10" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="quoteleft,quotedblleft" 	k="184" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="quoteright,quotedblright" 	k="182" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="quotedbl,quotesingle" 	k="184" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="t,tcaron,tbar,uni021B" 	k="37" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="w,wcircumflex" 	k="72" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="y,yacute,ydieresis,ycircumflex" 	k="94" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="guillemotright,guilsinglright" 	k="29" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="10" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="J,Jcircumflex" 	k="39" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="T,Tcaron,Tbar,uni021A" 	k="41" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="W,Wcircumflex" 	k="6" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="57" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="25" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="10" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="23" />
<hkern g1="R,Racute,Rcommaaccent,Rcaron" 	g2="J,Jcircumflex" 	k="10" />
<hkern g1="R,Racute,Rcommaaccent,Rcaron" 	g2="T,Tcaron,Tbar,uni021A" 	k="20" />
<hkern g1="R,Racute,Rcommaaccent,Rcaron" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="43" />
<hkern g1="R,Racute,Rcommaaccent,Rcaron" 	g2="d,q,dcaron,dcroat" 	k="18" />
<hkern g1="R,Racute,Rcommaaccent,Rcaron" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="16" />
<hkern g1="R,Racute,Rcommaaccent,Rcaron" 	g2="guillemotleft,guilsinglleft" 	k="29" />
<hkern g1="R,Racute,Rcommaaccent,Rcaron" 	g2="hyphen,endash,emdash" 	k="20" />
<hkern g1="R,Racute,Rcommaaccent,Rcaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="18" />
<hkern g1="R,Racute,Rcommaaccent,Rcaron" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="6" />
<hkern g1="R,Racute,Rcommaaccent,Rcaron" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="14" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	g2="J,Jcircumflex" 	k="29" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="31" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="16" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	g2="t,tcaron,tbar,uni021B" 	k="18" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	g2="w,wcircumflex" 	k="18" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	g2="y,yacute,ydieresis,ycircumflex" 	k="20" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="20" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	g2="z,zacute,zdotaccent,zcaron" 	k="6" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	k="29" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="J,Jcircumflex" 	k="45" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="41" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="d,q,dcaron,dcroat" 	k="184" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="207" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="guillemotleft,guilsinglleft" 	k="125" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="hyphen,endash,emdash" 	k="131" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="186" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="t,tcaron,tbar,uni021B" 	k="53" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="w,wcircumflex" 	k="141" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="y,yacute,ydieresis,ycircumflex" 	k="139" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="guillemotright,guilsinglright" 	k="119" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron" 	k="176" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="178" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="96" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="129" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="z,zacute,zdotaccent,zcaron" 	k="176" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute" 	k="182" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="b" 	k="14" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="colon,semicolon" 	k="113" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	k="180" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="J,Jcircumflex" 	k="33" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="d,q,dcaron,dcroat" 	k="10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="23" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="14" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="25" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	k="10" />
<hkern g1="W,Wcircumflex" 	g2="J,Jcircumflex" 	k="39" />
<hkern g1="W,Wcircumflex" 	g2="d,q,dcaron,dcroat" 	k="41" />
<hkern g1="W,Wcircumflex" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="49" />
<hkern g1="W,Wcircumflex" 	g2="guillemotleft,guilsinglleft" 	k="35" />
<hkern g1="W,Wcircumflex" 	g2="hyphen,endash,emdash" 	k="39" />
<hkern g1="W,Wcircumflex" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="41" />
<hkern g1="W,Wcircumflex" 	g2="guillemotright,guilsinglright" 	k="16" />
<hkern g1="W,Wcircumflex" 	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron" 	k="31" />
<hkern g1="W,Wcircumflex" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="25" />
<hkern g1="W,Wcircumflex" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="45" />
<hkern g1="W,Wcircumflex" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="74" />
<hkern g1="W,Wcircumflex" 	g2="z,zacute,zdotaccent,zcaron" 	k="6" />
<hkern g1="W,Wcircumflex" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute" 	k="31" />
<hkern g1="W,Wcircumflex" 	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	k="31" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	k="55" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="J,Jcircumflex" 	k="78" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="59" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	k="37" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="d,q,dcaron,dcroat" 	k="145" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="154" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="135" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="hyphen,endash,emdash" 	k="145" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="143" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="t,tcaron,tbar,uni021B" 	k="35" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="w,wcircumflex" 	k="92" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="y,yacute,ydieresis,ycircumflex" 	k="86" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="guillemotright,guilsinglright" 	k="102" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron" 	k="125" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="121" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="98" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="152" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="z,zacute,zdotaccent,zcaron" 	k="98" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute" 	k="133" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="colon,semicolon" 	k="88" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	k="143" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	k="10" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="10" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="d,q,dcaron,dcroat" 	k="23" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="31" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="guillemotleft,guilsinglleft" 	k="33" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="hyphen,endash,emdash" 	k="66" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="20" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="t,tcaron,tbar,uni021B" 	k="8" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="w,wcircumflex" 	k="27" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="y,yacute,ydieresis,ycircumflex" 	k="25" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="guillemotright,guilsinglright" 	k="10" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron" 	k="12" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="23" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute" 	g2="T,Tcaron,Tbar,uni021A" 	k="162" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="8" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute" 	g2="W,Wcircumflex" 	k="29" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="129" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute" 	g2="quotedbl,quotesingle" 	k="8" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute" 	g2="y,yacute,ydieresis,ycircumflex" 	k="10" />
<hkern g1="b,p" 	g2="J,Jcircumflex" 	k="53" />
<hkern g1="b,p" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	k="14" />
<hkern g1="b,p" 	g2="T,Tcaron,Tbar,uni021A" 	k="186" />
<hkern g1="b,p" 	g2="W,Wcircumflex" 	k="39" />
<hkern g1="b,p" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="139" />
<hkern g1="b,p" 	g2="quoteleft,quotedblleft" 	k="31" />
<hkern g1="b,p" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="b,p" 	g2="quotedbl,quotesingle" 	k="33" />
<hkern g1="b,p" 	g2="t,tcaron,tbar,uni021B" 	k="6" />
<hkern g1="b,p" 	g2="w,wcircumflex" 	k="10" />
<hkern g1="b,p" 	g2="y,yacute,ydieresis,ycircumflex" 	k="18" />
<hkern g1="b,p" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="12" />
<hkern g1="b,p" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="16" />
<hkern g1="b,p" 	g2="z,zacute,zdotaccent,zcaron" 	k="6" />
<hkern g1="b,p" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron" 	k="10" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="J,Jcircumflex" 	k="14" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="T,Tcaron,Tbar,uni021A" 	k="209" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="100" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="d,q,dcaron,dcroat" 	k="10" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="10" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="guillemotleft,guilsinglleft" 	k="63" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="hyphen,endash,emdash" 	k="82" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="12" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="guillemotright,guilsinglright" 	k="14" />
<hkern g1="colon,semicolon" 	g2="T,Tcaron,Tbar,uni021A" 	k="113" />
<hkern g1="colon,semicolon" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="88" />
<hkern g1="d,dcroat" 	g2="J,Jcircumflex" 	k="35" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="J,Jcircumflex" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	k="8" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="T,Tcaron,Tbar,uni021A" 	k="188" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="W,Wcircumflex" 	k="35" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="172" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="quotedbl,quotesingle" 	k="27" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="t,tcaron,tbar,uni021B" 	k="6" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="w,wcircumflex" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="y,yacute,ydieresis,ycircumflex" 	k="18" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="10" />
<hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	g2="T,Tcaron,Tbar,uni021A" 	k="150" />
<hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="66" />
<hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	g2="guillemotleft,guilsinglleft" 	k="8" />
<hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	g2="hyphen,endash,emdash" 	k="25" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="J,Jcircumflex" 	k="37" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T,Tcaron,Tbar,uni021A" 	k="121" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="W,Wcircumflex" 	k="16" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="104" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="quoteright,quotedblright" 	k="72" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="quotedbl,quotesingle" 	k="76" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="t,tcaron,tbar,uni021B" 	k="16" />
<hkern g1="guillemotright,guilsinglright" 	g2="J,Jcircumflex" 	k="39" />
<hkern g1="guillemotright,guilsinglright" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	k="31" />
<hkern g1="guillemotright,guilsinglright" 	g2="T,Tcaron,Tbar,uni021A" 	k="125" />
<hkern g1="guillemotright,guilsinglright" 	g2="W,Wcircumflex" 	k="35" />
<hkern g1="guillemotright,guilsinglright" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="135" />
<hkern g1="guillemotright,guilsinglright" 	g2="quoteright,quotedblright" 	k="125" />
<hkern g1="guillemotright,guilsinglright" 	g2="quotedbl,quotesingle" 	k="133" />
<hkern g1="guillemotright,guilsinglright" 	g2="t,tcaron,tbar,uni021B" 	k="14" />
<hkern g1="guillemotright,guilsinglright" 	g2="w,wcircumflex" 	k="12" />
<hkern g1="guillemotright,guilsinglright" 	g2="y,yacute,ydieresis,ycircumflex" 	k="25" />
<hkern g1="guillemotright,guilsinglright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="10" />
<hkern g1="guillemotright,guilsinglright" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="23" />
<hkern g1="guillemotright,guilsinglright" 	g2="z,zacute,zdotaccent,zcaron" 	k="53" />
<hkern g1="hyphen,endash,emdash" 	g2="J,Jcircumflex" 	k="59" />
<hkern g1="hyphen,endash,emdash" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	k="55" />
<hkern g1="hyphen,endash,emdash" 	g2="T,Tcaron,Tbar,uni021A" 	k="131" />
<hkern g1="hyphen,endash,emdash" 	g2="W,Wcircumflex" 	k="39" />
<hkern g1="hyphen,endash,emdash" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="145" />
<hkern g1="hyphen,endash,emdash" 	g2="quoteright,quotedblright" 	k="141" />
<hkern g1="hyphen,endash,emdash" 	g2="quotedbl,quotesingle" 	k="152" />
<hkern g1="hyphen,endash,emdash" 	g2="t,tcaron,tbar,uni021B" 	k="31" />
<hkern g1="hyphen,endash,emdash" 	g2="w,wcircumflex" 	k="16" />
<hkern g1="hyphen,endash,emdash" 	g2="y,yacute,ydieresis,ycircumflex" 	k="29" />
<hkern g1="hyphen,endash,emdash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="20" />
<hkern g1="hyphen,endash,emdash" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="57" />
<hkern g1="hyphen,endash,emdash" 	g2="z,zacute,zdotaccent,zcaron" 	k="76" />
<hkern g1="hyphen,endash,emdash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute" 	k="16" />
<hkern g1="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,jcircumflex" 	g2="J,Jcircumflex" 	k="35" />
<hkern g1="k,kcommaaccent" 	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	k="6" />
<hkern g1="k,kcommaaccent" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="8" />
<hkern g1="k,kcommaaccent" 	g2="T,Tcaron,Tbar,uni021A" 	k="154" />
<hkern g1="k,kcommaaccent" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="84" />
<hkern g1="k,kcommaaccent" 	g2="d,q,dcaron,dcroat" 	k="25" />
<hkern g1="k,kcommaaccent" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="20" />
<hkern g1="k,kcommaaccent" 	g2="guillemotleft,guilsinglleft" 	k="55" />
<hkern g1="k,kcommaaccent" 	g2="hyphen,endash,emdash" 	k="74" />
<hkern g1="k,kcommaaccent" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="27" />
<hkern g1="k,kcommaaccent" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute" 	k="6" />
<hkern g1="l,lacute,lcommaaccent,lslash" 	g2="J,Jcircumflex" 	k="35" />
<hkern g1="dcaron,lcaron" 	g2="d,q,dcaron,dcroat" 	k="10" />
<hkern g1="dcaron,lcaron" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="8" />
<hkern g1="dcaron,lcaron" 	g2="guillemotleft,guilsinglleft" 	k="37" />
<hkern g1="dcaron,lcaron" 	g2="hyphen,endash,emdash" 	k="45" />
<hkern g1="dcaron,lcaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="12" />
<hkern g1="dcaron,lcaron" 	g2="quotedbl,quotesingle" 	k="-35" />
<hkern g1="dcaron,lcaron" 	g2="t,tcaron,tbar,uni021B" 	k="-29" />
<hkern g1="dcaron,lcaron" 	g2="w,wcircumflex" 	k="-10" />
<hkern g1="dcaron,lcaron" 	g2="y,yacute,ydieresis,ycircumflex" 	k="-27" />
<hkern g1="dcaron,lcaron" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="43" />
<hkern g1="dcaron,lcaron" 	g2="b" 	k="-90" />
<hkern g1="dcaron,lcaron" 	g2="h,k,germandbls,hcircumflex,hbar,kcommaaccent" 	k="-90" />
<hkern g1="dcaron,lcaron" 	g2="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,jcircumflex" 	k="-90" />
<hkern g1="dcaron,lcaron" 	g2="l,lacute,lcommaaccent,lcaron,lslash" 	k="-78" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="J,Jcircumflex" 	k="37" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	k="12" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="T,Tcaron,Tbar,uni021A" 	k="195" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="12" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="W,Wcircumflex" 	k="37" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="141" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="quoteleft,quotedblleft" 	k="16" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="quoteright,quotedblright" 	k="16" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="quotedbl,quotesingle" 	k="25" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="t,tcaron,tbar,uni021B" 	k="6" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="w,wcircumflex" 	k="10" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="y,yacute,ydieresis,ycircumflex" 	k="12" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="6" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="J,Jcircumflex" 	k="53" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	k="16" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="T,Tcaron,Tbar,uni021A" 	k="190" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="10" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="W,Wcircumflex" 	k="41" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="143" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="quoteright,quotedblright" 	k="23" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="quotedbl,quotesingle" 	k="31" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="t,tcaron,tbar,uni021B" 	k="8" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="w,wcircumflex" 	k="10" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="y,yacute,ydieresis,ycircumflex" 	k="20" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="12" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="16" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="z,zacute,zdotaccent,zcaron" 	k="6" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron" 	k="10" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	k="23" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="23" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="T,Tcaron,Tbar,uni021A" 	k="131" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="25" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="W,Wcircumflex" 	k="74" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="152" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="hyphen,endash,emdash" 	k="70" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="quoteleft,quotedblleft" 	k="291" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="quoteright,quotedblright" 	k="295" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="quotedbl,quotesingle" 	k="301" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="t,tcaron,tbar,uni021B" 	k="39" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="w,wcircumflex" 	k="57" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="y,yacute,ydieresis,ycircumflex" 	k="74" />
<hkern g1="quoteleft,quotedblleft" 	g2="J,Jcircumflex" 	k="45" />
<hkern g1="quoteleft,quotedblleft" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="10" />
<hkern g1="quoteleft,quotedblleft" 	g2="d,q,dcaron,dcroat" 	k="61" />
<hkern g1="quoteleft,quotedblleft" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="35" />
<hkern g1="quoteleft,quotedblleft" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="41" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="100" />
<hkern g1="quoteleft,quotedblleft" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="311" />
<hkern g1="quoteleft,quotedblleft" 	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	k="29" />
<hkern g1="quoteright,quotedblright" 	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	k="10" />
<hkern g1="quoteright,quotedblright" 	g2="J,Jcircumflex" 	k="45" />
<hkern g1="quoteright,quotedblright" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="18" />
<hkern g1="quoteright,quotedblright" 	g2="d,q,dcaron,dcroat" 	k="70" />
<hkern g1="quoteright,quotedblright" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="43" />
<hkern g1="quoteright,quotedblright" 	g2="guillemotleft,guilsinglleft" 	k="162" />
<hkern g1="quoteright,quotedblright" 	g2="hyphen,endash,emdash" 	k="168" />
<hkern g1="quoteright,quotedblright" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="49" />
<hkern g1="quoteright,quotedblright" 	g2="guillemotright,guilsinglright" 	k="94" />
<hkern g1="quoteright,quotedblright" 	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron" 	k="6" />
<hkern g1="quoteright,quotedblright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="102" />
<hkern g1="quoteright,quotedblright" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="322" />
<hkern g1="quoteright,quotedblright" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute" 	k="10" />
<hkern g1="quoteright,quotedblright" 	g2="colon,semicolon" 	k="10" />
<hkern g1="quoteright,quotedblright" 	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	k="35" />
<hkern g1="quotedbl,quotesingle" 	g2="J,Jcircumflex" 	k="47" />
<hkern g1="quotedbl,quotesingle" 	g2="d,q,dcaron,dcroat" 	k="41" />
<hkern g1="quotedbl,quotesingle" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="25" />
<hkern g1="quotedbl,quotesingle" 	g2="guillemotleft,guilsinglleft" 	k="135" />
<hkern g1="quotedbl,quotesingle" 	g2="hyphen,endash,emdash" 	k="154" />
<hkern g1="quotedbl,quotesingle" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="31" />
<hkern g1="quotedbl,quotesingle" 	g2="guillemotright,guilsinglright" 	k="76" />
<hkern g1="quotedbl,quotesingle" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="88" />
<hkern g1="quotedbl,quotesingle" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="303" />
<hkern g1="quotedbl,quotesingle" 	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	k="10" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="J,Jcircumflex" 	k="57" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="T,Tcaron,Tbar,uni021A" 	k="141" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="51" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="d,q,dcaron,dcroat" 	k="27" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="14" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="guillemotleft,guilsinglleft" 	k="86" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="hyphen,endash,emdash" 	k="94" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="20" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="guillemotright,guilsinglright" 	k="45" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="68" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="33" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="113" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	g2="J,Jcircumflex" 	k="45" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	g2="T,Tcaron,Tbar,uni021A" 	k="182" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	g2="W,Wcircumflex" 	k="25" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="115" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="10" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	g2="hyphen,endash,emdash" 	k="20" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	g2="w,wcircumflex" 	k="8" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	g2="y,yacute,ydieresis,ycircumflex" 	k="16" />
<hkern g1="t,tbar,uni021B" 	g2="T,Tcaron,Tbar,uni021A" 	k="154" />
<hkern g1="t,tbar,uni021B" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="59" />
<hkern g1="t,tbar,uni021B" 	g2="guillemotleft,guilsinglleft" 	k="61" />
<hkern g1="t,tbar,uni021B" 	g2="hyphen,endash,emdash" 	k="47" />
<hkern g1="t,tbar,uni021B" 	g2="guillemotright,guilsinglright" 	k="35" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="J,Jcircumflex" 	k="35" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="T,Tcaron,Tbar,uni021A" 	k="178" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="W,Wcircumflex" 	k="31" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="125" />
<hkern g1="w,wcircumflex" 	g2="J,Jcircumflex" 	k="53" />
<hkern g1="w,wcircumflex" 	g2="T,Tcaron,Tbar,uni021A" 	k="141" />
<hkern g1="w,wcircumflex" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="92" />
<hkern g1="w,wcircumflex" 	g2="d,q,dcaron,dcroat" 	k="10" />
<hkern g1="w,wcircumflex" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="14" />
<hkern g1="w,wcircumflex" 	g2="guillemotleft,guilsinglleft" 	k="12" />
<hkern g1="w,wcircumflex" 	g2="hyphen,endash,emdash" 	k="16" />
<hkern g1="w,wcircumflex" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="10" />
<hkern g1="w,wcircumflex" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="33" />
<hkern g1="w,wcircumflex" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="33" />
<hkern g1="w,wcircumflex" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="57" />
<hkern g1="w,wcircumflex" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute" 	k="10" />
<hkern g1="w,wcircumflex" 	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	k="8" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="J,Jcircumflex" 	k="53" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="T,Tcaron,Tbar,uni021A" 	k="137" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="84" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="d,q,dcaron,dcroat" 	k="18" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="20" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="guillemotleft,guilsinglleft" 	k="25" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="hyphen,endash,emdash" 	k="29" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="18" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="37" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="33" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="74" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute" 	k="18" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	k="14" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="J,Jcircumflex" 	k="14" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="T,Tcaron,Tbar,uni021A" 	k="176" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="W,Wcircumflex" 	k="12" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="104" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="d,q,dcaron,dcroat" 	k="8" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="guillemotleft,guilsinglleft" 	k="53" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="hyphen,endash,emdash" 	k="66" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="6" />
</font>
</defs></svg> 