/*! jQuery UI - v1.10.4 - 2014-06-03
* http://jqueryui.com
* Includes: jquery.ui.theme.css
* To view and modify this theme, visit http://jqueryui.com/themeroller/?ffDefault=Lucida%20Grande%2CLucida%20Sans%2CArial%2Csans-serif&fwDefault=normal&fsDefault=1.1em&cornerRadius=10px&bgColorHeader=3a8104&bgTextureHeader=highlight_soft&bgImgOpacityHeader=33&borderColorHeader=3f7506&fcHeader=ffffff&iconColorHeader=ffffff&bgColorContent=285c00&bgTextureContent=inset_soft&bgImgOpacityContent=10&borderColorContent=72b42d&fcContent=ffffff&iconColorContent=72b42d&bgColorDefault=4ca20b&bgTextureDefault=highlight_soft&bgImgOpacityDefault=60&borderColorDefault=45930b&fcDefault=ffffff&iconColorDefault=ffffff&bgColorHover=4eb305&bgTextureHover=highlight_soft&bgImgOpacityHover=50&borderColorHover=8bd83b&fcHover=ffffff&iconColorHover=ffffff&bgColorActive=285c00&bgTextureActive=highlight_hard&bgImgOpacityActive=30&borderColorActive=72b42d&fcActive=ffffff&iconColorActive=ffffff&bgColorHighlight=fbf5d0&bgTextureHighlight=glass&bgImgOpacityHighlight=55&borderColorHighlight=f9dd34&fcHighlight=363636&iconColorHighlight=4eb305&bgColorError=ffdc2e&bgTextureError=diagonals_thick&bgImgOpacityError=95&borderColorError=fad000&fcError=2b2b2b&iconColorError=cd0a0a&bgColorOverlay=444444&bgTextureOverlay=diagonals_thick&bgImgOpacityOverlay=15&opacityOverlay=30&bgColorShadow=aaaaaa&bgTextureShadow=diagonals_small&bgImgOpacityShadow=0&opacityShadow=30&thicknessShadow=0px&offsetTopShadow=4px&offsetLeftShadow=4px&cornerRadiusShadow=4px
* Copyright 2014 jQuery Foundation and other contributors; Licensed MIT */

/* FONT-FACE*/
@font-face {
    font-family: 'titillium_webregular';
    src: url("#{resource['primefaces-sentinel:fonts/titilliumweb-regular-webfont.eot']}");
    src: url("#{resource['primefaces-sentinel:fonts/titilliumweb-regular-webfont.eot']}#iefix") format('embedded-opentype'),
         url("#{resource['primefaces-sentinel:fonts/titilliumweb-regular-webfont.woff']}") format('woff'),
         url("#{resource['primefaces-sentinel:fonts/titilliumweb-regular-webfont.ttf']}") format('truetype'),
         url("#{resource['primefaces-sentinel:fonts/titilliumweb-regular-webfont.svg']}#titillium_webregular") format('svg');
    font-weight: normal;
    font-style: normal;

}

@font-face {
    font-family: 'titillium_websemibold';
    src: url("#{resource['primefaces-sentinel:fonts/titilliumweb-semibold-webfont.eot']}");
    src: url("#{resource['primefaces-sentinel:fonts/titilliumweb-semibold-webfont.eot']}#iefix") format('embedded-opentype'),
         url("#{resource['primefaces-sentinel:fonts/titilliumweb-semibold-webfont.woff']}") format('woff'),
         url("#{resource['primefaces-sentinel:fonts/titilliumweb-semibold-webfont.ttf']}") format('truetype'),
         url("#{resource['primefaces-sentinel:fonts/titilliumweb-semibold-webfont.svg']}#titillium_websemibold") format('svg');
    font-weight: normal;
    font-style: normal;

}

@font-face {
    font-family: 'titillium_webbold';
    src: url("#{resource['primefaces-sentinel:fonts/titilliumweb-bold-webfont.eot']}");
    src: url("#{resource['primefaces-sentinel:fonts/titilliumweb-bold-webfont.eot']}#iefix") format('embedded-opentype'),
         url("#{resource['primefaces-sentinel:fonts/titilliumweb-bold-webfont.woff']}") format('woff'),
         url("#{resource['primefaces-sentinel:fonts/titilliumweb-bold-webfont.ttf']}") format('truetype'),
         url("#{resource['primefaces-sentinel:fonts/titilliumweb-bold-webfont.svg']}#titillium_webbold") format('svg');
    font-weight: normal;
    font-style: normal;

}

@font-face {
    font-family: 'titillium_webbold_italic';
    src: url("#{resource['primefaces-sentinel:fonts/titilliumweb-bolditalic-webfont.eot']}");
    src: url("#{resource['primefaces-sentinel:fonts/titilliumweb-bolditalic-webfont.eot']}#iefix") format('embedded-opentype'),
         url("#{resource['primefaces-sentinel:fonts/titilliumweb-bolditalic-webfont.woff']}") format('woff'),
         url("#{resource['primefaces-sentinel:fonts/titilliumweb-bolditalic-webfont.ttf']}") format('truetype'),
         url("#{resource['primefaces-sentinel:fonts/titilliumweb-bolditalic-webfont.svg']}#titillium_webbold_italic") format('svg');
    font-weight: normal;
    font-style: normal;

}

@font-face {
    font-family: 'titillium_websemibold_italic';
    src: url("#{resource['primefaces-sentinel:fonts/titilliumweb-semibolditalic-webfont.eot']}");
    src: url("#{resource['primefaces-sentinel:fonts/titilliumweb-semibolditalic-webfont.eot']}#iefix") format('embedded-opentype'),
         url("#{resource['primefaces-sentinel:fonts/titilliumweb-semibolditalic-webfont.woff']}") format('woff'),
         url("#{resource['primefaces-sentinel:fonts/titilliumweb-semibolditalic-webfont.ttf']}") format('truetype'),
         url("#{resource['primefaces-sentinel:fonts/titilliumweb-semibolditalic-webfont.svg']}#titillium_websemibold_italic") format('svg');
    font-weight: normal;
    font-style: normal;

}


/* Component containers
----------------------------------*/
.ui-widget {
/*-moz-transition: all 0.5s ease;
-ms-transition: all 0.5s ease;
-o-transition: all 0.5s ease;
transition: all 0.5s ease;*/
font-family: 'titillium_webregular' !important;
}
.ui-widget .ui-widget {
	/* font-size: 1em; */
/*-moz-transition: all 0.5s ease;
-ms-transition: all 0.5s ease;
-o-transition: all 0.5s ease;
transition: all 0.5s ease;*/
font-family: 'titillium_webregular' !important;
}
.ui-widget input,
.ui-widget select,
.ui-widget textarea,
.ui-widget button {
	/* font-size: 1em; */
-moz-transition: all 0.5s ease;
-ms-transition: all 0.5s ease;
-o-transition: all 0.5s ease;
transition: all 0.5s ease;
}
.ui-widget-content {
	/*border: 1px solid #72b42d;*/
	/*background: #285c00 url("images/ui-bg_inset-soft_10_285c00_1x100.png") 50% bottom repeat-x;*/
	/*color: #ffffff;*/
}
.ui-widget-content a {
	color: #ffffff;
}
.ui-widget-header {
	/*border: 1px solid #3f7506;*/
	/*background: #3a8104 url("images/ui-bg_highlight-soft_33_3a8104_1x100.png") 50% 50% repeat-x;*/
	color: #ffffff;
	font-weight: bold;
}
.ui-widget-header a {
	color: #ffffff;
}

/* Interaction states
----------------------------------*/
.ui-state-default,
.ui-widget-content .ui-state-default,
.ui-widget-header .ui-state-default {
	/* border:0px;
	color:#ffffff; */
}
.ui-state-default a,
.ui-state-default a:link,
.ui-state-default a:visited {
	color: #ffffff;
	text-decoration: none;
}

.ui-state-focus,
.ui-widget-content .ui-state-focus,
.ui-widget-header .ui-state-focus {
   
}

.ui-state-hover {
    -moz-transition: all 0.5s ease;
    -ms-transition: all 0.5s ease;
    -o-transition: all 0.5s ease;
    transition: all 0.5s ease;
}

.ui-state-hover a,
.ui-state-hover a:hover,
.ui-state-hover a:link,
.ui-state-hover a:visited,
.ui-state-focus a,
.ui-state-focus a:hover,
.ui-state-focus a:link,
.ui-state-focus a:visited {
	color: #ffffff;
	text-decoration: none;
}
.ui-state-active,
.ui-widget-content .ui-state-active,
.ui-widget-header .ui-state-active {

}
.ui-state-active a,
.ui-state-active a:link,
.ui-state-active a:visited {
	color: #ffffff;
	text-decoration: none;
}

/* Interaction Cues
----------------------------------*/
.ui-widget-content .ui-state-highlight,
.ui-widget-header .ui-state-highlight {
	/* background-color:#FBFCFD !important; */
	/*color: #363636;*/
}
.ui-state-highlight a,
.ui-widget-content .ui-state-highlight a,
.ui-widget-header .ui-state-highlight a {
	color: #363636;
}
input[type="text"].ui-state-error,
input[type="password"].ui-state-error,
.ui-widget-content .ui-state-error,
.ui-widget-header .ui-state-error {
	border: 1px solid #F03369 !important;
	color: #F03369;
}
.ui-widget-content label.ui-state-error{border:0px !important; border:none; box-shadow:none; -webkit-box-shadow:none;}

.ui-state-focus{}

label.ui-state-error{color:#F03369 !important;}

.ui-state-error a,
.ui-widget-content .ui-state-error a,
.ui-widget-header .ui-state-error a {
	color: #2b2b2b;
}
.ui-state-error-text,
.ui-widget-content .ui-state-error-text,
.ui-widget-header .ui-state-error-text {
	color: #2b2b2b;
}
.ui-priority-primary,
.ui-widget-content .ui-priority-primary,
.ui-widget-header .ui-priority-primary {
	font-weight: bold;
}
.ui-priority-secondary,
.ui-widget-content .ui-priority-secondary,
.ui-widget-header .ui-priority-secondary {
	opacity: .7;
	filter:Alpha(Opacity=70);
	font-weight: normal;
}
.ui-state-disabled,
.ui-widget-content .ui-state-disabled,
.ui-widget-header .ui-state-disabled {
	opacity: .35;
	filter:Alpha(Opacity=35);
	background-image: none;
}
.ui-state-disabled .ui-icon {
	filter:Alpha(Opacity=35); /* For IE8 - See #6059 */
}

/* Icons
----------------------------------*/

/* states and images */
.ui-icon {
	width: 16px;
	height: 16px;
}
.ui-icon,
.ui-widget-content .ui-icon {
	background-image: url("#{resource['primefaces-sentinel:images/ui-icons_72b42d_256x240.png']}");
}
.ui-widget-header .ui-icon {
	/*background-image: url("images/ui-icons_ffffff_256x240.png");*/
}
.ui-state-default .ui-icon {
	
}
.ui-state-hover .ui-icon,
.ui-state-focus .ui-icon {
	background-image: url("#{resource['primefaces-sentinel:images/ui-icons_ffffff_256x240.png']}");
}
.ui-state-active .ui-icon {
	background-image: url("#{resource['primefaces-sentinel:images/ui-icons_ffffff_256x240.png']}");
}
.ui-state-highlight .ui-icon {
	background-image: url("#{resource['primefaces-sentinel:images/ui-icons_4eb305_256x240.png']}");
}
.ui-state-error .ui-icon,
.ui-state-error-text .ui-icon,
.ui-selectcheckboxmenu-multiple .ui-icon {
	background-image: url("#{resource['primefaces-sentinel:images/ui-icons_cd0a0a_256x240.png']}");
}


/* positioning */
.ui-icon-default { background-image:url("#{resource['primefaces-sentinel:images/default.svg']}") !important; background-position:center; background-size:100%;}
.ui-icon-blank { background-image:url("#{resource['primefaces-sentinel:images/blank.svg']}") !important; background-position:center; background-size:100%;}
.ui-icon-blank { background-position: 16px 16px; }
.ui-icon-carat-1-n { background-position: 0 0; }
.ui-icon-carat-1-ne { background-position: -16px 0; }
.ui-icon-carat-1-e { background-position: -32px 0; }
.ui-icon-carat-1-se { background-position: -48px 0; }
.ui-icon-carat-1-s { background-position: -64px 0; }
.ui-icon-carat-1-sw { background-position: -80px 0; }
.ui-icon-carat-1-w { background-position: -96px 0; }
.ui-icon-carat-1-nw { background-position: -112px 0; }
.ui-icon-carat-2-n-s { background-position: -128px 0; }
.ui-icon-carat-2-e-w { background-position: -144px 0; }
.ui-icon-triangle-1-n { background-position: 0 -16px; }
.ui-icon-triangle-1-ne { background-position: -16px -16px; }
.ui-icon-triangle-1-e { background-position: -32px -16px; }
.ui-icon-triangle-1-se { background-position: -48px -16px; }
.ui-icon-triangle-1-s { background-position: -64px -16px; }
.ui-icon-triangle-1-sw { background-position: -80px -16px; }
.ui-icon-triangle-1-w { background-position: -96px -16px; }
.ui-icon-triangle-1-nw { background-position: -112px -16px; }
.ui-icon-triangle-2-n-s { background-position: -128px -16px; }
.ui-icon-triangle-2-e-w { background-position: -144px -16px; }
.ui-icon-arrow-1-n { background-position: 0 -32px; }
.ui-icon-arrow-1-ne { background-position: -16px -32px; }
.ui-icon-arrow-1-e { background-position: -32px -32px; }
.ui-icon-arrow-1-se { background-position: -48px -32px; }
.ui-icon-arrow-1-s { background-position: -64px -32px; }
.ui-icon-arrow-1-sw { background-position: -80px -32px; }
.ui-icon-arrow-1-w { background-position: -96px -32px; }
.ui-icon-arrow-1-nw { background-position: -112px -32px; }
.ui-icon-arrow-2-n-s { background-position: -128px -32px; }
.ui-icon-arrow-2-ne-sw { background-position: -144px -32px; }
.ui-icon-arrow-2-e-w { background-position: -160px -32px; }
.ui-icon-arrow-2-se-nw { background-position: -176px -32px; }
.ui-icon-arrowstop-1-n { background-position: -192px -32px; }
.ui-icon-arrowstop-1-e { background-position: -208px -32px; }
.ui-icon-arrowstop-1-s { background-position: -224px -32px; }
.ui-icon-arrowstop-1-w { background-position: -240px -32px; }
.ui-icon-arrowthick-1-n { background-position: 0 -48px; }
.ui-icon-arrowthick-1-ne { background-position: -16px -48px; }
.ui-icon-arrowthick-1-e { background-position: -32px -48px; }
.ui-icon-arrowthick-1-se { background-position: -48px -48px; }
.ui-icon-arrowthick-1-s { background-position: -64px -48px; }
.ui-icon-arrowthick-1-sw { background-position: -80px -48px; }
.ui-icon-arrowthick-1-w { background-position: -96px -48px; }
.ui-icon-arrowthick-1-nw { background-position: -112px -48px; }
.ui-icon-arrowthick-2-n-s { background-position: -128px -48px; }
.ui-icon-arrowthick-2-ne-sw { background-position: -144px -48px; }
.ui-icon-arrowthick-2-e-w { background-position: -160px -48px; }
.ui-icon-arrowthick-2-se-nw { background-position: -176px -48px; }
.ui-icon-arrowthickstop-1-n { background-position: -192px -48px; }
.ui-icon-arrowthickstop-1-e { background-position: -208px -48px; }
.ui-icon-arrowthickstop-1-s { background-position: -224px -48px; }
.ui-icon-arrowthickstop-1-w { background-position: -240px -48px; }
.ui-icon-arrowreturnthick-1-w { background-position: 0 -64px; }
.ui-icon-arrowreturnthick-1-n { background-position: -16px -64px; }
.ui-icon-arrowreturnthick-1-e { background-position: -32px -64px; }
.ui-icon-arrowreturnthick-1-s { background-position: -48px -64px; }
.ui-icon-arrowreturn-1-w { background-position: -64px -64px; }
.ui-icon-arrowreturn-1-n { background-position: -80px -64px; }
.ui-icon-arrowreturn-1-e { background-position: -96px -64px; }
.ui-icon-arrowreturn-1-s { background-position: -112px -64px; }
.ui-icon-arrowrefresh-1-w { background-position: -128px -64px; }
.ui-icon-arrowrefresh-1-n { background-position: -144px -64px; }
.ui-icon-arrowrefresh-1-e { background-position: -160px -64px; }
.ui-icon-arrowrefresh-1-s { background-position: -176px -64px; }
.ui-icon-arrow-4 { background-position: 0 -80px; }
.ui-icon-arrow-4-diag { background-position: -16px -80px; }
.ui-icon-extlink { background-position: -32px -80px; }
.ui-icon-newwin { background-position: -48px -80px; }
.ui-icon-refresh { background-position: -64px -80px; }
.ui-icon-shuffle { background-position: -80px -80px; }
.ui-icon-transfer-e-w { background-position: -96px -80px; }
.ui-icon-transferthick-e-w { background-position: -112px -80px; }
.ui-icon-folder-collapsed { background-position: 0 -96px; }
.ui-icon-folder-open { background-position: -16px -96px; }
.ui-icon-document { background-position: -32px -96px; }
.ui-icon-document-b { background-position: -48px -96px; }
.ui-icon-note { background-image:url("#{resource['primefaces-sentinel:images/note.svg']}"); background-position:center; background-size:100%;}
.ui-icon-mail-closed { background-position: -80px -96px; }
.ui-icon-mail-open { background-position: -96px -96px; }
.ui-icon-suitcase { background-position: -112px -96px; }
.ui-icon-comment { background-position: -128px -96px; }
.ui-icon-person { background-position: -144px -96px; }
.ui-icon-print { background-position: -160px -96px; }
.ui-icon-trash { background-position: -176px -96px; }
.ui-icon-locked { background-position: -192px -96px; }
.ui-icon-unlocked { background-position: -208px -96px; }
.ui-icon-bookmark { background-position: -224px -96px; }
.ui-icon-tag { background-position: -240px -96px; }
.ui-icon-home { background-position: 0 -112px; }
.ui-icon-flag { background-position: -16px -112px; }
.ui-icon-calendar { background-position: -32px -112px; }
.ui-icon-cart { background-position: -48px -112px; }
.ui-icon-pencil { background-position: -64px -112px; }
.ui-icon-clock { background-position: -80px -112px; }
.ui-icon-disk { background-position: -96px -112px; }
.ui-icon-calculator { background-position: -112px -112px; }
.ui-icon-zoomin { background-position: -128px -112px; }
.ui-icon-zoomout { background-position: -144px -112px; }
.ui-icon-search { background-position: -160px -112px; }
.ui-icon-wrench { background-position: -176px -112px; }
.ui-icon-gear { background-position: -192px -112px; }
.ui-icon-heart { background-position: -208px -112px; }
.ui-icon-star { background-position: -224px -112px; }
.ui-icon-link { background-position: -240px -112px; }
.ui-icon-cancel { background-position: 0 -128px; }
.ui-icon-plus { background-position: -16px -128px; }
.ui-icon-plusthick { background-position: -32px -128px; }
.ui-icon-minus { background-position: -48px -128px; }
.ui-icon-minusthick { background-position: -64px -128px; }
.ui-icon-close { background-position: -80px -128px; }
.ui-icon-closethick { background-position: -96px -128px; }
.ui-icon-key { background-position: -112px -128px; }
.ui-icon-lightbulb { background-position: -128px -128px; }
.ui-icon-scissors { background-position: -144px -128px; }
.ui-icon-clipboard { background-position: -160px -128px; }
.ui-icon-copy { background-position: -176px -128px; }
.ui-icon-contact { background-position: -192px -128px; }
.ui-icon-image { background-position: -208px -128px; }
.ui-icon-video { background-position: -224px -128px; }
.ui-icon-script { background-position: -240px -128px; }
.ui-icon-alert { background-position: 0 -144px; }
.ui-icon-info { background-position: -16px -144px; }
.ui-icon-notice { background-position: -32px -144px; }
.ui-icon-help { background-position: -48px -144px; }
.ui-icon-check { background-position: -64px -144px; }
.ui-icon-bullet { background-position: -80px -144px; }
.ui-icon-radio-on { background-position: -96px -144px; }
.ui-icon-radio-off { background-position: -112px -144px; }
.ui-icon-pin-w { background-position: -128px -144px; }
.ui-icon-pin-s { background-position: -144px -144px; }
.ui-icon-play { background-position: 0 -160px; }
.ui-icon-pause { background-position: -16px -160px; }
.ui-icon-seek-next { background-position: -32px -160px; }
.ui-icon-seek-prev { background-position: -48px -160px; }
.ui-icon-seek-end { background-position: -64px -160px; }
.ui-icon-seek-start { background-position: -80px -160px; }
/* ui-icon-seek-first is deprecated, use ui-icon-seek-start instead */
.ui-icon-seek-first { background-position: -80px -160px; }
.ui-icon-stop { background-position: -96px -160px; }
.ui-icon-eject { background-position: -112px -160px; }
.ui-icon-volume-off { background-position: -128px -160px; }
.ui-icon-volume-on { background-position: -144px -160px; }
.ui-icon-power { background-position: 0 -176px; }
.ui-icon-signal-diag { background-position: -16px -176px; }
.ui-icon-signal { background-position: -32px -176px; }
.ui-icon-battery-0 { background-position: -48px -176px; }
.ui-icon-battery-1 { background-position: -64px -176px; }
.ui-icon-battery-2 { background-position: -80px -176px; }
.ui-icon-battery-3 { background-position: -96px -176px; }
.ui-icon-circle-plus { background-position: 0 -192px; }
.ui-icon-circle-minus { background-position: -16px -192px; }
.ui-icon-circle-close { background-position: -32px -192px; }
.ui-icon-circle-triangle-e { background-position: -48px -192px; }
.ui-icon-circle-triangle-s { background-position: -64px -192px; }
.ui-icon-circle-triangle-w { background-position: -80px -192px; }
.ui-icon-circle-triangle-n { background-position: -96px -192px; }
.ui-icon-circle-arrow-e { background-position: -112px -192px; }
.ui-icon-circle-arrow-s { background-position: -128px -192px; }
.ui-icon-circle-arrow-w { background-position: -144px -192px; }
.ui-icon-circle-arrow-n { background-position: -160px -192px; }
.ui-icon-circle-zoomin { background-position: -176px -192px; }
.ui-icon-circle-zoomout { background-position: -192px -192px; }
.ui-icon-circle-check { background-position: -208px -192px; }
.ui-icon-circlesmall-plus { background-position: 0 -208px; }
.ui-icon-circlesmall-minus { background-position: -16px -208px; }
.ui-icon-circlesmall-close { background-position: -32px -208px; }
.ui-icon-squaresmall-plus { background-position: -48px -208px; }
.ui-icon-squaresmall-minus { background-position: -64px -208px; }
.ui-icon-squaresmall-close { background-position: -80px -208px; }
.ui-icon-grip-dotted-vertical { background-position: 0 -224px; }
.ui-icon-grip-dotted-horizontal { background-position: -16px -224px; }
.ui-icon-grip-solid-vertical { background-position: -32px -224px; }
.ui-icon-grip-solid-horizontal { background-position: -48px -224px; }
.ui-icon-gripsmall-diagonal-se { background-position: -64px -224px; }
.ui-icon-grip-diagonal-se { background-position: -80px -224px; }


/* Misc visuals
----------------------------------*/

/* Corner radius */
.ui-corner-all,
.ui-corner-top,
.ui-corner-left,
.ui-corner-tl {
	border-top-left-radius: 5px;
}
.ui-corner-all,
.ui-corner-top,
.ui-corner-right,
.ui-corner-tr {
	border-top-right-radius: 5px;
}
.ui-corner-all,
.ui-corner-bottom,
.ui-corner-left,
.ui-corner-bl {
	border-bottom-left-radius: 5px;
}
.ui-corner-all,
.ui-corner-bottom,
.ui-corner-right,
.ui-corner-br {
	border-bottom-right-radius: 5px;
}

/* Overlays */
.ui-widget-overlay {
	background: #444444;
	opacity: .3;
	filter: Alpha(Opacity=30);
}
.ui-widget-shadow {
	margin: 4px 0 0 4px;
	padding: 0px;
	background: #aaaaaa;
	opacity: .3;
	filter: Alpha(Opacity=30);
	border-radius: 4px;
}

label{font-weight:normal !important;}


/*==============================================================================================================================================================*/
/*==============================================================================================================================================================*/
/*=========================================================== STYLES FOR SENTINEL BLUE THEME ===================================================================*/
/*==============================================================================================================================================================*/
/*==============================================================================================================================================================*/

.ui-shadow{-webkit-box-shadow: 0px 0px 3px 0px rgba(0,0,0,0.3) !important; -moz-box-shadow: 0px 0px 3px 0px rgba(0,0,0,0.3) !important; box-shadow: 0px 0px 3px 0px rgba(0,0,0,0.3) !important;}

/*Style For Breadcrumb ------------------------------------------------------------------------------------- */
.ui-breadcrumb{background-color:#F9FBFC; background-image:initial !important; padding:10px 16px 6px 16px !important; border-radius:0px !important;}
.ui-breadcrumb:hover, .ui-breadcrumb:focus{ background-color:#F3F6F9;}
.ui-breadcrumb ul li .ui-menuitem-link{font-size:14px; color: #9EADB5;}
.ui-breadcrumb ul li .ui-menuitem-link:hover{color:#00AEEF;}
.ui-breadcrumb ul li{z-index:990 !important; margin-right:10px !important;}
.ui-breadcrumb .ui-icon-home{background-image:url("#{resource['primefaces-sentinel:images/breadcrumb-home.svg']}") !important; background-position:center 5px !important; background-size:100% !important; height:30px;}
.ui-breadcrumb-chevron{height:20px !important; background-image:url("#{resource['primefaces-sentinel:images/breadcrumb-arrow.svg']}") !important; background-position:center !important; background-size:60% !important;}

/*Style For DataTable ------------------------------------------------------------------------------------- */

.ui-datatable{border:solid 1px #D1D3D4; border-radius:5px; -webkit-border-radius:5px; -moz-border-radius:5px; background-color: #FBFCFD; color: #9FADB5;}
.ui-datatable .ui-datatable-sticky{border:0px;}
.ui-datatable .ui-datatable-sticky:hover{border:0px !important;}
/*.ui-datatable-scrollable{border:0px; background-color:transparent;}
.ui-datatable-scrollable:hover{border:0px !important; background-color:transparent;}*/
    .ui-datatable .ui-icon-arrowthick-1-s{background-image:url("#{resource['primefaces-sentinel:images/right-one-arrow-for-data.svg']}"); background-position:center !important; 
                           background-size:110%; background-repeat:no-repeat;}
    .ui-datatable .ui-icon-arrowthick-1-s{background-image:url("#{resource['primefaces-sentinel:images/right-one-arrow-for-data.svg']}"); background-position:center !important; 
                           background-size:110%; background-repeat:no-repeat;}
    .ui-datatable .ui-datatable-odd{background-color: #F3F6F9;}
    .ui-datatable .ui-datatable-even{background-color: #FBFCFD;}
.ui-datatable:hover, .ui-datagrid:focus{border:solid 1px #27AAE1;}
    .ui-datatable .ui-datagrid-header,
    .ui-datatable .ui-datatable-header,
    .ui-datatable .ui-datatable-subtable-header,
    .ui-datatable .ui-datatable-summaryrow{border: solid 1px #175787; color: #ffffff; text-shadow: 0 -1px 0 #0A385E; margin:2px 2px 0px 2px;
                                     border-radius: 3px !important; -webkit-border-radius: 3px !important; -moz-border-radius: 3px !important;
background: #1578c9;
background: -moz-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
background: -webkit-gradient(left top, left bottom, color-stop(0%, #1578c9), color-stop(100%, #0b66b1));
background: -webkit-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
background: -o-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
background: -ms-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
background: linear-gradient(to bottom, #1578c9 0%, #0b66b1 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#1578c9', endColorstr='#0b66b1', GradientType=0 );}
    
    
    .ui-datatable .ui-datatable-scrollable-header,
    .ui-datatable .ui-datatable-scrollable-footer{color: #9FADB5; background-color: #dae8ef; outline: none; -webkit-transition: all 0.5s ease; -moz-transition: all 0.5s ease; -ms-transition: all 0.5s ease;
                                                  -o-transition: all 0.5s ease; transition: all 0.5s ease;}
    .ui-datatable .ui-datatable-scrollable-header{border-radius: 5px 5px 0px 0px; -moz-border-radius: 5px 5px 0px 0px; -webkit-border-radius: 5px 5px 0px 0px; border: 0px solid #000000;}
    .ui-datatable .ui-datatable-scrollable-footer{border-radius: 0px 0px 5px 5px; -moz-border-radius: 0px 0px 5px 5px; -webkit-border-radius: 0px 0px 5px 5px; border: 0px solid #000000;}
    .ui-datatable-frozenlayout-left .ui-datatable-scrollable-header,
    .ui-datatable-frozenlayout-left .ui-datatable-scrollable-footer{border-radius: 0px !important; -webkit-border-radius: 0px !important; -moz-border-radius: 0px !important;}
    .ui-datatable-frozenlayout-right .ui-datatable-scrollable-header,
    .ui-datatable-frozenlayout-right .ui-datatable-scrollable-footer{border-radius: 0px !important; -webkit-border-radius: 0px !important; -moz-border-radius: 0px !important;}
    .ui-datatable .ui-datagrid-content{font-size:14px;}

    .ui-datatable .ui-paginator{background-color:#F3F5F7; margin: 0px;}
    .ui-datatable .ui-paginator-top{ border-bottom:solid 1px #E8EDF2 !important;}
    .ui-datatable .ui-paginator-bottom{border-top: solid 1px #E8EDF2 !important; border-bottom: solid 1px #E8EDF2 !important;}
	.ui-datatable .ui-paginator .ui-paginator-current{font-size:14px; padding:15px; color: #9fadb5;}
	.ui-datatable .ui-paginator .ui-paginator-first, .ui-datatable .ui-paginator .ui-paginator-prev,
	.ui-datatable .ui-paginator .ui-paginator-pages .ui-paginator-page, .ui-datatable .ui-paginator .ui-paginator-next,
	.ui-datatable .ui-paginator .ui-paginator-last, .ui-datatable .ui-paginator .ui-paginator-rpp-options{font-size:14px; padding:4px 6px; border:solid 1px #b9cdd5; font-weight: bold;
                                 border-radius:5px !important; -webkit-border-radius:5px !important; -moz-border-radius:5px !important; color:#B9CDD5; -webkit-transition: all 0.5s ease;
                                 -moz-transition: all 0.5s ease; -ms-transition: all 0.5s ease; -o-transition: all 0.5s ease; transition: all 0.5s ease;}
	.ui-datatable .ui-paginator .ui-paginator-rpp-options{outline:none;}
	.ui-datatable .ui-paginator .ui-paginator-first .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/left-double-arrow-for-data.svg']}"); background-position:center !important; background-size:110%; background-repeat:no-repeat;}
	.ui-datatable .ui-paginator .ui-paginator-last .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/right-double-arrow-for-data.svg']}"); background-position:center !important; background-size:110%; background-repeat:no-repeat;}
	.ui-datatable .ui-paginator .ui-paginator-prev .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/left-one-arrow-for-data.svg']}"); background-position:center !important; background-size:110%; background-repeat:no-repeat;}
	.ui-datatable .ui-paginator .ui-paginator-next .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/right-one-arrow-for-data.svg']}"); background-position:center !important; background-size:110%; background-repeat:no-repeat;}
	.ui-datatable .ui-paginator .ui-state-active, .ui-datatable .ui-paginator .ui-state-hover,
	.ui-datatable .ui-paginator .ui-paginator-pages .ui-state-active, .ui-datatable .ui-paginator .ui-paginator-pages .ui-state-hover{color:#27AAE1; background-repeat:no-repeat; border:solid 1px #27aae1;}
        .ui-datatable .ui-paginator .ui-paginator-rpp-options{outline:none; color:#B9CDD5; font-family: 'titillium_webregular';}
        
    .ui-datatable .ui-datatable-tablewrapper{padding:4px;}    
    .ui-datatable .ui-datatable-tablewrapper table{border-collapse: collapse; font-size: 14px;}
        .ui-datatable .ui-datatable-tablewrapper table thead tr th,
        .ui-datatable .ui-datatable-tablewrapper table tfoot tr td,
        .ui-datatable table thead tr th{color:#9FADB5; background-color:#dae8ef; border: solid 1px #C1D5DF; outline:none; padding:5px;}
            .ui-datatable .ui-datatable-tablewrapper table thead tr th .ui-state-hover{ background-color: #fff;}
        .ui-datatable .ui-datatable-tablewrapper table thead tr th.ui-state-hover,
        .ui-datatable .ui-datatable-tablewrapper table thead tr th.ui-state-active,
        .ui-datatable .ui-datatable-sticky table thead tr th.ui-state-hover,
        .ui-datatable .ui-datatable-sticky table thead tr th.ui-state-active{background-color: #27aae1 !important; color: #fff;}
        .ui-datatable .ui-datatable-tablewrapper table thead tr .ui-chkbox-box.ui-state-hover,.ui-datatable .ui-datatable-tablewrapper table thead tr .ui-chkbox-box.ui-state-active{background-color:transparent; color: #fff;}
        .ui-datatable .ui-datatable-tablewrapper table thead tr th .ui-column-customfilter .ui-spinner-button.ui-state-active,
        .ui-datatable .ui-datatable-tablewrapper table thead tr th .ui-column-customfilter .ui-spinner-button.ui-state-hover{background-color: transparent;}
        .ui-datatable .ui-datatable-tablewrapper table thead tr th .ui-column-customfilter .ui-spinner-button.ui-state-active .ui-icon-triangle-1-n,
        .ui-datatable .ui-datatable-tablewrapper table thead tr th .ui-column-customfilter .ui-spinner-button.ui-state-active .ui-icon-triangle-1-s{background-position: center !important; 
                             background-size: 80% !important; margin-left: 12px !important; margin-top: -8px !important;}
        .ui-datatable table tbody tr td{color:#9FADB5; border: solid 1px #E5EBF0;
        -webkit-transition: all 0.5s ease; -moz-transition: all 0.5s ease; -ms-transition: all 0.5s ease; -o-transition: all 0.5s ease; transition: all 0.5s ease;}
            .ui-datatable .ui-datatable-tablewrapper table tbody tr td .ui-row-toggler.ui-icon-circle-triangle-s{background-image:url("#{resource['primefaces-sentinel:images/downarrow-gray.svg']}"); 
                            background-position:center !important; background-size:110%;}
            .ui-datatable .ui-datatable-tablewrapper table tbody tr td .ui-row-toggler.ui-icon-circle-triangle-e{background-image:url("#{resource['primefaces-sentinel:images/rightarrow-gray.svg']}"); 
                            background-position:center !important; background-size:110%;}
        .ui-datatable .ui-datatable-tablewrapper table tbody tr td .ui-cell-editor-output{}
            .ui-datatable .ui-datatable-tablewrapper table tbody tr td .ui-row-editor .ui-icon-pencil{background-image:url("#{resource['primefaces-sentinel:images/pen-gray.svg']}"); 
                            background-position:center !important; background-size:120%;}
            .ui-datatable .ui-datatable-tablewrapper table tbody tr td .ui-row-editor .ui-icon-check{background-image:url("#{resource['primefaces-sentinel:images/check-gray.svg']}"); 
                            background-position:center !important; background-size:120%;}
            .ui-datatable .ui-datatable-tablewrapper table tbody tr td .ui-row-editor .ui-icon-close{background-image:url("#{resource['primefaces-sentinel:images/delete-gray.svg']}"); 
                            background-position:center !important; background-size:120%;}
        .ui-datatable .ui-datatable-tablewrapper table tbody .ui-state-hover td{background-color: #eff3f5; color: #27aae1;}
        .ui-datatable .ui-datatable-tablewrapper table tbody .ui-state-highlight td{background-color: #eff3f5; color: #F686A6;}
            .ui-datatable .ui-datatable-tablewrapper table tbody tr td .ui-icon-arrow-4{background-image:url("#{resource['primefaces-sentinel:images/movearrow-gray.svg']}"); 
                            background-position:center !important; background-size:100%;}
        
        .ui-datatable .ui-datatable-tablewrapper table thead tr .ui-state-default .ui-icon-carat-2-n-s,
        .ui-datatable .ui-datatable-sticky table thead tr .ui-state-default .ui-icon-carat-2-n-s{background-image:url("#{resource['primefaces-sentinel:images/updown-gray-arrow.svg']}"); 
                            background-position:center !important; background-size:110%; background-repeat:no-repeat; margin-left: 5px;}
        .ui-datatable .ui-datatable-tablewrapper table thead tr .ui-state-hover .ui-icon-carat-2-n-s,
        .ui-datatable .ui-datatable-sticky table thead tr .ui-state-hover .ui-icon-carat-2-n-s{background-image:url("#{resource['primefaces-sentinel:images/updown-arrow.svg']}"); 
                            background-position:center !important; background-size:110%; background-repeat:no-repeat; margin-left: 5px;}
        .ui-datatable .ui-datatable-tablewrapper table thead tr .ui-state-active .ui-icon-triangle-1-n,
        .ui-datatable .ui-datatable-sticky table thead tr .ui-state-active .ui-icon-triangle-1-n{background-image:url("#{resource['primefaces-sentinel:images/uparrow.svg']}"); 
                            background-position:center !important; background-size:110%; background-repeat:no-repeat; margin-left: 5px;}
        .ui-datatable .ui-datatable-tablewrapper table thead tr .ui-state-active .ui-icon-triangle-1-s,
        .ui-datatable .ui-datatable-sticky table thead tr .ui-state-active .ui-icon-triangle-1-s{background-image:url("#{resource['primefaces-sentinel:images/downarrow.svg']}"); 
                            background-position:center !important; background-size:110%; background-repeat:no-repeat; margin-left: 5px;}
        
    .ui-datatable-footer{background-color:#F9FAFC; color:#B9CDD5;}
    
@media (max-width: 35em){
    .ui-datatable-reflow .ui-datatable-data td {border:0px !important;}
}

.ui-columntoggler{border: solid 1px #D1D3D4; background-color: #FBFCFD; color: #9FADB5;}

.ui-datatable-scrollable{overflow:hidden;}
.ui-datatable-scrollable-header-box table thead tr th, .ui-datatable-scrollable-footer-box table tfoot tr td{border-width: 1px; border-right: solid 1px #C1D5DF; border-left:solid 1px transparent; border-bottom:0px; border-top: 0px;}

.ui-draggable-dragging{color:#27AAE1; border: solid 1px #ccc;}
.ui-draggable-dragging .ui-icon-carat-2-n-s{background-image:none;}
.ui-draggable-dragging .ui-icon-carat-1-n{background-image:none;}
.ui-draggable-dragging .ui-icon-carat-1-s{background-image:none;}
.ui-datatable > .ui-icon-arrowthick-1-s{background-image:url("#{resource['primefaces-sentinel:images/downarrow-blue.svg']}"); 
                            background-position:center !important; background-size:100%; background-repeat:no-repeat; margin-left: 5px;}
.ui-datatable > .ui-icon-arrowthick-1-n{background-image:url("#{resource['primefaces-sentinel:images/uparrow-blue.svg']}"); 
                            background-position:center !important; background-size:100%; background-repeat:no-repeat; margin-left: 5px;}

/*Style For Data Grid ------------------------------------------------------------------------------------- */

.ui-datagrid{border:solid 1px #D1D3D4; border-radius:5px; -webkit-border-radius:5px; -moz-border-radius:5px; background-color: #FBFCFD; color: #9FADB5;}
.ui-datagrid:hover, .ui-datagrid:focus{border:solid 1px #27AAE1;}
    .ui-datagrid .ui-datagrid-header{border: solid 1px #175787; color: #ffffff; text-shadow: 0 -1px 0 #0A385E; margin:2px 2px 0px 2px;
                                     border-radius: 3px !important; -webkit-border-radius: 3px !important; -moz-border-radius: 3px !important;
background: #1578c9;
background: -moz-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
background: -webkit-gradient(left top, left bottom, color-stop(0%, #1578c9), color-stop(100%, #0b66b1));
background: -webkit-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
background: -o-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
background: -ms-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
background: linear-gradient(to bottom, #1578c9 0%, #0b66b1 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#1578c9', endColorstr='#0b66b1', GradientType=0 );}
    .ui-datagrid .ui-datagrid-content{font-size:14px;}

    .ui-datagrid .ui-paginator{background-color: #F3F5F7; margin: 0px;}
    .ui-datagrid .ui-paginator-top{ border-bottom:solid 1px #E8EDF2 !important; margin-bottom: 3px;}
    .ui-datagrid .ui-paginator-bottom{border-top: solid 1px #E8EDF2 !important; margin-top: 3px;}
	.ui-datagrid .ui-paginator .ui-paginator-current{font-size:14px; padding:15px; color: #9fadb5;}
	.ui-datagrid .ui-paginator .ui-paginator-first, .ui-datagrid .ui-paginator .ui-paginator-prev,
	.ui-datagrid .ui-paginator .ui-paginator-pages .ui-paginator-page, .ui-datagrid .ui-paginator .ui-paginator-next,
	.ui-datagrid .ui-paginator .ui-paginator-last, .ui-datagrid .ui-paginator .ui-paginator-rpp-options{font-size:14px; padding:4px 6px; border:solid 1px #b9cdd5; font-weight: bold;
                                 border-radius:5px !important; -webkit-border-radius:5px !important; -moz-border-radius:5px !important; color:#B9CDD5; -webkit-transition: all 0.5s ease;
                                 -moz-transition: all 0.5s ease; -ms-transition: all 0.5s ease; -o-transition: all 0.5s ease; transition: all 0.5s ease;}
	.ui-datagrid .ui-paginator .ui-paginator-rpp-options{outline:none;}
	.ui-datagrid .ui-paginator .ui-paginator-first .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/left-double-arrow-for-data.svg']}"); background-position:center !important; background-size:110%; background-repeat:no-repeat;}
	.ui-datagrid .ui-paginator .ui-paginator-last .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/right-double-arrow-for-data.svg']}"); background-position:center !important; background-size:110%; background-repeat:no-repeat;}
	.ui-datagrid .ui-paginator .ui-paginator-prev .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/left-one-arrow-for-data.svg']}"); background-position:center !important; background-size:110%; background-repeat:no-repeat;}
	.ui-datagrid .ui-paginator .ui-paginator-next .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/right-one-arrow-for-data.svg']}"); background-position:center !important; background-size:110%; background-repeat:no-repeat;}
	.ui-datagrid .ui-paginator .ui-state-active, .ui-datagrid .ui-paginator .ui-state-hover,
	.ui-datagrid .ui-paginator .ui-paginator-pages .ui-state-active, .ui-datagrid .ui-paginator .ui-paginator-pages .ui-state-hover{color:#27AAE1; background-repeat:no-repeat; border:solid 1px #27aae1;}
        .ui-datagrid .ui-paginator .ui-paginator-rpp-options{outline:none; color:#B9CDD5; font-family: 'titillium_webregular';}
        
        .ui-datagrid-footer{height: auto !important; background-color: #F9FAFC; margin: 0px; padding: 10px 10px !important; border-top: solid 1px #E8EDF2 !important; font-size: 14px; color: #C7D1D7; 
                            border-top-left-radius: 0px !important; border-top-right-radius: 0px !important; -webkit-border-top-left-radius: 0px !important; -webkit-border-top-right-radius: 0px !important;
                            -moz-border-top-left-radius: 0px !important; -moz-border-top-right-radius: 0px !important;}
        

/*Style For Data List ------------------------------------------------------------------------------------- */

.ui-datalist{border:solid 1px #D1D3D4; border-radius:5px; -webkit-border-radius:5px; -moz-border-radius:5px; background-color: #FBFCFD; color: #9FADB5;}
.ui-datalist:hover, .ui-datalist:focus{border:solid 1px #27AAE1;}
    .ui-datalist .ui-datalist-header{border: solid 1px #175787; color: #ffffff; text-shadow: 0 -1px 0 #0A385E; margin:2px 2px 0px 2px;
                                     border-radius: 3px !important; -webkit-border-radius: 3px !important; -moz-border-radius: 3px !important;
background: #1578c9;
background: -moz-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
background: -webkit-gradient(left top, left bottom, color-stop(0%, #1578c9), color-stop(100%, #0b66b1));
background: -webkit-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
background: -o-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
background: -ms-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
background: linear-gradient(to bottom, #1578c9 0%, #0b66b1 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#1578c9', endColorstr='#0b66b1', GradientType=0 );}
    .ui-datalist .ui-datalist-content .ui-datalist-data{margin: 0px; padding-top: 15px; padding-bottom:15px; padding-right:20px;}
    
    .ui-datalist-item{list-style:inherit !important;}

    .ui-datalist .ui-paginator{background-color: #F3F5F7; margin: 0px; padding: 5px 0px;}
    .ui-datalist .ui-paginator-top{ border-bottom:solid 1px #E8EDF2 !important; margin-bottom: 3px;}
    .ui-datalist .ui-paginator-bottom{border-top: solid 1px #E8EDF2 !important; margin-top: 3px;}
	.ui-datalist .ui-paginator .ui-paginator-current{font-size:14px; padding:15px; color: #9fadb5;}
	.ui-datalist .ui-paginator .ui-paginator-first, .ui-datalist .ui-paginator .ui-paginator-prev,
	.ui-datalist .ui-paginator .ui-paginator-pages .ui-paginator-page, .ui-datalist .ui-paginator .ui-paginator-next,
	.ui-datalist .ui-paginator .ui-paginator-last, .ui-datalist .ui-paginator .ui-paginator-rpp-options{font-size:14px; padding:4px 6px; border:solid 1px #b9cdd5; font-weight: bold;
                                 border-radius:5px !important; -webkit-border-radius:5px !important; -moz-border-radius:5px !important; color:#B9CDD5; -webkit-transition: all 0.5s ease;
                                 -moz-transition: all 0.5s ease; -ms-transition: all 0.5s ease; -o-transition: all 0.5s ease; transition: all 0.5s ease;}
	.ui-datalist .ui-paginator .ui-paginator-rpp-options{outline:none;}
	.ui-datalist .ui-paginator .ui-paginator-first .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/left-double-arrow-for-data.svg']}"); background-position:center !important; background-size:110%; background-repeat:no-repeat;}
	.ui-datalist .ui-paginator .ui-paginator-last .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/right-double-arrow-for-data.svg']}"); background-position:center !important; background-size:110%; background-repeat:no-repeat;}
	.ui-datalist .ui-paginator .ui-paginator-prev .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/left-one-arrow-for-data.svg']}"); background-position:center !important; background-size:110%; background-repeat:no-repeat;}
	.ui-datalist .ui-paginator .ui-paginator-next .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/right-one-arrow-for-data.svg']}"); background-position:center !important; background-size:110%; background-repeat:no-repeat;}
	.ui-datalist .ui-paginator .ui-state-active, .ui-datalist .ui-paginator .ui-state-hover,
	.ui-datalist .ui-paginator .ui-paginator-pages .ui-state-active, .ui-datalist .ui-paginator .ui-paginator-pages .ui-state-hover{color:#27AAE1; background-repeat:no-repeat; border:solid 1px #27aae1;}
        .ui-datalist .ui-paginator .ui-paginator-rpp-options{outline:none; color:#B9CDD5; font-family: 'titillium_webregular';}
        
        .ui-datalist-footer{height: auto !important; background-color: #F9FAFC; margin: 0px; padding: 10px 10px !important; border-top: solid 1px #E8EDF2 !important; font-size: 14px; color: #C7D1D7; 
                            border-top-left-radius: 0px !important; border-top-right-radius: 0px !important; -webkit-border-top-left-radius: 0px !important; -webkit-border-top-right-radius: 0px !important;
                            -moz-border-top-left-radius: 0px !important; -moz-border-top-right-radius: 0px !important;}
        

/*Style For PickList ------------------------------------------------------------------------------------- */

.ui-picklist{display:table !important; font-size: 14px; color: #9FADB5;}
.ui-picklist:hover .ui-picklist-list, .ui-picklist:hover .ui-picklist-caption{border-color:#27AAE1;}
.ui-picklist .ui-picklist-filter{width:81.5% !important; padding:6px 6px 6px 30px !important;}
.ui-picklist .ui-picklist-filter-container .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/search-icon.svg']}"); background-position: center !important; background-repeat: no-repeat;
                                                    background-size: 100%; width: 28px; height: 28px; left: 3px; top: 3px !important;}
.ui-picklist .ui-picklist-buttons{display: table-cell; float: none; vertical-align:middle;}
.ui-picklist .ui-picklist-caption{border:solid 1px #d1d3d4; background-color: #F3F5F7; color: #A1AFB6; padding: 4px 0px !important; margin: 4px 0px 0px 0px;
-webkit-transition: all 0.5s ease; -moz-transition: all 0.5s ease; -ms-transition: all 0.5s ease; -o-transition: all 0.5s ease; transition: all 0.5s ease;}
.ui-picklist .ui-picklist-list{border:solid 1px #d1d3d4; padding: 3px !important; -webkit-transition: all 0.5s ease; -moz-transition: all 0.5s ease; -ms-transition: all 0.5s ease; 
                 -o-transition: all 0.5s ease; transition: all 0.5s ease; background-color: #FBFCFD;}
.ui-picklist .ui-picklist-list:hover, .ui-picklist .ui-picklist-list:focus{border:solid 1px #27AAE1;}
.ui-picklist .ui-picklist-list .ui-picklist-item{padding: 2px 4px !important; -webkit-transition: all 0.5s ease; -moz-transition: all 0.5s ease; -ms-transition: all 0.5s ease; -o-transition: all 0.5s ease; transition: all 0.5s ease;}
.ui-picklist .ui-picklist-list .ui-state-hover{background-color: #F1F4F7; color: #27AAE1;}
.ui-picklist .ui-picklist-list .ui-state-highlight{background-color: #F1F4F7; color: #27AAE1;}

.ui-picklist .ui-picklist-buttons-cell .ui-picklist-button-move-up .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/uparrow.svg']}"); background-position: center !important; background-repeat: no-repeat;
                                                    background-size: 70%; left:38% !important; margin-left: -8px; top:32% !important; margin-top: -8px; width: 26px; height: 26px;}
.ui-picklist .ui-picklist-buttons-cell .ui-picklist-button-move-top .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/toparrow.svg']}"); background-position: center !important; background-repeat: no-repeat;
                                                    background-size: 70%; left:38% !important; margin-left: -8px; top:32% !important; margin-top: -8px; width: 26px; height: 26px;}
.ui-picklist .ui-picklist-buttons-cell .ui-picklist-button-move-down .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/downarrow.svg']}"); background-position: center !important; background-repeat: no-repeat;
                                                    background-size: 70%; left:38% !important; margin-left: -8px; top:37% !important; margin-top: -8px; width: 26px; height: 26px;}
.ui-picklist .ui-picklist-buttons-cell .ui-picklist-button-move-bottom .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/bottomarrow.svg']}"); background-position: center !important; background-repeat: no-repeat;
                                                    background-size: 70%; left:38% !important; margin-left: -8px; top:33% !important; margin-top: -8px; width: 26px; height: 26px;}
.ui-picklist .ui-picklist-buttons-cell .ui-picklist-button-add .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/right-arrow.svg']}"); background-position: center !important; background-repeat: no-repeat;
                                                    background-size: 65%; left:38% !important; margin-left: -8px; top:35% !important; margin-top: -8px; width: 26px; height: 26px;}
.ui-picklist .ui-picklist-buttons-cell .ui-picklist-button-add-all .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/right-end-arrow.svg']}"); background-position: center !important; background-repeat: no-repeat;
                                                    background-size: 70%; left:38% !important; margin-left: -8px; top:32% !important; margin-top: -8px; width: 26px; height: 26px;}
.ui-picklist .ui-picklist-buttons-cell .ui-picklist-button-remove .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/left-arrow.svg']}"); background-position: center !important; background-repeat: no-repeat;
                                                    background-size: 65%; left:34% !important; margin-left: -8px; top:37% !important; margin-top: -8px; width: 26px; height: 26px;}
.ui-picklist .ui-picklist-buttons-cell .ui-picklist-button-remove-all .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/left-end-arrow.svg']}"); background-position: center !important; background-repeat: no-repeat;
                                                    background-size: 70%; left:38% !important; margin-left: -8px; top:33% !important; margin-top: -8px; width: 26px; height: 26px;}

.ui-picklist-vertical .ui-picklist-buttons-cell .ui-picklist-button-move-up .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/uparrow.svg']}"); background-position: center !important; background-repeat: no-repeat;
                                                    background-size: 70%; left:35% !important; margin-left: -8px; top:32% !important; margin-top: -8px; width: 26px; height: 26px;}
.ui-picklist-vertical .ui-picklist-buttons-cell .ui-picklist-button-move-top .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/toparrow.svg']}"); background-position: center !important; background-repeat: no-repeat;
                                                    background-size: 70%; left:35% !important; margin-left: -8px; top:32% !important; margin-top: -8px; width: 26px; height: 26px;}
.ui-picklist-vertical .ui-picklist-buttons-cell .ui-picklist-button-move-down .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/downarrow.svg']}"); background-position: center !important; background-repeat: no-repeat;
                                                    background-size: 70%; left:34% !important; margin-left: -8px; top:37% !important; margin-top: -8px; width: 26px; height: 26px;}
.ui-picklist-vertical .ui-picklist-buttons-cell .ui-picklist-button-move-bottom .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/bottomarrow.svg']}"); background-position: center !important; background-repeat: no-repeat;
                                                    background-size: 70%; left:34% !important; margin-left: -8px; top:33% !important; margin-top: -8px; width: 26px; height: 26px;}

.ui-picklist-vertical .ui-picklist-buttons-cell .ui-picklist-button-add .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/downarrow.svg']}"); background-position: center !important; background-repeat: no-repeat;
                                                    background-size: 70%; left:35% !important; margin-left: -8px; top:34% !important; margin-top: -8px; width: 26px; height: 26px;}
.ui-picklist-vertical .ui-picklist-buttons-cell .ui-picklist-button-add-all .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/bottomarrow.svg']}"); background-position: center !important; background-repeat: no-repeat;
                                                    background-size: 70%; left:35% !important; margin-left: -8px; top:34% !important; margin-top: -8px; width: 26px; height: 26px;}
.ui-picklist-vertical .ui-picklist-buttons-cell .ui-picklist-button-remove .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/uparrow.svg']}"); background-position: center !important; background-repeat: no-repeat;
                                                    background-size: 70%; left:34% !important; margin-left: -8px; top:34% !important; margin-top: -8px; width: 26px; height: 26px;}
.ui-picklist-vertical .ui-picklist-buttons-cell .ui-picklist-button-remove-all .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/toparrow.svg']}"); background-position: center !important; background-repeat: no-repeat;
                                                    background-size: 70%; left:34% !important; margin-left: -8px; top:34% !important; margin-top: -8px; width: 26px; height: 26px;}
															

/*Style For UI OrderList ------------------------------------------------------------------------------------- */

.ui-orderlist{border:solid 1px #d1d3d4; border-radius:5px !important; -webkit-border-radius:5px !important; -moz-border-radius:5px !important; background-color: #FBFCFD; color: #9FADB5;}
.ui-orderlist:hover, .ui-orderlist:focus{border-color:#27AAE1 !important;}
.ui-orderlist .ui-orderlist-caption{border: solid 1px #D1D3D4 !important; background-color: #F3F5F7; color: #A7B4BB; border-radius: 3px; -webkit-border-radius: 3px; -moz-border-radius: 3px;}
.ui-orderlist .ui-orderlist-list .ui-orderlist-item{padding: 2px 4px !important; font-size: 14px; -webkit-transition: all 0.5s ease; -moz-transition: all 0.5s ease; -ms-transition: all 0.5s ease;
                  -o-transition: all 0.5s ease; transition: all 0.5s ease; background-image:url("#{resource['primefaces-sentinel:images/move-gray.svg']}"); background-position: center right !important; 
                         background-repeat: no-repeat; background-size: 42px;}
.ui-orderlist .ui-orderlist-list .ui-state-hover{background-color: #F1F4F7; color: #27AAE1;}
.ui-orderlist .ui-orderlist-list .ui-state-highlight{background-color: #F1F4F7; color: #27AAE1;}

.ui-orderlist .ui-orderlist-controls .ui-orderlist-button-move-up .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/uparrow.svg']}"); background-position: center !important; background-repeat: no-repeat;
                                                    background-size: 70%; left:33% !important; margin-left: -8px; top:32% !important; margin-top: -8px; width: 26px; height: 26px;}
.ui-orderlist .ui-orderlist-controls .ui-orderlist-button-move-top .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/toparrow.svg']}"); background-position: center !important; background-repeat: no-repeat;
                                                    background-size: 70%; left:35% !important; margin-left: -8px; top:32% !important; margin-top: -8px; width: 26px; height: 26px;}
.ui-orderlist .ui-orderlist-controls .ui-orderlist-button-move-down .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/downarrow.svg']}"); background-position: center !important; background-repeat: no-repeat;
                                                    background-size: 70%; left:34% !important; margin-left: -8px; top:37% !important; margin-top: -8px; width: 26px; height: 26px;}
.ui-orderlist .ui-orderlist-controls .ui-orderlist-button-move-bottom .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/bottomarrow.svg']}"); background-position: center !important; background-repeat: no-repeat;
                                                    background-size: 70%; left:34% !important; margin-left: -8px; top:33% !important; margin-top: -8px; width: 26px; height: 26px;}


/*Style For UI Carousel --------------------------------------------------------------------------------------*/

.ui-carousel{border:solid 1px #d1d3d4; padding: 2px 1px !important; background-color: #FBFCFD; color: #9FADB5;}
.ui-carousel:hover, .ui-carousel:focus{border:solid 1px #27aae1;}
    .ui-carousel .ui-carousel-header{border:solid 1px #175787 !important; height:auto !important; padding: 2px 10px !important; border-radius: 3px; -webkit-border-radius: 3px; -moz-border-radius: 3px;
        background: #1578c9;
        background: -moz-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
        background: -webkit-gradient(left top, left bottom, color-stop(0%, #1578c9), color-stop(100%, #0b66b1));
        background: -webkit-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
        background: -o-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
        background: -ms-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
        background: linear-gradient(to bottom, #1578c9 0%, #0b66b1 100%);
        filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#1578c9', endColorstr='#0b66b1', GradientType=0 );}
        .ui-carousel .ui-carousel-header .ui-carousel-header-title{width:40% !important;}
        .ui-carousel .ui-carousel-header .ui-carousel-next-button{background-image:url("#{resource['primefaces-sentinel:images/right-arrow.svg']}"); background-position: center !important; background-repeat: no-repeat;
                                                    background-size: 90%; width: 20px; height: 20px; margin-top: 3px; padding: 2px;}
        .ui-carousel .ui-carousel-header .ui-carousel-prev-button{background-image:url("#{resource['primefaces-sentinel:images/left-arrow.svg']}"); background-position: center !important; background-repeat: no-repeat;
                                                    background-size: 90%; width: 20px; height: 20px; margin-top: 3px; padding: 2px;}
        .ui-carousel .ui-carousel-header .ui-carousel-next-button:hover, .ui-carousel .ui-carousel-header .ui-carousel-prev-button:hover{background-color:#175787; border-radius:3px !important; -webkit-border-radius:3px !important; -moz-border-radius:3px !important;}
        .ui-carousel .ui-carousel-header .ui-carousel-page-links{margin-top: 7px;}
        .ui-carousel .ui-carousel-header .ui-carousel-page-links .ui-carousel-page-link{background-image:url("#{resource['primefaces-sentinel:images/blank.svg']}"); background-position: center !important; background-repeat: no-repeat;
                                                    background-size: 100%;}
        .ui-carousel .ui-carousel-item {border:1px solid transparent}
        .ui-carousel .ui-carousel-header .ui-carousel-page-links .ui-icon-radio-on{background-image:url("#{resource['primefaces-sentinel:images/blank-active.svg']}");}
          
    .ui-carousel-viewport ul .ui-carousel-item table tbody tr td{border: solid 1px #e5ebf0; -webkit-transition: all 0.5s ease;
                                       -moz-transition: all 0.5s ease; -ms-transition: all 0.5s ease; -o-transition: all 0.5s ease; transition: all 0.5s ease; font-size: 14px;}
    .ui-carousel-viewport ul .ui-carousel-item table tbody tr:hover td{/*border:solid 1px #27AAE1; color:#27AAE1;*/}
    
    .ui-carousel .ui-carousel-footer{height:auto !important; background-color: #F3F5F7; margin: 0px; padding: 10px 10px !important; border-top: solid 1px #E8EDF2 !important;
                                     margin: 3px -1px -2px -1px !important; font-size: 14px;
                                     color: #C7D1D7; border-top-left-radius:0px !important; border-top-right-radius:0px !important;
                                     -webkit-border-top-left-radius:0px !important; -webkit-border-top-right-radius:0px !important;
                                     -moz-border-top-left-radius:0px !important; -moz-border-top-right-radius:0px !important;}
    

/*Style For Schedule --------------------------------------------------------------------------------------*/

.fc{border:solid 1px #d1d3d4; border-radius: 5px; -webkit-border-radius: 5px; -moz-border-radius: 5px; -webkit-transition: all 0.5s ease; -moz-transition: all 0.5s ease; -ms-transition: all 0.5s ease;
                  -o-transition: all 0.5s ease; transition: all 0.5s ease; background-color: #FBFCFD; color: #9FADB5;}
.fc:hover, .fc:focus{border:solid 1px #27AAE1;}
.fc-header{border-bottom: solid 1px #E8EDF2; background-color: #F3F5F7;
           border-top-left-radius:4px !important; border-top-right-radius:4px !important;
           -webkit-border-top-left-radius:4px !important; -webkit-border-top-right-radius:4px !important;
           -moz-border-top-left-radius:4px !important; -moz-border-top-right-radius:4px !important;}
.fc-header-left{width:30%;}
.fc-header-right{width:30%;}
    .fc-header tbody tr td{vertical-align:middle; padding: 1.5%;}
        .fc-header tbody tr td .fc-button{margin:0px 2px !important; font-size: 14px; overflow:hidden;  height:auto !important; color:#b9cdd5;
               border-radius: 3px; -webkit-border-radius: 3px; -moz-border-radius: 3px; font-weight: bold; border: solid 1px #b9cdd5;}
        .fc-header tbody tr td .ui-state-hover, .fc-header tbody tr td .ui-state-active{color: #27AAE1; background-repeat: no-repeat; border: solid 1px #27aae1;}
            .fc-header tbody tr td .fc-button .fc-icon-wrap .ui-icon{margin:5px;}
            .fc-header tbody tr td .fc-button-next .fc-icon-wrap .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/right-one-arrow-for-data.svg']}"); background-position: center !important;
                                                                          background-repeat: no-repeat; background-size: 100%;}
            .fc-header tbody tr td .fc-button-prev .fc-icon-wrap .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/left-one-arrow-for-data.svg']}"); background-position: center !important;
                                                                          background-repeat: no-repeat; background-size: 100%;}
        .fc-header tbody tr td span h2{font-size:18px; margin:0px; color:#27AAE1;}
.fc-content{padding: 10px;}
    .fc-content .fc-event{background-color:#F686A6; border: solid 1px #F686A6;}
        .fc-content .fc-event .fc-event-inner{margin:3px;}
    .fc-content .fc-view-month table{border-collapse:inherit; border-spacing:2px;}
        .fc-content table thead tr th{ background-color: #eff3f5; color:#9fadb5; border-collapse:separate; padding: 5px; border-radius:3px !important; -webkit-border-radius:3px !important;
                                         -moz-border-radius:3px !important;}
        .fc-content table thead tr .fc-sun, .fc-content table thead tr .fc-sat{background-color:#dae8ef;}
        .fc-content table tbody tr td{border:solid 1px #e5ebf0; color:#9fadb5; border-collapse:separate; padding: 5px; border-radius:3px !important; -webkit-border-radius:3px !important;
                                         -moz-border-radius:3px !important; -webkit-transition: all 0.5s ease; -moz-transition: all 0.5s ease; -ms-transition: all 0.5s ease;
                                         -o-transition: all 0.5s ease; transition: all 0.5s ease; border-top-width: 1px !important;}
        .fc-content .fc-view-month table tbody tr td:hover{border-color:#27AAE1; color:#27AAE1;}
        .fc-content .ui-state-highlight{border-color:#f686a6; color:#f686a6;}
        
.fc-agenda-slots tbody tr .ui-widget-header, .fc-agenda-allday tbody tr .ui-widget-header{color:#9FADB5 !important;}


/*Style For Schedule (V. 5.1.12) --------------------------------------------------------------------------------------*/

.fc{border:solid 1px #d1d3d4; border-radius: 5px; -webkit-border-radius: 5px; -moz-border-radius: 5px; -webkit-transition: all 0.5s ease; -moz-transition: all 0.5s ease; -ms-transition: all 0.5s ease;
                  -o-transition: all 0.5s ease; transition: all 0.5s ease; background-color: #FBFCFD; color: #9FADB5;}
.fc:hover, .fc:focus{border:solid 1px #27AAE1;}
.fc-toolbar{border-bottom: solid 1px #E8EDF2; background-color: #F3F5F7; padding: 1.5%; margin: 0px !important;
           border-top-left-radius:4px !important; border-top-right-radius:4px !important;
           -webkit-border-top-left-radius:4px !important; -webkit-border-top-right-radius:4px !important;
           -moz-border-top-left-radius:4px !important; -moz-border-top-right-radius:4px !important;}
.fc-toolbar button{border:solid 1px #b9cdd6; background-color: #f3f5f7; color:#b9cdd6; border-radius: 3px; -webkit-border-radius: 3px; -moz-border-radius: 3px; font-size: 14px;}
    .fc-toolbar .fc-prev-button .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/left-one-arrow-for-data.svg']}"); background-position: center !important;
                                                                          background-repeat: no-repeat; background-size: 100%;}
    .fc-toolbar .fc-next-button .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/right-one-arrow-for-data.svg']}"); background-position: center !important;
                                                                          background-repeat: no-repeat; background-size: 100%;}
.fc-toolbar .fc-button-group button{ margin: 0px 1px;}
.fc-toolbar .ui-state-active, .fc-toolbar .ui-state-hover{border:solid 1px #27AAE1; color:#27aee1;}

.fc-left{}
.fc-right{}
.fc-center{color: #27aae1; font-size: 12px;}

.fc-basic-view td.fc-week-number span, .fc-basic-view td.fc-day-number{padding-right:10px; padding-top: 5px !important;}
.fc-day-grid .fc-week{border-right:solid 1px transparent !important;}
.fc-view-container{padding: 10px;}
    .fc-view-container .fc-event{background-color:#F686A6; border: solid 1px #F686A6;}
        .fc-view-container .fc-event .fc-event-inner{margin:3px;}
    .fc-view-container .fc-month-view table{border-collapse:inherit; border-spacing:1px;}
        .fc-view-container table thead tr th{ background-color: #eff3f5; color:#9fadb5; border-collapse:separate; padding: 5px; border-radius:3px !important; -webkit-border-radius:3px !important;
                                         -moz-border-radius:3px !important; border:0px;}
        .fc-view-container table thead tr .fc-sun, .fc-view-container table thead tr .fc-sat{background-color:#dae8ef;}
        .fc-view-container .fc-content-skeleton table thead tr .fc-sun, .fc-view-container .fc-content-skeleton table thead tr .fc-sat{background-color:transparent;}
        .fc-view-container table tbody tr .fc-day{border:solid 1px #e5ebf0 !important; color:#9fadb5; border-collapse:separate; padding: 5px; border-radius:3px !important; -webkit-border-radius:3px !important;
                                         -moz-border-radius:3px !important; -webkit-transition: all 0.5s ease; -moz-transition: all 0.5s ease; -ms-transition: all 0.5s ease;
                                         -o-transition: all 0.5s ease; transition: all 0.5s ease; border-top-width: 1px !important;}
        .fc-view-container .fc-month-view table tbody tr .fc-day:hover{border-color:#27AAE1 !important; color:#27AAE1;}
        .fc-view-container .fc-month-view .fc-bg table tbody tr .fc-today{border-color:#f686a6 !important; color:#f686a6;}
        .fc-view-container .fc-agendaWeek-view .fc-bg table tbody tr .fc-today{background-color: #EFF3F5; color:#f686a6;}
        .fc-view-container  table  tbody  tr > .ui-widget-content{border-width: 0px;}
        .fc-slats table tbody tr td{border-width:1px !important; border-color:#e5ebf0;}
 
.fc-agenda-slots tbody tr .ui-widget-header, .fc-agenda-allday tbody tr .ui-widget-header{color:#9FADB5 !important;}
.fc-daygrid .fc-row{border-right:0px !important;}
hr.ui-widget-header{background-color: #E5EBF0; border-width:0px;}

.fc-more-cell .fc-more{color: #27aae1; text-decoration: underline;}
.fc-popover{border:solid 1px #D1D3D4; background-color: #FBFCFD; border-radius:5px; -webkit-border-radius:5px; -moz-border-radius:5px;}
    .fc-popover .fc-header {padding:5px 7px !important; color: #9FADB5;}
        .fc-popover .fc-header .fc-close{background-image:url("#{resource['primefaces-sentinel:images/delete-gray.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 100%;}


/*Style For Tree --------------------------------------------------------------------------------------*/

.ui-tree{border:solid 1px #D1D3D4; border-radius:5px; -webkit-border-radius:5px; -moz-border-radius:5px; color: #9FADB5; -webkit-transition: all 0.5s ease; -moz-transition: all 0.5s ease;
         -ms-transition: all 0.5s ease; -o-transition: all 0.5s ease; transition: all 0.5s ease; background-color: #FBFCFD; padding-top: 3px;}
.ui-tree:hover{border: solid 1px #27AAE1;}
    .ui-tree .ui-treenode{padding:2px !important;}
    .ui-tree .ui-treenode .ui-treenode-label.ui-state-hover{color:#27aae1;}
    .ui-tree .ui-tree-droppoint.ui-state-hover{background-color:#27aae1;}
    .ui-tree .ui-treenode .ui-state-highlight{color: #F686A6;}
        .ui-tree .ui-treenode .ui-treenode-leaf-icon{/*background-image:url("#{resource['primefaces-sentinel:images/treenode-gray.svg']}");*/ background-position: center !important; 
                         background-repeat: no-repeat; background-size: 90%;}
        .ui-tree .ui-treenode .ui-icon-grip-dotted-vertical{background-image:url("#{resource['primefaces-sentinel:images/treenode-dots-gray.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 100%;}
        .ui-tree .ui-treenode-content{line-height:16px;}
            .ui-tree .ui-treenode-content .ui-chkbox-box{margin-top:0px;}
            .ui-tree .ui-treenode .ui-treenode-label{ margin: 0px; padding: 0px 6px; font-size: 12px;}
                .ui-tree .ui-treenode-content .ui-chkbox-box .ui-icon-minus{background-image:url("#{resource['primefaces-sentinel:images/minus-sign-gray.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 100%;}
    
    .ui-tree .ui-icon{ margin-right:3px;} 
    .ui-tree .ui-icon-triangle-1-e{background-image:url("#{resource['primefaces-sentinel:images/rightarrow-gray.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 120%;}
    .ui-tree .ui-icon-triangle-1-s{background-image:url("#{resource['primefaces-sentinel:images/downarrow-gray.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 130%;}
    .ui-tree .ui-icon-folder-collapsed{background-image:url("#{resource['primefaces-sentinel:images/folder-close-gray.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 140%;}
    .ui-tree .ui-icon-folder-open{background-image:url("#{resource['primefaces-sentinel:images/folder-open-gray.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 160%;}
    .ui-tree .ui-icon-document{background-image:url("#{resource['primefaces-sentinel:images/document-gray.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 160%;}
    .ui-tree .ui-icon-note{background-image:url("#{resource['primefaces-sentinel:images/note-gray.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 150%;}
    .ui-tree .ui-icon-image{background-image:url("#{resource['primefaces-sentinel:images/image-gray.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 150%;}
    .ui-tree .ui-icon-video{background-image:url("#{resource['primefaces-sentinel:images/video-gray.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 160%;}
    .ui-tree .ui-icon-music{background-image:url("#{resource['primefaces-sentinel:images/music-gray.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 160%;}
    

/*Style For TreeTable --------------------------------------------------------------------------------------*/

.ui-treetable{border:solid 1px #D1D3D4; border-radius:5px; -webkit-border-radius:5px; -moz-border-radius:5px; color: #9FADB5 !important; -webkit-transition: all 0.5s ease; -moz-transition: all 0.5s ease;
         -ms-transition: all 0.5s ease; -o-transition: all 0.5s ease; transition: all 0.5s ease; background-color: #FBFCFD; padding: 2px;}
.ui-treetable:hover{border: solid 1px #27AAE1;}
    .ui-treetable table tbody tr td .ui-chkbox-box{margin-top:-1px; width: 12px; height: 12px;}
        .ui-treetable table tbody tr td .ui-chkbox-box .ui-icon{width:12px; height: 12px;}
            
    .ui-treetable .ui-treetable-header{border-bottom: solid 1px #E8EDF2 !important; background-color: #F3F5F7; border-top-left-radius: 4px !important; border-top-right-radius: 4px !important;
                                       -webkit-border-top-left-radius: 4px !important; -webkit-border-top-right-radius: 4px !important; -moz-border-top-left-radius: 4px !important;
                                       -moz-border-top-right-radius: 4px !important; margin: -2px -2px 2px -2px; color: #9FADB5 !important; padding:10px !important;}
    .ui-treetable table{margin: 0px 0px; font-size: 14px; line-height: 14px;}
        .ui-treetable table thead tr th{color: #9FADB5; background-color: #dae8ef; border: solid 1px #C1D5DF; outline: none; padding: 5px;}
        .ui-treetable table thead tr th.ui-state-active, .ui-treetable table thead tr th.ui-state-hover{background-color: #27aae1; color: #fff;}
        
        .ui-treetable table tbody tr td{color: #9FADB5; border: solid 1px #E5EBF0; -webkit-transition: all 0.5s ease; -moz-transition: all 0.5s ease; -ms-transition: all 0.5s ease; 
                                        -o-transition: all 0.5s ease; transition: all 0.5s ease; padding: 8px;}
        .ui-treetable table tbody .ui-state-hover td{color:#27AAE1 !important; background-color:#eff3f5;}
        .ui-treetable table tbody .ui-state-highlight td{color:#f686a6 !important; background-color:#eff3f5;}
    
    .ui-treetable .ui-icon{ margin-right:7px;}
    .ui-treetable .ui-icon-triangle-1-e{background-image:url("#{resource['primefaces-sentinel:images/rightarrow-gray.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 100%;}
    .ui-treetable .ui-icon-triangle-1-s{background-image:url("#{resource['primefaces-sentinel:images/downarrow-gray.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 110%;}
    .ui-treetable .ui-icon-triangle-1-s{background-image:url("#{resource['primefaces-sentinel:images/downarrow-gray.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 110%;}
    .ui-treetable .ui-chkbox-box .ui-icon-minus{background-image:url("#{resource['primefaces-sentinel:images/minus-sign-gray.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 110%;}
    
    .ui-treetable table thead tr th .ui-icon-triangle-1-n{background-image:url("#{resource['primefaces-sentinel:images/uparrow.svg']}") !important; background-position: center !important; 
                         background-repeat: no-repeat; background-size: 110%;}
    .ui-treetable table thead tr th .ui-icon-triangle-1-s{background-image:url("#{resource['primefaces-sentinel:images/downarrow.svg']}") !important; background-position: center !important; 
                         background-repeat: no-repeat; background-size: 110%;}
    .ui-treetable table thead tr th .ui-icon-carat-2-n-s{background-image:url("#{resource['primefaces-sentinel:images/updown-gray-arrow.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 110%;}
    .ui-treetable table thead tr th.ui-state-hover .ui-icon-carat-2-n-s{background-image:url("#{resource['primefaces-sentinel:images/updown-arrow.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 110%;}

.ui-treetable-scrollable{border:0px;}
.ui-treetable-scrollable:hover{border:none;}

.ui-tree-horizontal{}
    .ui-tree-horizontal .ui-treenode{padding:5px 40px !important;}
    .ui-tree-horizontal .ui-treenode .ui-state-hover{color:#27AAE1; border: solid 1px #27AAE1 !important;}
    .ui-tree-horizontal .ui-treenode .ui-state-highlight{color:#f686a6; border: solid 1px #f686a6 !important;}
    .ui-tree-horizontal .ui-treenode-collapsed, .ui-tree-horizontal .ui-treenode-leaf{padding:5px 0px 5px 40px !important;}
        .ui-tree-horizontal .ui-treenode .ui-treenode-content{border: solid 1px #9FADB5; background-color: #FBFCFD;}
        
        .ui-tree-horizontal .ui-icon-plus{background-image:url("#{resource['primefaces-sentinel:images/plus-sign-gray.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 100%;}
        .ui-tree-horizontal .ui-icon-minus{background-image:url("#{resource['primefaces-sentinel:images/minus-sign-gray.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 110%;}
        
        .ui-treetable-footer{height: auto !important; background-color: #F9FAFC; margin: 4px -2px 0px -2px; padding: 10px 10px !important; border-top: solid 1px #E8EDF2 !important; font-size: 14px; color: #C7D1D7; 
                            border-top-left-radius: 0px !important; border-top-right-radius: 0px !important; -webkit-border-top-left-radius: 0px !important; -webkit-border-top-right-radius: 0px !important;
                            -moz-border-top-left-radius: 0px !important; -moz-border-top-right-radius: 0px !important;}
        

/*Style For UI Accordion --------------------------------------------------------------------------------------*/

.ui-accordion{border:solid 1px #d1d3d4 !important; border-radius:5px; -webkit-border-radius:5px; -moz-border-radius:5px; background-color: #FBFCFD; padding: 1px 2px 2px 2px !important;}
.ui-accordion:hover{border:solid 1px #27AAE1 !important;}
    .ui-accordion-header{border-radius:3px; -webkit-border-radius:3px; -moz-border-radius:3px; font-size: 14px !important; color:#ffffff; border:solid 1px #175787 !important;
                         font-weight:normal; padding: 5px 5px 5px 30px !important;
                         background: #1578c9;
background: -moz-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
background: -webkit-gradient(left top, left bottom, color-stop(0%, #1578c9), color-stop(100%, #0b66b1));
background: -webkit-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
background: -o-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
background: -ms-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
background: linear-gradient(to bottom, #1578c9 0%, #0b66b1 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#1578c9', endColorstr='#0b66b1', GradientType=0 );}
    .ui-accordion .ui-state-hover, .ui-accordion .ui-state-active{background: #0b66b1;
                            background: -moz-linear-gradient(top, #0b66b1 0%, #1578c9 100%);
                            background: -webkit-gradient(left top, left bottom, color-stop(0%, #0b66b1), color-stop(100%, #1578c9));
                            background: -webkit-linear-gradient(top, #0b66b1 0%, #1578c9 100%);
                            background: -o-linear-gradient(top, #0b66b1 0%, #1578c9 100%);
                            background: -ms-linear-gradient(top, #0b66b1 0%, #1578c9 100%);
                            background: linear-gradient(to bottom, #0b66b1 0%, #1578c9 100%);
                            filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#0b66b1', endColorstr='#1578c9', GradientType=0 );}
        .ui-accordion-header .ui-icon-triangle-1-s{background-image:url("#{resource['primefaces-sentinel:images/downarrow.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 110%;}
        .ui-accordion-header .ui-icon-triangle-1-e{background-image:url("#{resource['primefaces-sentinel:images/right-arrow.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 100%;}
    .ui-accordion-content{padding:5px !important; font-size: 14px !important;}
    

/*Style For UI PanelGrid --------------------------------------------------------------------------------------*/

.ui-panelgrid{border:solid 1px #d1d3d4; background-color: #FBFCFD; font-size: 14px;}
    .ui-panelgrid thead tr td, .ui-panelgrid tfoot tr td{color:#27AAE1; background-color: #eff3f5; border: solid 1px #D1D3D4 !important;}
    .ui-panelgrid tbody tr td{-webkit-transition: all 0.5s ease; -moz-transition: all 0.5s ease; -ms-transition: all 0.5s ease; -o-transition: all 0.5s ease; transition: all 0.5s ease;
    border: solid 1px #D1D3D4 !important;}
    .ui-panelgrid tbody tr td:hover{}
    
    @media (max-width: 35em)
    {.ui-panelgrid .ui-grid-responsive .ui-grid-row {border-color: #D1D3D4 !important;}}
    
/*Style For UI ScrollPanel --------------------------------------------------------------------------------------*/

.ui-scrollpanel{border:solid 1px #d1d3d4; padding: 5px !important; color: #9FADB5; background-color: #FBFCFD; border-radius:5px; -webkit-border-radius:5px; -moz-border-radius:5px;}
.ui-scrollpanel:hover{border:solid 1px #27AAE1;}
    .ui-scrollpanel .ui-scrollpanel-vbar{width:10px; background-color: #eff3f5; border: solid 1px #fbfcfd; border-radius:3px; -webkit-border-radius:3px; -moz-border-radius:3px;}
        .ui-scrollpanel .ui-scrollpanel-vbar .ui-scrollpanel-track .ui-scrollpanel-drag{background-color:#9FADB5; border-radius:3px; -webkit-border-radius:3px; -moz-border-radius:3px;}
        

/*Style For UI ToolBar --------------------------------------------------------------------------------------*/

.ui-toolbar{border:solid 1px #d1d3d4; background-color: #eff3f5; border-radius:5px; -webkit-border-radius:5px; -moz-border-radius:5px; padding: 4px !important;}
.ui-toolbar:hover{border:solid 1px #27AAE1;}

/*Style For UI NotificationBar --------------------------------------------------------------------------------------*/

.ui-notificationbar{z-index:99999999999999; background-color: #A02121; color: #ffffff; height: 50px !important;}


/*Style For Ribbon And TabView --------------------------------------------------------------------------------------*/

.ui-tabs-top{border:solid 1px #d1d3d4; background-color: #FBFCFD; border-radius:6px; -webkit-border-radius:6px; -moz-border-radius:6px; padding: 0px !important;}
.ui-tabs-top:hover{border:solid 1px #27AAE1;}
    .ui-tabs-top .ui-tabs-nav{border-bottom: solid 1px #E8EDF2; border-bottom-right-radius: 0px; border-bottom-left-radius: 0px; border:solid 1px #175787 !important; font-weight:normal;
            background: #1578c9;
background: -moz-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
background: -webkit-gradient(left top, left bottom, color-stop(0%, #1578c9), color-stop(100%, #0b66b1));
background: -webkit-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
background: -o-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
background: -ms-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
background: linear-gradient(to bottom, #1578c9 0%, #0b66b1 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#1578c9', endColorstr='#0b66b1', GradientType=0 );}
    .ui-tabs-top .ui-tabs-navscroller{border-top-right-radius: 5px; border-top-left-radius: 5px; padding-left: 24px !important;}
        .ui-tabs-top .ui-tabs-navscroller .ui-tabs-nav{border-radius:0px; -webkit-border-radius:0px; -moz-border-radius:0px;}
        .ui-tabs-top .ui-tabs-navscroller .ui-tabs-navscroller-btn{border:solid 1px #175787; height: 32px; padding: 0px 3px;
            -webkit-box-shadow: inset 0 0 0 1px #26A3DB; -moz-box-shadow: inset 0 0 0 1px #26A3DB; box-shadow: inset 0 0 0 1px #26A3DB;
            background: #1578c9;
            background: -moz-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
            background: -webkit-gradient(left top, left bottom, color-stop(0%, #1578c9), color-stop(100%, #0b66b1));
            background: -webkit-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
            background: -o-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
            background: -ms-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
            background: linear-gradient(to bottom, #1578c9 0%, #0b66b1 100%);
            filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#1578c9', endColorstr='#0b66b1', GradientType=0 );}
        .ui-tabs-top .ui-tabs-navscroller .ui-tabs-navscroller-btn-left{border-bottom-right-radius: 0px; border-top-right-radius: 0px; border-top-left-radius: 5px; left:0px;}
            .ui-tabs-top .ui-tabs-navscroller .ui-tabs-navscroller-btn-left .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/left-arrow.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 110%; margin-top: 9px;}
        .ui-tabs-top .ui-tabs-navscroller .ui-tabs-navscroller-btn-right{border-bottom-left-radius: 0px; border-top-left-radius: 0px; border-top-right-radius: 5px; right: 0px;}
            .ui-tabs-top .ui-tabs-navscroller .ui-tabs-navscroller-btn-right .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/right-arrow.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 110%; margin-top: 9px;}
        
        .ui-tabs-top .ui-tabs-navscroller .ui-state-disabled{opacity:1; filter: alpha(opacity=100);}
            .ui-tabs-top .ui-tabs-navscroller .ui-state-disabled .ui-icon{opacity:0.4; filter: alpha(opacity=40);}
        
        .ui-tabs-top .ui-tabs-nav li{border:solid 1px transparent !important; top:1px !important; font-size: 14px;}
            .ui-tabs-top .ui-tabs-nav li .ui-icon-close{background-image:url("#{resource['primefaces-sentinel:images/delete.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 100%;}
            .ui-tabs-top .ui-tabs-nav li.ui-state-hover .ui-icon-close,
            .ui-tabs-top .ui-tabs-nav li.ui-state-active .ui-icon-close{background-image:url("#{resource['primefaces-sentinel:images/delete-blue.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 100%;}
        .ui-tabs-top .ui-tabs-nav li a{color:#FBFCFD !important;}
        .ui-tabs-top .ui-tabs-nav li.ui-tabs-selected, .ui-tabs-top .ui-tabs-nav li.ui-tabs-active, .ui-tabs-top .ui-tabs-nav li.ui-state-hover{background-color:#FBFCFD !important; border: solid 1px #FBFCFD !important;}
        .ui-tabs-top .ui-tabs-nav li.ui-tabs-selected a, .ui-tabs-top .ui-tabs-nav li.ui-tabs-active a, .ui-tabs-top .ui-tabs-nav li.ui-state-hover a{color:#27AAE1 !important;}
    .ui-tabs-top .ui-tabs-panels{ background-color:transparent; margin:-1px 0px 3px 0px; border: solid 1px #FBFCFD; color: #9FADB5; background-image: none !important;}
        .ui-tabs-top .ui-tabs-panels .ui-ribbon-groups{background-color:transparent; background: none; filter:none !important;}
        .ui-tabs-top .ui-tabs-panels .ui-tabs-panel{background-image:none !important;}
            .ui-tabs-top .ui-ribbon-group{border-right: solid 1px #C8D9E2; background-image: none !important; background-color: transparent !important; filter:none !important;}
                .ui-ribbon .ui-ribbon-group .ui-ribbon-group-content{display: table; height:auto !important; margin-bottom: 10px;}
                .ui-ribbon .ui-ribbon-group .ui-ribbon-group-label{color:#9FADB5 !important;}
                .ui-ribbon .ui-button{width:auto !important; height:auto !important; font-size: 12px;}
                .ui-ribbon .ui-selectonemenu{border-radius:5px !important; -webkit-border-radius:5px !important; -moz-border-radius:5px !important;}
                    .ui-ribbon .ui-button .ui-button-text, .ui-ribbon .ui-selectonemenu-label{font-size:12px !important;}
                    .ui-ribbon .ui-selectonemenu .ui-selectonemenu-trigger .ui-icon{margin-top:6px !important;}
                    .ui-ribbon-bigbutton .ui-icon, .ui-ribbon-group .ui-icon{height: 16px !important; width: 16px !important; margin: -6px 0px 0px 0px !important; left:15% !important;
                            font-size: 13px !important; color:#fff;}
                    .ui-colorpicker .ui-c span{border:solid 1px #fff !important; border-radius:3px; -webkit-border-radius:3px; -moz-border-radius:3px;}
                    
                    
/* left */                    
.ui-tabs-left{border:solid 1px #d1d3d4; background-color: #FBFCFD; border-radius:6px; -webkit-border-radius:6px; -moz-border-radius:6px; padding: 0px !important;}
.ui-tabs-left:hover{border:solid 1px #27AAE1;}
    .ui-tabs-left .ui-tabs-nav{border-bottom: solid 1px #E8EDF2; border-bottom-right-radius: 0px; border-top-right-radius: 0px; border:solid 1px #175787; font-weight:normal;
            background: #1578c9 !important;
background: -moz-linear-gradient(top, #1578c9 0%, #0b66b1 100%) !important;
background: -webkit-gradient(left top, left bottom, color-stop(0%, #1578c9), color-stop(100%, #0b66b1)) !important;
background: -webkit-linear-gradient(top, #1578c9 0%, #0b66b1 100%) !important;
background: -o-linear-gradient(top, #1578c9 0%, #0b66b1 100%) !important;
background: -ms-linear-gradient(top, #1578c9 0%, #0b66b1 100%) !important;
background: linear-gradient(to bottom, #1578c9 0%, #0b66b1 100%) !important;
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#1578c9', endColorstr='#0b66b1', GradientType=0 ) !important;}
        .ui-tabs-left .ui-tabs-nav li{border:solid 1px transparent !important; top:1px !important; font-size: 14px; margin-right: -1px !important;}
        .ui-tabs-left .ui-tabs-nav li a{color:#FBFCFD !important; width: auto !important;}
        .ui-tabs-left .ui-tabs-nav li.ui-tabs-selected, .ui-tabs-left .ui-tabs-nav li.ui-tabs-active, .ui-tabs-left .ui-tabs-nav li.ui-state-hover{background-color:#FBFCFD !important; border: solid 1px #FBFCFD !important;}
        .ui-tabs-left .ui-tabs-nav li.ui-tabs-selected a, .ui-tabs-left .ui-tabs-nav li.ui-tabs-active a, .ui-tabs-left .ui-tabs-nav li.ui-state-hover a{color:#27AAE1 !important;}
/* right */                    
.ui-tabs-right{border:solid 1px #d1d3d4; background-color: #FBFCFD; border-radius:6px; -webkit-border-radius:6px; -moz-border-radius:6px; padding: 0px !important;}
.ui-tabs-right:hover{border:solid 1px #27AAE1;}
    .ui-tabs-right .ui-tabs-nav{border-bottom: solid 1px #E8EDF2; border-bottom-left-radius: 0px; border-top-left-radius: 0px; border:solid 1px #175787; font-weight:normal; height:auto !important;
            background: #1578c9 !important;
background: -moz-linear-gradient(top, #1578c9 0%, #0b66b1 100%) !important;
background: -webkit-gradient(left top, left bottom, color-stop(0%, #1578c9), color-stop(100%, #0b66b1)) !important;
background: -webkit-linear-gradient(top, #1578c9 0%, #0b66b1 100%) !important;
background: -o-linear-gradient(top, #1578c9 0%, #0b66b1 100%) !important;
background: -ms-linear-gradient(top, #1578c9 0%, #0b66b1 100%) !important;
background: linear-gradient(to bottom, #1578c9 0%, #0b66b1 100%) !important;
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#1578c9', endColorstr='#0b66b1', GradientType=0 ) !important;}
        .ui-tabs-right .ui-tabs-nav li{border:solid 1px transparent !important; top:1px !important; font-size: 14px; margin-left: -1px !important;}
        .ui-tabs-right .ui-tabs-nav li a{color:#FBFCFD !important; width:auto !important;}
        .ui-tabs-right .ui-tabs-nav li.ui-tabs-selected, .ui-tabs-right .ui-tabs-nav li.ui-tabs-active, .ui-tabs-right .ui-tabs-nav li.ui-state-hover{background-color:#FBFCFD !important; border: solid 1px #FBFCFD !important;}
        .ui-tabs-right .ui-tabs-nav li.ui-tabs-selected a, .ui-tabs-right .ui-tabs-nav li.ui-tabs-active a, .ui-tabs-right .ui-tabs-nav li.ui-state-hover a{color:#27AAE1 !important;}
/* bottom */        
.ui-tabs-bottom{border:solid 1px #d1d3d4; background-color: #FBFCFD; border-radius:6px; -webkit-border-radius:6px; -moz-border-radius:6px; padding: 0px !important;}
.ui-tabs-bottom:hover{border:solid 1px #27AAE1;}
    .ui-tabs-bottom .ui-tabs-nav{border-bottom: solid 1px #E8EDF2; border-top-left-radius: 0px; border-top-right-radius: 0px; border:solid 1px #175787; font-weight:normal;
            background: #1578c9 !important;
background: -moz-linear-gradient(top, #1578c9 0%, #0b66b1 100%) !important;
background: -webkit-gradient(left top, left bottom, color-stop(0%, #1578c9), color-stop(100%, #0b66b1)) !important;
background: -webkit-linear-gradient(top, #1578c9 0%, #0b66b1 100%) !important;
background: -o-linear-gradient(top, #1578c9 0%, #0b66b1 100%) !important;
background: -ms-linear-gradient(top, #1578c9 0%, #0b66b1 100%) !important;
background: linear-gradient(to bottom, #1578c9 0%, #0b66b1 100%) !important;
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#1578c9', endColorstr='#0b66b1', GradientType=0 ) !important;}
        .ui-tabs-bottom .ui-tabs-nav li{border:solid 1px transparent !important; top:-1px !important; font-size: 14px; }
        .ui-tabs-bottom .ui-tabs-nav li a{color:#FBFCFD !important; }
        .ui-tabs-bottom .ui-tabs-nav li.ui-tabs-selected, .ui-tabs-bottom .ui-tabs-nav li.ui-tabs-active, .ui-tabs-bottom .ui-tabs-nav li.ui-state-hover{background-color:#FBFCFD !important; border: solid 1px #FBFCFD !important;}
        .ui-tabs-bottom .ui-tabs-nav li.ui-tabs-selected a, .ui-tabs-bottom .ui-tabs-nav li.ui-tabs-active a, .ui-tabs-bottom .ui-tabs-nav li.ui-state-hover a{color:#27AAE1 !important;}
 
 
/*Style For UI Dasboard------------------------------------------------------------------------------------- */

.ui-dashboard{border:solid 1px #d1d3d4; background-color: #FBFCFD; display:table; padding: 5px; border-radius:5px; -webkit-border-radius:5px; -moz-border-radius:5px;
 -webkit-transition: all 0.5s ease; -moz-transition: all 0.5s ease; -ms-transition: all 0.5s ease; -o-transition: all 0.5s ease; transition: all 0.5s ease;}
.ui-dashboard:hover{border:solid 1px #27AAE1;}
    .ui-dashboard-column{margin:2px; min-width: 20px; padding-bottom:20px !important;}
        .ui-dashboard-column .ui-panel{margin-bottom:2px;}
    .ui-dashboard-column .ui-state-hover{border-radius:5px; -webkit-border-radius:5px; -moz-border-radius:5px; border: dashed 1px #27AAE1; background-color:#DAE8EF;}
    
    
/*Style For UI Dialog, Overlay Panel And Lightbox ------------------------------------------------------------------------------------- */

.ui-dialog{background-color: #FBFCFD; color: #9FADB5;}
    .ui-dialog .ui-dialog-titlebar{padding:5px 15px !important; border-bottom: solid 1px #E8EDF2 !important; background-color: #F3F5F7; color: #9FADB5; font-size: 16px;}
        .ui-dialog .ui-dialog-titlebar .ui-dialog-titlebar-icon{padding:0px; margin-top: 8px;}
        .ui-dialog .ui-dialog-titlebar .ui-dialog-titlebar-icon .ui-icon-closethick{background-image:url("#{resource['primefaces-sentinel:images/delete-gray.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 100%;}
        .ui-dialog .ui-dialog-titlebar .ui-dialog-titlebar-icon .ui-icon-minus{background-image:url("#{resource['primefaces-sentinel:images/minus-sign-gray.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 100%;}
        .ui-dialog-docking-zone .ui-icon-plus{background-image:url("#{resource['primefaces-sentinel:images/plus-sign-gray.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 90%;}
        .ui-dialog .ui-dialog-titlebar .ui-dialog-titlebar-icon .ui-icon-extlink{background-image:url("#{resource['primefaces-sentinel:images/external-gray.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 100%;}
        .ui-dialog .ui-dialog-titlebar .ui-icon-newwin{background-image:url("#{resource['primefaces-sentinel:images/external-gray-revert.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 100%;}
    .ui-confirm-dialog .ui-dialog-content .ui-confirm-dialog-message{color:#D76666;}
    .ui-dialog .ui-dialog-content{padding:20px !important; color:#9FADB5; font-size: 16px;}
        /* .ui-dialog .ui-dialog-content .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/warn-red.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 140%; margin: 4px 10px 0px 0px;} */
        .ui-dialog .ui-dialog-content .ui-confirm-dialog-message{margin:0px;}
    .ui-dialog .ui-dialog-footer{padding: 10px 20px !important; border-top: solid 1px #E8EDF2 !important; background-color: #F3F5F7; margin:0px !important; text-align: right !important;}
    .ui-dialog .ui-resizable-se{background-image:url("#{resource['primefaces-sentinel:images/handle-gray.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 100%;}
    
.ui-overlaypanel{color: #9FADB5;}
    .ui-overlaypanel .ui-overlaypanel-close{background-color: #9FADB5;}
        .ui-overlaypanel .ui-overlaypanel-close .ui-icon-closethick{background-image:url("#{resource['primefaces-sentinel:images/delete.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 90%; width: 20px; height: 20px;}
    .ui-overlaypanel .ui-overlaypanel-content{background-color: #FBFCFD; padding: 20px !important; border-radius:5px; -webkit-border-radius:5px; -moz-border-radius:5px;}
    
.ui-lightbox{color: #9FADB5; background-color: #FBFCFD; padding: 10px; border: solid 1px transparent;}
    .ui-lightbox .ui-lightbox-content-wrapper{ }
        .ui-lightbox .ui-lightbox-content-wrapper .ui-lightbox-nav-left{left:5px;}
        .ui-lightbox .ui-lightbox-content-wrapper .ui-lightbox-nav-right{right:5px;}
            .ui-lightbox .ui-lightbox-content-wrapper .ui-lightbox-nav-left .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/left-arrow.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 130%; width: 40px; height: 40px; opacity: 0.6; filter: alpha(opacity=60);}
            .ui-lightbox .ui-lightbox-content-wrapper .ui-lightbox-nav-left .ui-icon:hover{opacity: 1; filter: alpha(opacity=100);}
            .ui-lightbox .ui-lightbox-content-wrapper .ui-lightbox-nav-right .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/right-arrow.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 130%; width: 40px; height: 40px; opacity: 0.6; filter: alpha(opacity=60);}
            .ui-lightbox .ui-lightbox-content-wrapper .ui-lightbox-nav-right .ui-icon:hover{opacity: 1; filter: alpha(opacity=100);}
        .ui-lightbox .ui-lightbox-content-wrapper .ui-lightbox-content{}
    .ui-lightbox .ui-lightbox-caption{padding: 5px 20px 10px 20px !important; border-top: solid 1px #E8EDF2 !important; background-color: #F3F5F7; margin:10px -10px -10px -10px !important; color: #9FADB5;
    border-bottom-right-radius: 5px; border-bottom-left-radius: 5px;}
        .ui-lightbox .ui-lightbox-caption .ui-lightbox-close{ padding: 0px; margin-top: 8px;}
        .ui-lightbox .ui-lightbox-caption .ui-lightbox-close .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/delete-gray.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 100%;}
        
        .ui-dialog-docking-zone{z-index: 999999999999999;}
        .ui-dialog-maximized{z-index: 999999999999999 !important;}
    
        

/*Style For UI Fieldset------------------------------------------------------------------------------------- */

.ui-fieldset{border:solid 1px #d1d3d4; background-color: #FBFCFD;}
.ui-fieldset:hover{border:solid 1px #27AAE1;}
.ui-fieldset-legend{border:solid 1px #175787; color:#ffffff; font-size: 14px; line-height: 14px;
    -webkit-transition: all 0.5s ease; -moz-transition: all 0.5s ease; -ms-transition: all 0.5s ease; -o-transition: all 0.5s ease; transition: all 0.5s ease;
background: #1578c9;
background: -moz-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
background: -webkit-gradient(left top, left bottom, color-stop(0%, #1578c9), color-stop(100%, #0b66b1));
background: -webkit-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
background: -o-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
background: -ms-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
background: linear-gradient(to bottom, #1578c9 0%, #0b66b1 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#1578c9', endColorstr='#0b66b1', GradientType=0 );}
.ui-fieldset .ui-state-hover{ background: #0b66b1;
                            background: -moz-linear-gradient(top, #0b66b1 0%, #1578c9 100%);
                            background: -webkit-gradient(left top, left bottom, color-stop(0%, #0b66b1), color-stop(100%, #1578c9));
                            background: -webkit-linear-gradient(top, #0b66b1 0%, #1578c9 100%);
                            background: -o-linear-gradient(top, #0b66b1 0%, #1578c9 100%);
                            background: -ms-linear-gradient(top, #0b66b1 0%, #1578c9 100%);
                            background: linear-gradient(to bottom, #0b66b1 0%, #1578c9 100%);
                            filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#0b66b1', endColorstr='#1578c9', GradientType=0 );}

.ui-fieldset-legend .ui-icon-plusthick{background-image:url("#{resource['primefaces-sentinel:images/plus-sign.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 90%; margin-right: 5px;}
.ui-fieldset-legend .ui-icon-minusthick{background-image:url("#{resource['primefaces-sentinel:images/minus-sign.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 90%; margin-right: 5px;}

.ui-fieldset-content{font-size: 14px; color: #9FADB5;}


/*Style For UI InputField ------------------------------------------------------------------------------------- */

.ui-inputfield{color:#9EADB5; font-size:14px; font-family: 'titillium_webregular' !important; padding:6px !important; border:solid 1px #D1D3D4; -webkit-transition: all 0.5s ease;
-moz-transition: all 0.5s ease; -ms-transition: all 0.5s ease; -o-transition: all 0.5s ease; transition: all 0.5s ease; background-color: #fff;}
.ui-inputfield.ui-state-hover, .ui-inputfield.ui-state-focus{border:solid 1px #00AEEF; color:#00AEEF !important;}


/*Style For Button ------------------------------------------------------------------------------------- */

.ui-button{outline: none;}
.ui-button-text-only .ui-button-text{padding: 5px 20px 6px 20px !important;}
.ui-button-text{padding: 6px 20px 6px 40px !important; font-size: 14px; border:solid 1px #175787; color:#ffffff; text-shadow: 0 -1px 0 #0A385E; position:relative; z-index:100;
					-webkit-box-shadow:inset 0 0 0 1px #26A3DB; -moz-box-shadow:inset 0 0 0 1px #26A3DB; box-shadow:inset 0 0 0 1px #26A3DB;
					border-radius:5px !important; -webkit-border-radius:5px !important; -moz-border-radius:5px !important;
					background: #1578c9;
					background: -moz-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
					background: -webkit-gradient(left top, left bottom, color-stop(0%, #1578c9), color-stop(100%, #0b66b1));
					background: -webkit-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
					background: -o-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
					background: -ms-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
					background: linear-gradient(to bottom, #1578c9 0%, #0b66b1 100%);
					filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#1578c9', endColorstr='#0b66b1', GradientType=0 ); font-family: 'titillium_webregular' !important;}
.ui-button.ui-state-active .ui-button-text, .ui-buttonset .ui-state-active .ui-button-text{background: #0b66b1;
					background: -moz-linear-gradient(top, #0b66b1 0%, #1578c9 100%);
					background: -webkit-gradient(left top, left bottom, color-stop(0%, #0b66b1), color-stop(100%, #1578c9));
					background: -webkit-linear-gradient(top, #0b66b1 0%, #1578c9 100%);
					background: -o-linear-gradient(top, #0b66b1 0%, #1578c9 100%);
					background: -ms-linear-gradient(top, #0b66b1 0%, #1578c9 100%);
					background: linear-gradient(to bottom, #0b66b1 0%, #1578c9 100%);
					filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#0b66b1', endColorstr='#1578c9', GradientType=0 );
					background-repeat:no-repeat; color:#7FC3FA !important}
.ui-button.ui-state-hover .ui-button-text, .ui-buttonset .ui-state-hover .ui-button-text{color:#7FC3FA;}
.ui-button-icon-left{z-index:120; margin-left:8px;}
.ui-button{border:0px;}
.ui-buttonset .ui-button{ margin-right: 1px !important;}
.ui-button-icon-only{min-width: 30px;}
    .ui-button-icon-only .ui-button-text{padding: 6px 0px !important;}
    
    
/*Style For UI SplitButton ------------------------------------------------------------------------------------- */ 

.ui-splitbutton{border:solid 1px #d1d3d4; border-radius:8px; -webkit-border-radius:8px; -moz-border-radius:8px; padding: 2px; background-color: #EFF3F5;}
.ui-splitbutton:hover, .ui-splitbutton:focus{border:solid 1px #27AAE1;}
.ui-splitbutton .ui-splitbutton-menubutton .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/menu-dots.svg']}") !important; background-size: 100%; background-position: center;}
.ui-splitbutton .ui-button{border-radius:5px; -webkit-border-radius:5px; -moz-border-radius:5px;}


/*Style For UI Menu ------------------------------------------------------------------------------------- */

.ui-menu{border:solid 1px #d1d3d4; background-color: #FBFCFD; color: #9FADB5;}
.ui-menu:hover, .ui-menu:focus{border:solid 1px #27aae1;}
    .ui-menu .ui-menu-list .ui-widget-header{font-size:14px; color: #A1AFB7; background-color: #e5ebf0; width:100% !important;}
    .ui-menu .ui-menu-list .ui-widget-header:hover{color:#27AAE1;}
    .ui-menu .ui-menu-list .ui-widget-header .ui-icon-triangle-1-s{background-image:url("#{resource['primefaces-sentinel:images/downarrow-gray.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 100%;}
    .ui-menu .ui-menu-list .ui-widget-header .ui-icon-triangle-1-e{background-image:url("#{resource['primefaces-sentinel:images/rightarrow-gray.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 100%;}
    
    .ui-menu .ui-menu-list .ui-menuitem{font-size:14px; display: table;}
    .ui-megamenu-vertical .ui-menu-list .ui-menuitem{font-size:14px; width:100%; display: table;}
    .ui-menu .ui-menu-list .ui-menuitem .ui-state-hover, .ui-menu .ui-menu-list .ui-state-hover{background-color:#f1f4f7; color:#27AAE1;}
    .ui-menu .ui-state-hover .ui-menuitem-link{color:#27aae1 !important;}
    .ui-menu .ui-menuitem .ui-menuitem-link{color:#9EADB5; width:94% !important;}
        .ui-menu .ui-menuitem .ui-menuitem-link .ui-icon-triangle-1-e{background-image:url("#{resource['primefaces-sentinel:images/rightarrow-gray.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 100%;}
        .ui-menu .ui-menuitem .ui-menuitem-link .ui-icon-triangle-1-s{background-image:url("#{resource['primefaces-sentinel:images/downarrow-gray.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 100%;}
    .ui-menu .ui-menuitem .ui-menuitem-link .ui-icon{margin-right:5px;}
        .ui-menu .ui-menuitem .ui-menu-list{background-color: #FBFCFD;}
        .ui-slidemenu .ui-menuitem .ui-menu-child{box-shadow:none !important;}
    .ui-menu .ui-slidemenu-wrapper .ui-slidemenu-backward{background-color:#eff3f5; left: 2px; color:#9FADB5; padding: 3px 3px 4px 3px; font-size: 14px;}
    .ui-menu .ui-slidemenu-wrapper .ui-slidemenu-backward:hover{color:#27AAE1;}
        .ui-menu .ui-slidemenu-wrapper .ui-slidemenu-backward .ui-icon-triangle-1-w{background-image:url("#{resource['primefaces-sentinel:images/leftarrow-gray.svg']}"); margin: 2px 5px 0px 0px;
                                                                                    background-position: center !important; background-repeat: no-repeat; background-size: 85%;}
        
    .ui-panelmenu{border:solid 1px #d1d3d4; background-color: #FBFCFD; color: #9FADB5; border-radius:5px; -webkit-border-radius:5px; -moz-border-radius:5px; padding: 2px;}
    .ui-panelmenu:hover{border:solid 1px #27AAE1;}
        .ui-panelmenu .ui-panelmenu-panel .ui-panelmenu-header{border-radius:5px !important; -webkit-border-radius:5px !important; -moz-border-radius:5px !important; border:solid 1px #175787; color:#ffffff;
                                                               font-size: 14px; font-weight: normal; margin-bottom: 1px;
					background: #1578c9;
					background: -moz-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
					background: -webkit-gradient(left top, left bottom, color-stop(0%, #1578c9), color-stop(100%, #0b66b1));
					background: -webkit-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
					background: -o-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
					background: -ms-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
					background: linear-gradient(to bottom, #1578c9 0%, #0b66b1 100%);
					filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#1578c9', endColorstr='#0b66b1', GradientType=0 );}
        .ui-panelmenu .ui-panelmenu-panel h3.ui-state-hover{background: #0b66b1;
					background: -moz-linear-gradient(top, #0b66b1 0%, #1578c9 100%);
					background: -webkit-gradient(left top, left bottom, color-stop(0%, #0b66b1), color-stop(100%, #1578c9));
					background: -webkit-linear-gradient(top, #0b66b1 0%, #1578c9 100%);
					background: -o-linear-gradient(top, #0b66b1 0%, #1578c9 100%);
					background: -ms-linear-gradient(top, #0b66b1 0%, #1578c9 100%);
					background: linear-gradient(to bottom, #0b66b1 0%, #1578c9 100%);
					filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#0b66b1', endColorstr='#1578c9', GradientType=0 );}
            .ui-panelmenu .ui-panelmenu-panel .ui-panelmenu-header .ui-icon-triangle-1-e{background-image:url("#{resource['primefaces-sentinel:images/right-arrow.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 90%;}
            .ui-panelmenu .ui-panelmenu-panel .ui-panelmenu-header .ui-icon-triangle-1-s{background-image:url("#{resource['primefaces-sentinel:images/downarrow.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 110%;}
            .ui-panelmenu .ui-panelmenu-panel .ui-panelmenu-header a{padding: 5px 5px 6px 30px;}
        
        .ui-panelmenu .ui-panelmenu-panel .ui-panelmenu-content .ui-menuitem:hover{background-color:#f1f4f7;}
        .ui-panelmenu .ui-panelmenu-panel .ui-panelmenu-content .ui-menuitem .ui-state-hover{color:#27AAE1;}
            .ui-panelmenu .ui-panelmenu-panel .ui-panelmenu-content .ui-menuitem a{color: #9EADB5;}
            .ui-panelmenu .ui-panelmenu-panel .ui-panelmenu-content .ui-menuitem .ui-menuitem-link .ui-icon-triangle-1-e{background-image:url("#{resource['primefaces-sentinel:images/rightarrow-gray.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 90%;}
            .ui-panelmenu .ui-panelmenu-panel .ui-panelmenu-content .ui-menuitem .ui-menuitem-link .ui-icon-triangle-1-s{background-image:url("#{resource['primefaces-sentinel:images/downarrow-gray.svg']}"); background-position: center !important; 
                         background-repeat: no-repeat; background-size: 90%;}
            
.ui-tabmenu{border:solid 1px #d1d3d4; background-color: #FBFCFD; color: #9FADB5; border-radius:5px; -webkit-border-radius:5px; -moz-border-radius:5px; padding:2px 2px 0px 2px;}
    .ui-tabmenu .ui-tabmenu-nav .ui-tabmenuitem{border:solid 1px transparent;}
    .ui-tabmenu .ui-tabmenu-nav .ui-tabmenuitem a{font-size:14px; color:#9FADB5; padding: 6px 12px !important;}
    .ui-tabmenu .ui-tabmenu-nav .ui-state-hover, .ui-tabmenu .ui-tabmenu-nav .ui-state-active{border:solid 1px #175787;
                                        background: #1578c9;
					background: -moz-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
					background: -webkit-gradient(left top, left bottom, color-stop(0%, #1578c9), color-stop(100%, #0b66b1));
					background: -webkit-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
					background: -o-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
					background: -ms-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
					background: linear-gradient(to bottom, #1578c9 0%, #0b66b1 100%);
					filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#1578c9', endColorstr='#0b66b1', GradientType=0 );}
    .ui-tabmenu .ui-tabmenu-nav .ui-state-hover a, .ui-tabmenu .ui-tabmenu-nav .ui-state-active a{color:#fff;}
    .ui-tabmenu .ui-tabmenu-nav .ui-state-active a .ui-icon{background-position: center !important; background-repeat: no-repeat; background-size: 90%; top: 1px;}


/*Style For UI Messages ------------------------------------------------------------------------------------- */
.ui-messages-error,
.ui-message-error{color: #F03369 !important; background-color: #FBFCFD !important; border-color: #F4BACB !important; font-size: 14px; font-weight: normal; padding:6px !important;}
.ui-messages-info,
.ui-message-info{color: #63BCE2 !important;background-color: #FBFCFD !important; border-color: #CEE4F5 !important; font-size: 14px; font-weight: normal; padding:6px !important;}
    .ui-messages-info .ui-icon-close,
    .ui-messages-warn .ui-icon-close,
    .ui-messages-error .ui-icon-close,
    .ui-messages-fatal .ui-icon-close{background-image:url("#{resource['primefaces-sentinel:images/delete-softblue.svg']}") !important; background-size: 110%;}
.ui-messages-warn,
ui-message-warn{color: #F9BF08 !important; background-color: #FBFCFD !important; border-color: #F7E3A4 !important; font-size: 14px; font-weight: normal; padding:6px !important;}
.ui-messages-fatal,
ui-message-fatal{color: #F55151 !important; background-color: #FBFCFD !important; border-color: #F5B1B1 !important; font-size: 14px; font-weight: normal; padding:6px !important;}
.ui-messages-info-icon,
.ui-messages-warn-icon, .ui-messages-error-icon,.ui-message-error-icon, .ui-messages-fatal-icon{margin:0px !important; background-repeat:no-repeat  !important;
                       background:none; background-position:center  !important; background-size:70%  !important;}
	.ui-message-error-icon{background-image:url("#{resource['primefaces-sentinel:images/warn-pink.svg']}") !important;  background-position: top !important; background-size: 120% !important;}
	.ui-messages-error-icon{background-image:url("#{resource['primefaces-sentinel:images/warn-pink.svg']}") !important; background-position: top !important; background-size: 90% !important;}
	.ui-messages-info-icon{background-image:url("#{resource['primefaces-sentinel:images/warn-blue.svg']}") !important; background-position: top !important; background-size: 90% !important;}
        .ui-messages-warn-icon{background-image:url("#{resource['primefaces-sentinel:images/warn-hardal.svg']}") !important; background-position: top !important; background-size: 90% !important;}
        .ui-messages-fatal-icon{background-image:url("#{resource['primefaces-sentinel:images/warn-red.svg']}") !important; background-position: top !important; background-size: 90% !important;}


/*Style For UI Panel ------------------------------------------------------------------------------------- */

.ui-panel{border: 1px solid #C8D9E2; padding:2px !important;}
    .ui-panel .ui-panel-titlebar{padding: 4px 10px 5px 10px !important; font-size: 14px; border:solid 1px #175787; color:#ffffff; text-shadow: 0 -1px 0 #0A385E; z-index:100;
                                    border-radius:3px !important; -webkit-border-radius:3px !important; -moz-border-radius:3px !important;
                                    background: #1578c9;
                                    background: -moz-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
                                    background: -webkit-gradient(left top, left bottom, color-stop(0%, #1578c9), color-stop(100%, #0b66b1));
                                    background: -webkit-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
                                    background: -o-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
                                    background: -ms-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
                                    background: linear-gradient(to bottom, #1578c9 0%, #0b66b1 100%);
                                    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#1578c9', endColorstr='#0b66b1', GradientType=0 ); font-family: 'titillium_webregular' !important;}
        .ui-panel .ui-panel-titlebar .ui-panel-titlebar-icon,
        .ui-panel .ui-panel-titlebar .ui-panel-titlebar-icon:hover,
        .ui-panel .ui-panel-titlebar .ui-panel-titlebar-icon:focus{margin-top:0px;}
             .ui-panel .ui-panel-titlebar .ui-icon-closethick{background-image:url("#{resource['primefaces-sentinel:images/delete.svg']}") !important; background-position: center !important;
                                background-size: 110% !important;}
             .ui-panel .ui-panel-titlebar .ui-icon-minusthick{background-image:url("#{resource['primefaces-sentinel:images/minus-sign.svg']}") !important; background-position: center !important;
                                background-size: 110% !important;}
             .ui-panel .ui-panel-titlebar .ui-icon-plusthick{background-image:url("#{resource['primefaces-sentinel:images/plus-sign.svg']}") !important; background-position: center !important;
                                background-size: 110% !important;}
             .ui-panel .ui-panel-titlebar .ui-icon-gear{background-image:url("#{resource['primefaces-sentinel:images/gear.svg']}") !important; background-position: center !important;
                                background-size: 110% !important;}
    .ui-panel .ui-panel-content{padding:5px !important; overflow:hidden; font-size: 14px;}
    .ui-panel .ui-panel-footer{border-top: solid 1px #E8EDF2 !important; background-color: #F3F5F7; margin: 0px -2px -2px -2px !important; font-size: 14px;
                    border-bottom-right-radius: 5px; border-bottom-left-radius: 5px;}
	
	
/*Style For SelectOneMenu And SelectCheckBoxMenu ------------------------------------------------------------------------------------- */

.ui-selectonemenu, .ui-selectcheckboxmenu{font-size:16px !important; font-family: 'titillium_webregular' !important; padding:0px 15px 0px 0px !important; border:solid 1px #D1D3D4 !important; -webkit-transition: all 0.5s ease;
				 -moz-transition: all 0.5s ease; -ms-transition: all 0.5s ease; -o-transition: all 0.5s ease; transition: all 0.5s ease; color: #9FADB5;}
	.ui-selectonemenu .ui-selectonemenu-trigger, .ui-selectcheckboxmenu .ui-selectcheckboxmenu-trigger{padding: 0 5px !important; font-size: 16px !important; font-size:16px; border:solid 1px #175787 !important; color:#ffffff !important;
					-webkit-box-shadow:inset 0 0 0 1px #26A3DB !important; -moz-box-shadow:inset 0 0 0 1px #26A3DB !important; box-shadow:inset 0 0 0 1px #26A3DB !important;
					text-shadow: 0 -1px 0 #0A385E; z-index:100; border-bottom-right-radius: 5px !important; border-top-right-radius: 5px !important; margin:-1px -1px -1px 0px !important;
					background: #1578c9 !important;
					background: -moz-linear-gradient(top, #1578c9 0%, #0b66b1 100%) !important;
					background: -webkit-gradient(left top, left bottom, color-stop(0%, #1578c9), color-stop(100%, #0b66b1)) !important;
					background: -webkit-linear-gradient(top, #1578c9 0%, #0b66b1 100%) !important;
					background: -o-linear-gradient(top, #1578c9 0%, #0b66b1 100%) !important;
					background: -ms-linear-gradient(top, #1578c9 0%, #0b66b1 100%) !important;
					background: linear-gradient(to bottom, #1578c9 0%, #0b66b1 100%);
					filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#1578c9', endColorstr='#0b66b1', GradientType=0 ) !important; font-family: 'titillium_webregular' !important;}
.ui-selectonemenu-panel{background-color:#fff; overflow:hidden; border: solid 1px #27AAE1; -webkit-box-shadow: 0px 0px 3px 0px rgba(0,0,0,0.3) !important; 
                            -moz-box-shadow: 0px 0px 3px 0px rgba(0,0,0,0.3) !important; box-shadow: 0px 0px 3px 0px rgba(0,0,0,0.3) !important;}
.ui-selectonemenu-panel .ui-selectonemenu-list, .ui-selectcheckboxmenu .ui-selectcheckboxmenu-list{padding:0px !important; background-color:#F1F4F7; }
.ui-selectonemenu-panel .ui-selectonemenu-list-item, .ui-selectcheckboxmenu-panel .ui-selectcheckboxmenu-list-item{border-bottom:solid 1px #E5EAF0 !important; border-radius:0px !important; -webkit-border-radius:0px !important; font-size: 14px;
                                                    -moz-border-radius:0px !important; margin:0px !important; color: #9EADB5; padding: 6px !important; -webkit-transition: all 0.5s ease; 
                                                    -moz-transition: all 0.5s ease; -ms-transition: all 0.5s ease; -o-transition: all 0.5s ease; transition: all 0.5s ease;}
.ui-selectonemenu-panel .ui-selectonemenu-item-group{border-bottom:solid 1px #E5EAF0 !important; border-radius:0px !important; -webkit-border-radius:0px !important; font-size: 16px;
                                                    -moz-border-radius:0px !important; margin:0px !important; color: #6C7A83; padding: 6px !important; -webkit-transition: all 0.5s ease; 
                                                    -moz-transition: all 0.5s ease; -ms-transition: all 0.5s ease; -o-transition: all 0.5s ease; transition: all 0.5s ease;
                                                    background-color: #E5EAEF;}
.ui-selectonemenu-panel .ui-state-highlight, .ui-selectcheckboxmenu-panel .ui-state-highlight{color:#27AAE1 !important;}
.ui-selectonemenu-panel .ui-state-hover, .ui-selectcheckboxmenu-panel .ui-state-hover{ background-color:#ffffff !important; -webkit-transition: all 0.5s ease;
										-moz-transition: all 0.5s ease; -ms-transition: all 0.5s ease; -o-transition: all 0.5s ease; transition: all 0.5s ease;}
	.ui-selectonemenu-trigger .ui-icon-triangle-1-s, .ui-selectcheckboxmenu-trigger .ui-icon-triangle-1-s{ background-image:url("#{resource['primefaces-sentinel:images/downarrow.svg']}") !important; margin-top:8px !important;
                              background-size: 100%; background-position: center;}
 
 .ui-selectonemenu-panel{background-color: #F1F4F7;}
 .ui-selectonemenu-panel .ui-selectonemenu-filter-container .ui-icon-search{width: 30px; height: 30px; background-size: 100%; background-position: center; background-repeat: no-repeat;
                                                                            background-image: url("#{resource['primefaces-sentinel:images/search-icon.svg']}"); 
                                                                            left: 5px !important; top:8px !important;}
 .ui-selectonemenu-panel .ui-selectonemenu-filter-container .ui-inputfield{padding-left:30px !important; width:75%;}
 .ui-selectonemenu-panel .ui-selectonemenu-table td{border: solid 1px #E5EAF0 !important; border-radius: 0px !important; -webkit-border-radius: 0px !important; font-size: 14px; 
                                                        -moz-border-radius: 0px !important; margin: 0px !important; color: #9EADB5; padding: 6px !important; 
                                                        -webkit-transition: all 0.5s ease; -moz-transition: all 0.5s ease; -ms-transition: all 0.5s ease; -o-transition: all 0.5s ease;
                                                        transition: all 0.5s ease;}
 .ui-selectonemenu-panel tr.ui-state-highlight td{color:#27AAE1; background-color:#fff;}

        
/*Style For SelectCheckBoxMenu ------------------------------------------------------------------------------------- */

.ui-selectcheckboxmenu{font-size:16px !important; font-family: 'titillium_webregular' !important; padding:0px 15px 0px 0px !important; border:solid 1px #D1D3D4 !important; -webkit-transition: all 0.5s ease;
    -moz-transition: all 0.5s ease; -ms-transition: all 0.5s ease; -o-transition: all 0.5s ease; transition: all 0.5s ease; color: #9FADB5;}
    .ui-selectcheckboxmenu .ui-selectcheckboxmenu-label{padding:5px 26px 5px 5px !important; background-color: #fff;}
    .ui-selectcheckboxmenu .ui-selectcheckboxmenu-label.ui-state-hover{color:#9FADB5 !important;}
    .ui-selectcheckboxmenu-panel{ background-color:#FBFCFD; overflow:hidden; border: solid 1px #27AAE1; -webkit-box-shadow: 0px 0px 3px 0px rgba(0,0,0,0.3) !important; 
                            -moz-box-shadow: 0px 0px 3px 0px rgba(0,0,0,0.3) !important; box-shadow: 0px 0px 3px 0px rgba(0,0,0,0.3) !important;}
    .ui-selectcheckboxmenu-panel .ui-selectcheckboxmenu-list-item{ font-size: 14px !important; border:0px !important; padding:7px !important;}
        .ui-selectcheckboxmenu-panel .ui-selectcheckboxmenu-list-item .ui-chkbox .ui-chkbox-box{margin-top:0px;}
    .ui-selectcheckboxmenu-panel .ui-selectcheckboxmenu-filter-container{width:80%;}
        .ui-selectcheckboxmenu-panel .ui-selectcheckboxmenu-filter-container .ui-inputfield{width:75%; padding-left: 30px !important;}
        .ui-selectcheckboxmenu-panel .ui-selectcheckboxmenu-filter-container .ui-icon-search{width:30px; height:30px; background-size: 100%; background-position: center; background-repeat: no-repeat;
                        background-image:url("#{resource['primefaces-sentinel:images/search-icon.svg']}"); left:0px;}
        .ui-selectcheckboxmenu-panel .ui-selectcheckboxmenu-header .ui-chkbox{ margin-top: 5px !important;}
        .ui-selectcheckboxmenu-panel .ui-selectcheckboxmenu-checked{ background-color: #F1F4F7;}
        .ui-selectcheckboxmenu-panel .ui-selectcheckboxmenu-close{padding:0px !important; margin-right: -2px !important; margin-top: -7px;}
        .ui-selectcheckboxmenu-panel .ui-selectcheckboxmenu-close .ui-icon-circle-close{background-image:url("#{resource['primefaces-sentinel:images/delete-gray.svg']}") !important;
                             background-size:90%; background-position: center;}
                       

/*Style For UI Growl Item ------------------------------------------------------------------------------------- */

.ui-growl{width: 200px !important; top: 60px !important; right:12px !important;}
	.ui-growl-item-container{opacity: 1; filter: alpha(opacity=100); padding:14px;}
        .ui-growl-item{padding: 20px 20px !important; background-color: #FBFCFD; border: solid 1px #F03369; border-radius: 3px; -webkit-border-radius: 3px; -moz-border-radius: 3px;}
	.ui-growl-message{width:100px !important; font-size:13px !important; line-height: 15px !important; color:#F03369 !important;}
	.ui-growl-image{width: 21px !important; height: 23px !important;}
	.ui-growl-image-error, .ui-growl-image-warn{background-image:url("#{resource['primefaces-sentinel:images/warn-pink.svg']}") !important; background-position:center !important; 
                             background-size: 140% !important; margin-top:5px;}
        .ui-growl-image-info{background-image:url("#{resource['primefaces-sentinel:images/warn-pink.svg']}") !important; background-position:center !important; 
                             background-size: 140% !important; margin-top:5px;}
	.ui-growl-icon-close{width:10px !important; height:10px !important; background-image:url("#{resource['primefaces-sentinel:images/delete-pink.svg']}") !important; 
                            background-position:center !important; background-size:130% !important;}


/*Style For UI Log ------------------------------------------------------------------------------------- */

.ui-log{border: solid 1px #D1D3D4; padding: 2px !important; background-color: #FBFCFD !important;-webkit-box-shadow: 0px 0px 5px 0px rgba(0,0,0,0.2) !important;
		-moz-box-shadow: 0px 0px 5px 0px rgba(0,0,0,0.2) !important; box-shadow: 0px 0px 5px 0px rgba(0,0,0,0.2) !important; color: #9FADB5; color: #9FADB5;}
.ui-log:hover, .ui-log:focus{border:solid 1px #27AAE1;}
	.ui-log-header{padding:10px !important; font-size: 16px !important; font-size:16px; border:solid 1px #175787; color:#ffffff; text-shadow: 0 -1px 0 #0A385E; position:relative; z-index:100;
					border-radius:3px !important; -webkit-border-radius:3px !important; -moz-border-radius:3px !important;
					background: #1578c9;
					background: -moz-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
					background: -webkit-gradient(left top, left bottom, color-stop(0%, #1578c9), color-stop(100%, #0b66b1));
					background: -webkit-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
					background: -o-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
					background: -ms-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
					background: linear-gradient(to bottom, #1578c9 0%, #0b66b1 100%);
					filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#1578c9', endColorstr='#0b66b1', GradientType=0 ); font-family: 'titillium_webregular' !important;}
        .ui-log-header .ui-log-clear .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/clear.svg']}") !important; background-position:center !important; background-size:140%;}
        
        .ui-log-header .ui-log-all .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/all-doc.svg']}") !important; background-position:center !important; background-size:140%;}
        
        .ui-log-header .ui-log-info .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/info.svg']}") !important; background-position:center !important; background-size:140%;}
        .ui-log-item-info .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/info-gray.svg']}") !important; background-position:center !important; background-size:140%;
        width:20px; height: 20px;}
        
        .ui-log-header .ui-log-warn .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/warn.svg']}") !important; background-position:center !important; background-size:140%;}
        .ui-log-item-warn .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/warn-gray.svg']}") !important; background-position:center !important; background-size:140%;
        width:20px; height: 20px;}
        
        .ui-log-header .ui-log-debug .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/debug.svg']}") !important; background-position:center !important; background-size:140%;}
        .ui-log-item-debug .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/debug-gray.svg']}") !important; background-position:center !important; background-size:140%; 
                width:20px; height: 20px;}
        
        .ui-log-header .ui-log-error .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/error.svg']}") !important; background-position:center !important; background-size:140%;}
        .ui-log-item-error .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/error-gray.svg']}") !important; background-position:center !important; background-size:140%;
        width:20px; height: 20px;}
        
	.ui-log-button{top:initial !important; left:initial !important; position:relative !important; display:inline-block !important; padding:0px !important; margin-right:5px;}
	.ui-log-content{height:250px !important; color: #9FADB5; margin: 5px;}
	.ui-log-items{font-size:14px; color: #9FADB5;}
        
        
/*Style For Checkbox --------------------------------------------------------------------------------------*/

.ui-selectmanycheckbox label{margin:-2px 15px 0px 0px !important; color: #9FADB5;}
.ui-chkbox .ui-chkbox-box{ background:none; box-shadow: none; margin-right: 5px; margin-top: 2px; color:#A9A9A9; border: solid 1px #D1D3D4; cursor:pointer;}
        .ui-chkbox .ui-state-hover, .ui-chkbox .ui-state-focus{border:solid 1px #00AEEF;}
            .ui-chkbox-box .ui-icon-blank{ background: none;}
            .ui-chkbox-box .ui-icon-check{border-radius: 0px; background-image:url("#{resource['primefaces-sentinel:images/check.svg']}") !important; background-size: 100%; background-position: center;}
            .ui-chkbox .ui-chkbox-label{margin:0px 15px 0px 5px !important;}

            
/*Style For Radio Button --------------------------------------------------------------------------------------*/ 

.ui-selectoneradio label{margin:0px 15px 0px 0px !important; color: #9FADB5;}
.ui-radiobutton{ margin:5px 5px 0px 0px !important; cursor:pointer;}
.ui-radiobutton .ui-radiobutton-box{background:none; box-shadow: none; color:#A9A9A9; border: solid 1px #D1D3D4;}
.ui-radiobutton .ui-state-hover, .ui-radiobutton .ui-state-focus{border:solid 1px #00AEEF;}
.ui-radiobutton .ui-state-active .ui-icon-bullet, .ui-radiobutton .ui-state-focus .ui-icon-bullet {background-image:url("#{resource['primefaces-sentinel:images/radio-on.svg']}") !important; background-size: 100%; background-position: center; margin: 0px;}
            
/*Style For Seperator --------------------------------------------------------------------------------------*/            

.ui-separator{border: 0px; border-bottom: solid 1px #EAEAEA;}

/*Style For Keypad --------------------------------------------------------------------------------------*/  

#keypad-div{ background-color:#F1F4F7; padding:5px !important; border: solid 1px #27AAE1;}
#keypad-div .keypad-row button{font-family: 'titillium_webregular'; font-size: 12px; padding: 1px 3px; border: solid 1px #9FADB5; color: #9EADB5;}
#keypad-div .keypad-row button.ui-state-hover{background-color: #FBFCFD; color: #27AAE1;}
#keypad-div .keypad-row button.keypad-close{background-color: #DB5B5B; color: #FFF; border:solid 1px #DB5B5B;}
#keypad-div .keypad-row button.keypad-back{background-color: #82ACCF; color: #FFF; border: solid 1px #82ACCF;}
#keypad-div .keypad-row button.keypad-clear{background-color: #D2B26E; color: #FFF; border: solid 1px #D2B26E;}
#keypad-div .keypad-row button.keypad-shift{background-color: #7EB5B5; color: #FFF; border: solid 1px #7EB5B5;}


/*Style For Password Panel --------------------------------------------------------------------------------------*/  

.ui-password-panel{font-size: 14px; color: #9FADB5; border: solid 1px #27AAE1; padding: 10px !important; background-color:#FBFCFD !important;}
    .ui-password-panel .ui-password-meter{background-image:url("#{resource['primefaces-sentinel:images/password-meter.svg']}") !important; background-size: 100% !important;}


/*Style For Input Switch --------------------------------------------------------------------------------------*/ 

.ui-inputswitch{border:solid 1px #D1D3D4; padding: 1px !important; color: #9FADB5; font-size: 16px;}
.ui-inputswitch:hover, .ui-inputswitch:focus{border:solid 1px #27AAE1;}
    .ui-inputswitch-off, .ui-inputswitch-on{padding-top:0px !important; font-weight: normal !important;}
    .ui-inputswitch-handle{height:22px !important; border-radius:4px; -webkit-border-radius:4px; -moz-border-radius:4px; margin-top: 1px; margin-left: 1px;
        border:solid 1px #175787 !important; -webkit-box-shadow:inset 0 0 0 1px #26A3DB; -moz-box-shadow:inset 0 0 0 1px #26A3DB; box-shadow:inset 0 0 0 1px #26A3DB;
        background: #1578c9;
        background: -moz-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
        background: -webkit-gradient(left top, left bottom, color-stop(0%, #1578c9), color-stop(100%, #0b66b1));
        background: -webkit-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
        background: -o-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
        background: -ms-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
        background: linear-gradient(to bottom, #1578c9 0%, #0b66b1 100%);
        filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#1578c9', endColorstr='#0b66b1', GradientType=0 );}
    

/*Style For SelectOneListBox --------------------------------------------------------------------------------------*/ 

.ui-selectonelistbox{background-color: #FBFCFD; color: #9FADB5;}
.ui-selectonelistbox:hover{border:solid 1px #27AAE1;}
    .ui-selectonelistbox .ui-selectlistbox-item{padding:3px 5px;}
    .ui-selectonelistbox .ui-state-hover{color:#27AAE1;}
    .ui-selectonelistbox .ui-state-highlight{background-color: #F1F4F7 !important; color:#27AAE1;}
    
.ui-selectonelistbox .ui-selectlistbox-filter-container .ui-icon-search{width: 30px; height: 30px; background-size: 100%; background-position: center; background-repeat: no-repeat;
                                                                        background-image: url("#{resource['primefaces-sentinel:images/search-icon.svg']}"); left: 0px; top: 2px;}
.ui-selectonelistbox .ui-selectlistbox-filter-container .ui-inputfield{padding-left:30px !important; width: 75%;}
    
    
/*Style For SelectManyMenu --------------------------------------------------------------------------------------*/ 

.ui-selectmanymenu{background-color: #FBFCFD; color: #9FADB5;}
.ui-selectmanymenu:hover{border:solid 1px #27AAE1;}
    .ui-selectmanymenu .ui-selectlistbox-filter-container .ui-icon-search{width:30px; height:30px; background-size: 100%; background-position: center; background-repeat: no-repeat;
                        background-image:url("#{resource['primefaces-sentinel:images/search-icon.svg']}"); left:0px; top:2px;}
    .ui-selectmanymenu .ui-selectlistbox-filter-container .ui-inputfield{width:70%; padding-left: 30px !important;}
    .ui-selectmanymenu .ui-selectlistbox-item{padding:3px 5px 6px 5px;}
        .ui-selectmanymenu .ui-selectlistbox-item .ui-chkbox{float: left;}
    .ui-selectmanymenu .ui-state-hover{color:#27AAE1;}
    .ui-selectmanymenu .ui-state-highlight{background-color: #F1F4F7 !important; color:#27AAE1;}
    
    
/*Style For SelectManyMenu --------------------------------------------------------------------------------------*/ 

.ui-slider{background-color: #D5DFE4; color: #9FADB5;}
.ui-slider-horizontal{margin: 2px 11px;}
.ui-slider-vertical{margin: 11px 5px;}
    .ui-slider-range{background-color: #27AAE1;}
    .ui-slider-handle{width:21px !important; height: 21px !important; cursor:move !important;
        border:solid 1px #175787 !important; -webkit-box-shadow:inset 0 0 0 1px #26A3DB; -moz-box-shadow:inset 0 0 0 1px #26A3DB; box-shadow:inset 0 0 0 1px #26A3DB;
        background: #1578c9;
        background: -moz-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
        background: -webkit-gradient(left top, left bottom, color-stop(0%, #1578c9), color-stop(100%, #0b66b1));
        background: -webkit-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
        background: -o-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
        background: -ms-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
        background: linear-gradient(to bottom, #1578c9 0%, #0b66b1 100%);
        filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#1578c9', endColorstr='#0b66b1', GradientType=0 );}
    .ui-slider-vertical .ui-slider-handle{left:-6px !important;}
    .ui-slider-horizontal .ui-slider-handle{top:-6px !important;}
    

/*Style For SelectManyMenu --------------------------------------------------------------------------------------*/

.ui-rating-cancel a{background-image:url("#{resource['primefaces-sentinel:images/dislike.svg']}") !important; background-position: center !important; background-size:100% !important;
                   margin-right: 3px; width:16px !important; height: 16px !important; color: #9FADB5;}
.ui-rating-cancel-hover a{background-image:url("#{resource['primefaces-sentinel:images/dislike-on.svg']}") !important; background-position: center !important; background-size:100% !important;}
.ui-rating-star a{background-image:url("#{resource['primefaces-sentinel:images/star.svg']}") !important; background-position: center !important; background-size:100% !important; margin:0px 1px;}
.ui-rating-star-on a{background-image:url("#{resource['primefaces-sentinel:images/star-on.svg']}") !important; background-position: center !important; background-size:100% !important;}


/*Style For Spinner --------------------------------------------------------------------------------------*/

.ui-spinner{}
.ui-spinner .ui-spinner-input{text-align:left; padding:9px !important;}
.ui-spinner .ui-spinner-button{width:auto; height:17px; padding: 2px;}
.ui-spinner .ui-spinner-button .ui-button-text{height:3px; text-align: center;}
.ui-spinner .ui-spinner-up .ui-button-text .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/plus-sign.svg']}") !important; background-position: center !important;
        background-size:80% !important; margin-left:12px; margin-top:-8px;}
.ui-spinner .ui-spinner-down .ui-button-text .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/minus-sign.svg']}") !important; background-position: center !important;
        background-size:80% !important; margin-left:12px;}


/*Style For DatePicker And Calendar--------------------------------------------------------------------------------------*/

.ui-datepicker{border:solid 1px #D1D3D4; padding: 2px !important; background-color:#FBFCFD !important;}
.ui-datepicker:hover, .ui-datepicker:focus{border:solid 1px #27AAE1;}
    .ui-datepicker-header{font-size:14px; border:solid 1px #175787 !important; border-radius: 3px; -webkit-border-radius: 3px; -moz-border-radius: 3px;
        background: #1578c9;
        background: -moz-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
        background: -webkit-gradient(left top, left bottom, color-stop(0%, #1578c9), color-stop(100%, #0b66b1));
        background: -webkit-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
        background: -o-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
        background: -ms-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
        background: linear-gradient(to bottom, #1578c9 0%, #0b66b1 100%);
        filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#1578c9', endColorstr='#0b66b1', GradientType=0 );}
    .ui-datepicker-header .ui-datepicker-prev{left:3px !important; top:3px !important; cursor:pointer;}
    .ui-datepicker-header .ui-datepicker-next{right:3px !important; top:3px !important; cursor:pointer;}
    .ui-datepicker-header .ui-datepicker-prev-hover{background-color: #175787; left:3px; top:3px;}
    .ui-datepicker-header .ui-datepicker-next-hover{background-color: #175787; right:3px; top:3px;}
    .ui-datepicker-header .ui-datepicker-prev .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/left-arrow.svg']}"); background-position: center !important;
        background-size:100% !important; top:50%; left:48%;}
    .ui-datepicker-header .ui-datepicker-next .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/right-arrow.svg']}"); background-position: center !important;
        background-size:100% !important; top:50%; left:51%;}
    
    .ui-datepicker-calendar{margin:2px 0px 0px 0px !important; border-collapse:inherit !important;}
        .ui-datepicker-calendar thead tr th{background-color: #EFF3F5; color: #9FADB5; border-radius: 3px; -webkit-border-radius: 3px; -moz-border-radius: 3px; cursor:default; padding: 10px 0px;}
        .ui-datepicker-calendar thead tr .ui-datepicker-week-end{background-color: #DAE8EF;}
        
        .ui-datepicker-calendar tbody tr td{border: solid 1px #E5EBF0; border-radius: 3px; -webkit-border-radius: 3px; -moz-border-radius: 3px; cursor:default; padding: 5px 0px; 
                                           -webkit-transition: all 0.5s ease; -moz-transition: all 0.5s ease; -ms-transition: all 0.5s ease; -o-transition: all 0.5s ease; transition: all 0.5s ease;}
        .ui-datepicker-calendar tbody tr td:hover{border: solid 1px #27AAE1;}
        .ui-datepicker-calendar tbody tr td:hover a{color:#27AAE1; -webkit-transition: all 0.5s ease; -moz-transition: all 0.5s ease; -ms-transition: all 0.5s ease; 
                -o-transition: all 0.5s ease; transition: all 0.5s ease;}
        .ui-datepicker-calendar tbody tr td a{color: #B9CDD5; background-color:transparent !important;}
        .ui-datepicker-calendar tbody tr .ui-datepicker-today{background-color: #F4F7F9; border: solid 1px #F686A6;}
        .ui-datepicker-calendar tbody tr .ui-datepicker-today a{color: #F686A6;}
        .ui-datepicker-calendar tbody tr .ui-datepicker-current-day{color:#27AAE1; border: solid 1px #27AAE1;}
        .ui-datepicker-calendar tbody tr .ui-datepicker-current-day a{color:#27AAE1;}
    
    .ui-datepicker-trigger{width:auto !important; margin-left: 3px;}
    .ui-datepicker-trigger .ui-icon{background-image:url("#{resource['primefaces-sentinel:images/calendar.svg']}"); background-position: center !important;
        background-size:140% !important;}
    
    .ui-datepicker-group .ui-datepicker-calendar{width:100% !important;}
 
.ui-timepicker-div{color: #A1AFB7;}
.ui-timepicker-div dl{padding: 1px 10px;}
    .ui-timepicker-div .ui-widget-header{font-size:14px; border:solid 1px #175787 !important; padding: 3px; border-radius: 3px; -webkit-border-radius: 3px; -moz-border-radius: 3px;
        background: #1578c9;
        background: -moz-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
        background: -webkit-gradient(left top, left bottom, color-stop(0%, #1578c9), color-stop(100%, #0b66b1));
        background: -webkit-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
        background: -o-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
        background: -ms-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
        background: linear-gradient(to bottom, #1578c9 0%, #0b66b1 100%);
        filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#1578c9', endColorstr='#0b66b1', GradientType=0 );}
        

/*Style For UI Tooltip --------------------------------------------------------------------------------------*/

.ui-tooltip{background-color:#FBFCFD; border: solid 1px #27AAE1; color:#27AAE1; border-top-left-radius: 0px; opacity:0.8;}


/*Style For UI CommandLink --------------------------------------------------------------------------------------*/

.ui-commandlink{color: #27AAE1 !important; text-decoration: underline; margin-right: 5px;}

/*Style For UI Link --------------------------------------------------------------------------------------*/

.ui-link{color: #27AAE1 !important; text-decoration: underline; margin-right: 5px;}


/*Style For UI Editor --------------------------------------------------------------------------------------*/

.ui-editor{border: solid 1px #D1D3D4; border-radius: 5px !important; -webkit-border-radius: 5px !important; -moz-border-radius: 5px  !important; overflow: hidden;
-webkit-transition: all 0.5s ease; -moz-transition: all 0.5s ease; -ms-transition: all 0.5s ease; -o-transition: all 0.5s ease; transition: all 0.5s ease; padding:5px;
font-family: 'titillium_webregular';}
.ui-editor:hover, .ui-editor:focus{border:solid 1px #27AAE1;}
    .ui-editor-toolbar{ background-image: none !important; background-color: #D5DFE4 !important; border: solid 1px #ADBAC0;
    border-radius: 3px !important; -webkit-border-radius: 3px !important; -moz-border-radius: 3px  !important;}
    

.ui-outputlabel, table tbody tr td{color: #9FADB5;}


/*Style For UI Galleria --------------------------------------------------------------------------------------*/

.ui-galleria{border: solid 1px #D1D3D4; border-radius: 5px !important; -webkit-border-radius: 5px !important; -moz-border-radius: 5px  !important; 
            background-color: #FBFCFD; color: #9FADB5;}
.ui-galleria-filmstrip-wrapper{border-top: solid 1px #E8EDF2 !important; background-color: #F3F5F7;}
    .ui-galleria-nav-prev, .ui-galleria-nav-next{padding: 2px; bottom: 14px !important;}
    .ui-galleria-nav-prev:hover, .ui-galleria-nav-next:hover{}
    .ui-galleria-nav-prev{left:2px !important; background-image:url("#{resource['primefaces-sentinel:images/leftarrow-gray.svg']}") !important; background-position: center !important;
        background-size:100% !important;}
    .ui-galleria-nav-next{right:2px !important; background-image:url("#{resource['primefaces-sentinel:images/rightarrow-gray.svg']}") !important; background-position: center !important;
        background-size:120% !important;}
    
    
/*Style For UI ProgressBar --------------------------------------------------------------------------------------*/

.ui-progressbar{border: solid 1px #D1D3D4; background-color: #FBFCFD;}
.ui-progressbar:hover{border:solid 1px #27AAE1;}
    .ui-progressbar-value{background-color: #dae8ef; margin: 0px !important; background-image:url("#{resource['primefaces-sentinel:images/progressbar-back.svg']}") !important;
                          background-position: center left !important; background-size:51px !important; background-repeat:repeat-x;}
    .ui-progressbar-label{color:#616E74; font-size: 16px; line-height: 16px;}
    
    
/*Style For UI ProgressBar --------------------------------------------------------------------------------------*/

.ui-clock{border:solid 1px #175787 !important; padding: 3px; border-radius: 3px; -webkit-border-radius: 3px; -moz-border-radius: 3px;
        background: #1578c9;
        background: -moz-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
        background: -webkit-gradient(left top, left bottom, color-stop(0%, #1578c9), color-stop(100%, #0b66b1));
        background: -webkit-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
        background: -o-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
        background: -ms-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
        background: linear-gradient(to bottom, #1578c9 0%, #0b66b1 100%);
        filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#1578c9', endColorstr='#0b66b1', GradientType=0 );}

/* Style For UI Autocomplete --------------------------------------------------------------------------------------*/

.ui-autocomplete{}
.ui-autocomplete .ui-autocomplete-dropdown{}
.ui-autocomplete .ui-autocomplete-dropdown .ui-icon-triangle-1-s{background-image:url("#{resource['primefaces-sentinel:images/downarrow.svg']}"); background-position: center !important;
        background-size:100% !important; z-index: 9999;}
.ui-autocomplete-panel{border: solid 1px #27AAE1; background-color: #FBFCFD; background-color: #F1F4F7;}
    .ui-autocomplete-panel .ui-autocomplete-items{padding:0px !important;}
        .ui-autocomplete-panel .ui-autocomplete-items .ui-autocomplete-item{color:#9FADB5; border-bottom: solid 1px #E5EAF0; font-size: 14px; padding: 6px 8px; border-radius: 0px;
                    -webkit-transition: all 0.5s ease; -moz-transition: all 0.5s ease; -ms-transition: all 0.5s ease; -o-transition: all 0.5s ease; transition: all 0.5s ease; margin: 0px;}
        .ui-autocomplete-panel .ui-autocomplete-items .ui-autocomplete-group{color: #6C7A83; background-color: #E5EAEF; padding: 6px 8px; font-size: 12px; border:0px; margin: 0px;}
        .ui-autocomplete-panel .ui-autocomplete-items .ui-state-highlight{background-color:#ffffff;}
        
.ui-autocomplete-panel .ui-autocomplete-table th, .ui-autocomplete-panel .ui-autocomplete-table td{ border-color: #E5EAF0 !important;}

.ui-autocomplete-multiple .ui-autocomplete-multiple-container .ui-autocomplete-token{background-color: #F1F4F7; padding: 5px;}
.ui-autocomplete-multiple .ui-autocomplete-multiple-container .ui-autocomplete-token .ui-icon-close{background-image:url("#{resource['primefaces-sentinel:images/delete-gray.svg']}") !important;
                    background-position: center !important; background-size:100% !important;}
    .ui-autocomplete-input-token input[type="text"]{color: #9EADB5; font-size: 14px; font-family: 'titillium_webregular' !important;}
    
.ui-autocomplete-itemtip{border: solid 1px #D1D3D4; background-color: #FBFCFD; font-size: 14px; padding:2px !important;}
.ui-autocomplete-itemtip table thead tr th{border-bottom: solid 1px #E8EDF2 !important; color: #9FADB5; font-size: 16px;}


/* Style For UI MultiSelectListbox --------------------------------------------------------------------------------------*/
.ui-multiselectlistbox .ui-multiselectlistbox-listcontainer .ui-multiselectlistbox-header{border: solid 1px #d1d3d4; background-color: #F3F5F7; color: #A1AFB6; padding: 4px 0px !important;
                margin: 4px 0px 0px 0px; -webkit-transition: all 0.5s ease; -moz-transition: all 0.5s ease; -ms-transition: all 0.5s ease; -o-transition: all 0.5s ease; transition: all 0.5s ease;}
    .ui-multiselectlistbox .ui-multiselectlistbox-listcontainer .ui-multiselectlistbox-list{border: solid 1px #d1d3d4; background-color: #FBFCFD; border-top:0px;}
        .ui-multiselectlistbox .ui-multiselectlistbox-listcontainer .ui-multiselectlistbox-list .ui-multiselectlistbox-item{font-size:14px; padding: 1px 5px; border-radius:5px; 
                                   -webkit-border-radius:5px; -moz-border-radius:5px;}
        .ui-multiselectlistbox .ui-multiselectlistbox-listcontainer .ui-multiselectlistbox-list .ui-state-hover{color:#27AAE1; background-color: #F1F4F7 !important;}
        .ui-multiselectlistbox .ui-multiselectlistbox-listcontainer .ui-multiselectlistbox-list .ui-state-highlight{color:#27AAE1; background-color: #F1F4F7 !important;}
        
        
/* Style For UI Terminal --------------------------------------------------------------------------------------*/

.ui-terminal{background-color: #F3F5F7; border: solid 1px #d1d3d4; padding: 10px; color: #9FADB5;}


/* Style For UI MenuButton --------------------------------------------------------------------------------------*/

.ui-menubutton .ui-icon-triangle-1-s{background-image:url("#{resource['primefaces-sentinel:images/downarrow.svg']}") !important;
                    background-position: center !important; background-size:120% !important;}

/* Style For Toolbar Seperator --------------------------------------------------------------------------------------*/

.ui-toolbar .ui-separator{padding:0px 5px;}

/*Style For Jqplot Charts --------------------------------------------------------------------------------------*/

.jqplot-target{}
.jqplot-axis {}
.jqplot-xaxis{}
.jqplot-yaxis{}
.jqplot-x2axis, .jqplot-x3axis{} /* ... Styles applied to the 2nd, 3rd, etc. x axis only. */
.jqplot-y2axis, .jqplot-y3axis{} /* ... Styles applied to the 2nd, 3rd, etc. y axis only. */
.jqplot-axis-tick{}
.jqplot-xaxis-tick{}
.jqplot-x2axis-tick{}
.jqplot-yaxis-tick{}
.jqplot-y2axis-tick{}
table.jqplot-table-legend{}
.jqplot-title{}
.jqplot-cursor-tooltip{}
.jqplot-highlighter-tooltip{}
div.jqplot-table-legend-swatch


/*Style For UI Outline --------------------------------------------------------------------------------------*/

.ui-tabs-outline{outline:none !important;}
.ui-tabs-top .ui-tabs-nav .ui-tabs-outline,
.ui-tabs-bottom .ui-tabs-nav .ui-tabs-outline,
.ui-tabs-left .ui-tabs-nav .ui-tabs-outline,
.ui-tabs-right .ui-tabs-nav .ui-tabs-outline{outline:none !important; border:dotted 1px #fff !important; background-color: transparent !important;}
.ui-accordion .ui-tabs-outline{outline:none !important;  -webkit-box-shadow: 0px 0px 0px 3px rgba(224,40,50,0.4);  -moz-box-shadow: 0px 0px 0px 3px rgba(224,40,50,0.4); 
                box-shadow: 0px 0px 0px 3px rgba(224,40,50,0.4);}


/*Style For UploadButton --------------------------------------------------------------------------------------*/

.ui-fileupload-choose input[type="file"],
.ui-fileupload-simple input[type="file"]{z-index:999;}
.ui-fileupload-choose .ui-icon-plusthick,
.ui-fileupload-simple .ui-icon-plusthick{background-image:url("#{resource['primefaces-sentinel:images/plus-sign.svg']}") !important;
                    background-position: center !important; background-size:100% !important;}
.ui-fileupload-upload .ui-icon-arrowreturnthick-1-n{background-image:url("#{resource['primefaces-sentinel:images/upload.svg']}") !important;
                    background-position: center !important; background-size:100% !important;}
.ui-fileupload-cancel .ui-icon-cancel{background-image:url("#{resource['primefaces-sentinel:images/delete.svg']}") !important;
                    background-position: center !important; background-size:100% !important;}
.ui-fileupload-cancel .ui-icon-close{background-image:url("#{resource['primefaces-sentinel:images/delete.svg']}") !important;
                    background-position: center !important; background-size:100% !important;}


/*Style For UI Steps --------------------------------------------------------------------------------------*/

.ui-steps {min-height:50px; margin-top: 50px;}
.ui-steps .ui-steps-item{background-color: #D5DFE4; margin-right: 1px;}
.ui-steps .ui-steps-item .ui-menuitem-link{height:10px; padding: 0px 10px !important;}
.ui-steps .ui-steps-item .ui-steps-number{border: solid 1px #175787 !important; display: inline-block; margin-top: -30px; height: auto !important; padding: 1px 7px !important; font-size: 20px;
color: #fff; border-radius: 5px; -webkit-border-radius: 5px; -moz-border-radius: 5px;
background: #1578c9;
background: -moz-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
background: -webkit-gradient(left top, left bottom, color-stop(0%, #1578c9), color-stop(100%, #0b66b1));
background: -webkit-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
background: -o-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
background: -ms-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
background: linear-gradient(to bottom, #1578c9 0%, #0b66b1 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#1578c9', endColorstr='#0b66b1', GradientType=0 );}
.ui-steps .ui-steps-title{color: #9FADB5; margin: 0px 10px;}


/*Style For UI TagCloud --------------------------------------------------------------------------------------*/

.ui-tagcloud{border: solid 1px #d1d3d4;}
.ui-tagcloud li a{color: #9FADB5; padding:3px; margin: 3px; border-radius: 5px; -webkit-border-radius: 5px; -moz-border-radius: 5px;}
.ui-tagcloud li a.ui-state-hover{color:#fff;
background: #1578c9;
background: -moz-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
background: -webkit-gradient(left top, left bottom, color-stop(0%, #1578c9), color-stop(100%, #0b66b1));
background: -webkit-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
background: -o-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
background: -ms-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
background: linear-gradient(to bottom, #1578c9 0%, #0b66b1 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#1578c9', endColorstr='#0b66b1', GradientType=0 );}


/*Style For UI Inplace --------------------------------------------------------------------------------------*/

.ui-inplace .ui-inplace-editor .ui-button .ui-icon-check{background-image:url("#{resource['primefaces-sentinel:images/check-white.svg']}") !important;
                    background-position: center !important; background-size:110% !important;}
.ui-inplace .ui-inplace-editor .ui-button .ui-icon-close{background-image:url("#{resource['primefaces-sentinel:images/delete.svg']}") !important;
                    background-position: center !important; background-size:110% !important;}
.ui-inplace .ui-inplace-display.ui-state-highlight{background-color: #F3F5F7; padding: 3px; border: solid 1px #D1D3D4 !important; border-radius: 3px;}


/*Style For UI DataScroller --------------------------------------------------------------------------------------*/

.ui-datascroller{border: solid 1px #d1d3d4; border-radius: 5px; -webkit-border-radius: 5px; -moz-border-radius: 5px; background-color: #FBFCFD;}
.ui-datascroller .ui-datascroller-header{
    background: #1578c9;
background: -moz-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
background: -webkit-gradient(left top, left bottom, color-stop(0%, #1578c9), color-stop(100%, #0b66b1));
background: -webkit-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
background: -o-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
background: -ms-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
background: linear-gradient(to bottom, #1578c9 0%, #0b66b1 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#1578c9', endColorstr='#0b66b1', GradientType=0 );}
.ui-datascroller .ui-datascroller-item{border-bottom: solid 1px #d1d3d4 !important;}


/*Style For UI LayoutContainer --------------------------------------------------------------------------------------*/

.ui-layout-container .ui-layout-unit{border: solid 1px #d1d3d4; background-color: #FBFCFD; color: #9FADB5; border-radius: 5px; -webkit-border-radius: 5px; -moz-border-radius: 5px;}


/*Style For UI LayoutUnit --------------------------------------------------------------------------------------*/

.ui-layout-unit .ui-layout-unit-header{color: #fff; padding: 6px 6px 8px 10px !important; border: solid 1px #175787; font-size: 14px;
background: #1578c9;
background: -moz-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
background: -webkit-gradient(left top, left bottom, color-stop(0%, #1578c9), color-stop(100%, #0b66b1));
background: -webkit-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
background: -o-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
background: -ms-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
background: linear-gradient(to bottom, #1578c9 0%, #0b66b1 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#1578c9', endColorstr='#0b66b1', GradientType=0 );}

.ui-layout-unit .ui-layout-unit-header .ui-layout-unit-header-icon .ui-icon-close{background-image:url("#{resource['primefaces-sentinel:images/delete.svg']}") !important;
                    background-position: center !important; background-size:110% !important; margin-top: 4px;}
.ui-layout-unit .ui-layout-unit-header .ui-layout-unit-header-icon .ui-icon-triangle-1-n{background-image:url("#{resource['primefaces-sentinel:images/uparrow.svg']}") !important;
                    background-position: center !important; background-size:120% !important; margin-top: 4px;}
.ui-layout-unit .ui-layout-unit-header .ui-layout-unit-header-icon .ui-icon-triangle-1-w{background-image:url("#{resource['primefaces-sentinel:images/left-arrow.svg']}") !important;
                    background-position: center !important; background-size:100% !important; margin-top: 4px;}
.ui-layout-unit .ui-layout-unit-header .ui-layout-unit-header-icon .ui-icon-triangle-1-e{background-image:url("#{resource['primefaces-sentinel:images/right-arrow.svg']}") !important;
                    background-position: center !important; background-size:100% !important; margin-top: 4px;}
.ui-layout-unit .ui-layout-unit-header .ui-layout-unit-header-icon .ui-icon-triangle-1-s{background-image:url("#{resource['primefaces-sentinel:images/downarrow.svg']}") !important;
                    background-position: center !important; background-size:120% !important; margin-top: 4px;}
.ui-layout-resizer .ui-layout-toggler .ui-icon-arrow-4-diag{background-image:url("#{resource['primefaces-sentinel:images/expand-gray.svg']}") !important;
                    background-position: center !important; background-size:110% !important;}

/*Style For UI Wizard --------------------------------------------------------------------------------------*/

.ui-wizard{}
.ui-wizard .ui-wizard-step-titles li{padding: 6px 20px !important; font-size: 14px; border: solid 1px #175787; color: #ffffff; text-shadow: 0 -1px 0 #0A385E;
border-radius: 5px !important;
-webkit-border-radius: 5px !important;
-moz-border-radius: 5px !important;
background: #1578c9;
background: -moz-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
background: -webkit-gradient(left top, left bottom, color-stop(0%, #1578c9), color-stop(100%, #0b66b1));
background: -webkit-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
background: -o-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
background: -ms-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
background: linear-gradient(to bottom, #1578c9 0%, #0b66b1 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#1578c9', endColorstr='#0b66b1', GradientType=0 );}
.ui-wizard .ui-wizard-step-titles li.ui-state-highlight{
background: #0b66b1;
background: -moz-linear-gradient(top, #0b66b1 0%, #1578c9 100%);
background: -webkit-gradient(left top, left bottom, color-stop(0%, #0b66b1), color-stop(100%, #1578c9));
background: -webkit-linear-gradient(top, #0b66b1 0%, #1578c9 100%);
background: -o-linear-gradient(top, #0b66b1 0%, #1578c9 100%);
background: -ms-linear-gradient(top, #0b66b1 0%, #1578c9 100%);
background: linear-gradient(to bottom, #0b66b1 0%, #1578c9 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#0b66b1', endColorstr='#1578c9', GradientType=0 );
-webkit-box-shadow: inset 0px 0px 10px 0px rgba(255,255,255,0.8) !important;
-moz-box-shadow: inset 0px 0px 10px 0px rgba(255,255,255,0.8) !important;
box-shadow: inset 0px 0px 10px 0px rgba(255,255,255,0.8) !important;}
.ui-wizard .ui-wizard-nav-back .ui-icon-arrowthick-1-w{background-image:url("#{resource['primefaces-sentinel:images/left-arrow.svg']}") !important;
                    background-position: center !important; background-size:110% !important;}
.ui-wizard .ui-wizard-nav-next .ui-icon-arrowthick-1-e{background-image:url("#{resource['primefaces-sentinel:images/right-arrow.svg']}") !important;
                    background-position: center !important; background-size:110% !important;}

/*Style For UI State Focus --------------------------------------------------------------------------------------*/

.ui-button.ui-state-focus .ui-button-text,
.ui-inputswitch-handle.ui-state-focus,
.ui-slider-handle.ui-state-focus{
-webkit-box-shadow: inset 0px 0px 10px 0px rgba(255,255,255,0.8) !important;
-moz-box-shadow: inset 0px 0px 10px 0px rgba(255,255,255,0.8) !important;
box-shadow: inset 0px 0px 10px 0px rgba(255,255,255,0.8) !important;}

.ui-selectonemenu.ui-state-focus,
.ui-selectcheckboxmenu.ui-state-focus,
.ui-paginator-page.ui-state-focus,
.ui-paginator-next.ui-state-focus,
.ui-paginator-prev.ui-state-focus,
.ui-paginator-last.ui-state-focus,
.ui-paginator-first.ui-state-focus,
.ui-paginator-rpp-options.ui-state-focus{border:solid 1px #27AAE1 !important;}

.ui-datatable .ui-datatable-tablewrapper table thead tr th.ui-state-focus{background-color: #27aae1; color: #fff;}


/* button colors */
.RedButton .ui-button-text{box-shadow: inset 0 0 0 1px #F23030; border: solid 1px #871717; text-shadow: 0 -1px 0 #670606;
background: #c91515;
background: -moz-linear-gradient(top, #c91515 0%, #b10b0b 100%);
background: -webkit-gradient(left top, left bottom, color-stop(0%, #c91515), color-stop(100%, #b10b0b));
background: -webkit-linear-gradient(top, #c91515 0%, #b10b0b 100%);
background: -o-linear-gradient(top, #c91515 0%, #b10b0b 100%);
background: -ms-linear-gradient(top, #c91515 0%, #b10b0b 100%);
background: linear-gradient(to bottom, #c91515 0%, #b10b0b 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#c91515', endColorstr='#b10b0b', GradientType=0 );}
.RedButton:hover .ui-button-text, .RedButton:focus .ui-button-text{color:#ffffff !important;
background: #b10b0b;
background: -moz-linear-gradient(top, #b10b0b 0%, #c91515 100%);
background: -webkit-gradient(left top, left bottom, color-stop(0%, #b10b0b), color-stop(100%, #c91515));
background: -webkit-linear-gradient(top, #b10b0b 0%, #c91515 100%);
background: -o-linear-gradient(top, #b10b0b 0%, #c91515 100%);
background: -ms-linear-gradient(top, #b10b0b 0%, #c91515 100%);
background: linear-gradient(to bottom, #b10b0b 0%, #c91515 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#b10b0b', endColorstr='#c91515', GradientType=0 );}

.GreenButton .ui-button-text{box-shadow: inset 0 0 0 1px #54DB26; border: solid 1px #2B8717; text-shadow: 0 -1px 0 #18840B;
background: #2dc915;
background: -moz-linear-gradient(top, #2dc915 0%, #0bb113 100%);
background: -webkit-gradient(left top, left bottom, color-stop(0%, #2dc915), color-stop(100%, #0bb113));
background: -webkit-linear-gradient(top, #2dc915 0%, #0bb113 100%);
background: -o-linear-gradient(top, #2dc915 0%, #0bb113 100%);
background: -ms-linear-gradient(top, #2dc915 0%, #0bb113 100%);
background: linear-gradient(to bottom, #2dc915 0%, #0bb113 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#2dc915', endColorstr='#0bb113', GradientType=0 );}
.GreenButton:hover .ui-button-text, .GreenButton:focus .ui-button-text{color:#ffffff !important;
background: #0bb113;
background: -moz-linear-gradient(top, #0bb113 0%, #2dc915 100%);
background: -webkit-gradient(left top, left bottom, color-stop(0%, #0bb113), color-stop(100%, #2dc915));
background: -webkit-linear-gradient(top, #0bb113 0%, #2dc915 100%);
background: -o-linear-gradient(top, #0bb113 0%, #2dc915 100%);
background: -ms-linear-gradient(top, #0bb113 0%, #2dc915 100%);
background: linear-gradient(to bottom, #0bb113 0%, #2dc915 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#0bb113', endColorstr='#2dc915', GradientType=0 );}

.OrangeButton .ui-button-text{box-shadow: inset 0 0 0 1px #DBB626; border: solid 1px #876C17; text-shadow: 0 -1px 0 #7B6308;
background: #c99f15;
background: -moz-linear-gradient(top, #c99f15 0%, #b1900b 100%);
background: -webkit-gradient(left top, left bottom, color-stop(0%, #c99f15), color-stop(100%, #b1900b));
background: -webkit-linear-gradient(top, #c99f15 0%, #b1900b 100%);
background: -o-linear-gradient(top, #c99f15 0%, #b1900b 100%);
background: -ms-linear-gradient(top, #c99f15 0%, #b1900b 100%);
background: linear-gradient(to bottom, #c99f15 0%, #b1900b 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#c99f15', endColorstr='#b1900b', GradientType=0 );}
.OrangeButton:hover .ui-button-text, .OrangeButton:focus .ui-button-text{color:#ffffff !important;
background: #b1900b;
background: -moz-linear-gradient(top, #b1900b 0%, #c99f15 100%);
background: -webkit-gradient(left top, left bottom, color-stop(0%, #b1900b), color-stop(100%, #c99f15));
background: -webkit-linear-gradient(top, #b1900b 0%, #c99f15 100%);
background: -o-linear-gradient(top, #b1900b 0%, #c99f15 100%);
background: -ms-linear-gradient(top, #b1900b 0%, #c99f15 100%);
background: linear-gradient(to bottom, #b1900b 0%, #c99f15 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#b1900b', endColorstr='#c99f15', GradientType=0 );}

/* shadow for elements*/
.shadows{-webkit-box-shadow: 0px 0px 5px 0px rgba(0,0,0,0.5); -moz-box-shadow: 0px 0px 5px 0px rgba(0,0,0,0.5); box-shadow: 0px 0px 5px 0px rgba(0,0,0,0.5);}

/* font colors */
.gray{color:#5A5A5A;}
.softgray{color:#CDCDCD;}
.yellow{color:#FFD100;}
.white{ color:#ffffff;}
.red{color:#e90c45;}
.orange{color:#F15A29;}
.softblue{color:#C1E1F4;}
.hardblue{color:#27AAE1;}
.leaden{color:#9FADB5;}

/* fonts */
.fontRegular{font-family: 'titillium_webregular' !important;}
.fontItalic{font-style:italic;}

.fontSemibold{font-family: 'titillium_websemibold';}
.fontSemiboldItalic{font-family: 'titillium_websemibold_italic';}

.fontBold{font-family: 'titillium_webbold';}
.fontBoldItalic{font-family: 'titillium_webbold_italic';}