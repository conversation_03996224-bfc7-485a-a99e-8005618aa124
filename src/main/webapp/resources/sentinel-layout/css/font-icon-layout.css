@font-face {
  font-family: 'fontello';
  src: url("#{resource['sentinel-layout:fonts/sentinel.eot']}&33675971");
  src: url("#{resource['sentinel-layout:fonts/sentinel.eot']}&33675971#iefix") format('embedded-opentype'),
       url("#{resource['sentinel-layout:fonts/sentinel.woff']}&33675971") format('woff'),
       url("#{resource['sentinel-layout:fonts/sentinel.ttf']}&33675971") format('truetype'),
       url("#{resource['sentinel-layout:fonts/sentinel.svg']}&33675971#fontello") format('svg');
  font-weight: normal;
  font-style: normal;
}
/* Chrome hack: SVG is rendered more smooth in Windozze. 100% magic, uncomment if you need it. */
/* Note, that will break hinting! In other OS-es font will be not as sharp as it could be */
/*
@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: 'fontello';
    src: url('../font/fontello.svg?33675971#fontello') format('svg');
  }
}
*/
 
 [class^="icon-"]:before, [class*=" icon-"]:before {
  font-family: "fontello";
  font-style: normal;
  font-weight: normal;
  speak: none;
 
  display: inline-block;
  text-decoration: inherit;
  width: 1em;
  margin-right: .2em;
  text-align: center;
  /* opacity: .8; */
 
  /* For safety - reset parent styles, that can break glyph codes*/
  font-variant: normal;
  text-transform: none;
     
  /* fix buttons height, for twitter bootstrap */
  line-height: 1em;
 
  /* Animation center compensation - margins should be symmetric */
  /* remove if not needed */
  margin-left: .2em;
 
  /* you can be more comfortable with increased icons size */
  /* font-size: 120%; */
 
  /* Uncomment for 3D effect */
  /* text-shadow: 1px 1px 1px rgba(127, 127, 127, 0.3); */
}
 
.icon-laptop:before { content: '\e800'; } /* '' */
.icon-tablet:before { content: '\e801'; } /* '' */
.icon-mobile:before { content: '\e802'; } /* '' */
.icon-inbox:before { content: '\e803'; } /* '' */
.icon-globe:before { content: '\e804'; } /* '' */
.icon-sun:before { content: '\e805'; } /* '' */
.icon-cloud:before { content: '\e806'; } /* '' */
.icon-flash:before { content: '\e807'; } /* '' */
.icon-moon:before { content: '\e808'; } /* '' */
.icon-umbrella:before { content: '\e809'; } /* '' */
.icon-flight:before { content: '\e80a'; } /* '' */
.icon-fighter-jet:before { content: '\e80b'; } /* '' */
.icon-paper-plane:before { content: '\e80c'; } /* '' */
.icon-paper-plane-empty:before { content: '\e80d'; } /* '' */
.icon-space-shuttle:before { content: '\e80e'; } /* '' */
.icon-leaf:before { content: '\e80f'; } /* '' */
.icon-font:before { content: '\e810'; } /* '' */
.icon-bold:before { content: '\e811'; } /* '' */
.icon-italic:before { content: '\e812'; } /* '' */
.icon-header:before { content: '\e813'; } /* '' */
.icon-paragraph:before { content: '\e814'; } /* '' */
.icon-text-height:before { content: '\e815'; } /* '' */
.icon-text-width:before { content: '\e816'; } /* '' */
.icon-align-left:before { content: '\e817'; } /* '' */
.icon-align-center:before { content: '\e818'; } /* '' */
.icon-align-right:before { content: '\e819'; } /* '' */
.icon-align-justify:before { content: '\e81a'; } /* '' */
.icon-list:before { content: '\e81b'; } /* '' */
.icon-indent-left:before { content: '\e81c'; } /* '' */
.icon-indent-right:before { content: '\e81d'; } /* '' */
.icon-list-bullet:before { content: '\e81e'; } /* '' */
.icon-list-numbered:before { content: '\e81f'; } /* '' */
.icon-strike:before { content: '\e820'; } /* '' */
.icon-underline:before { content: '\e821'; } /* '' */
.icon-superscript:before { content: '\e822'; } /* '' */
.icon-subscript:before { content: '\e823'; } /* '' */
.icon-table:before { content: '\e824'; } /* '' */
.icon-columns:before { content: '\e825'; } /* '' */
.icon-crop:before { content: '\e826'; } /* '' */
.icon-scissors:before { content: '\e827'; } /* '' */
.icon-paste:before { content: '\e828'; } /* '' */
.icon-briefcase:before { content: '\e829'; } /* '' */
.icon-suitcase:before { content: '\e82a'; } /* '' */
.icon-ellipsis:before { content: '\e82b'; } /* '' */
.icon-ellipsis-vert:before { content: '\e82c'; } /* '' */
.icon-off:before { content: '\e82d'; } /* '' */
.icon-road:before { content: '\e82e'; } /* '' */
.icon-list-alt:before { content: '\e82f'; } /* '' */
.icon-qrcode:before { content: '\e830'; } /* '' */
.icon-barcode:before { content: '\e831'; } /* '' */
.icon-book:before { content: '\e832'; } /* '' */
.icon-ajust:before { content: '\e833'; } /* '' */
.icon-tint:before { content: '\e834'; } /* '' */
.icon-check:before { content: '\e835'; } /* '' */
.icon-check-empty:before { content: '\e836'; } /* '' */
.icon-circle:before { content: '\e837'; } /* '' */
.icon-circle-empty:before { content: '\e838'; } /* '' */
.icon-circle-thin:before { content: '\e839'; } /* '' */
.icon-circle-notch:before { content: '\e83a'; } /* '' */
.icon-dot-circled:before { content: '\e83b'; } /* '' */
.icon-asterisk:before { content: '\e83c'; } /* '' */
.icon-gift:before { content: '\e83d'; } /* '' */
.icon-fire:before { content: '\e83e'; } /* '' */
.icon-magnet:before { content: '\e83f'; } /* '' */
.icon-chart-bar:before { content: '\e840'; } /* '' */
.icon-ticket:before { content: '\e841'; } /* '' */
.icon-credit-card:before { content: '\e842'; } /* '' */
.icon-floppy:before { content: '\e843'; } /* '' */
.icon-megaphone:before { content: '\e844'; } /* '' */
.icon-hdd:before { content: '\e845'; } /* '' */
.icon-key:before { content: '\e846'; } /* '' */
.icon-fork:before { content: '\e847'; } /* '' */
.icon-rocket:before { content: '\e848'; } /* '' */
.icon-bug:before { content: '\e849'; } /* '' */
.icon-certificate:before { content: '\e84a'; } /* '' */
.icon-tasks:before { content: '\e84b'; } /* '' */
.icon-filter:before { content: '\e84c'; } /* '' */
.icon-beaker:before { content: '\e84d'; } /* '' */
.icon-magic:before { content: '\e84e'; } /* '' */
.icon-cab:before { content: '\e84f'; } /* '' */
.icon-taxi:before { content: '\e850'; } /* '' */
.icon-truck:before { content: '\e851'; } /* '' */
.icon-money:before { content: '\e852'; } /* '' */
.icon-euro:before { content: '\e853'; } /* '' */
.icon-pound:before { content: '\e854'; } /* '' */
.icon-dollar:before { content: '\e855'; } /* '' */
.icon-rupee:before { content: '\e856'; } /* '' */
.icon-yen:before { content: '\e857'; } /* '' */
.icon-rouble:before { content: '\e858'; } /* '' */
.icon-try:before { content: '\e859'; } /* '' */
.icon-won:before { content: '\e85a'; } /* '' */
.icon-bitcoin:before { content: '\e85b'; } /* '' */
.icon-sort:before { content: '\e85c'; } /* '' */
.icon-sort-down:before { content: '\e85d'; } /* '' */
.icon-sort-up:before { content: '\e85e'; } /* '' */
.icon-sort-alt-up:before { content: '\e85f'; } /* '' */
.icon-sort-alt-down:before { content: '\e860'; } /* '' */
.icon-sort-name-up:before { content: '\e861'; } /* '' */
.icon-sort-name-down:before { content: '\e862'; } /* '' */
.icon-sort-number-up:before { content: '\e863'; } /* '' */
.icon-sort-number-down:before { content: '\e864'; } /* '' */
.icon-hammer:before { content: '\e865'; } /* '' */
.icon-gauge:before { content: '\e866'; } /* '' */
.icon-sitemap:before { content: '\e867'; } /* '' */
.icon-spinner:before { content: '\e868'; } /* '' */
.icon-coffee:before { content: '\e869'; } /* '' */
.icon-food:before { content: '\e86a'; } /* '' */
.icon-beer:before { content: '\e86b'; } /* '' */
.icon-user-md:before { content: '\e86c'; } /* '' */
.icon-stethoscope:before { content: '\e86d'; } /* '' */
.icon-ambulance:before { content: '\e86e'; } /* '' */
.icon-medkit:before { content: '\e86f'; } /* '' */
.icon-h-sigh:before { content: '\e870'; } /* '' */
.icon-hospital:before { content: '\e871'; } /* '' */
.icon-building:before { content: '\e872'; } /* '' */
.icon-building-filled:before { content: '\e873'; } /* '' */
.icon-bank:before { content: '\e874'; } /* '' */
.icon-smile:before { content: '\e875'; } /* '' */
.icon-frown:before { content: '\e876'; } /* '' */
.icon-meh:before { content: '\e877'; } /* '' */
.icon-anchor:before { content: '\e878'; } /* '' */
.icon-terminal:before { content: '\e879'; } /* '' */
.icon-eraser:before { content: '\e87a'; } /* '' */
.icon-puzzle:before { content: '\e87b'; } /* '' */
.icon-shield:before { content: '\e87c'; } /* '' */
.icon-extinguisher:before { content: '\e87d'; } /* '' */
.icon-bullseye:before { content: '\e87e'; } /* '' */
.icon-wheelchair:before { content: '\e87f'; } /* '' */
.icon-language:before { content: '\e880'; } /* '' */
.icon-graduation-cap:before { content: '\e881'; } /* '' */
.icon-paw:before { content: '\e882'; } /* '' */
.icon-spoon:before { content: '\e883'; } /* '' */
.icon-cube:before { content: '\e884'; } /* '' */
.icon-cubes:before { content: '\e885'; } /* '' */
.icon-recycle:before { content: '\e886'; } /* '' */
.icon-tree:before { content: '\e887'; } /* '' */
.icon-database:before { content: '\e888'; } /* '' */
.icon-lifebuoy:before { content: '\e889'; } /* '' */
.icon-rebel:before { content: '\e88a'; } /* '' */
.icon-empire:before { content: '\e88b'; } /* '' */
.icon-bomb:before { content: '\e88c'; } /* '' */
.icon-adn:before { content: '\e88d'; } /* '' */
.icon-android:before { content: '\e88e'; } /* '' */
.icon-apple:before { content: '\e88f'; } /* '' */
.icon-behance:before { content: '\e890'; } /* '' */
.icon-behance-squared:before { content: '\e891'; } /* '' */
.icon-bitbucket:before { content: '\e892'; } /* '' */
.icon-bitbucket-squared:before { content: '\e893'; } /* '' */
.icon-codeopen:before { content: '\e894'; } /* '' */
.icon-css3:before { content: '\e895'; } /* '' */
.icon-delicious:before { content: '\e896'; } /* '' */
.icon-deviantart:before { content: '\e897'; } /* '' */
.icon-digg:before { content: '\e898'; } /* '' */
.icon-dribbble:before { content: '\e899'; } /* '' */
.icon-dropbox:before { content: '\e89a'; } /* '' */
.icon-drupal:before { content: '\e89b'; } /* '' */
.icon-facebook:before { content: '\e89c'; } /* '' */
.icon-facebook-squared:before { content: '\e89d'; } /* '' */
.icon-flickr:before { content: '\e89e'; } /* '' */
.icon-foursquare:before { content: '\e89f'; } /* '' */
.icon-git-squared:before { content: '\e8a0'; } /* '' */
.icon-git:before { content: '\e8a1'; } /* '' */
.icon-github:before { content: '\e8a2'; } /* '' */
.icon-github-squared:before { content: '\e8a3'; } /* '' */
.icon-github-circled:before { content: '\e8a4'; } /* '' */
.icon-gittip:before { content: '\e8a5'; } /* '' */
.icon-google:before { content: '\e8a6'; } /* '' */
.icon-gplus:before { content: '\e8a7'; } /* '' */
.icon-gplus-squared:before { content: '\e8a8'; } /* '' */
.icon-hacker-news:before { content: '\e8a9'; } /* '' */
.icon-html5:before { content: '\e8aa'; } /* '' */
.icon-instagramm:before { content: '\e8ab'; } /* '' */
.icon-joomla:before { content: '\e8ac'; } /* '' */
.icon-jsfiddle:before { content: '\e8ad'; } /* '' */
.icon-linkedin-squared:before { content: '\e8ae'; } /* '' */
.icon-linux:before { content: '\e8af'; } /* '' */
.icon-linkedin:before { content: '\e8b0'; } /* '' */
.icon-maxcdn:before { content: '\e8b1'; } /* '' */
.icon-openid:before { content: '\e8b2'; } /* '' */
.icon-pagelines:before { content: '\e8b3'; } /* '' */
.icon-pied-piper-squared:before { content: '\e8b4'; } /* '' */
.icon-pied-piper-alt:before { content: '\e8b5'; } /* '' */
.icon-pinterest-circled:before { content: '\e8b6'; } /* '' */
.icon-pinterest-squared:before { content: '\e8b7'; } /* '' */
.icon-qq:before { content: '\e8b8'; } /* '' */
.icon-reddit:before { content: '\e8b9'; } /* '' */
.icon-reddit-squared:before { content: '\e8ba'; } /* '' */
.icon-renren:before { content: '\e8bb'; } /* '' */
.icon-skype:before { content: '\e8bc'; } /* '' */
.icon-slack:before { content: '\e8bd'; } /* '' */
.icon-soundclowd:before { content: '\e8be'; } /* '' */
.icon-spotify:before { content: '\e8bf'; } /* '' */
.icon-stackexchange:before { content: '\e8c0'; } /* '' */
.icon-stackoverflow:before { content: '\e8c1'; } /* '' */
.icon-steam:before { content: '\e8c2'; } /* '' */
.icon-steam-squared:before { content: '\e8c3'; } /* '' */
.icon-stumbleupon:before { content: '\e8c4'; } /* '' */
.icon-stumbleupon-circled:before { content: '\e8c5'; } /* '' */
.icon-tencent-weibo:before { content: '\e8c6'; } /* '' */
.icon-trello:before { content: '\e8c7'; } /* '' */
.icon-tumblr:before { content: '\e8c8'; } /* '' */
.icon-tumblr-squared:before { content: '\e8c9'; } /* '' */
.icon-twitter-squared:before { content: '\e8ca'; } /* '' */
.icon-twitter:before { content: '\e8cb'; } /* '' */
.icon-vimeo-squared:before { content: '\e8cc'; } /* '' */
.icon-vine:before { content: '\e8cd'; } /* '' */
.icon-vkontakte:before { content: '\e8ce'; } /* '' */
.icon-wechat:before { content: '\e8cf'; } /* '' */
.icon-weibo:before { content: '\e8d0'; } /* '' */
.icon-windows:before { content: '\e8d1'; } /* '' */
.icon-wordpress:before { content: '\e8d2'; } /* '' */
.icon-xing:before { content: '\e8d3'; } /* '' */
.icon-xing-squared:before { content: '\e8d4'; } /* '' */
.icon-youtube:before { content: '\e8d5'; } /* '' */
.icon-yahoo:before { content: '\e8d6'; } /* '' */
.icon-youtube-squared:before { content: '\e8d7'; } /* '' */
.icon-youtube-play:before { content: '\e8d8'; } /* '' */
.icon-blank:before { content: '\e8d9'; } /* '' */
.icon-lemon:before { content: '\e8da'; } /* '' */
.icon-glass:before { content: '\e8db'; } /* '' */
.icon-music:before { content: '\e8dc'; } /* '' */
.icon-search:before { content: '\e8dd'; } /* '' */
.icon-mail:before { content: '\e8de'; } /* '' */
.icon-mail-alt:before { content: '\e8df'; } /* '' */
.icon-mail-squared:before { content: '\e8e0'; } /* '' */
.icon-heart:before { content: '\e8e1'; } /* '' */
.icon-heart-empty:before { content: '\e8e2'; } /* '' */
.icon-star:before { content: '\e8e3'; } /* '' */
.icon-star-empty:before { content: '\e8e4'; } /* '' */
.icon-star-half:before { content: '\e8e5'; } /* '' */
.icon-star-half-alt:before { content: '\e8e6'; } /* '' */
.icon-user:before { content: '\e8e7'; } /* '' */
.icon-users:before { content: '\e8e8'; } /* '' */
.icon-male:before { content: '\e8e9'; } /* '' */
.icon-female:before { content: '\e8ea'; } /* '' */
.icon-child:before { content: '\e8eb'; } /* '' */
.icon-video:before { content: '\e8ec'; } /* '' */
.icon-videocam:before { content: '\e8ed'; } /* '' */
.icon-picture:before { content: '\e8ee'; } /* '' */
.icon-camera:before { content: '\e8ef'; } /* '' */
.icon-camera-alt:before { content: '\e8f0'; } /* '' */
.icon-th-large:before { content: '\e8f1'; } /* '' */
.icon-th:before { content: '\e8f2'; } /* '' */
.icon-th-list:before { content: '\e8f3'; } /* '' */
.icon-ok:before { content: '\e8f4'; } /* '' */
.icon-ok-circled:before { content: '\e8f5'; } /* '' */
.icon-ok-circled2:before { content: '\e8f6'; } /* '' */
.icon-ok-squared:before { content: '\e8f7'; } /* '' */
.icon-cancel:before { content: '\e8f8'; } /* '' */
.icon-cancel-circled:before { content: '\e8f9'; } /* '' */
.icon-cancel-circled2:before { content: '\e8fa'; } /* '' */
.icon-plus:before { content: '\e8fb'; } /* '' */
.icon-plus-circled:before { content: '\e8fc'; } /* '' */
.icon-plus-squared:before { content: '\e8fd'; } /* '' */
.icon-plus-squared-alt:before { content: '\e8fe'; } /* '' */
.icon-minus:before { content: '\e8ff'; } /* '' */
.icon-minus-circled:before { content: '\e900'; } /* '' */
.icon-minus-squared:before { content: '\e901'; } /* '' */
.icon-minus-squared-alt:before { content: '\e902'; } /* '' */
.icon-help:before { content: '\e903'; } /* '' */
.icon-help-circled:before { content: '\e904'; } /* '' */
.icon-info-circled:before { content: '\e905'; } /* '' */
.icon-info:before { content: '\e906'; } /* '' */
.icon-home:before { content: '\e907'; } /* '' */
.icon-link:before { content: '\e908'; } /* '' */
.icon-unlink:before { content: '\e909'; } /* '' */
.icon-link-ext:before { content: '\e90a'; } /* '' */
.icon-link-ext-alt:before { content: '\e90b'; } /* '' */
.icon-attach:before { content: '\e90c'; } /* '' */
.icon-lock:before { content: '\e90d'; } /* '' */
.icon-lock-open:before { content: '\e90e'; } /* '' */
.icon-lock-open-alt:before { content: '\e90f'; } /* '' */
.icon-pin:before { content: '\e910'; } /* '' */
.icon-eye:before { content: '\e911'; } /* '' */
.icon-eye-off:before { content: '\e912'; } /* '' */
.icon-tag:before { content: '\e913'; } /* '' */
.icon-tags:before { content: '\e914'; } /* '' */
.icon-bookmark:before { content: '\e915'; } /* '' */
.icon-bookmark-empty:before { content: '\e916'; } /* '' */
.icon-flag:before { content: '\e917'; } /* '' */
.icon-flag-empty:before { content: '\e918'; } /* '' */
.icon-flag-checkered:before { content: '\e919'; } /* '' */
.icon-thumbs-up:before { content: '\e91a'; } /* '' */
.icon-thumbs-down:before { content: '\e91b'; } /* '' */
.icon-thumbs-up-alt:before { content: '\e91c'; } /* '' */
.icon-thumbs-down-alt:before { content: '\e91d'; } /* '' */
.icon-download:before { content: '\e91e'; } /* '' */
.icon-upload:before { content: '\e91f'; } /* '' */
.icon-download-cloud:before { content: '\e920'; } /* '' */
.icon-upload-cloud:before { content: '\e921'; } /* '' */
.icon-reply:before { content: '\e922'; } /* '' */
.icon-reply-all:before { content: '\e923'; } /* '' */
.icon-forward:before { content: '\e924'; } /* '' */
.icon-quote-left:before { content: '\e925'; } /* '' */
.icon-quote-right:before { content: '\e926'; } /* '' */
.icon-code:before { content: '\e927'; } /* '' */
.icon-export:before { content: '\e928'; } /* '' */
.icon-export-alt:before { content: '\e929'; } /* '' */
.icon-share:before { content: '\e92a'; } /* '' */
.icon-share-squared:before { content: '\e92b'; } /* '' */
.icon-pencil:before { content: '\e92c'; } /* '' */
.icon-pencil-squared:before { content: '\e92d'; } /* '' */
.icon-edit:before { content: '\e92e'; } /* '' */
.icon-print:before { content: '\e92f'; } /* '' */
.icon-retweet:before { content: '\e930'; } /* '' */
.icon-keyboard:before { content: '\e931'; } /* '' */
.icon-gamepad:before { content: '\e932'; } /* '' */
.icon-comment:before { content: '\e933'; } /* '' */
.icon-chat:before { content: '\e934'; } /* '' */
.icon-comment-empty:before { content: '\e935'; } /* '' */
.icon-chat-empty:before { content: '\e936'; } /* '' */
.icon-bell:before { content: '\e937'; } /* '' */
.icon-bell-alt:before { content: '\e938'; } /* '' */
.icon-attention-alt:before { content: '\e939'; } /* '' */
.icon-attention:before { content: '\e93a'; } /* '' */
.icon-attention-circled:before { content: '\e93b'; } /* '' */
.icon-location:before { content: '\e93c'; } /* '' */
.icon-direction:before { content: '\e93d'; } /* '' */
.icon-compass:before { content: '\e93e'; } /* '' */
.icon-trash:before { content: '\e93f'; } /* '' */
.icon-doc:before { content: '\e940'; } /* '' */
.icon-docs:before { content: '\e941'; } /* '' */
.icon-doc-text:before { content: '\e942'; } /* '' */
.icon-doc-inv:before { content: '\e943'; } /* '' */
.icon-doc-text-inv:before { content: '\e944'; } /* '' */
.icon-file-pdf:before { content: '\e945'; } /* '' */
.icon-file-word:before { content: '\e946'; } /* '' */
.icon-file-excel:before { content: '\e947'; } /* '' */
.icon-file-powerpoint:before { content: '\e948'; } /* '' */
.icon-file-image:before { content: '\e949'; } /* '' */
.icon-file-archive:before { content: '\e94a'; } /* '' */
.icon-file-audio:before { content: '\e94b'; } /* '' */
.icon-file-video:before { content: '\e94c'; } /* '' */
.icon-file-code:before { content: '\e94d'; } /* '' */
.icon-folder:before { content: '\e94e'; } /* '' */
.icon-folder-open:before { content: '\e94f'; } /* '' */
.icon-folder-empty:before { content: '\e950'; } /* '' */
.icon-folder-open-empty:before { content: '\e951'; } /* '' */
.icon-box:before { content: '\e952'; } /* '' */
.icon-rss:before { content: '\e953'; } /* '' */
.icon-rss-squared:before { content: '\e954'; } /* '' */
.icon-phone:before { content: '\e955'; } /* '' */
.icon-phone-squared:before { content: '\e956'; } /* '' */
.icon-fax:before { content: '\e957'; } /* '' */
.icon-menu:before { content: '\e958'; } /* '' */
.icon-cog:before { content: '\e959'; } /* '' */
.icon-cog-alt:before { content: '\e95a'; } /* '' */
.icon-wrench:before { content: '\e95b'; } /* '' */
.icon-sliders:before { content: '\e95c'; } /* '' */
.icon-basket:before { content: '\e95d'; } /* '' */
.icon-calendar:before { content: '\e95e'; } /* '' */
.icon-calendar-empty:before { content: '\e95f'; } /* '' */
.icon-login:before { content: '\e960'; } /* '' */
.icon-logout:before { content: '\e961'; } /* '' */
.icon-mic:before { content: '\e962'; } /* '' */
.icon-mute:before { content: '\e963'; } /* '' */
.icon-volume-off:before { content: '\e964'; } /* '' */
.icon-volume-down:before { content: '\e965'; } /* '' */
.icon-volume-up:before { content: '\e966'; } /* '' */
.icon-headphones:before { content: '\e967'; } /* '' */
.icon-clock:before { content: '\e968'; } /* '' */
.icon-lightbulb:before { content: '\e969'; } /* '' */
.icon-block:before { content: '\e96a'; } /* '' */
.icon-resize-full:before { content: '\e96b'; } /* '' */
.icon-resize-full-alt:before { content: '\e96c'; } /* '' */
.icon-resize-small:before { content: '\e96d'; } /* '' */
.icon-resize-vertical:before { content: '\e96e'; } /* '' */
.icon-resize-horizontal:before { content: '\e96f'; } /* '' */
.icon-move:before { content: '\e970'; } /* '' */
.icon-zoom-in:before { content: '\e971'; } /* '' */
.icon-zoom-out:before { content: '\e972'; } /* '' */
.icon-down-circled2:before { content: '\e973'; } /* '' */
.icon-up-circled2:before { content: '\e974'; } /* '' */
.icon-left-circled2:before { content: '\e975'; } /* '' */
.icon-right-circled2:before { content: '\e976'; } /* '' */
.icon-down-dir:before { content: '\e977'; } /* '' */
.icon-up-dir:before { content: '\e978'; } /* '' */
.icon-left-dir:before { content: '\e979'; } /* '' */
.icon-right-dir:before { content: '\e97a'; } /* '' */
.icon-down-open:before { content: '\e97b'; } /* '' */
.icon-left-open:before { content: '\e97c'; } /* '' */
.icon-right-open:before { content: '\e97d'; } /* '' */
.icon-up-open:before { content: '\e97e'; } /* '' */
.icon-angle-left:before { content: '\e97f'; } /* '' */
.icon-angle-right:before { content: '\e980'; } /* '' */
.icon-angle-up:before { content: '\e981'; } /* '' */
.icon-angle-down:before { content: '\e982'; } /* '' */
.icon-angle-circled-left:before { content: '\e983'; } /* '' */
.icon-angle-circled-right:before { content: '\e984'; } /* '' */
.icon-angle-circled-up:before { content: '\e985'; } /* '' */
.icon-angle-circled-down:before { content: '\e986'; } /* '' */
.icon-angle-double-left:before { content: '\e987'; } /* '' */
.icon-angle-double-right:before { content: '\e988'; } /* '' */
.icon-angle-double-up:before { content: '\e989'; } /* '' */
.icon-angle-double-down:before { content: '\e98a'; } /* '' */
.icon-down:before { content: '\e98b'; } /* '' */
.icon-left:before { content: '\e98c'; } /* '' */
.icon-right:before { content: '\e98d'; } /* '' */
.icon-up:before { content: '\e98e'; } /* '' */
.icon-down-big:before { content: '\e98f'; } /* '' */
.icon-left-big:before { content: '\e990'; } /* '' */
.icon-right-big:before { content: '\e991'; } /* '' */
.icon-up-big:before { content: '\e992'; } /* '' */
.icon-right-hand:before { content: '\e993'; } /* '' */
.icon-left-hand:before { content: '\e994'; } /* '' */
.icon-up-hand:before { content: '\e995'; } /* '' */
.icon-down-hand:before { content: '\e996'; } /* '' */
.icon-left-circled:before { content: '\e997'; } /* '' */
.icon-right-circled:before { content: '\e998'; } /* '' */
.icon-up-circled:before { content: '\e999'; } /* '' */
.icon-down-circled:before { content: '\e99a'; } /* '' */
.icon-cw:before { content: '\e99b'; } /* '' */
.icon-ccw:before { content: '\e99c'; } /* '' */
.icon-arrows-cw:before { content: '\e99d'; } /* '' */
.icon-level-up:before { content: '\e99e'; } /* '' */
.icon-level-down:before { content: '\e99f'; } /* '' */
.icon-shuffle:before { content: '\e9a0'; } /* '' */
.icon-exchange:before { content: '\e9a1'; } /* '' */
.icon-history:before { content: '\e9a2'; } /* '' */
.icon-expand:before { content: '\e9a3'; } /* '' */
.icon-collapse:before { content: '\e9a4'; } /* '' */
.icon-expand-right:before { content: '\e9a5'; } /* '' */
.icon-collapse-left:before { content: '\e9a6'; } /* '' */
.icon-play:before { content: '\e9a7'; } /* '' */
.icon-play-circled:before { content: '\e9a8'; } /* '' */
.icon-play-circled2:before { content: '\e9a9'; } /* '' */
.icon-stop:before { content: '\e9aa'; } /* '' */
.icon-pause:before { content: '\e9ab'; } /* '' */
.icon-to-end:before { content: '\e9ac'; } /* '' */
.icon-to-end-alt:before { content: '\e9ad'; } /* '' */
.icon-to-start:before { content: '\e9ae'; } /* '' */
.icon-to-start-alt:before { content: '\e9af'; } /* '' */
.icon-fast-fw:before { content: '\e9b0'; } /* '' */
.icon-fast-bw:before { content: '\e9b1'; } /* '' */
.icon-eject:before { content: '\e9b2'; } /* '' */
.icon-target:before { content: '\e9b3'; } /* '' */
.icon-signal:before { content: '\e9b4'; } /* '' */
.icon-award:before { content: '\e9b5'; } /* '' */
.icon-desktop:before { content: '\e9b6'; } /* '' */
.icon-music-outline:before { content: '\e9b7'; } /* '' */
.icon-music-1:before { content: '\e9b8'; } /* '' */
.icon-search-outline:before { content: '\e9b9'; } /* '' */
.icon-search-1:before { content: '\e9ba'; } /* '' */
.icon-mail-1:before { content: '\e9bb'; } /* '' */
.icon-heart-1:before { content: '\e9bc'; } /* '' */
.icon-heart-filled:before { content: '\e9bd'; } /* '' */
.icon-star-1:before { content: '\e9be'; } /* '' */
.icon-star-filled:before { content: '\e9bf'; } /* '' */
.icon-user-outline:before { content: '\e9c0'; } /* '' */
.icon-user-1:before { content: '\e9c1'; } /* '' */
.icon-users-outline:before { content: '\e9c2'; } /* '' */
.icon-users-1:before { content: '\e9c3'; } /* '' */
.icon-user-add-outline:before { content: '\e9c4'; } /* '' */
.icon-user-add:before { content: '\e9c5'; } /* '' */
.icon-user-delete-outline:before { content: '\e9c6'; } /* '' */
.icon-user-delete:before { content: '\e9c7'; } /* '' */
.icon-video-1:before { content: '\e9c8'; } /* '' */
.icon-videocam-outline:before { content: '\e9c9'; } /* '' */
.icon-videocam-1:before { content: '\e9ca'; } /* '' */
.icon-picture-outline:before { content: '\e9cb'; } /* '' */
.icon-picture-1:before { content: '\e9cc'; } /* '' */
.icon-camera-outline:before { content: '\e9cd'; } /* '' */
.icon-camera-1:before { content: '\e9ce'; } /* '' */
.icon-th-outline:before { content: '\e9cf'; } /* '' */
.icon-th-1:before { content: '\e9d0'; } /* '' */
.icon-th-large-outline:before { content: '\e9d1'; } /* '' */
.icon-th-large-1:before { content: '\e9d2'; } /* '' */
.icon-th-list-outline:before { content: '\e9d3'; } /* '' */
.icon-th-list-1:before { content: '\e9d4'; } /* '' */
.icon-ok-outline:before { content: '\e9d5'; } /* '' */
.icon-ok-1:before { content: '\e9d6'; } /* '' */
.icon-cancel-outline:before { content: '\e9d7'; } /* '' */
.icon-cancel-1:before { content: '\e9d8'; } /* '' */
.icon-cancel-alt:before { content: '\e9d9'; } /* '' */
.icon-cancel-alt-filled:before { content: '\e9da'; } /* '' */
.icon-cancel-circled-outline:before { content: '\e9db'; } /* '' */
.icon-cancel-circled-1:before { content: '\e9dc'; } /* '' */
.icon-plus-outline:before { content: '\e9dd'; } /* '' */
.icon-plus-1:before { content: '\e9de'; } /* '' */
.icon-minus-outline:before { content: '\e9df'; } /* '' */
.icon-minus-1:before { content: '\e9e0'; } /* '' */
.icon-divide-outline:before { content: '\e9e1'; } /* '' */
.icon-divide:before { content: '\e9e2'; } /* '' */
.icon-eq-outline:before { content: '\e9e3'; } /* '' */
.icon-eq:before { content: '\e9e4'; } /* '' */
.icon-info-outline:before { content: '\e9e5'; } /* '' */
.icon-info-1:before { content: '\e9e6'; } /* '' */
.icon-home-outline:before { content: '\e9e7'; } /* '' */
.icon-home-1:before { content: '\e9e8'; } /* '' */
.icon-link-outline:before { content: '\e9e9'; } /* '' */
.icon-link-1:before { content: '\e9ea'; } /* '' */
.icon-attach-outline:before { content: '\e9eb'; } /* '' */
.icon-attach-1:before { content: '\e9ec'; } /* '' */
.icon-lock-1:before { content: '\e9ed'; } /* '' */
.icon-lock-filled:before { content: '\e9ee'; } /* '' */
.icon-lock-open-1:before { content: '\e9ef'; } /* '' */
.icon-lock-open-filled:before { content: '\e9f0'; } /* '' */
.icon-pin-outline:before { content: '\e9f1'; } /* '' */
.icon-pin-1:before { content: '\e9f2'; } /* '' */
.icon-eye-outline:before { content: '\e9f3'; } /* '' */
.icon-eye-1:before { content: '\e9f4'; } /* '' */
.icon-tag-1:before { content: '\e9f5'; } /* '' */
.icon-tags-1:before { content: '\e9f6'; } /* '' */
.icon-bookmark-1:before { content: '\e9f7'; } /* '' */
.icon-flag-1:before { content: '\e9f8'; } /* '' */
.icon-flag-filled:before { content: '\e9f9'; } /* '' */
.icon-thumbs-up-1:before { content: '\e9fa'; } /* '' */
.icon-thumbs-down-1:before { content: '\e9fb'; } /* '' */
.icon-download-outline:before { content: '\e9fc'; } /* '' */
.icon-download-1:before { content: '\e9fd'; } /* '' */
.icon-upload-outline:before { content: '\e9fe'; } /* '' */
.icon-upload-1:before { content: '\e9ff'; } /* '' */
.icon-upload-cloud-outline:before { content: '\ea00'; } /* '' */
.icon-upload-cloud-1:before { content: '\ea01'; } /* '' */
.icon-reply-outline:before { content: '\ea02'; } /* '' */
.icon-reply-1:before { content: '\ea03'; } /* '' */
.icon-forward-outline:before { content: '\ea04'; } /* '' */
.icon-forward-1:before { content: '\ea05'; } /* '' */
.icon-code-outline:before { content: '\ea06'; } /* '' */
.icon-code-1:before { content: '\ea07'; } /* '' */
.icon-export-outline:before { content: '\ea08'; } /* '' */
.icon-export-1:before { content: '\ea09'; } /* '' */
.icon-pencil-1:before { content: '\ea0a'; } /* '' */
.icon-pen:before { content: '\ea0b'; } /* '' */
.icon-feather:before { content: '\ea0c'; } /* '' */
.icon-edit-1:before { content: '\ea0d'; } /* '' */
.icon-print-1:before { content: '\ea0e'; } /* '' */
.icon-comment-1:before { content: '\ea0f'; } /* '' */
.icon-chat-1:before { content: '\ea10'; } /* '' */
.icon-chat-alt:before { content: '\ea11'; } /* '' */
.icon-bell-1:before { content: '\ea12'; } /* '' */
.icon-attention-1:before { content: '\ea13'; } /* '' */
.icon-attention-filled:before { content: '\ea14'; } /* '' */
.icon-warning-empty:before { content: '\ea15'; } /* '' */
.icon-warning:before { content: '\ea16'; } /* '' */
.icon-contacts:before { content: '\ea17'; } /* '' */
.icon-vcard:before { content: '\ea18'; } /* '' */
.icon-address:before { content: '\ea19'; } /* '' */
.icon-location-outline:before { content: '\ea1a'; } /* '' */
.icon-location-1:before { content: '\ea1b'; } /* '' */
.icon-map:before { content: '\ea1c'; } /* '' */
.icon-direction-outline:before { content: '\ea1d'; } /* '' */
.icon-direction-1:before { content: '\ea1e'; } /* '' */
.icon-compass-1:before { content: '\ea1f'; } /* '' */
.icon-trash-1:before { content: '\ea20'; } /* '' */
.icon-doc-1:before { content: '\ea21'; } /* '' */
.icon-doc-text-1:before { content: '\ea22'; } /* '' */
.icon-doc-add:before { content: '\ea23'; } /* '' */
.icon-doc-remove:before { content: '\ea24'; } /* '' */
.icon-news:before { content: '\ea25'; } /* '' */
.icon-folder-1:before { content: '\ea26'; } /* '' */
.icon-folder-add:before { content: '\ea27'; } /* '' */
.icon-folder-delete:before { content: '\ea28'; } /* '' */
.icon-archive:before { content: '\ea29'; } /* '' */
.icon-box-1:before { content: '\ea2a'; } /* '' */
.icon-rss-outline:before { content: '\ea2b'; } /* '' */
.icon-rss-1:before { content: '\ea2c'; } /* '' */
.icon-phone-outline:before { content: '\ea2d'; } /* '' */
.icon-phone-1:before { content: '\ea2e'; } /* '' */
.icon-menu-outline:before { content: '\ea2f'; } /* '' */
.icon-menu-1:before { content: '\ea30'; } /* '' */
.icon-cog-outline:before { content: '\ea31'; } /* '' */
.icon-cog-1:before { content: '\ea32'; } /* '' */
.icon-wrench-outline:before { content: '\ea33'; } /* '' */
.icon-wrench-1:before { content: '\ea34'; } /* '' */
.icon-basket-1:before { content: '\ea35'; } /* '' */
.icon-calendar-outlilne:before { content: '\ea36'; } /* '' */
.icon-calendar-1:before { content: '\ea37'; } /* '' */
.icon-mic-outline:before { content: '\ea38'; } /* '' */
.icon-mic-1:before { content: '\ea39'; } /* '' */
.icon-volume-off-1:before { content: '\ea3a'; } /* '' */
.icon-volume-low:before { content: '\ea3b'; } /* '' */
.icon-volume-middle:before { content: '\ea3c'; } /* '' */
.icon-volume-high:before { content: '\ea3d'; } /* '' */
.icon-headphones-1:before { content: '\ea3e'; } /* '' */
.icon-clock-1:before { content: '\ea3f'; } /* '' */
.icon-wristwatch:before { content: '\ea40'; } /* '' */
.icon-stopwatch:before { content: '\ea41'; } /* '' */
.icon-lightbulb-1:before { content: '\ea42'; } /* '' */
.icon-block-outline:before { content: '\ea43'; } /* '' */
.icon-block-1:before { content: '\ea44'; } /* '' */
.icon-resize-full-outline:before { content: '\ea45'; } /* '' */
.icon-resize-full-1:before { content: '\ea46'; } /* '' */
.icon-resize-normal-outline:before { content: '\ea47'; } /* '' */
.icon-resize-normal:before { content: '\ea48'; } /* '' */
.icon-move-outline:before { content: '\ea49'; } /* '' */
.icon-move-1:before { content: '\ea4a'; } /* '' */
.icon-popup:before { content: '\ea4b'; } /* '' */
.icon-zoom-in-outline:before { content: '\ea4c'; } /* '' */
.icon-zoom-in-1:before { content: '\ea4d'; } /* '' */
.icon-zoom-out-outline:before { content: '\ea4e'; } /* '' */
.icon-zoom-out-1:before { content: '\ea4f'; } /* '' */
.icon-popup-1:before { content: '\ea50'; } /* '' */
.icon-left-open-outline:before { content: '\ea51'; } /* '' */
.icon-left-open-1:before { content: '\ea52'; } /* '' */
.icon-right-open-outline:before { content: '\ea53'; } /* '' */
.icon-right-open-1:before { content: '\ea54'; } /* '' */
.icon-down-1:before { content: '\ea55'; } /* '' */
.icon-left-1:before { content: '\ea56'; } /* '' */
.icon-right-1:before { content: '\ea57'; } /* '' */
.icon-up-1:before { content: '\ea58'; } /* '' */
.icon-down-outline:before { content: '\ea59'; } /* '' */
.icon-left-outline:before { content: '\ea5a'; } /* '' */
.icon-right-outline:before { content: '\ea5b'; } /* '' */
.icon-up-outline:before { content: '\ea5c'; } /* '' */
.icon-down-small:before { content: '\ea5d'; } /* '' */
.icon-left-small:before { content: '\ea5e'; } /* '' */
.icon-right-small:before { content: '\ea5f'; } /* '' */
.icon-up-small:before { content: '\ea60'; } /* '' */
.icon-cw-outline:before { content: '\ea61'; } /* '' */
.icon-cw-1:before { content: '\ea62'; } /* '' */
.icon-arrows-cw-outline:before { content: '\ea63'; } /* '' */
.icon-arrows-cw-1:before { content: '\ea64'; } /* '' */
.icon-loop-outline:before { content: '\ea65'; } /* '' */
.icon-loop:before { content: '\ea66'; } /* '' */
.icon-loop-alt-outline:before { content: '\ea67'; } /* '' */
.icon-loop-alt:before { content: '\ea68'; } /* '' */
.icon-shuffle-1:before { content: '\ea69'; } /* '' */
.icon-play-outline:before { content: '\ea6a'; } /* '' */
.icon-play-1:before { content: '\ea6b'; } /* '' */
.icon-stop-outline:before { content: '\ea6c'; } /* '' */
.icon-stop-1:before { content: '\ea6d'; } /* '' */
.icon-pause-outline:before { content: '\ea6e'; } /* '' */
.icon-pause-1:before { content: '\ea6f'; } /* '' */
.icon-fast-fw-outline:before { content: '\ea70'; } /* '' */
.icon-fast-fw-1:before { content: '\ea71'; } /* '' */
.icon-rewind-outline:before { content: '\ea72'; } /* '' */
.icon-rewind:before { content: '\ea73'; } /* '' */
.icon-record-outline:before { content: '\ea74'; } /* '' */
.icon-record:before { content: '\ea75'; } /* '' */
.icon-eject-outline:before { content: '\ea76'; } /* '' */
.icon-eject-1:before { content: '\ea77'; } /* '' */
.icon-eject-alt-outline:before { content: '\ea78'; } /* '' */
.icon-eject-alt:before { content: '\ea79'; } /* '' */
.icon-bat1:before { content: '\ea7a'; } /* '' */
.icon-bat2:before { content: '\ea7b'; } /* '' */
.icon-bat3:before { content: '\ea7c'; } /* '' */
.icon-bat4:before { content: '\ea7d'; } /* '' */
.icon-bat-charge:before { content: '\ea7e'; } /* '' */
.icon-plug:before { content: '\ea7f'; } /* '' */
.icon-target-outline:before { content: '\ea80'; } /* '' */
.icon-target-1:before { content: '\ea81'; } /* '' */
.icon-wifi-outline:before { content: '\ea82'; } /* '' */
.icon-wifi:before { content: '\ea83'; } /* '' */
.icon-desktop-1:before { content: '\ea84'; } /* '' */
.icon-laptop-1:before { content: '\ea85'; } /* '' */
.icon-tablet-1:before { content: '\ea86'; } /* '' */
.icon-mobile-1:before { content: '\ea87'; } /* '' */
.icon-contrast:before { content: '\ea88'; } /* '' */
.icon-globe-outline:before { content: '\ea89'; } /* '' */
.icon-globe-1:before { content: '\ea8a'; } /* '' */
.icon-globe-alt-outline:before { content: '\ea8b'; } /* '' */
.icon-globe-alt:before { content: '\ea8c'; } /* '' */
.icon-sun-1:before { content: '\ea8d'; } /* '' */
.icon-sun-filled:before { content: '\ea8e'; } /* '' */
.icon-cloud-1:before { content: '\ea8f'; } /* '' */
.icon-flash-outline:before { content: '\ea90'; } /* '' */
.icon-flash-1:before { content: '\ea91'; } /* '' */
.icon-moon-1:before { content: '\ea92'; } /* '' */
.icon-waves-outline:before { content: '\ea93'; } /* '' */
.icon-waves:before { content: '\ea94'; } /* '' */
.icon-rain:before { content: '\ea95'; } /* '' */
.icon-cloud-sun:before { content: '\ea96'; } /* '' */
.icon-drizzle:before { content: '\ea97'; } /* '' */
.icon-snow:before { content: '\ea98'; } /* '' */
.icon-cloud-flash:before { content: '\ea99'; } /* '' */
.icon-cloud-wind:before { content: '\ea9a'; } /* '' */
.icon-wind:before { content: '\ea9b'; } /* '' */
.icon-plane-outline:before { content: '\ea9c'; } /* '' */
.icon-plane:before { content: '\ea9d'; } /* '' */
.icon-leaf-1:before { content: '\ea9e'; } /* '' */
.icon-lifebuoy-1:before { content: '\ea9f'; } /* '' */
.icon-briefcase-1:before { content: '\eaa0'; } /* '' */
.icon-brush:before { content: '\eaa1'; } /* '' */
.icon-pipette:before { content: '\eaa2'; } /* '' */
.icon-power-outline:before { content: '\eaa3'; } /* '' */
.icon-power:before { content: '\eaa4'; } /* '' */
.icon-check-outline:before { content: '\eaa5'; } /* '' */
.icon-check-1:before { content: '\eaa6'; } /* '' */
.icon-gift-1:before { content: '\eaa7'; } /* '' */
.icon-temperatire:before { content: '\eaa8'; } /* '' */
.icon-chart-outline:before { content: '\eaa9'; } /* '' */
.icon-chart:before { content: '\eaaa'; } /* '' */
.icon-chart-alt-outline:before { content: '\eaab'; } /* '' */
.icon-chart-alt:before { content: '\eaac'; } /* '' */
.icon-chart-bar-outline:before { content: '\eaad'; } /* '' */
.icon-chart-bar-1:before { content: '\eaae'; } /* '' */
.icon-chart-pie-outline:before { content: '\eaaf'; } /* '' */
.icon-chart-pie:before { content: '\eab0'; } /* '' */
.icon-ticket-1:before { content: '\eab1'; } /* '' */
.icon-credit-card-1:before { content: '\eab2'; } /* '' */
.icon-clipboard:before { content: '\eab3'; } /* '' */
.icon-database-1:before { content: '\eab4'; } /* '' */
.icon-key-outline:before { content: '\eab5'; } /* '' */
.icon-key-1:before { content: '\eab6'; } /* '' */
.icon-flow-split:before { content: '\eab7'; } /* '' */
.icon-flow-merge:before { content: '\eab8'; } /* '' */
.icon-flow-parallel:before { content: '\eab9'; } /* '' */
.icon-flow-cross:before { content: '\eaba'; } /* '' */
.icon-certificate-outline:before { content: '\eabb'; } /* '' */
.icon-certificate-1:before { content: '\eabc'; } /* '' */
.icon-scissors-outline:before { content: '\eabd'; } /* '' */
.icon-scissors-1:before { content: '\eabe'; } /* '' */
.icon-flask:before { content: '\eabf'; } /* '' */
.icon-wine:before { content: '\eac0'; } /* '' */
.icon-coffee-1:before { content: '\eac1'; } /* '' */
.icon-beer-1:before { content: '\eac2'; } /* '' */
.icon-anchor-outline:before { content: '\eac3'; } /* '' */
.icon-anchor-1:before { content: '\eac4'; } /* '' */
.icon-puzzle-outline:before { content: '\eac5'; } /* '' */
.icon-puzzle-1:before { content: '\eac6'; } /* '' */
.icon-tree-1:before { content: '\eac7'; } /* '' */
.icon-calculator:before { content: '\eac8'; } /* '' */
.icon-infinity-outline:before { content: '\eac9'; } /* '' */
.icon-infinity:before { content: '\eaca'; } /* '' */
.icon-pi-outline:before { content: '\eacb'; } /* '' */
.icon-pi:before { content: '\eacc'; } /* '' */
.icon-at:before { content: '\eacd'; } /* '' */
.icon-at-circled:before { content: '\eace'; } /* '' */
.icon-looped-square-outline:before { content: '\eacf'; } /* '' */
.icon-looped-square-interest:before { content: '\ead0'; } /* '' */
.icon-sort-alphabet-outline:before { content: '\ead1'; } /* '' */
.icon-sort-alphabet:before { content: '\ead2'; } /* '' */
.icon-sort-numeric-outline:before { content: '\ead3'; } /* '' */
.icon-sort-numeric:before { content: '\ead4'; } /* '' */
.icon-dribbble-circled:before { content: '\ead5'; } /* '' */
.icon-dribbble-1:before { content: '\ead6'; } /* '' */
.icon-facebook-circled:before { content: '\ead7'; } /* '' */
.icon-facebook-1:before { content: '\ead8'; } /* '' */
.icon-flickr-circled:before { content: '\ead9'; } /* '' */
.icon-flickr-1:before { content: '\eada'; } /* '' */
.icon-github-circled-1:before { content: '\eadb'; } /* '' */
.icon-github-1:before { content: '\eadc'; } /* '' */
.icon-lastfm-circled:before { content: '\eadd'; } /* '' */
.icon-lastfm:before { content: '\eade'; } /* '' */
.icon-linkedin-circled:before { content: '\eadf'; } /* '' */
.icon-linkedin-1:before { content: '\eae0'; } /* '' */
.icon-pinterest-circled-1:before { content: '\eae1'; } /* '' */
.icon-pinterest:before { content: '\eae2'; } /* '' */
.icon-skype-outline:before { content: '\eae3'; } /* '' */
.icon-skype-1:before { content: '\eae4'; } /* '' */
.icon-tumbler-circled:before { content: '\eae5'; } /* '' */
.icon-tumbler:before { content: '\eae6'; } /* '' */
.icon-twitter-circled:before { content: '\eae7'; } /* '' */
.icon-twitter-1:before { content: '\eae8'; } /* '' */
.icon-vimeo-circled:before { content: '\eae9'; } /* '' */
.icon-vimeo:before { content: '\eaea'; } /* '' */
.icon-search-2:before { content: '\eaeb'; } /* '' */
.icon-mail-2:before { content: '\eaec'; } /* '' */
.icon-heart-2:before { content: '\eaed'; } /* '' */
.icon-heart-broken:before { content: '\eaee'; } /* '' */
.icon-star-2:before { content: '\eaef'; } /* '' */
.icon-star-empty-1:before { content: '\eaf0'; } /* '' */
.icon-star-half-1:before { content: '\eaf1'; } /* '' */
.icon-star-half_empty:before { content: '\eaf2'; } /* '' */
.icon-user-2:before { content: '\eaf3'; } /* '' */
.icon-user-male:before { content: '\eaf4'; } /* '' */
.icon-user-female:before { content: '\eaf5'; } /* '' */
.icon-users-2:before { content: '\eaf6'; } /* '' */
.icon-movie:before { content: '\eaf7'; } /* '' */
.icon-ok-2:before { content: '\eaf8'; } /* '' */
.icon-ok-circled-1:before { content: '\eaf9'; } /* '' */
.icon-cancel-2:before { content: '\eafa'; } /* '' */
.icon-cancel-circled-2:before { content: '\eafb'; } /* '' */
.icon-plus-2:before { content: '\eafc'; } /* '' */
.icon-help-circled-1:before { content: '\eafd'; } /* '' */
.icon-help-circled-alt:before { content: '\eafe'; } /* '' */
.icon-info-circled-1:before { content: '\eaff'; } /* '' */
.icon-info-circled-alt:before { content: '\eb00'; } /* '' */
.icon-home-2:before { content: '\eb01'; } /* '' */
.icon-link-2:before { content: '\eb02'; } /* '' */
.icon-attach-2:before { content: '\eb03'; } /* '' */
.icon-lock-2:before { content: '\eb04'; } /* '' */
.icon-upload-cloud-2:before { content: '\eb05'; } /* '' */
.icon-reply-2:before { content: '\eb06'; } /* '' */
.icon-pencil-2:before { content: '\eb07'; } /* '' */
.icon-export-2:before { content: '\eb08'; } /* '' */
.icon-print-2:before { content: '\eb09'; } /* '' */
.icon-retweet-1:before { content: '\eb0a'; } /* '' */
.icon-comment-2:before { content: '\eb0b'; } /* '' */
.icon-chat-2:before { content: '\eb0c'; } /* '' */
.icon-bell-2:before { content: '\eb0d'; } /* '' */
.icon-attention-2:before { content: '\eb0e'; } /* '' */
.icon-attention-alt-1:before { content: '\eb0f'; } /* '' */
.icon-location-2:before { content: '\eb10'; } /* '' */
.icon-trash-2:before { content: '\eb11'; } /* '' */
.icon-calendar-2:before { content: '\eb12'; } /* '' */
.icon-login-1:before { content: '\eb13'; } /* '' */
.icon-logout-1:before { content: '\eb14'; } /* '' */
.icon-mic-2:before { content: '\eb15'; } /* '' */
.icon-mic-off:before { content: '\eb16'; } /* '' */
.icon-clock-2:before { content: '\eb17'; } /* '' */
.icon-stopwatch-1:before { content: '\eb18'; } /* '' */
.icon-hourglass:before { content: '\eb19'; } /* '' */
.icon-zoom-in-2:before { content: '\eb1a'; } /* '' */
.icon-zoom-out-2:before { content: '\eb1b'; } /* '' */
.icon-down-open-1:before { content: '\eb1c'; } /* '' */
.icon-left-open-2:before { content: '\eb1d'; } /* '' */
.icon-right-open-2:before { content: '\eb1e'; } /* '' */
.icon-right-bold:before { content: '\eb1f'; } /* '' */
.icon-up-bold:before { content: '\eb20'; } /* '' */
.icon-down-fat:before { content: '\eb21'; } /* '' */
.icon-left-fat:before { content: '\eb22'; } /* '' */
.icon-right-fat:before { content: '\eb23'; } /* '' */
.icon-up-fat:before { content: '\eb24'; } /* '' */
.icon-ccw-1:before { content: '\eb25'; } /* '' */
.icon-shuffle-2:before { content: '\eb26'; } /* '' */
.icon-play-2:before { content: '\eb27'; } /* '' */
.icon-pause-2:before { content: '\eb28'; } /* '' */
.icon-stop-2:before { content: '\eb29'; } /* '' */
.icon-to-end-1:before { content: '\eb2a'; } /* '' */
.icon-to-start-1:before { content: '\eb2b'; } /* '' */
.icon-data-science-inv:before { content: '\eb2c'; } /* '' */
.icon-inbox-1:before { content: '\eb2d'; } /* '' */
.icon-globe-2:before { content: '\eb2e'; } /* '' */
.icon-globe-inv:before { content: '\eb2f'; } /* '' */
.icon-flash-2:before { content: '\eb30'; } /* '' */
.icon-cloud-2:before { content: '\eb31'; } /* '' */
.icon-coverflow:before { content: '\eb32'; } /* '' */
.icon-coverflow-empty:before { content: '\eb33'; } /* '' */
.icon-math:before { content: '\eb34'; } /* '' */
.icon-math-circled:before { content: '\eb35'; } /* '' */
.icon-math-circled-empty:before { content: '\eb36'; } /* '' */
.icon-paper-plane-1:before { content: '\eb37'; } /* '' */
.icon-paper-plane-alt:before { content: '\eb38'; } /* '' */
.icon-ruler:before { content: '\eb39'; } /* '' */
.icon-vector:before { content: '\eb3a'; } /* '' */
.icon-vector-pencil:before { content: '\eb3b'; } /* '' */
.icon-at-1:before { content: '\eb3c'; } /* '' */
.icon-hash:before { content: '\eb3d'; } /* '' */
.icon-female-1:before { content: '\eb3e'; } /* '' */
.icon-male-1:before { content: '\eb3f'; } /* '' */
.icon-spread:before { content: '\eb40'; } /* '' */
.icon-king:before { content: '\eb41'; } /* '' */
.icon-anchor-2:before { content: '\eb42'; } /* '' */
.icon-joystick:before { content: '\eb43'; } /* '' */
.icon-spinner1:before { content: '\eb44'; } /* '' */
.icon-spinner2:before { content: '\eb45'; } /* '' */
.icon-videocam-2:before { content: '\eb46'; } /* '' */
.icon-isight:before { content: '\eb47'; } /* '' */
.icon-camera-2:before { content: '\eb48'; } /* '' */
.icon-menu-2:before { content: '\eb49'; } /* '' */
.icon-th-thumb:before { content: '\eb4a'; } /* '' */
.icon-th-thumb-empty:before { content: '\eb4b'; } /* '' */
.icon-th-list-2:before { content: '\eb4c'; } /* '' */
.icon-lock-alt:before { content: '\eb4d'; } /* '' */
.icon-lock-open-2:before { content: '\eb4e'; } /* '' */
.icon-lock-open-alt-1:before { content: '\eb4f'; } /* '' */
.icon-eye-2:before { content: '\eb50'; } /* '' */
.icon-download-2:before { content: '\eb51'; } /* '' */
.icon-upload-2:before { content: '\eb52'; } /* '' */
.icon-download-cloud-1:before { content: '\eb53'; } /* '' */
.icon-doc-2:before { content: '\eb54'; } /* '' */
.icon-newspaper:before { content: '\eb55'; } /* '' */
.icon-folder-2:before { content: '\eb56'; } /* '' */
.icon-folder-open-1:before { content: '\eb57'; } /* '' */
.icon-folder-empty-1:before { content: '\eb58'; } /* '' */
.icon-folder-open-empty-1:before { content: '\eb59'; } /* '' */
.icon-cog-2:before { content: '\eb5a'; } /* '' */
.icon-up-open-1:before { content: '\eb5b'; } /* '' */
.icon-down-2:before { content: '\eb5c'; } /* '' */
.icon-left-2:before { content: '\eb5d'; } /* '' */
.icon-right-2:before { content: '\eb5e'; } /* '' */
.icon-up-2:before { content: '\eb5f'; } /* '' */
.icon-down-bold:before { content: '\eb60'; } /* '' */
.icon-left-bold:before { content: '\eb61'; } /* '' */
.icon-fast-forward:before { content: '\eb62'; } /* '' */
.icon-fast-backward:before { content: '\eb63'; } /* '' */
.icon-trophy:before { content: '\eb64'; } /* '' */
.icon-monitor:before { content: '\eb65'; } /* '' */
.icon-tablet-2:before { content: '\eb66'; } /* '' */
.icon-mobile-2:before { content: '\eb67'; } /* '' */
.icon-data-science:before { content: '\eb68'; } /* '' */
.icon-paper-plane-alt2:before { content: '\eb69'; } /* '' */
.icon-fontsize:before { content: '\eb6a'; } /* '' */
.icon-color-adjust:before { content: '\eb6b'; } /* '' */
.icon-fire-1:before { content: '\eb6c'; } /* '' */
.icon-chart-bar-2:before { content: '\eb6d'; } /* '' */
.icon-hdd-1:before { content: '\eb6e'; } /* '' */
.icon-connected-object:before { content: '\eb6f'; } /* '' */
.icon-windy-rain-inv:before { content: '\eb70'; } /* '' */
.icon-snow-inv:before { content: '\eb71'; } /* '' */
.icon-snow-heavy-inv:before { content: '\eb72'; } /* '' */
.icon-hail-inv:before { content: '\eb73'; } /* '' */
.icon-clouds-inv:before { content: '\eb74'; } /* '' */
.icon-clouds-flash-inv:before { content: '\eb75'; } /* '' */
.icon-temperature:before { content: '\eb76'; } /* '' */
.icon-compass-2:before { content: '\eb77'; } /* '' */
.icon-na:before { content: '\eb78'; } /* '' */
.icon-celcius:before { content: '\eb79'; } /* '' */
.icon-fahrenheit:before { content: '\eb7a'; } /* '' */
.icon-clouds-flash-alt:before { content: '\eb7b'; } /* '' */
.icon-sun-inv:before { content: '\eb7c'; } /* '' */
.icon-moon-inv:before { content: '\eb7d'; } /* '' */
.icon-cloud-sun-inv:before { content: '\eb7e'; } /* '' */
.icon-cloud-moon-inv:before { content: '\eb7f'; } /* '' */
.icon-cloud-inv:before { content: '\eb80'; } /* '' */
.icon-cloud-flash-inv:before { content: '\eb81'; } /* '' */
.icon-drizzle-inv:before { content: '\eb82'; } /* '' */
.icon-rain-inv:before { content: '\eb83'; } /* '' */
.icon-windy-inv:before { content: '\eb84'; } /* '' */
.icon-sunrise:before { content: '\eb85'; } /* '' */
.icon-sun-2:before { content: '\eb86'; } /* '' */
.icon-moon-2:before { content: '\eb87'; } /* '' */
.icon-eclipse:before { content: '\eb88'; } /* '' */
.icon-mist:before { content: '\eb89'; } /* '' */
.icon-wind-1:before { content: '\eb8a'; } /* '' */
.icon-snowflake:before { content: '\eb8b'; } /* '' */
.icon-cloud-sun-1:before { content: '\eb8c'; } /* '' */
.icon-cloud-moon:before { content: '\eb8d'; } /* '' */
.icon-fog-sun:before { content: '\eb8e'; } /* '' */
.icon-fog-moon:before { content: '\eb8f'; } /* '' */
.icon-fog-cloud:before { content: '\eb90'; } /* '' */
.icon-fog:before { content: '\eb91'; } /* '' */
.icon-cloud-3:before { content: '\eb92'; } /* '' */
.icon-cloud-flash-1:before { content: '\eb93'; } /* '' */
.icon-cloud-flash-alt:before { content: '\eb94'; } /* '' */
.icon-drizzle-1:before { content: '\eb95'; } /* '' */
.icon-rain-1:before { content: '\eb96'; } /* '' */
.icon-windy:before { content: '\eb97'; } /* '' */
.icon-windy-rain:before { content: '\eb98'; } /* '' */
.icon-snow-1:before { content: '\eb99'; } /* '' */
.icon-snow-alt:before { content: '\eb9a'; } /* '' */
.icon-snow-heavy:before { content: '\eb9b'; } /* '' */
.icon-hail:before { content: '\eb9c'; } /* '' */
.icon-clouds:before { content: '\eb9d'; } /* '' */
.icon-clouds-flash:before { content: '\eb9e'; } /* '' */