<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:f="http://xmlns.jcp.org/jsf/core"
	xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:p="http://primefaces.org/ui">

<ui:composition template="/resources/template/template.xhtml">

	<ui:define name="content">

		<script type="text/javascript">
    //<![CDATA[
function start() {
    PF('statusDialog').show();
}
 
function stop() {
    PF('statusDialog').hide();
}
    //]]>
</script>


		<p:dialog modal="true" widgetVar="statusDialog" header="Status"
			draggable="false" closable="false" resizable="false">
			<p:graphicImage name="ajaxloadingbar.gif" library="imagens" />
		</p:dialog>

		<h:form prependId="false">
			<p:messages />
			<p:fieldset legend="Dados para a consulta por cargos">

				<div class="ui-g">
					<div class="ui-g-12 ui-md-6 ui-lg-4">
						<h:outputText value="Entidade: " styleClass="FontBold" />
						<p:selectOneMenu value="#{cargoEntidadeBean.entidade}"
							filter="true" filterMatchMode="contains">
							<f:selectItem itemLabel="Todas" itemValue="#{0}" />
							<f:selectItems value="#{cargoEntidadeBean.listaEntidades}"
								var="entidade" itemLabel="#{entidade.nome}"
								itemValue="#{entidade.idEntidadeCjur}" />

							<p:ajax update="selectCompetencia" event="itemSelect"
								listener="#{cargoEntidadeBean.listarCompetencia()}" />
						</p:selectOneMenu>
					</div>
					<div class="ui-g-12 ui-md-6 ui-lg-4">
						<h:outputText value="Competência: " styleClass="FontBold" />
						<p:selectOneMenu id="selectCompetencia"
							value="#{cargoEntidadeBean.competencia}">
							<f:selectItem itemLabel="Selecione a competencia"
								itemValue="#{0}" />
							<f:selectItems value="#{cargoEntidadeBean.listaCompetencias}"
								var="competencia"
								itemLabel="#{competencia[2]}/#{competencia[0]}"
								itemValue="#{competencia[1]}/#{competencia[0]}" />

						</p:selectOneMenu>
					</div>
				</div>
				<div class="ui-g">
					<div class="ui-g-12">
						<p:commandButton value="Pesquisar" style="width: auto;"
							icon="fa fa-fw fa-search white" update="@form"
							action="#{cargoEntidadeBean.pesquisar()}" />

					</div>
				</div>


			</p:fieldset>

			<div class="EmptyBox10"></div>
			<p:fieldset legend="Resultados da consulta">
				<p:dataTable id="tblCargoEntId" var="cargo"
					sortBy="#{cargo.entidadeNome}" tableStyle="table-layout: auto;"
					value="#{cargoEntidadeBean.listaCargosEntidades}"
					emptyMessage="Nenhum cargo encontrada." rows="50" paginator="true"
					paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown} {Exporters}"
					rowsPerPageTemplate="50,100,300,500"
					currentPageReportTemplate="página {currentPage} de {totalPages}">

					<!-- 					<f:facet name="header"> -->
					<!-- 						<h:outputText value="Cargos por Entidade" /> -->
					<!-- 					</f:facet> -->

					<!-- 					<p:columnGroup type="header"> -->
					<!-- 						<p:row> -->
					<!-- 							<p:column colspan="4" headerText="Entidade" /> -->
					<!-- 						</p:row> -->

					<!-- 						<p:row> -->
					<!-- 							<p:column headerText="Tipo" /> -->
					<!-- 							<p:column headerText="Cargo de Referência" /> -->
					<!-- 							<p:column headerText="Cargo" /> -->
					<!-- 							<p:column headerText="Qtd. Servidores" /> -->
					<!-- 						</p:row> -->
					<!-- 					</p:columnGroup> -->

					<!-- 					<p:subTable var="cargo" value="#{entidade}"> -->
					<!-- 						<f:facet name="header"> -->
					<!-- 							<h:outputText value="#{cargo.entidadeNome}" /> -->
					<!-- 						</f:facet> -->

					<!-- 						<p:column> -->
					<!-- 							<f:facet name="header"> -->
					<!-- 								<h:outputText value="Tipo" /> -->
					<!-- 							</f:facet> -->
					<!-- 							<h:outputText value="#{cargo.cargoTipo}" /> -->
					<!-- 						</p:column> -->
					<!-- 						<p:column> -->
					<!-- 							<f:facet name="header"> -->
					<!-- 								<h:outputText value="Cargo de Referência" /> -->
					<!-- 							</f:facet> -->
					<!-- 							<h:outputText value="#{cargo.cargoReferencia}" /> -->
					<!-- 						</p:column> -->
					<!-- 						<p:column> -->
					<!-- 							<f:facet name="header"> -->
					<!-- 								<h:outputText value="Cargo" /> -->
					<!-- 							</f:facet> -->
					<!-- 							<h:outputText value="#{cargo.cargoNome}" /> -->
					<!-- 						</p:column> -->
					<!-- 						<p:column> -->
					<!-- 							<f:facet name="header"> -->
					<!-- 								<h:outputText value="Qtd. Servidores" /> -->
					<!-- 							</f:facet> -->
					<!-- 							<h:outputText value="#{cargo.cargoQuantidadeServidores}" /> -->
					<!-- 						</p:column> -->
					<!-- 					</p:subTable> -->

					<f:facet name="{Exporters}">
						<div style="float: right; padding-top: 10px;">
							<p:commandLink title="Exportar para Excel" ajax="false"
								onclick="PrimeFaces.monitorDownload(start, stop)">
								<p:graphicImage value="/resources/imagens/logoExcel.png"
									width="26" />
								<p:dataExporter type="xlsxstream" target="tblCargoEntId"
									fileName="cargosEntidade" />
							</p:commandLink>
						</div>
					</f:facet>

					<!-- 					<p:headerRow> -->
					<!-- 						<p:column colspan="4"> -->
					<!-- 							<f:facet name="header"> -->
					<!-- 								<h:outputText value="Entidade" /> -->
					<!-- 							</f:facet> -->
					<!-- 							<h:outputText value="#{cargo.entidadeNome}" /> -->
					<!-- 						</p:column> -->
					<!-- 					</p:headerRow> -->

					<p:column groupRow="true">
						<f:facet name="header">
							<h:outputText value="Entidade" />
						</f:facet>
						<h:outputText value="#{cargo.entidadeNome}" />
					</p:column>

					<p:column>
						<f:facet name="header">
							<h:outputText value="Tipo" />
						</f:facet>
						<h:outputText value="#{cargo.cargoTipo}" />
					</p:column>
					<p:column>
						<f:facet name="header">
							<h:outputText value="Cargo de Referência" />
						</f:facet>
						<h:outputText value="#{cargo.cargoReferencia}" />
					</p:column>
					<p:column>
						<f:facet name="header">
							<h:outputText value="Cargo" />
						</f:facet>
						<h:outputText value="#{cargo.cargoNome}" />
					</p:column>
					<p:column>
						<f:facet name="header">
							<h:outputText value="Qtd. Servidores" />
						</f:facet>
						<h:outputText value="#{cargo.cargoQuantidadeServidores}" />
					</p:column>


					<!-- 					<p:summaryRow> -->
					<!-- 						<p:column colspan="3" style="text-align:right"> -->
					<!-- 							<f:facet name="header"> -->
					<!-- 								<h:outputText value="Total" /> -->
					<!-- 							</f:facet> -->
					<!-- 							<h:outputText style="color: white" value="Total:" /> -->
					<!-- 						</p:column> -->
					<!-- 						<p:column> -->
					<!-- 							<f:facet name="header"> -->
					<!-- 								<h:outputText value="Total2" /> -->
					<!-- 							</f:facet> -->
					<!-- 							<h:outputText style="color: white" -->
					<!-- 								value="#{cargo.entidadeQuantidadeServidores}"> -->

					<!-- 							</h:outputText> -->
					<!-- 						</p:column> -->
					<!-- 					</p:summaryRow> -->

				</p:dataTable>
			</p:fieldset>
		</h:form>

	</ui:define>
</ui:composition>
</html>