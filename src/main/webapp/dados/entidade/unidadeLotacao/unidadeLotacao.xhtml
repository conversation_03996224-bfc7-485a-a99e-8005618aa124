<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:p="http://primefaces.org/ui"
	xmlns:pe="http://primefaces.org/ui/extensions"
	xmlns:fn="http://java.sun.com/jsp/jstl/functions"
	template="/resources/template/template.xhtml">

	<ui:define name="content">
		<!-- 		<p:fieldset legend="Lista Unidades de Lotação"> -->
		<h:form prependId="false">
			<p:fieldset legend="Dados para a consulta por unidades de lotação">

				<div class="ui-g">
					<div class="ui-g-12 ui-md-6 ui-lg-4">
						<h:panelGroup>
							<h:outputText value="Estado: " styleClass="FontBold" />
							<p:selectOneMenu value="#{unidadeLotacaoBean.uf.id}">
								<f:selectItem itemLabel="Todos" itemValue="#{0}" />
								<f:selectItems value="#{unidadeLotacaoBean.listaUfs}" var="uf"
									itemLabel="#{uf.nome}" itemValue="#{uf.id}" />

								<p:ajax update="idOneMenuMunicipio" event="itemSelect"
									listener="#{unidadeLotacaoBean.perquisarMunicipio()}" />
							</p:selectOneMenu>
						</h:panelGroup>
					</div>
					<div class="ui-g-12 ui-md-6 ui-lg-4">
						<h:panelGroup>
							<h:outputText value="Município: " styleClass="FontBold" />
							<p:selectOneMenu id="idOneMenuMunicipio"
								value="#{unidadeLotacaoBean.municipio.id}">
								<f:selectItem itemLabel="Todos" itemValue="#{0}" />
								<f:selectItems value="#{unidadeLotacaoBean.listaMunicipios}"
									var="municipio" itemLabel="#{municipio.nome}"
									itemValue="#{municipio.id}" />
							</p:selectOneMenu>
						</h:panelGroup>
					</div>
				</div>
				<div class="ui-g">
					<div class="ui-g-12 ui-md-6 ui-lg-4">
						<h:panelGroup>
							<h:outputText value="Unidades: " styleClass="FontBold" />
							<p:selectOneMenu
								value="#{unidadeLotacaoBean.unidadeLotacaoFiltro.id}"
								filter="true" filterMatchMode="contains">
								<f:selectItem itemLabel="Todas" itemValue="#{0}" />
								<f:selectItems
									value="#{unidadeLotacaoBean.listaUnidadesSelectOneMenu}"
									var="unidade" itemLabel="#{unidade.nome}"
									itemValue="#{unidade.id}" />
							</p:selectOneMenu>
						</h:panelGroup>
					</div>
					<div class="ui-g-12 ui-md-6 ui-lg-4">
						<h:panelGroup>
							<h:outputText value="Nome da unidade: " styleClass="FontBold" />
							<p:inputText value="#{unidadeLotacaoBean.nome}" size="40" />
						</h:panelGroup>
					</div>
				</div>

				<div class="ui-g">
					<div class="ui-g-12">
						<p:commandButton value="Pesquisar" icon="fa fa-fw fa-search white"
							actionListener="#{unidadeLotacaoBean.pesquisar()}"
							style="width: auto;" update="masterDetail" />
					</div>
				</div>

			</p:fieldset>

			<div class="EmptyBox10"></div>

			<p:fieldset legend="Resultados da consulta">
				<pe:masterDetail id="masterDetail"
					level="#{unidadeLotacaoBean.currentLevel}"
					showAllBreadcrumbItems="true">

					<f:facet name="header">
						<p:messages showDetail="false" showSummary="true" />
					</f:facet>

					<pe:masterDetailLevel id="detailInicio" level="1">
						<f:facet name="label">
							<h:outputText value="Início" />
						</f:facet>

						<p:dataTable id="tabelaUnidadesLotacao" var="unidade"
							widgetVar="tabelaUnidadesLotacao"
							value="#{unidadeLotacaoBean.listaUnidadesLotacao}"
							emptyMessage="Nenhuma unidade de lotação encontrada." rows="10"
							paginator="true"
							paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
							rowsPerPageTemplate="10,20,30,40,50,100,300,500"
							currentPageReportTemplate="página {currentPage} de {totalPages}"
							paginatorPosition="bottom">

							<p:column headerText="Código" width="15%">
								<h:outputText value="#{unidade.codigo}" />
							</p:column>

							<p:column headerText="Nome" width="15%">
								<p:commandLink value="#{unidade.nome}" process="@this"
									immediate="true">
									<pe:selectDetailLevel
										listener="#{unidadeLotacaoBean.buscarUnidadePorCompetencia(unidade)}" />
								</p:commandLink>
							</p:column>

<!-- 							<p:column headerText="UF" width="15%"> -->
<!-- 								<h:outputText value="#{unidade.municipio.uf.sigla}" /> -->
<!-- 							</p:column> -->

							<p:column headerText="Municipio" width="15%">
								<h:outputText value="#{unidade.municipio.nome} - #{unidade.municipio.uf.sigla}" />
							</p:column>

							<f:facet name="footer">
								<h:outputLabel
									value="Total de unidades de lotação: #{unidadeLotacaoBean.listaUnidadesLotacao.size()}" />
							</f:facet>

						</p:dataTable>
					</pe:masterDetailLevel>

					<pe:masterDetailLevel id="detailQuantidadePorCompetencia" level="2"
						contextVar="dados">
						<f:facet name="label">
							<h:outputText value="Lotação por competência" />
						</f:facet>
<!-- 						<p:fieldset> -->
							<p:dataTable id="tabelaUnidadesLotacaoPorCompetencia"
								var="unidade" widgetVar="tabelaUnidadesLotacaoPorCompetencia"
								value="#{unidadeLotacaoBean.listaUnidadesLotacaoPorCompetencia}"
								emptyMessage="Nenhuma unidade de lotação encontrada." rows="12"
								paginator="true"
								paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
								rowsPerPageTemplate="12,20,30,40,50,100,300,500"
								currentPageReportTemplate="página {currentPage} de {totalPages}"
								paginatorPosition="bottom">

								<p:column headerText="Ano" width="15%">
									<h:outputText value="#{unidade[2]}" />
								</p:column>

								<p:column headerText="Mês" width="15%">
									<p:commandLink value="#{unidade[4]}" process="@this"
										immediate="true">
										<pe:selectDetailLevel
											listener="#{unidadeLotacaoBean.buscarServidoresPorCompetencia(unidade[0], unidade[1])}" />
									</p:commandLink>
								</p:column>

								<p:column headerText="Quantidade de servidores" width="15%">
									<h:outputText value="#{unidade[5]}" />
								</p:column>

								<f:facet name="footer">
									<h:outputLabel
										value="Total de registros: #{unidadeLotacaoBean.listaUnidadesLotacaoPorCompetencia.size()}" />
								</f:facet>

							</p:dataTable>

							<h:panelGrid columns="2">
								<p:commandButton value="Voltar" style="margin-top: 10px;"
									icon="ui-icon-arrowthick-1-w" process="@this" immediate="true">
									<pe:selectDetailLevel step="-1" />
								</p:commandButton>
							</h:panelGrid>
<!-- 						</p:fieldset> -->
					</pe:masterDetailLevel>

					<pe:masterDetailLevel id="detailServidoresPorCompetencia" level="3"
						contextVar="dados">
						<f:facet name="label">
							<h:outputText value="Servidores" />
						</f:facet>
<!-- 						<p:fieldset> -->
							<p:dataTable id="tabelaServidoresPorCompetencia" var="servidor"
								widgetVar="tabelaServidorPorCompetencia"
								value="#{unidadeLotacaoBean.listaServidoresPorCompetencia}"
								emptyMessage="Nenhum servidor encontrado." rows="15"
								paginator="true"
								paginatorAlwaysVisible="false"
								paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
								rowsPerPageTemplate="15,20,30,40,50,100,300,500"
								currentPageReportTemplate="página {currentPage} de {totalPages}"
								paginatorPosition="bottom">

								<p:column headerText="CPF" width="10%">
									<h:outputText value="#{servidor[0]}" />
								</p:column>

								<p:column headerText="Nome" width="50%"
									filterBy="#{servidor[1]}" filterMatchMode="contains"
									filterStyle="width: 75% !important;">
									<h:outputText value="#{servidor[1]}" />
								</p:column>

								<p:column headerText="Matricula" width="10%">
									<h:outputText value="#{servidor[2]}" />
								</p:column>

								<p:column headerText="Cargo" width="30%">
									<h:outputText value="#{servidor[3]}" />
								</p:column>

								<f:facet name="footer">
									<h:outputLabel
										value="Total de servidores: #{unidadeLotacaoBean.listaServidoresPorCompetencia.size()}" />
								</f:facet>

							</p:dataTable>

							<h:panelGrid columns="2">
								<p:commandButton value="Voltar" style="margin-top: 10px;"
									icon="ui-icon-arrowthick-1-w" process="@this" immediate="true">
									<pe:selectDetailLevel step="-1" />
								</p:commandButton>
								<p:commandButton value="Início" style="margin-top: 10px;"
									icon="ui-icon-home" process="@this" immediate="true">
									<pe:selectDetailLevel step="-2" />
								</p:commandButton>
							</h:panelGrid>
<!-- 						</p:fieldset> -->
					</pe:masterDetailLevel>
				</pe:masterDetail>
			</p:fieldset>
		</h:form>
		<!-- 		</p:fieldset> -->
	</ui:define>
</ui:composition>