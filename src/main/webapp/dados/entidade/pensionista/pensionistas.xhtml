<ui:composition xmlns="http://www.w3.org/1999/xhtml" xmlns:h="http://java.sun.com/jsf/html" xmlns:f="http://java.sun.com/jsf/core"
	xmlns:ui="http://java.sun.com/jsf/facelets" xmlns:p="http://primefaces.org/ui" xmlns:pe="http://primefaces.org/ui/extensions"
	xmlns:fn="http://java.sun.com/jsp/jstl/functions" template="/resources/template/template.xhtml">

	<ui:define name="content">
		<p:fieldset legend="Lista de Pensionistas">
			<h:form prependId="false">
				<p:fieldset>

					<h:panelGrid columns="4" style="margin-bottom:10px;" cellpadding="5">

						<h:outputText value="Tipo Dependência: " styleClass="FontBold" />
						<h:panelGrid columns="1" style="width: 30%;">
							<p:selectOneMenu value="#{pensionistaBean.tipoDependencia.id}" autoWidth="false" style="width: 200px!important;">
								<f:selectItem itemLabel="Todos" itemValue="#{0}" />
								<f:selectItems value="#{pensionistaBean.listaTipoDependencia}" var="tipoDependencia" itemLabel="#{tipoDependencia.descricao}"
									itemValue="#{tipoDependencia.id}" />
							</p:selectOneMenu>
						</h:panelGrid>

						<h:outputText value="Tipo Pensão: " styleClass="FontBold" />
						<h:panelGrid columns="1" style="width: 30%;">
							<p:selectOneMenu value="#{pensionistaBean.tipoPensao}" autoWidth="false" style="width: 200px!important;">
								<f:selectItem itemLabel="Todos" itemValue="#{0}" />
								<f:selectItems value="#{pensionistaBean.listaTipoPensao}" var="tipoPensao" itemLabel="#{tipoPensao.descricao}" itemValue="#{tipoPensao.id}" />
							</p:selectOneMenu>
						</h:panelGrid>

						<h:outputText value="Matricula Pensionista: " styleClass="FontBold" />
						<p:inputText value="#{pensionistaBean.matriculaPensionista}" size="20" />

						<h:outputText value="CPF Pensionista: " styleClass="FontBold" />
						<p:inputText value="#{pensionistaBean.cpfPensionista}" size="20" />

						<h:outputText value="Nome Pensionista: " styleClass="FontBold" />
						<p:inputText value="#{pensionistaBean.nomePensionista}" size="40" />

						<h:outputText value="CPF Servidor: " styleClass="FontBold" />
						<p:inputText value="#{pensionistaBean.cpfServidor}" size="20" />

						<h:outputText value="Nome Servidor: " styleClass="FontBold" />
						<p:inputText value="#{pensionistaBean.nomeServidor}" size="40" />

						<p:commandButton value="Pesquisar" icon="fa fa-fw fa-search white" actionListener="#{pensionistaBean.pesquisar()}" update="masterDetail" />
					</h:panelGrid>
				</p:fieldset>

				<pe:masterDetail id="masterDetail" level="#{pensionistaBean.currentLevel}" showAllBreadcrumbItems="true">

					<f:facet name="header">
						<p:messages showDetail="false" showSummary="true" />
					</f:facet>

					<pe:masterDetailLevel id="detailInicio" level="1">
						<f:facet name="label">
							<h:outputText value="Início" />
						</f:facet>

						<p:dataTable id="tabelaPensionista" var="pensionista" widgetVar="tabelaPensionista" value="#{pensionistaBean.listaPensionista}"
							emptyMessage="Nenhum pensionista encontrado." rows="10" paginator="true"
							paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
							rowsPerPageTemplate="10,20,30,40,50,100,300,500" currentPageReportTemplate="página {currentPage} de {totalPages}" paginatorPosition="bottom">

							<p:column headerText="CPF" width="5%">
								<h:outputText value="#{pensionista.cadastroUnico.cpf}" />
							</p:column>

							<p:column headerText="Pensionista" width="15%">
								<p:commandLink value="#{pensionista.cadastroUnico.listaPessoaFisica[0].nome}" process="@this" immediate="true">
									<pe:selectDetailLevel listener="#{pensionistaBean.buscarDadosPensionista(pensionista)}" />
								</p:commandLink>
							</p:column>

							<p:column headerText="Matricula Pensionista" width="15%">
								<h:outputText value="#{pensionista.matricula}" />
							</p:column>

							<p:column headerText="Instituidor" width="15%">
								<h:outputText value="#{pensionista.listaPensoes[0].servidor.cadastroUnico.listaPessoaFisica[0].nome}" />
							</p:column>

							<f:facet name="footer">
								<h:outputLabel value="Total de pensionistas: #{pensionistaBean.listaPensionista.size()}" />
							</f:facet>

						</p:dataTable>
					</pe:masterDetailLevel>

					<pe:masterDetailLevel id="detailDadosPensionista" level="2" contextVar="dados">
						<f:facet name="label">
							<h:outputText value="Dados do Pensionista" />
						</f:facet>
						<p:fieldset>
							<p:panel header="Dados Pessoais">
								<h:panelGrid columns="2">
									<h:outputText value="CPF: " styleClass="FontBold" />
									<h:outputText value="#{pensionistaBean.beneficiarioPensionista.cadastroUnico.cpf}" />

									<h:outputText value="Nome: " styleClass="FontBold" />
									<h:outputText value="#{pensionistaBean.beneficiarioPensionista.cadastroUnico.listaPessoaFisica[0].nome}" />

									<h:outputText value="Sexo: " styleClass="FontBold" />
									<h:outputText value="#{pensionistaBean.beneficiarioPensionista.cadastroUnico.listaPessoaFisica[0].sexo.descricao}" />

									<h:outputText value="Data de Nascimento: " styleClass="FontBold" />
									<h:outputText value="#{pensionistaBean.beneficiarioPensionista.cadastroUnico.listaPessoaFisica[0].dataNascimento}">
										<f:converter converterId="dateConverter" />
									</h:outputText>

									<h:outputText value="Nome da mãe: " styleClass="FontBold" />
									<h:outputText value="#{pensionistaBean.beneficiarioPensionista.cadastroUnico.listaPessoaFisica[0].nomeMae}" />
								</h:panelGrid>
							</p:panel>
							<p:spacer />
							<p:panel header="Dados da Pensão">
								<h:panelGrid columns="2">

									<h:outputText value="Matricula: " styleClass="FontBold" />
									<h:outputText value="#{pensionistaBean.beneficiarioPensionista.matricula}" />

									<h:outputText value="Data de Início: " styleClass="FontBold" />
									<h:outputText value="#{pensionistaBean.beneficiarioPensionista.listaPensoes[0].dataInicio}">
										<f:converter converterId="dateConverter" />
									</h:outputText>

									<h:outputText value="Tipo de Dependência: " styleClass="FontBold" />
									<h:outputText value="#{pensionistaBean.beneficiarioPensionista.listaPensoes[0].tipoDependencia.descricao}" />

									<h:outputText value="Tipo de Pensão: " styleClass="FontBold" />
									<h:outputText value="#{pensionistaBean.beneficiarioPensionista.listaPensoes[0].tipoPensao.descricao}" />

									<h:outputText value="Data de Término: " styleClass="FontBold" />
									<h:outputText value="#{pensionistaBean.beneficiarioPensionista.listaPensoes[0].dataFim}">
										<f:converter converterId="dateConverter" />
									</h:outputText>

								</h:panelGrid>

								<p:spacer />

								<p:fieldset legend="Instituidor">
									<h:panelGrid columns="2">
										<h:outputText value="CPF: " styleClass="FontBold" />
										<h:outputText value="#{pensionistaBean.beneficiarioPensionista.listaPensoes[0].servidor.cadastroUnico.cpf}" />

										<h:outputText value="Nome: " styleClass="FontBold" />
										<h:outputText value="#{pensionistaBean.beneficiarioPensionista.listaPensoes[0].servidor.cadastroUnico.listaPessoaFisica[0].nome}" />

										<h:outputText value="Sexo: " styleClass="FontBold" />
										<h:outputText value="#{pensionistaBean.beneficiarioPensionista.listaPensoes[0].servidor.cadastroUnico.listaPessoaFisica[0].sexo.descricao}" />

										<h:outputText value="Data de Nascimento: " styleClass="FontBold" />
										<h:outputText value="#{pensionistaBean.beneficiarioPensionista.listaPensoes[0].servidor.cadastroUnico.listaPessoaFisica[0].dataNascimento}">
											<f:converter converterId="dateConverter" />
										</h:outputText>

										<h:outputText value="Nome da Mãe: " styleClass="FontBold" />
										<h:outputText value="#{pensionistaBean.beneficiarioPensionista.listaPensoes[0].servidor.cadastroUnico.listaPessoaFisica[0].nomeMae}" />

									</h:panelGrid>

									<p:panel header="Vínculo Funcional do Servidor">
										<h:panelGrid columns="2">
											<h:outputText value="Matricula: " styleClass="FontBold" />
											<h:outputText value="#{pensionistaBean.beneficiarioPensionista.listaPensoes[0].servidor.matricula}" />

											<h:outputText value="Data de Admissão: " styleClass="FontBold" />
											<h:outputText value="#{pensionistaBean.beneficiarioPensionista.listaPensoes[0].servidor.listaVinculosFuncionais[0].dataAdmissao}">
												<f:converter converterId="dateConverter" />
											</h:outputText>

											<h:outputText value="Tipo: " styleClass="FontBold" />
											<h:outputText value="#{pensionistaBean.beneficiarioPensionista.listaPensoes[0].servidor.listaVinculosFuncionais[0].tipoServidor.descricao}" />

											<h:outputText value="Tipo de Vínculo: " styleClass="FontBold" />
											<h:outputText value="#{pensionistaBean.beneficiarioPensionista.listaPensoes[0].servidor.listaVinculosFuncionais[0].tipoVinculo.descricao}" />

											<h:outputText value="Regime Previdenciário: " styleClass="FontBold" />
											<h:outputText
												value="#{pensionistaBean.beneficiarioPensionista.listaPensoes[0].servidor.listaVinculosFuncionais[0].regimePrevidenciario.descricao}" />
										</h:panelGrid>
										<p:panel header="Cargo">
											<h:panelGrid columns="2">
												<h:outputText value="Nome: " styleClass="FontBold" />
												<h:outputText value="#{pensionistaBean.beneficiarioPensionista.listaPensoes[0].servidor.listaVinculosFuncionais[0].cargo.nome}" />

												<h:outputText value="Carga Horaria Mensal: " styleClass="FontBold" />
												<h:outputText
													value="#{pensionistaBean.beneficiarioPensionista.listaPensoes[0].servidor.listaVinculosFuncionais[0].cargo.cargaHorariaMensal}" />

												<h:outputText value="Carga Horaria Semanal: " styleClass="FontBold" />
												<h:outputText
													value="#{(pensionistaBean.beneficiarioPensionista.listaPensoes[0].servidor.listaVinculosFuncionais[0].cargo.cargaHorariaMensal) / 4}">
													<f:convertNumber integerOnly="true" />
												</h:outputText>

												<h:outputText value="Escolaridade: " styleClass="FontBold" />
												<h:outputText
													value="#{pensionistaBean.beneficiarioPensionista.listaPensoes[0].servidor.listaVinculosFuncionais[0].cargo.escolaridade.descricao}" />

												<h:outputText value="Tipo: " styleClass="FontBold" />
												<h:outputText value="#{pensionistaBean.beneficiarioPensionista.listaPensoes[0].servidor.listaVinculosFuncionais[0].cargo.tipo}" />

												<h:outputText value="Cargo Acumulável: " styleClass="FontBold" />
												<h:outputText
													value="#{pensionistaBean.beneficiarioPensionista.listaPensoes[0].servidor.listaVinculosFuncionais[0].cargo.tipoAcumulavel.descricao}" />

												<h:outputText value="Situação: " styleClass="FontBold" />
												<h:outputText
													value="#{pensionistaBean.beneficiarioPensionista.listaPensoes[0].servidor.listaVinculosFuncionais[0].cargo.situacao.descricao}" />

												<h:outputText value="Data de Extinção: " styleClass="FontBold" />
												<h:outputText value="#{pensionistaBean.beneficiarioPensionista.listaPensoes[0].servidor.listaVinculosFuncionais[0].cargo.dataExtincao}">
													<f:converter converterId="dateConverter" />
												</h:outputText>
											</h:panelGrid>
										</p:panel>
									</p:panel>
								</p:fieldset>
							</p:panel>
							<h:panelGrid columns="2">
								<p:commandButton value="Voltar" style="margin-top: 10px;" icon="ui-icon-arrowthick-1-w" process="@this" immediate="true">
									<pe:selectDetailLevel step="-1" />
								</p:commandButton>
								<p:commandButton value="Avançar" style="margin-top: 10px;" icon="ui-icon-arrowthick-1-e" process="@this" immediate="true">
									<pe:selectDetailLevel listener="#{pensionistaBean.buscarContraCheques(pensionistaBean.beneficiarioPensionista)}" />
								</p:commandButton>
							</h:panelGrid>
						</p:fieldset>
					</pe:masterDetailLevel>
					<pe:masterDetailLevel level="3">
						<f:facet name="label">
							<h:outputText value="ContraCheques" />
						</f:facet>

						<p:panel header="Servidor">
							<h:panelGrid columns="2">
								<h:outputText value="Entidade: " styleClass="FontBold" />
								<h:outputText value="#{pensionistaBean.beneficiarioPensionista.entidade.nome}" />
								<h:outputText value="Nome: " styleClass="FontBold" />
								<h:outputText value="#{pensionistaBean.beneficiarioPensionista.cadastroUnico.listaPessoaFisica[0].nome}" />
							</h:panelGrid>
						</p:panel>
						<p:panel>
							<p:dataTable id="tabelaPensionistaContraCheque" var="contracheque" widgetVar="tabelaPensionistaContraCheque"
								value="#{pensionistaBean.listaContraCheque}" emptyMessage="Nenhum contracheque encontrado." rows="10" paginator="true"
								paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
								rowsPerPageTemplate="10,20,30,40,50,100,300,500" currentPageReportTemplate="página {currentPage} de {totalPages}" paginatorPosition="bottom">

								<p:column headerText="Competência" width="50%">
									<p:commandLink value="#{contracheque.mes}/#{contracheque.ano}" process="@this" immediate="true">
										<pe:selectDetailLevel listener="#{pensionistaBean.detalharContraCheque(contracheque)}" />
									</p:commandLink>
								</p:column>

								<p:column headerText="Folha" width="50%">
									<h:outputText value="#{contracheque.tipoFolha.descricao}" />
								</p:column>

							</p:dataTable>
						</p:panel>

						<h:panelGrid columns="2">
							<p:commandButton value="Voltar" style="margin-top: 10px;" icon="ui-icon-arrowthick-1-w" process="@this" immediate="true">
								<pe:selectDetailLevel step="-1" />
							</p:commandButton>
						</h:panelGrid>
					</pe:masterDetailLevel>
					<pe:masterDetailLevel level="4">
						<f:facet name="label">
							<h:outputText value="Detalhar ContraCheque" rendered="#{pensionistaBean.contraCheque.mes eq null}" />
							<h:outputText value="Detalhar #{pensionistaBean.contraCheque.mes}/#{pensionistaBean.contraCheque.ano}"
								rendered="#{pensionistaBean.contraCheque.mes != null}" />
						</f:facet>
						<p:panel header="Informações do Servidor">
							<h:panelGrid columns="2" columnClasses="label, value">
								<h:outputText value="Competência: " styleClass="FontBold" />
								<h:outputText value="#{pensionistaBean.contraCheque.mes}/#{pensionistaBean.contraCheque.ano}" />

								<h:outputText value="Nome: " styleClass="FontBold" />
								<h:outputText value="#{pensionistaBean.contraCheque.beneficiario.cadastroUnico.listaPessoaFisica[0].nome}" />

								<h:outputText value="Situação do Beneficiário: " styleClass="FontBold" />
								<h:outputText value="#{pensionistaBean.contraCheque.situacaoBeneficiario.descricao}" />

								<h:outputText value="Folha: " styleClass="FontBold" />
								<h:outputText value="#{pensionistaBean.contraCheque.tipoFolha.descricao}" />

							</h:panelGrid>
						</p:panel>
						<p:panel header="Contracheque">
							<p:dataTable value="#{pensionistaBean.contraCheque.listaVerbasContraCheque}" var="verbas" emptyMessage="Nenhuma verba encontrada.">
								<f:facet name="header">
									<h:outputText value="Verbas" />
								</f:facet>
								<p:column headerText="Código" styleClass="TexAlCenter" style="width: 70px">
									<h:outputText value="#{verbas.verba.codigo}" />
								</p:column>

								<p:column headerText="Nome">
									<h:outputText value="#{verbas.verba.descricao}" />
								</p:column>

								<p:column headerText="Natureza" styleClass="TexAlCenter" style="width: 70px">
									<h:outputText value="#{verbas.verba.natureza.descricao}" />
								</p:column>

								<p:column headerText="Referência" styleClass="TexAlRight">
									<h:outputText value="#{verbas.referencia}" />
								</p:column>

								<p:column headerText="Valor" styleClass="TexAlRight">
									<h:outputText value="#{verbas.valor}">
										<f:convertNumber currencySymbol="R$" type="currency" />
									</h:outputText>
								</p:column>

							</p:dataTable>

							<p:spacer />
							<h:panelGrid columns="2" columnClasses="label, value">

								<h:outputText value="Total Bruto: " styleClass="FontBold" />
								<h:outputText value="#{pensionistaBean.contraCheque.totalVencimentos}">
									<f:convertNumber currencySymbol="R$" type="currency" />
								</h:outputText>

								<h:outputText value="Base Previdência Segurado: " styleClass="FontBold" />
								<h:outputText value="#{pensionistaBean.contraCheque.basePrevidenciariaSegurado}">
									<f:convertNumber currencySymbol="R$" type="currency" />
								</h:outputText>

								<h:outputText value="Base Previdência Patronal: " styleClass="FontBold" />
								<h:outputText value="#{pensionistaBean.contraCheque.basePrevidenciariaPatronal}">
									<f:convertNumber currencySymbol="R$" type="currency" />
								</h:outputText>

								<h:outputText value="Base FGTS: " styleClass="FontBold" />
								<h:outputText value="#{pensionistaBean.contraCheque.baseFgts}">
									<f:convertNumber currencySymbol="R$" type="currency" />
								</h:outputText>

								<h:outputText value="Base IRPF: " styleClass="FontBold" />
								<h:outputText value="#{pensionistaBean.contraCheque.baseIrpf}">
									<f:convertNumber currencySymbol="R$" type="currency" />
								</h:outputText>

								<h:outputText value="Total de Descontos: " styleClass="FontBold" />
								<h:outputText value="#{pensionistaBean.contraCheque.totalDescontos}">
									<f:convertNumber currencySymbol="R$" type="currency" />
								</h:outputText>

								<h:outputText value="Total de Líquido " styleClass="FontBold" />
								<h:outputText value="#{pensionistaBean.contraCheque.totalVencimentos - pensionistaBean.contraCheque.totalDescontos}">
									<f:convertNumber currencySymbol="R$" type="currency" />
								</h:outputText>

							</h:panelGrid>

						</p:panel>

						<h:panelGrid columns="2">
							<p:commandButton value="Voltar" style="margin-top: 10px;" icon="ui-icon-arrowthick-1-w" process="@this" immediate="true">
								<pe:selectDetailLevel step="-1" />
							</p:commandButton>
							<p:commandButton value="Início" style="margin-top: 10px;" icon="ui-icon-home" process="@this" immediate="true">
								<pe:selectDetailLevel step="-3" />
							</p:commandButton>
						</h:panelGrid>
					</pe:masterDetailLevel>

				</pe:masterDetail>
			</h:form>
		</p:fieldset>
	</ui:define>
</ui:composition>