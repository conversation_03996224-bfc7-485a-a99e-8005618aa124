<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:p="http://primefaces.org/ui"
	xmlns:pe="http://primefaces.org/ui/extensions"
	xmlns:fn="http://java.sun.com/jsp/jstl/functions"
	template="/resources/template/template.xhtml">

	<ui:define name="content">

		<h:form prependId="false">
			<p:fieldset legend="Dados para a consulta por servidores">

				<div class="ui-g">
					<div class="ui-g-12 ui-md-6 ui-lg-4">
						<h:panelGroup>
							<h:outputText value="Tipo: " styleClass="FontBold" />
							<p:selectOneMenu value="#{servidorBean.tipoServidor}">
								<f:selectItem itemLabel="Todos" itemValue="#{0}" />
								<f:selectItems value="#{servidorBean.listaTipoServidor}"
									var="tipo" itemLabel="#{tipo.descricao}" itemValue="#{tipo.id}" />
							</p:selectOneMenu>
						</h:panelGroup>
					</div>
					<div class="ui-g-12 ui-md-6 ui-lg-4">
						<h:panelGroup>
							<h:outputText value="Tipo Vínculo: " styleClass="FontBold" />
							<p:selectOneMenu value="#{servidorBean.tipoVinculo.id}">
								<f:selectItem itemLabel="Todos" itemValue="#{0}" />
								<f:selectItems value="#{servidorBean.listaTipoVinculo}"
									var="tipoVinculo" itemLabel="#{tipoVinculo.descricao}"
									itemValue="#{tipoVinculo.id}" />
							</p:selectOneMenu>
						</h:panelGroup>
					</div>
				</div>
				<div class="ui-g">
					<div class="ui-g-12 ui-md-6 ui-lg-4">
						<h:panelGroup>
							<h:outputText value="Regime Previdenciário: "
								styleClass="FontBold" />
							<p:selectOneMenu value="#{servidorBean.regimePrevidenciario}">
								<f:selectItem itemLabel="Todos" itemValue="#{0}" />
								<f:selectItems value="#{servidorBean.listaRegimePrevidenciario}"
									var="regime" itemLabel="#{regime.descricao}"
									itemValue="#{regime.id}" />
							</p:selectOneMenu>
						</h:panelGroup>
					</div>
					<div class="ui-g-12 ui-md-6 ui-lg-4">
						<h:panelGroup>
							<h:outputText value="CPF: " styleClass="FontBold" />
							<p:inputText value="#{servidorBean.cpf}" />
						</h:panelGroup>
					</div>
				</div>
				<div class="ui-g">
					<div class="ui-g-12 ui-md-6 ui-lg-4">
						<h:panelGroup>
							<h:outputText value="Nome: " styleClass="FontBold" />
							<p:inputText value="#{servidorBean.nome}" />


						</h:panelGroup>
					</div>
					<div class="ui-g-12 ui-md-6 ui-lg-4">
						<h:panelGroup>
							<h:outputText value="Matricula: " styleClass="FontBold" />
							<p:inputText value="#{servidorBean.matricula}" />
						</h:panelGroup>
					</div>
				</div>
				<div class="ui-g">
					<div class="ui-g-12 ui-md-6 ui-lg-4">
						<h:panelGroup>
							<h:outputText value="Cargo: " styleClass="FontBold" />
							<p:selectOneMenu value="#{servidorBean.cargo.id}" filter="true"
								filterMatchMode="contains">
								<f:selectItem itemLabel="Todos" itemValue="#{0}" />
								<f:selectItems value="#{servidorBean.listaCargo}" var="cargo"
									itemLabel="#{cargo.nome}" itemValue="#{cargo.id}" />
							</p:selectOneMenu>
						</h:panelGroup>
					</div>
				</div>
				<div class="ui-g">
					<div class="ui-g-12">
						<p:commandButton value="Pesquisar" icon="fa fa-fw fa-search white" style="width: auto;"
							actionListener="#{servidorBean.pesquisar()}"
							update="masterDetail" />
					</div>
				</div>

			</p:fieldset>

			<div class="EmptyBox10" />

			<p:fieldset legend="Resultados da consulta">

				<pe:masterDetail id="masterDetail"
					level="#{servidorBean.currentLevel}" showAllBreadcrumbItems="true">

					<f:facet name="header">
						<p:messages showDetail="false" showSummary="true" />
					</f:facet>

					<pe:masterDetailLevel id="detailInicio" level="1">
						<f:facet name="label">
							<h:outputText value="Início" />
						</f:facet>

						<p:dataTable id="tabelaServidor" var="servidor"
							widgetVar="tabelaServidor" value="#{servidorBean.listaServidor}"
							emptyMessage="Nenhum servidor encontrado." rows="10"
							paginator="true"
							paginatorAlwaysVisible="false"
							paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
							rowsPerPageTemplate="10,20,30,40,50,100,300,500"
							currentPageReportTemplate="página {currentPage} de {totalPages}"
							paginatorPosition="bottom">

							<p:column headerText="CPF" width="5%">
								<h:outputText value="#{servidor.cadastroUnico.cpf}" />
							</p:column>

							<p:column headerText="Nome" width="15%">
								<p:commandLink
									value="#{servidor.cadastroUnico.listaPessoaFisica[0].nome}"
									process="@this" immediate="true">
									<pe:selectDetailLevel
										listener="#{servidorBean.buscarDadosServidor(servidor)}" />
								</p:commandLink>
							</p:column>

							<p:column headerText="Matricula" width="15%">
								<h:outputText value="#{servidor.matricula}" />
							</p:column>

							<p:column headerText="Cargo" width="15%">
								<h:outputText
									value="#{servidor.listaVinculosFuncionais[0].cargo.nome}" />
							</p:column>

							<f:facet name="footer">
								<h:outputLabel
									value="Total de servidores: #{servidorBean.listaServidor.size()}" />
							</f:facet>

						</p:dataTable>
					</pe:masterDetailLevel>

					<pe:masterDetailLevel id="detailDadosServidor" level="2"
						contextVar="dados">
						<f:facet name="label">
							<h:outputText value="Dados do Servidor" />
						</f:facet>
						<p:fieldset>
							<p:panel header="Dados Pessoais">
								<h:panelGrid columns="2">
									<h:outputText value="CPF: " styleClass="FontBold" />
									<h:outputText
										value="#{servidorBean.beneficiarioServidor.cadastroUnico.cpf}" />

									<h:outputText value="Nome: " styleClass="FontBold" />
									<h:outputText
										value="#{servidorBean.beneficiarioServidor.cadastroUnico.listaPessoaFisica[0].nome}" />

									<h:outputText value="Sexo: " styleClass="FontBold" />
									<h:outputText
										value="#{servidorBean.beneficiarioServidor.cadastroUnico.listaPessoaFisica[0].sexo.descricao}" />

									<h:outputText value="Data de Nascimento: "
										styleClass="FontBold" />
									<h:outputText
										value="#{servidorBean.beneficiarioServidor.cadastroUnico.listaPessoaFisica[0].dataNascimento}">
										<f:converter converterId="dateConverter" />
									</h:outputText>

									<h:outputText value="Nome da mãe: " styleClass="FontBold" />
									<h:outputText
										value="#{servidorBean.beneficiarioServidor.cadastroUnico.listaPessoaFisica[0].nomeMae}" />
								</h:panelGrid>
							</p:panel>
							<p:spacer />
							<p:panel header="Dados Funcionais">
								<h:panelGrid columns="2">

									<h:outputText value="Matricula: " styleClass="FontBold" />
									<h:outputText
										value="#{servidorBean.beneficiarioServidor.matricula}" />

									<h:outputText value="Data de Admissão: " styleClass="FontBold" />
									<h:outputText
										value="#{servidorBean.beneficiarioServidor.listaVinculosFuncionais[0].dataAdmissao}">
										<f:converter converterId="dateConverter" />
									</h:outputText>

									<h:outputText value="Tipo: " styleClass="FontBold" />
									<h:outputText
										value="#{servidorBean.beneficiarioServidor.listaVinculosFuncionais[0].tipoServidor.descricao}" />

									<h:outputText value="Tipo de Vínculo: " styleClass="FontBold" />
									<h:outputText
										value="#{servidorBean.beneficiarioServidor.listaVinculosFuncionais[0].tipoVinculo.descricao}" />

									<h:outputText value="Regime Previdenciário: "
										styleClass="FontBold" />
									<h:outputText
										value="#{servidorBean.beneficiarioServidor.listaVinculosFuncionais[0].regimePrevidenciario.descricao}" />
								</h:panelGrid>

								<p:spacer />

								<p:fieldset legend="Dados do Cargo">
									<h:panelGrid columns="2">
										<h:outputText value="Nome: " styleClass="FontBold" />
										<h:outputText
											value="#{servidorBean.beneficiarioServidor.listaVinculosFuncionais[0].cargo.nome}" />

										<h:outputText value="Carga Horaria Mensal: "
											styleClass="FontBold" />
										<h:outputText
											value="#{servidorBean.beneficiarioServidor.listaVinculosFuncionais[0].cargo.cargaHorariaMensal}" />

										<h:outputText value="Carga Horaria Semanal: "
											styleClass="FontBold" />
										<h:outputText
											value="#{(servidorBean.beneficiarioServidor.listaVinculosFuncionais[0].cargo.cargaHorariaMensal) / 4}">
											<f:convertNumber integerOnly="true" />
										</h:outputText>

										<h:outputText value="Escolaridade: " styleClass="FontBold" />
										<h:outputText
											value="#{servidorBean.beneficiarioServidor.listaVinculosFuncionais[0].cargo.escolaridade.descricao}" />

										<h:outputText value="Tipo: " styleClass="FontBold" />
										<h:outputText
											value="#{servidorBean.beneficiarioServidor.listaVinculosFuncionais[0].cargo.tipo}" />

										<h:outputText value="Cargo Acumulável: " styleClass="FontBold" />
										<h:outputText
											value="#{servidorBean.beneficiarioServidor.listaVinculosFuncionais[0].cargo.tipoAcumulavel.descricao}" />

										<h:outputText value="Situação: " styleClass="FontBold" />
										<h:outputText
											value="#{servidorBean.beneficiarioServidor.listaVinculosFuncionais[0].cargo.situacao.descricao}" />

										<h:outputText value="Data de Extinção: " styleClass="FontBold" />
										<h:outputText
											value="#{servidorBean.beneficiarioServidor.listaVinculosFuncionais[0].cargo.dataExtincao}">
											<f:converter converterId="dateConverter" />
										</h:outputText>
									</h:panelGrid>
								</p:fieldset>
							</p:panel>
							<h:panelGrid columns="2">
								<p:commandButton value="Voltar" style="margin-top: 10px;"
									icon="ui-icon-arrowthick-1-w" process="@this" immediate="true">
									<pe:selectDetailLevel step="-1" />
								</p:commandButton>
								<p:commandButton value="Avançar" style="margin-top: 10px;"
									icon="ui-icon-arrowthick-1-e" process="@this" immediate="true">
									<pe:selectDetailLevel
										listener="#{servidorBean.buscarHistoriocoFuncionalServidor(servidorBean.beneficiarioServidor)}" />
								</p:commandButton>
							</h:panelGrid>
						</p:fieldset>
					</pe:masterDetailLevel>
					<pe:masterDetailLevel id="detailHistoricoFuncional" level="3"
						contextVar="historicoServidor">
						<f:facet name="label">
							<h:outputText value="Histórico Funcional" />
						</f:facet>
						<p:panel>
							<h:panelGrid columns="2">
								<h:outputText value="Entidade: " styleClass="FontBold" />
								<h:outputText
									value="#{servidorBean.beneficiarioServidor.entidade.nome}" />
								<h:outputText value="Nome: " styleClass="FontBold" />
								<h:outputText
									value="#{servidorBean.beneficiarioServidor.cadastroUnico.listaPessoaFisica[0].nome}" />
							</h:panelGrid>
						</p:panel>
						<p:spacer />
<!-- 						<p:panel> -->
							<p:dataTable id="tabelaServidorHistorico" var="historico"
								widgetVar="tabelaServidorHistorico"
								value="#{servidorBean.listaHistoricoFuncional}"
								emptyMessage="Nenhum histórico encontrado." rows="10"
								paginator="true"
								paginatorAlwaysVisible="false"
								paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
								rowsPerPageTemplate="10,20,30,40,50,100,300,500"
								currentPageReportTemplate="página {currentPage} de {totalPages}"
								paginatorPosition="bottom">

								<p:column headerText="Data da Ocorrência" width="50%">
									<h:outputText value="#{historico.dataOcorrencia}">
										<f:converter converterId="dateConverter" />
									</h:outputText>
								</p:column>

								<p:column headerText="Situação Funcional" width="50%">
									<h:outputText value="#{historico.situacaoFuncional.descricao}" />
								</p:column>
							</p:dataTable>
<!-- 						</p:panel> -->
						<h:panelGrid columns="2">
							<p:commandButton value="Voltar" style="margin-top: 10px;"
								icon="ui-icon-arrowthick-1-w" process="@this" immediate="true">
								<pe:selectDetailLevel step="-1" />
							</p:commandButton>
							<p:commandButton value="Avançar" style="margin-top: 10px;"
								icon="ui-icon-arrowthick-1-e" process="@this" immediate="true">
								<pe:selectDetailLevel
									listener="#{servidorBean.buscarPensionistas(servidorBean.beneficiarioServidor)}" />
							</p:commandButton>
						</h:panelGrid>
					</pe:masterDetailLevel>
					<pe:masterDetailLevel level="4" contextVar="pensionistas">
						<f:facet name="label">
							<h:outputText value="Pensionistas" />
						</f:facet>
						<p:panel>
							<h:panelGrid columns="2">
								<h:outputText value="Entidade: " styleClass="FontBold" />
								<h:outputText
									value="#{servidorBean.beneficiarioServidor.entidade.nome}" />
								<h:outputText value="Nome: " styleClass="FontBold" />
								<h:outputText
									value="#{servidorBean.beneficiarioServidor.cadastroUnico.listaPessoaFisica[0].nome}" />
							</h:panelGrid>
						</p:panel>
						<p:spacer />
<!-- 						<p:panel> -->
							<p:dataTable id="tabelaServidorDependentes" var="dependente"
								widgetVar="tabelaServidorDependentes"
								value="#{servidorBean.listaPensionista}"
								emptyMessage="Nenhum dependente encontrado." rows="10"
								paginator="true"
								paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
								rowsPerPageTemplate="10,20,30,40,50,100,300,500"
								currentPageReportTemplate="página {currentPage} de {totalPages}"
								paginatorPosition="bottom">

								<p:column headerText="CPF" width="50%">
									<h:outputText value="#{dependente.cadastroUnico.cpf}" />
								</p:column>

								<p:column headerText="Nome" width="50%">
									<h:outputText
										value="#{dependente.cadastroUnico.listaPessoaFisica[0].nome}" />
								</p:column>

								<p:column headerText="Sexo" width="50%">
									<h:outputText
										value="#{dependente.cadastroUnico.listaPessoaFisica[0].sexo.descricao}" />
								</p:column>

								<p:column headerText="Data de Nascimento" width="50%">
									<h:outputText
										value="#{dependente.cadastroUnico.listaPessoaFisica[0].dataNascimento}">
										<f:converter converterId="dateConverter" />
									</h:outputText>
								</p:column>

								<p:column headerText="Tipo de Dependência" width="50%">
									<h:outputText
										value="#{dependente.listaPensoes[0].tipoDependencia.descricao}" />
								</p:column>

								<p:column headerText="Matricula" width="50%">
									<h:outputText value="#{dependente.matricula}" />
								</p:column>
							</p:dataTable>
<!-- 						</p:panel> -->
						<h:panelGrid columns="2">
							<p:commandButton value="Voltar" style="margin-top: 10px;"
								icon="ui-icon-arrowthick-1-w" process="@this" immediate="true">
								<pe:selectDetailLevel step="-1" />
							</p:commandButton>
							<p:commandButton value="Avançar" style="margin-top: 10px;"
								icon="ui-icon-arrowthick-1-e" process="@this" immediate="true">
								<pe:selectDetailLevel
									listener="#{servidorBean.buscarLotacaoServidor(servidorBean.beneficiarioServidor)}" />
							</p:commandButton>
						</h:panelGrid>
					</pe:masterDetailLevel>
					<pe:masterDetailLevel level="5">
						<f:facet name="label">
							<h:outputText value="Lotação" />
						</f:facet>
						<p:panel>
							<h:panelGrid columns="2">
								<h:outputText value="Entidade: " styleClass="FontBold" />
								<h:outputText
									value="#{servidorBean.beneficiarioServidor.entidade.nome}" />
								<h:outputText value="Nome: " styleClass="FontBold" />
								<h:outputText
									value="#{servidorBean.beneficiarioServidor.cadastroUnico.listaPessoaFisica[0].nome}" />
							</h:panelGrid>
						</p:panel>
						<p:spacer />
<!-- 						<p:panel> -->
							<p:dataTable id="tabelaServidorLotacao" var="lotacao"
								widgetVar="tabelaServidorLotacao"
								value="#{servidorBean.listaLotacao}"
								emptyMessage="Nenhum lotação encontrada para o servidor."
								rows="10" paginator="true"
								paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
								rowsPerPageTemplate="10,20,30,40,50,100,300,500"
								currentPageReportTemplate="página {currentPage} de {totalPages}"
								paginatorPosition="bottom">

								<p:column headerText="Competência" width="15%">
									<h:outputText value="#{lotacao.mes}/#{lotacao.ano}" />
								</p:column>

								<p:column headerText="Unidade">
									<h:outputText value="#{lotacao.unidadeLotacao.nome}" />
								</p:column>

								<p:column headerText="Município">
									<h:outputText value="#{lotacao.unidadeLotacao.municipio.nome}" />
								</p:column>

							</p:dataTable>
<!-- 						</p:panel> -->

						<h:panelGrid columns="2">
							<p:commandButton value="Voltar" style="margin-top: 10px;"
								icon="ui-icon-arrowthick-1-w" process="@this" immediate="true">
								<pe:selectDetailLevel step="-1" />
							</p:commandButton>
							<p:commandButton value="Avançar" style="margin-top: 10px;"
								icon="ui-icon-arrowthick-1-e" process="@this" immediate="true">
								<pe:selectDetailLevel
									listener="#{servidorBean.buscarContraCheques(servidorBean.beneficiarioServidor)}" />
							</p:commandButton>
						</h:panelGrid>
					</pe:masterDetailLevel>
					<pe:masterDetailLevel level="6">
						<f:facet name="label">
							<h:outputText value="ContraCheques" />
						</f:facet>
						<p:panel>
							<h:panelGrid columns="2">
								<h:outputText value="Entidade: " styleClass="FontBold" />
								<h:outputText
									value="#{servidorBean.beneficiarioServidor.entidade.nome}" />
								<h:outputText value="Nome: " styleClass="FontBold" />
								<h:outputText
									value="#{servidorBean.beneficiarioServidor.cadastroUnico.listaPessoaFisica[0].nome}" />
							</h:panelGrid>
						</p:panel>
						<p:spacer />
<!-- 						<p:panel> -->
							<p:dataTable id="tabelaServidorContraCheque" var="contracheque"
								widgetVar="tabelaServidorContraCheque"
								value="#{servidorBean.listaContraCheque}"
								emptyMessage="Nenhum contracheque encontrado." rows="10"
								paginator="true"
								paginatorAlwaysVisible="false"
								paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
								rowsPerPageTemplate="10,20,30,40,50,100,300,500"
								currentPageReportTemplate="página {currentPage} de {totalPages}"
								paginatorPosition="bottom">

								<p:column headerText="Competência" width="50%">
									<p:commandLink value="#{contracheque.mes}/#{contracheque.ano}"
										process="@this" immediate="true">
										<pe:selectDetailLevel
											listener="#{servidorBean.detalharContraCheque(contracheque)}" />
									</p:commandLink>
								</p:column>

								<p:column headerText="Folha" width="50%">
									<h:outputText value="#{contracheque.tipoFolha.descricao}" />
								</p:column>

							</p:dataTable>
<!-- 						</p:panel> -->

						<h:panelGrid columns="2">
							<p:commandButton value="Voltar" style="margin-top: 10px;"
								icon="ui-icon-arrowthick-1-w" process="@this" immediate="true">
								<pe:selectDetailLevel step="-1" />
							</p:commandButton>
						</h:panelGrid>
					</pe:masterDetailLevel>
					<pe:masterDetailLevel level="7">
						<f:facet name="label">
							<h:outputText value="Detalhar ContraCheque"
								rendered="#{servidorBean.contraCheque.mes eq null}" />
							<h:outputText
								value="Detalhar #{servidorBean.contraCheque.mes}/#{servidorBean.contraCheque.ano}"
								rendered="#{servidorBean.contraCheque.mes != null}" />
						</f:facet>
						<p:panel header="Informações do Servidor">
							<h:panelGrid columns="2" columnClasses="label, value">
								<h:outputText value="Competência: " styleClass="FontBold" />
								<h:outputText
									value="#{servidorBean.contraCheque.mes}/#{servidorBean.contraCheque.ano}" />

								<h:outputText value="Nome: " styleClass="FontBold" />
								<h:outputText
									value="#{servidorBean.contraCheque.beneficiario.cadastroUnico.listaPessoaFisica[0].nome}" />

								<h:outputText value="Situação do Beneficiário: "
									styleClass="FontBold" />
								<h:outputText
									value="#{servidorBean.contraCheque.situacaoBeneficiario.descricao}" />

								<h:outputText value="Cargo: " styleClass="FontBold" />
								<h:outputText value="#{servidorBean.contraCheque.cargo.nome}" />

								<h:outputText value="Referência: " styleClass="FontBold" />
								<h:outputText
									value="#{servidorBean.contraCheque.referenciaNivel}" />

								<h:outputText value="Tabela de Vencimentos: "
									styleClass="FontBold" />
								<h:outputText
									value="#{servidorBean.contraCheque.tabelaVencimento.nome}" />

								<h:outputText value="Unidade de Lotação: " styleClass="FontBold" />
								<h:outputText
									value="#{servidorBean.contraCheque.unidadeLotacao.nome}" />

								<h:outputText value="Folha: " styleClass="FontBold" />
								<h:outputText
									value="#{servidorBean.contraCheque.tipoFolha.descricao}" />

							</h:panelGrid>
<!-- 						</p:panel> -->
<p:spacer />
						<p:panel header="Contracheque">
							
							<p:dataTable
								value="#{servidorBean.contraCheque.listaVerbasContraCheque}"
								var="verbas" emptyMessage="Nenhuma verba encontrada.">
<!-- 								<f:facet name="header"> -->
<!-- 									<h:outputText value="Verbas" /> -->
<!-- 								</f:facet> -->
								<p:column headerText="Código" styleClass="TexAlCenter"
									style="width: 70px">
									<h:outputText value="#{verbas.verba.codigo}" />
								</p:column>

								<p:column headerText="Verba">
									<h:outputText value="#{verbas.verba.descricao}" />
								</p:column>

								<p:column headerText="Natureza" styleClass="TexAlCenter"
									style="width: 70px">
									<h:outputText value="#{verbas.verba.natureza.descricao}" />
								</p:column>

								<p:column headerText="Referência" styleClass="TexAlRight">
									<h:outputText value="#{verbas.referencia}" />
								</p:column>

								<p:column headerText="Valor" styleClass="TexAlRight">
									<h:outputText value="#{verbas.valor}">
										<f:convertNumber currencySymbol="R$" type="currency" />
									</h:outputText>
								</p:column>

							</p:dataTable>

							<p:spacer />
							<h:panelGrid columns="2" columnClasses="label, value">

								<h:outputText value="Total Bruto: " styleClass="FontBold" />
								<h:outputText
									value="#{servidorBean.contraCheque.totalVencimentos}">
									<f:convertNumber currencySymbol="R$" type="currency" />
								</h:outputText>

								<h:outputText value="Base Previdência Segurado: "
									styleClass="FontBold" />
								<h:outputText
									value="#{servidorBean.contraCheque.basePrevidenciariaSegurado}">
									<f:convertNumber currencySymbol="R$" type="currency" />
								</h:outputText>

								<h:outputText value="Base Previdência Patronal: "
									styleClass="FontBold" />
								<h:outputText
									value="#{servidorBean.contraCheque.basePrevidenciariaPatronal}">
									<f:convertNumber currencySymbol="R$" type="currency" />
								</h:outputText>

								<h:outputText value="Base FGTS: " styleClass="FontBold" />
								<h:outputText value="#{servidorBean.contraCheque.baseFgts}">
									<f:convertNumber currencySymbol="R$" type="currency" />
								</h:outputText>

								<h:outputText value="Base IRPF: " styleClass="FontBold" />
								<h:outputText value="#{servidorBean.contraCheque.baseIrpf}">
									<f:convertNumber currencySymbol="R$" type="currency" />
								</h:outputText>

								<h:outputText value="Total de Descontos: " styleClass="FontBold" />
								<h:outputText
									value="#{servidorBean.contraCheque.totalDescontos}">
									<f:convertNumber currencySymbol="R$" type="currency" />
								</h:outputText>

								<h:outputText value="Total de Líquido " styleClass="FontBold" />
								<h:outputText
									value="#{servidorBean.contraCheque.totalVencimentos - servidorBean.contraCheque.totalDescontos}">
									<f:convertNumber currencySymbol="R$" type="currency" />
								</h:outputText>

							</h:panelGrid>

						</p:panel>
												</p:panel>

						<h:panelGrid columns="2">
							<p:commandButton value="Voltar" style="margin-top: 10px;"
								icon="ui-icon-arrowthick-1-w" process="@this" immediate="true">
								<pe:selectDetailLevel step="-1" />
							</p:commandButton>
							<p:commandButton value="Início" style="margin-top: 10px;"
								icon="ui-icon-home" process="@this" immediate="true">
								<pe:selectDetailLevel step="-5" />
							</p:commandButton>
						</h:panelGrid>
					</pe:masterDetailLevel>

				</pe:masterDetail>
			</p:fieldset>
		</h:form>
	</ui:define>
</ui:composition>