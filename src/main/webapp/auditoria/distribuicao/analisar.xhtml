<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:f="http://xmlns.jcp.org/jsf/core"
	xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:p="http://primefaces.org/ui">

	<!-- 	<p:panel header="Informações para Análise"> -->
	<h:form id="formAnlsAcmlDespacho">
		<p:fieldset legend="Informações para análise">
			<div class="ui-g">
				<div class="ui-g-12">
					<h:panelGroup>
						<h:outputText value="Despacho:" styleClass="FontBold" />
						<p:inputTextarea id="despacho" rows="3" required="true"
							label="Despacho" value="#{acumulacaoBean.iniciarAnaliseDespacho}">
							<f:validateLength minimum="30" />
						</p:inputTextarea>
						<p:message for="despacho" display="text" />
					</h:panelGroup>
				</div>
			</div>

			<div class="EmptyBox5" />
			<p:commandButton update="@form :formDist:tblDist :formDist:messages"
				style="width:auto;" icon="fa fa-fw fa-check white" value="Confirmar"
				action="#{acumulacaoBean.iniciarAnalise()}" />
			<p:commandButton type="button" icon="fa fa-fw fa-close white"
				style="width:auto;" styleClass="RedButton" value="Cancelar"
				onclick="PF('dlgInitAnlAcmVar').hide()" />
		</p:fieldset>
	</h:form>

	<div class="EmptyBox10" />
	<h:form id="formTblAnlsAcml">
		<p:fieldset legend="Acumulações a serem analisadas">
			<p:dataTable var="analiseAcumulacao" id="tblAnlsAcml"
				tableStyle="table-layout: auto;" reflow="true"
				value="#{acumulacaoBean.acumulacoesSelecionadas}" rows="5"
				paginatorAlwaysVisible="false" paginator="true"
				emptyMessage="Nenhuma acumulação foi selecionada"
				paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
				currentPageReportTemplate="página {currentPage} de {totalPages}"
				paginatorPosition="bottom">

				<p:column headerText="Cód. Acumulação" width="8%"
					styleClass="TexAlCenter">
					<h:outputText value="#{analiseAcumulacao.id}" />
				</p:column>
				<p:column headerText="Referência" width="7%"
					styleClass="TexAlCenter">
					<h:outputText
						value="#{analiseAcumulacao.mes}/#{analiseAcumulacao.ano}" />
				</p:column>
				<p:column headerText="CPF" styleClass="TexAlCenter" width="15%">
					<h:outputText value="#{analiseAcumulacao.cpf}">
						<f:converter converterId="converter.CpfConverter" />
					</h:outputText>
				</p:column>
				<p:column headerText="Nome">
					<h:outputText value="#{analiseAcumulacao.nome}" />
				</p:column>
				<p:column headerText="Qtd. Vínculos" width="6%"
					styleClass="TexAlCenter">
					<h:outputText value="#{analiseAcumulacao.quantidadeVinculos}" />
				</p:column>

			</p:dataTable>
		</p:fieldset>
	</h:form>
	<!-- 	</p:panel> -->
</ui:composition>