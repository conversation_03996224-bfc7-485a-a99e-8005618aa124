
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:f="http://xmlns.jcp.org/jsf/core"
	xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:p="http://primefaces.org/ui">

	<h:form id="frmAnlHst">
	<p:dataTable tableStyle="table-layout: auto" 
		value="#{analiseHistoricoBean.listaAnaliseHistorico}" var="historico">
			<f:facet name="header">
				<h:outputText value="#{analiseHistoricoBean.acumulacao.cpf}">
					<f:converter converterId="converter.CpfConverter" />
				</h:outputText>
				<h:outputText
					value=" - #{analiseHistoricoBean.acumulacao.nome}" />
			</f:facet>
		<p:column headerText="Usuário" >
			<h:outputText value="#{historico.usuario.nome}" />
		</p:column>
		<p:column headerText="Procedimento Realizado">
			<h:outputText value="#{historico.procedimentoRealizado.descricao}" />
		</p:column>
		<p:column headerText="Data Movimentação">
			<h:outputText value="#{historico.data}">
				<f:converter converterId="converter.DateTimeConverter" />
			</h:outputText>
		</p:column>
		<p:column headerText="Despacho" width="50%">
			<h:outputText value="#{historico.despacho}" />
		</p:column>
	</p:dataTable>
	</h:form>
</ui:composition>
