<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:f="http://xmlns.jcp.org/jsf/core"
	xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:p="http://primefaces.org/ui">

<h:body>


	<h:form  acceptcharset="ISO-8859-1">

		<p:fieldset legend="Informações do resultado da análise">
			<!-- 			<p:fieldset id="concluirDados"> -->
			<div class="ui-g">
				<div class="ui-g-12 ui-md-6 ui-lg-6">
					<h:panelGroup>
						<h:outputText value="Situação da Análise: " styleClass="FontBold" />
						<p:selectOneMenu required="true" id="situacaoAnalise"
							requiredMessage="Selecione a Situação"
							value="#{concluirAnaliseBean.tipoSituacaoAnaliseSelecionado}"
							converter="tipoSituacaoAnaliseConverter">
							<f:selectItem itemLabel="Selecione a Situação da Análise"
								itemValue="#{null}" />
							<f:selectItems
								value="#{concluirAnaliseBean.listaTipoSituacaoAnalises}"
								var="situacao" itemLabel="#{situacao.descricao}"
								itemValue="#{situacao}" />
						</p:selectOneMenu>
						<p:message for="situacaoAnalise" display="text" />
					</h:panelGroup>
				</div>
				<div class="ui-g-12 ui-md-6 ui-lg-6">
					<h:panelGroup>
						<h:outputText value="Resultado Final: " styleClass="FontBold" />
						<p:selectOneMenu required="true" id="resultadoAnalise"
							autoWidth="false" requiredMessage="Selecione o Resultado"
							value="#{concluirAnaliseBean.resultadoAnalise.tipoResultadoAnalise}"
							converter="tipoResultadoAnaliseConverter">
							<f:selectItem itemLabel="Selecione o Resutado da Análise"
								itemValue="#{null}" />
							<f:selectItems
								value="#{concluirAnaliseBean.listaTipoResultadoAnalises}"
								var="resultado" itemLabel="#{resultado.descricao}"
								itemValue="#{resultado}" />
						</p:selectOneMenu>
						<p:message for="resultadoAnalise" display="text" />
					</h:panelGroup>
				</div>
			</div>
			<div class="ui-g">
				<div class="ui-g-12">
					<h:panelGroup>
						<h:outputText value="Despacho: " styleClass="FontBold" />
						<p:inputTextarea
							value="#{concluirAnaliseBean.resultadoAnalise.descricao}"
							id="despachoConclusao"
							validatorMessage="O despacho deve conter entre 30 e 255 caracteres."
							autoResize="true" rows="5">
							<f:validateLength minimum="30" />
							<f:validateLength maximum="255" />
						</p:inputTextarea>
						<p:message for="despachoConclusao" display="text" />
					</h:panelGroup>
				</div>
			</div>
			<div class="ui-g">
				<div class="ui-g-12">
					<h:panelGroup id="anexo">
						<h:outputText value="Relatório: " styleClass="FontBold" />
						<p:fileUpload label="Selecione o Arquivo" auto="true"
							fileUploadListener="#{concluirAnaliseBean.anexaRelatorio}" update="anexo"
							sizeLimit="5000000" allowTypes="/(\.|\/)(pdf)$/" />
						<h:outputText value="#{concluirAnaliseBean.arquivoNome}" />
					</h:panelGroup>

				</div>
			</div>

			<div class="ui-g">
				<div class="ui-g-12">
					<div class="EmptyBox10" />

					<p:commandButton value="Confirmar" style="width:auto;"
						icon="fa fa-fw fa-check white" update="@form"
						action="#{concluirAnaliseBean.concluirAnalises()}">
					</p:commandButton>



					<p:commandButton type="button" styleClass="RedButton"
						onclick="PF('dlgConclAnlVar').hide()" style="width: auto;"
						icon="fa fa-fw fa-close white" value="Cancelar">

					</p:commandButton>
				</div>
			</div>
		</p:fieldset>
		<p:ajaxStatus onstart="PF('statusAjaxDialog').show();"
			onsuccess="PF('statusAjaxDialog').hide();" />
	</h:form>
	<div class="EmptyBox10" />
	<h:form>
		<p:fieldset legend="Análises de acumulações a concluir">
			<p:dataTable id="tblAcumulacaoAnalise"
				value="#{concluirAnaliseBean.listaAnaliseAcumulacao}" var="aa"
				widgetVar="tblClcAnlVar" paginatorAlwaysVisible="false"
				paginator="true" rows="5"
				paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
				rowsPerPageTemplate="5,10,15" tableStyle="table-layout: auto"
				reflow="true"
				currentPageReportTemplate="página {currentPage} de {totalPages}">

				<p:column headerText="Cód. Acumulação" width="5%"
					styleClass="TexAlCenter">
					<h:outputText value="#{aa.acumulacaoId}" />
				</p:column>
				<p:column headerText="CPF" width="15%" styleClass="TexAlCenter">
					<h:outputText value="#{aa.acumulacaoCpf}">
						<f:converter converterId="converter.CpfConverter" />
					</h:outputText>
				</p:column>
				<p:column headerText="Nome">
					<h:outputText value="#{aa.acumulacaoNome}" />
				</p:column>
				<p:column headerText="Período" styleClass="TexAlCenter">
					<h:outputText value="#{aa.acumulacaoMes}/#{aa.acumulacaoAno}" />
				</p:column>

				<f:facet name="footer">
					<h:outputLabel
						value="Total de acumulações: #{concluirAnaliseBean.listaAnaliseAcumulacao.size()}" />
				</f:facet>

			</p:dataTable>
		</p:fieldset>
	</h:form>
</h:body>
</html>