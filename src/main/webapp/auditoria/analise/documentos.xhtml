<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:f="http://xmlns.jcp.org/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	xmlns:pe="http://primefaces.org/ui/extensions">
<h:head>


	<title>Documentos Para Auditoria</title>
	
	<meta name="viewport"
			content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" />

	<h:outputScript library="sentinel-layout" name="js/layout.js" />
	<h:outputScript name="locale-primefaces.js" library="js" />
	<h:outputScript name="css_browser_selector.js" library="js" />

	<h:outputStylesheet library="sentinel-layout"
		name="css/font-icon-layout.css" />
	<h:outputStylesheet library="sentinel-layout"
		name="css/sentinel-layout.css" />
	<h:outputStylesheet library="sentinel-layout"
		name="css/core-layout.css" />

	<h:outputStylesheet library="css" name="index.css" />

	<style type="text/css">
.ui-tree .ui-tree-container {
	white-space: normal;
}
</style>
</h:head>

<h:body>
	<h:form prependId="false">
		<h:panelGroup>

			<pe:layout style="width:100%; height:100%;">


				<pe:layoutPane position="west" size="320">


					<p:tree selectionMode="single" id="arvoreDocs"
						rendered="#{documentoAuditoriaBean.root != null}"
						selection="#{documentoAuditoriaBean.noSelecionado}"
						value="#{documentoAuditoriaBean.root}" var="documento">

						<p:ajax event="select"
							listener="#{documentoAuditoriaBean.onNodeSelect}"
							update="panelArquivo" />


						<p:treeNode>
							<h:outputText value="#{documento.nomeTipoDocumento}"
								styleClass="FontBold" />
						</p:treeNode>


						<p:treeNode type="document">
							<i class="icon-file-pdf"></i>
							<h:outputText
								value="#{documento.idNotificacao eq 0 ? documento.nomeDocumento : documento.nomeTipoDocumento}" />
						</p:treeNode>


					</p:tree>


				</pe:layoutPane>

				<pe:layoutPane position="center">
					<p:outputPanel id="panelArquivo" style="display:block; height:100%;">
						<embed src="#{documentoAuditoriaBean.enderecoDocumento()}"
							width="100%" height="97%" type="application/pdf" />
					</p:outputPanel>
					<!-- 					<h:panelGrid id="panelArquivo"  style="width:100%; height:100%;"> -->


					<!-- 					</h:panelGrid> -->
				</pe:layoutPane>


			</pe:layout>

		</h:panelGroup>
	</h:form>
</h:body>
</html>
