<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:f="http://xmlns.jcp.org/jsf/core"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:p="http://primefaces.org/ui"
	xmlns:pe="http://primefaces.org/ui/extensions"
	xmlns:fn="http://xmlns.jcp.org/jsp/jstl/functions"
	template="/resources/template/template.xhtml">

	<ui:define name="content">

		<!-- 		<p:panel header="Auditoria - Acumulações por Entidade"> -->
		<!-- 			<p:spacer /> -->
		<h:form prependId="false">
			<p:fieldset
				legend="Dados para a consulta por servidores acumulando cargos nas Entidades">

				<div class="ui-g">
					<div class="ui-g-12 ui-md-6 ui-lg-4">
						<h:outputText value="Competência: " styleClass="FontBold" />
						<p:selectOneMenu value="#{acumulacaoEntidadeBean.competencia}">
							<f:selectItem itemLabel="Todas" itemValue="#{0}" />
							<f:selectItems
								value="#{acumulacaoEntidadeBean.listaCompetencias}"
								var="competencia"
								itemLabel="#{competencia[2]}/#{competencia[0]}"
								itemValue="#{competencia[1]}/#{competencia[0]}" />

						</p:selectOneMenu>
					</div>
				</div>
				<div class="ui-g">
					<div class="ui-g-12 ui-md-6 ui-lg-4">
						<h:panelGroup>
							<h:outputText value="CPF: " styleClass="FontBold" />
							<p:inputText value="#{acumulacaoEntidadeBean.cpf}" />
						</h:panelGroup>
					</div>
				</div>
				<div class="ui-g">
					<div class="ui-g-12">
						<p:commandButton value="Pesquisar" style="width: auto;"
							icon="fa fa-fw fa-search white"
							actionListener="#{acumulacaoEntidadeBean.pesquisar()}"
							update="masterDetail" />

					</div>
				</div>
			</p:fieldset>

			<div class="EmptyBox10" />
			<p:fieldset legend="Resultados da consulta">
				<pe:masterDetail id="masterDetail"
					level="#{acumulacaoEntidadeBean.currentLevel}"
					showAllBreadcrumbItems="true">

					<f:facet name="header">
						<p:messages showDetail="false" showSummary="true" />
					</f:facet>



					<pe:masterDetailLevel id="detailInicio" level="1">
						<f:facet name="label">
							<h:outputText value="Início" />
						</f:facet>
						<p:dataTable id="tblEnt" var="entidade"
							widgetVar="tabelaEntidades" tableStyle="tabe"
							value="#{acumulacaoEntidadeBean.listaEntidades}"
							sortBy="#{entidade.nome}" sortOrder="ascending"
							emptyMessage="Nenhuma entidade encontrada." rows="20"
							paginatorAlwaysVisible="false" paginator="true"
							paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
							rowsPerPageTemplate="20,30,40,50,100,300,500, 1000"
							currentPageReportTemplate="página {currentPage} de {totalPages}"
							paginatorPosition="bottom">

							<p:column headerText="Entidade" id="filtroEntidade" 
								filterBy="#{entidade.nome}" filterMatchMode="contains"
								sortBy="#{entidade.nome}" filterStyle="width: 50% !important;" >
								
								<p:commandLink value="#{entidade.nome}">
									<pe:selectDetailLevel contextValue="#{entidade}"
										listener="#{acumulacaoEntidadeBean.detalharEntidade(entidade)}" />
								</p:commandLink>
							</p:column>
							<p:column width="10%" headerText="Quantidade"
								sortBy="#{entidade.quantidade}" styleClass="TexAlCenter">
								<h:outputText value="#{entidade.quantidade}" />
							</p:column>

							<f:facet name="footer">
								<h:outputLabel
									value="Total de entidades: #{acumulacaoEntidadeBean.listaEntidades.size()}" />
							</f:facet>
						</p:dataTable>
					</pe:masterDetailLevel>
					<pe:masterDetailLevel id="detailAcumulacoes" level="2"
						contextVar="entidade">
						<f:facet name="label">
							<h:outputText value="Entidade" />
						</f:facet>
						<h:panelGrid columns="2">
							<p:commandButton value="Voltar" icon="ui-icon-arrowthick-1-w"
								process="@this" immediate="true">
								<pe:selectDetailLevel step="-1" />
							</p:commandButton>
						</h:panelGrid>
						<div class="EmptyBox5" />
						<p:dataTable id="tabelaVinculos" var="vinculo"
							widgetVar="tabelaVinculos"
							reflow="true"
							value="#{acumulacaoEntidadeBean.listaVinculos}"
							emptyMessage="Nenhuma acumulação encontrada." rows="20"
							paginatorAlwaysVisible="false" paginator="true"
							tableStyle="table-layout: auto"
							paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
							rowsPerPageTemplate="20,30,40,50,100,300,500, 1000"
							currentPageReportTemplate="página {currentPage} de {totalPages}"
							paginatorPosition="bottom">

							<f:facet name="header">
						        #{acumulacaoEntidadeBean.listaVinculos[0].entidade.nome}
						    </f:facet>

							<p:column headerText="Matrícula" styleClass="TexAlCenter">
								<h:outputText value="#{vinculo.beneficiario.matricula}" />
							</p:column>

							<p:column headerText="CPF"  styleClass="TexAlCenter">
								<h:outputText value="#{vinculo.cpf}">
									<f:converter converterId="converter.CpfConverter" />
								</h:outputText>
							</p:column>

							<p:column headerText="Nome">
								<h:outputText value="#{vinculo.acumulacao.nome}" />
							</p:column>

							<p:column headerText="Município" styleClass="TexAlCenter">
								<h:outputText value="#{vinculo.municipio.nome}" />
							</p:column>


							<p:column headerText="Ano" styleClass="TexAlCenter">
								<h:outputText value="#{vinculo.ano}" />
							</p:column>

							<p:column headerText="Mês" styleClass="TexAlCenter">
								<h:outputText value="#{vinculo.mes}" />
							</p:column>

							<p:column headerText="Município" styleClass="TexAlCenter">
								<h:outputText value="#{vinculo.municipio.nome}" />
							</p:column>
							<p:column headerText="Cargo" styleClass="TexAlCenter">
								<h:outputText value="#{vinculo.cargo.nome}" />
							</p:column>

							<p:column headerText="Cargo Referência" width="10%"
								styleClass="TexAlCenter">
								<h:outputText value="#{vinculo.cargo.referenciaSub.descricao}" />
							</p:column>
							<p:column headerText="Tipo de Cargo" 
								styleClass="TexAlCenter">
								<h:outputText value="#{vinculo.cargo.tipo.descricao}" />
							</p:column>

							<p:column headerText="Carga Horária" styleClass="TexAlCenter">
								<h:outputText value="#{vinculo.cargo.cargaHorariaMensal}" />
							</p:column>

							<f:facet name="footer">
								<h:outputLabel
									value="Total de acumulações: #{acumulacaoEntidadeBean.listaVinculos.size()}" />
							</f:facet>
						</p:dataTable>
						<div class="EmptyBox10" />
						<h:panelGrid columns="2">
							<p:commandButton value="Voltar" icon="ui-icon-arrowthick-1-w"
								process="@this" immediate="true">
								<pe:selectDetailLevel step="-1" />
							</p:commandButton>
						</h:panelGrid>
					</pe:masterDetailLevel>

				</pe:masterDetail>
			</p:fieldset>

		</h:form>
		<!-- 		</p:panel> -->
	</ui:define>
</ui:composition>