<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.3.1.final using JasperReports Library version 6.3.1  -->
<!-- 2017-08-18T13:39:52 -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="subRelatorioAcumulacao1Semestre" pageWidth="842" pageHeight="595" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="800" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isFloatColumnFooter="true" uuid="4ecf7ae5-1156-4d77-9b23-17820bd4036f">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="sicap"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="175"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="817"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w1" value="551"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w2" value="435"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<parameter name="cpf" class="java.lang.String"/>
	<parameter name="ano" class="java.lang.Integer"/>
	<parameter name="idEntidadeCjur" class="java.lang.Integer"/>
	<parameter name="idBeneficiario" class="java.lang.Long"/>
	<queryString>
		<![CDATA[SELECT 
	   r.codigo,
	   r.descricao,
       r.ano,
       r.folha,
       MAX(r.janeiro) AS janeiro,
       MAX(r.fevereiro) AS fevereiro,
       MAX(r.marco) AS marco,
       MAX(r.abril) AS abril,
       MAX(r.maio) AS maio,
       MAX(r.junho) AS junho
FROM (SELECT
	    v.codigo, 
		vcc.idContraCheque, 
        v.descricao, 
        cc.ano, 
        CASE WHEN cc.mes = 1 THEN vcc.valor ELSE 0 END AS janeiro, 
        CASE WHEN cc.mes = 2 THEN vcc.valor ELSE 0 END AS fevereiro, 
        CASE WHEN cc.mes = 3 THEN vcc.valor ELSE 0 END AS marco, 
        CASE WHEN cc.mes = 4 THEN vcc.valor ELSE 0 END AS abril, 
        CASE WHEN cc.mes = 5 THEN vcc.valor ELSE 0 END AS maio, 
        CASE WHEN cc.mes = 6 THEN vcc.valor ELSE 0 END AS junho, 
        CASE WHEN cc.mes = 7 THEN vcc.valor ELSE 0 END AS julho, 
        CASE WHEN cc.mes = 8 THEN vcc.valor ELSE 0 END AS agosto, 
        CASE WHEN cc.mes = 9 THEN vcc.valor ELSE 0 END AS setembro, 
        CASE WHEN cc.mes = 10 THEN vcc.valor ELSE 0 END AS outubro, 
        CASE WHEN cc.mes = 11 THEN vcc.valor ELSE 0 END AS novembro, 
        CASE WHEN cc.mes = 12 THEN vcc.valor ELSE 0 END AS dezembro, 
        CASE WHEN (cc.mes = 12) AND (tf.descricao like '%13%' OR tf.descricao like '%decimo%' OR tf.descricao like '%terceiro%') THEN vcc.valor ELSE 0 END AS decimo, 
        tf.descricao as folha 
	 FROM ContraCheque cc 
     INNER JOIN VerbasContraCheque vcc ON vcc.idContraCheque = cc.id 
     INNER JOIN TipoFolha tf ON vcc.idTipoFolha = tf.id 
     INNER JOIN Verba v ON vcc.idVerba = v.id 
     INNER JOIN Beneficiario b ON cc.idBeneficiario = b.id 
     INNER JOIN CadastroUnico cadu ON b.idCadastroUnico = cadu.id 
     WHERE cc.ano = $P{ano}  AND cadu.cpf = $P{cpf}  AND cc.idEntidadeCjur = $P{idEntidadeCjur}  AND cc.idBeneficiario = $P{idBeneficiario} AND v.natureza = 'C' ) r
GROUP BY 
	r.codigo,
	r.descricao,
    r.ano,
    r.folha
ORDER BY 
	r.codigo asc]]>
	</queryString>
	<field name="codigo" class="java.lang.String"/>
	<field name="descricao" class="java.lang.String"/>
	<field name="ano" class="java.lang.Integer"/>
	<field name="folha" class="java.lang.String"/>
	<field name="janeiro" class="java.math.BigDecimal"/>
	<field name="fevereiro" class="java.math.BigDecimal"/>
	<field name="marco" class="java.math.BigDecimal"/>
	<field name="abril" class="java.math.BigDecimal"/>
	<field name="maio" class="java.math.BigDecimal"/>
	<field name="junho" class="java.math.BigDecimal"/>
	<columnHeader>
		<band height="12" splitType="Prevent">
			<staticText>
				<reportElement mode="Opaque" x="0" y="1" width="37" height="11" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" backcolor="#C0C0C0" uuid="58bab0b3-16a1-40d2-adf3-009293388799">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Código]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="37" y="1" width="282" height="11" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" backcolor="#C0C0C0" uuid="31d65a82-1c50-4666-82dd-41e3d470edf3"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Verba]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="319" y="1" width="123" height="11" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" backcolor="#C0C0C0" uuid="ea7faa75-4dec-418b-bf1c-17278318dbd5"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Tipo Folha]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="442" y="1" width="60" height="11" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" backcolor="#C0C0C0" uuid="43dcb40d-b123-4e4b-ac05-3fb28f65048c"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Janeiro]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="502" y="1" width="60" height="11" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" backcolor="#C0C0C0" uuid="ae8a89bb-4c22-4a9d-9a86-0c861ad66227"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Fevereiro]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="562" y="1" width="60" height="11" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" backcolor="#C0C0C0" uuid="8cca13c8-7a08-4a40-92f6-20d92b59fd5c"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Março]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="622" y="1" width="60" height="11" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" backcolor="#C0C0C0" uuid="78c13cc0-5314-4f1f-be08-22adeb704245">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Abril]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="682" y="1" width="60" height="11" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" backcolor="#C0C0C0" uuid="bfe4e3bd-3199-4f64-8121-2d0db7dfd8cf"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Maio]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="742" y="1" width="60" height="11" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" backcolor="#C0C0C0" uuid="c998f6db-f047-43fd-b36d-5b85c59cfffe">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Junho]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="11" splitType="Stretch">
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="0" width="37" height="11" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" uuid="8128eff7-5b39-45db-8ea9-a6d31368595c"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{codigo}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="37" y="0" width="282" height="11" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" uuid="b560ccf4-f984-4068-8817-87a7ceeb89bf"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
					<paragraph lineSpacingSize="0.0" firstLineIndent="0" leftIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{descricao}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="319" y="0" width="123" height="11" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" uuid="a348eeb2-1c46-4827-a92f-e592e58e9b49"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
					<paragraph lineSpacing="Single" firstLineIndent="0" leftIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{folha}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement x="442" y="0" width="60" height="11" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" uuid="f81a0adf-c881-42c2-a62f-cabc9b27c1b5"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
					<paragraph firstLineIndent="0" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{janeiro}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement x="502" y="0" width="60" height="11" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" uuid="5aa4fb90-4714-4ea3-87fa-bdea73e67fee">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
					<paragraph firstLineIndent="0" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{fevereiro}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement x="562" y="0" width="60" height="11" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" uuid="78273e44-2419-4a2e-b7b1-4cc670a54611">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
					<paragraph firstLineIndent="0" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{marco}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement x="622" y="0" width="60" height="11" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" uuid="a37a816a-4360-4d5e-8fde-6c968f1ce9b0">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
					<paragraph firstLineIndent="0" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{abril}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement x="682" y="0" width="60" height="11" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" uuid="8f29e3e2-5acf-405c-bf26-1d54f26fb24d">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
					<paragraph firstLineIndent="0" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{maio}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement x="742" y="0" width="60" height="11" isRemoveLineWhenBlank="true" isPrintInFirstWholeBand="true" uuid="f7457ff4-251c-49f4-b5a5-7931237bec06">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
					<paragraph firstLineIndent="0" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{junho}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
