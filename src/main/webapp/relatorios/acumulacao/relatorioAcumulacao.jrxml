<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.3.1.final using JasperReports Library version 6.3.1  -->
<!-- 2017-08-18T09:32:56 -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorioAcumulacao" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" isFloatColumnFooter="true" uuid="1e3c0046-3cde-4689-9ec6-aa60bac24108">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="sicap"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<parameter name="cpf" class="java.lang.String"/>
	<parameter name="usuario" class="java.lang.String"/>
	<parameter name="exercicio" class="java.lang.Integer"/>
	<parameter name="REPORT_IMAGE_DIR" class="java.lang.String" isForPrompting="false">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["C:\\Users\\<USER>\\JaspersoftWorkspace\\sicapAnalise\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["C:\\Users\\<USER>\\JaspersoftWorkspace\\sicapAnalise\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="mes" class="java.lang.Integer"/>
	<parameter name="competencia" class="java.lang.String"/>
	<queryString>
		<![CDATA[SELECT auditoria."vw_consultaPessoaPorCPF".cpf,
	auditoria."vw_consultaPessoaPorCPF".nome,
	auditoria."vw_consultaPessoaPorCPF"."dataNascimento",
	auditoria."vw_consultaPessoaPorCPF"."nomeMae",
	auditoria."vw_consultaPessoaPorCPF".sexo,
	auditoria."vw_consultaPessoaPorCPF"."cpfMask"
FROM auditoria."vw_consultaPessoaPorCPF"
WHERE 
	 auditoria."vw_consultaPessoaPorCPF".cpf = $P{cpf}]]>
	</queryString>
	<field name="cpf" class="java.lang.String"/>
	<field name="nome" class="java.lang.String"/>
	<field name="dataNascimento" class="java.util.Date"/>
	<field name="nomeMae" class="java.lang.String"/>
	<field name="sexo" class="java.lang.String"/>
	<field name="cpfMask" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band height="148" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<image hAlign="Center" vAlign="Middle">
				<reportElement x="0" y="0" width="60" height="60" uuid="19d8c8a6-5dab-4b4f-b91e-6171608defe8">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<imageExpression><![CDATA[$P{REPORT_IMAGE_DIR}+"logotce.png"]]></imageExpression>
			</image>
			<image hAlign="Center" vAlign="Middle">
				<reportElement x="722" y="1" width="60" height="60" uuid="3610c532-d117-4353-a39f-4b17cb6df684">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<imageExpression><![CDATA[$P{REPORT_IMAGE_DIR}+"brasao.png"]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="0" y="1" width="802" height="28" uuid="8259e93a-76ce-4765-9138-dd3c1cb6c3c2">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="18" isBold="true"/>
				</textElement>
				<text><![CDATA[Tribunal de Contas do Estado do Acre]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="30" width="802" height="28" uuid="8f4f5dde-2c07-4cb8-a86c-41534e5e1c8b">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="13" isBold="true"/>
				</textElement>
				<text><![CDATA[SICAP - Sistema de Controle de Atos de Pessoal]]></text>
			</staticText>
			<rectangle radius="0">
				<reportElement x="0" y="66" width="802" height="1" uuid="fd2140f1-632e-403b-8721-bcb0b235688d">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="0" y="70" width="802" height="13" uuid="48f11dfd-0aa6-46d3-9f1c-dee3fbcefb47">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isItalic="true"/>
				</textElement>
				<text><![CDATA[Missão: Exercer o controle externo, orientando e fiscalizando a gestão pública, e incentivar a sociedade ao exercício do controle social.]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="0" y="90" width="802" height="15" backcolor="#C0C0C0" uuid="15902bb4-e40f-454e-b890-340daf3d43a7">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[RELATÓRIO DE ACUMULAÇÕES]]></text>
			</staticText>
			<frame>
				<reportElement x="0" y="110" width="802" height="31" uuid="b21bd929-20e2-4b0d-b474-477381a55f9c">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField isBlankWhenNull="true">
					<reportElement x="637" y="-39" width="163" height="15" uuid="638689ea-b54a-4af9-9fd3-5d74247b9a9e">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="11" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Competência: " + $P{competencia}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="1" y="2" width="54" height="12" uuid="7be70824-7c24-4b70-aea6-5e7f95c52e7e">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[Nome:]]></text>
				</staticText>
				<textField isBlankWhenNull="true">
					<reportElement x="56" y="2" width="320" height="12" uuid="980721f7-acb4-4845-b81a-cfc9db37b36e">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="10"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{nome}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="401" y="2" width="28" height="12" uuid="0d97efd0-022a-4cb2-aba0-2ca10e0f76ca">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[CPF:]]></text>
				</staticText>
				<textField isBlankWhenNull="true">
					<reportElement x="430" y="2" width="122" height="12" uuid="0c104566-dd74-4a8a-bafc-c43a7f67b78b">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="10"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{cpfMask}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="599" y="2" width="100" height="12" uuid="9764fb56-b47b-40ea-a2d1-09fa3285eb14">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[Data de Nascimento:]]></text>
				</staticText>
				<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
					<reportElement x="700" y="2" width="100" height="12" uuid="4f9a5f33-3866-477a-8710-5ac1dfd209b5">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="10"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{dataNascimento}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="1" y="16" width="54" height="12" uuid="bbe62c38-dacf-424e-b3b9-5d1e2dd1fc14">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[Nome Mãe:]]></text>
				</staticText>
				<textField isBlankWhenNull="true">
					<reportElement x="56" y="16" width="320" height="12" uuid="93c6fd28-90c8-446b-b423-5bfdad26f409">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="10"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{nomeMae}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="401" y="16" width="28" height="12" uuid="92c7d89d-596c-4804-b036-8a51e5536002">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[Sexo:]]></text>
				</staticText>
				<textField isBlankWhenNull="true">
					<reportElement x="430" y="16" width="122" height="12" uuid="e989b3ee-dc0c-43a4-aafd-28ece454a870">
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="10"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{sexo}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</pageHeader>
	<detail>
		<band height="25">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<subreport>
				<reportElement key="" x="0" y="0" width="802" height="23" isRemoveLineWhenBlank="true" uuid="e5f6844a-5a9b-4ce5-a7aa-b74256fe0e3f">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<subreportParameter name="cpf">
					<subreportParameterExpression><![CDATA[$P{cpf}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="ano">
					<subreportParameterExpression><![CDATA[$P{exercicio}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="mes">
					<subreportParameterExpression><![CDATA[$P{mes}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "subRelatorioAcumulacaoVinculos.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
	<pageFooter>
		<band height="37" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<staticText>
				<reportElement x="1" y="3" width="580" height="12" uuid="2f1d6b86-5b94-4b95-89e2-2308634a00fe">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<text><![CDATA[Sistema de Controle de Atos de Pessoal do Tribunal de Contas do Estado do Acre. Resolução/TCE-AC n° 102/2016.]]></text>
			</staticText>
			<rectangle radius="0">
				<reportElement x="1" y="2" width="802" height="1" uuid="9c340cd3-b7e6-45f9-bd66-4f5500c23130">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="1" y="14" width="40" height="12" uuid="46dfb36b-15bf-4f36-b9a6-90417abbce74">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<text><![CDATA[Usuário:]]></text>
			</staticText>
			<textField>
				<reportElement x="42" y="14" width="338" height="12" uuid="04474135-25ef-46e2-8eb1-bf460a607b06">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{usuario}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="1" y="25" width="40" height="12" uuid="d6311f84-f90a-4fd6-a7da-0b04fe82cbd6">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<text><![CDATA[Emissão:]]></text>
			</staticText>
			<textField pattern="dd/MM/yyyy hh:mm:ss">
				<reportElement x="42" y="25" width="100" height="12" uuid="c8b01ac1-e40a-43fc-951f-b12d4f7e35be">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="692" y="25" width="62" height="12" uuid="bc8a39a4-b2a9-4b1e-a426-cc4280917442">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA["Página " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report" isBlankWhenNull="true">
				<reportElement x="756" y="25" width="46" height="12" uuid="2e55bc66-6b73-4712-8eec-9adaf852d8f7">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[" de " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
</jasperReport>
