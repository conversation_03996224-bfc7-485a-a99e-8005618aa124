<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.3.1.final using JasperReports Library version 6.3.1  -->
<!-- 2017-04-05T11:35:00 -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="subRelatorioVinculosRendimentos" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="800" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="b630daf3-e0eb-4f6b-9d25-8cbc3a3c6ed5">
	<property name="com.jaspersoft.studio.data.sql.tables" value="YXVkaXRvcmlhLnZ3X2ZpY2hhX2FudWFsX3JlbmRpbWVudG9zX3RvdGFsX2JydXRvICwxNSwxNSwz&#xA;ODhhYjlmZS01NDg2LTQwZTMtOWQ0Zi0wNDg0ZmZhOTEyMjA7"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="341"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="652"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="sicap"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w1" value="421"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w2" value="565"/>
	<parameter name="cpfBeneficiario" class="java.lang.String"/>
	<parameter name="ano" class="java.lang.Integer"/>
	<queryString language="SQL">
		<![CDATA[SELECT auditoria.vw_ficha_anual_rendimentos_total_bruto."idEntidadeCjur",
	auditoria.vw_ficha_anual_rendimentos_total_bruto.entidade,
	auditoria.vw_ficha_anual_rendimentos_total_bruto.cpf,
	auditoria.vw_ficha_anual_rendimentos_total_bruto.matricula,
	auditoria.vw_ficha_anual_rendimentos_total_bruto."nomeCargo",
	auditoria.vw_ficha_anual_rendimentos_total_bruto."TipoDependencia",
	auditoria.vw_ficha_anual_rendimentos_total_bruto."InicioDaPensao",
	auditoria.vw_ficha_anual_rendimentos_total_bruto."TipoDEPensao",
	auditoria.vw_ficha_anual_rendimentos_total_bruto."cargaHorariaMensal",
	auditoria.vw_ficha_anual_rendimentos_total_bruto."cargaHorariaSemanal",
	auditoria.vw_ficha_anual_rendimentos_total_bruto."TipoCargo",
	auditoria.vw_ficha_anual_rendimentos_total_bruto."escolaridadeDoCargo",
	auditoria.vw_ficha_anual_rendimentos_total_bruto."dataAdmissao",
	auditoria.vw_ficha_anual_rendimentos_total_bruto."tipoVinculo",
	auditoria.vw_ficha_anual_rendimentos_total_bruto."regimePrevidenciario",
	auditoria.vw_ficha_anual_rendimentos_total_bruto."tipoServidor",
	auditoria.vw_ficha_anual_rendimentos_total_bruto."dataUltimaOcorenciaFuncional",
	auditoria.vw_ficha_anual_rendimentos_total_bruto."SituacaoFuncional",
	auditoria.vw_ficha_anual_rendimentos_total_bruto.ano,
	auditoria.vw_ficha_anual_rendimentos_total_bruto."JANEIRO",
	auditoria.vw_ficha_anual_rendimentos_total_bruto."FEVEREIRO",
	auditoria.vw_ficha_anual_rendimentos_total_bruto."MARÇO",
	auditoria.vw_ficha_anual_rendimentos_total_bruto."ABRIL",
	auditoria.vw_ficha_anual_rendimentos_total_bruto."MAIO",
	auditoria.vw_ficha_anual_rendimentos_total_bruto."JUNHO",
	auditoria.vw_ficha_anual_rendimentos_total_bruto."JULHO",
	auditoria.vw_ficha_anual_rendimentos_total_bruto."AGOSTO",
	auditoria.vw_ficha_anual_rendimentos_total_bruto."SETEMBRO",
	auditoria.vw_ficha_anual_rendimentos_total_bruto."OUTUBRO",
	auditoria.vw_ficha_anual_rendimentos_total_bruto."NOVEMBRO",
	auditoria.vw_ficha_anual_rendimentos_total_bruto."DEZEMBRO",
	auditoria.vw_ficha_anual_rendimentos_total_bruto."SituacaoBeneficiario",
	auditoria.vw_ficha_anual_rendimentos_total_bruto."Total",
	auditoria.vw_ficha_anual_rendimentos_total_bruto."DECIMO"
FROM auditoria.vw_ficha_anual_rendimentos_total_bruto
WHERE 
	 auditoria.vw_ficha_anual_rendimentos_total_bruto.cpf = $P{cpfBeneficiario} 
	 AND ( 
	 auditoria.vw_ficha_anual_rendimentos_total_bruto.ano = $P{ano} 
	 OR auditoria.vw_ficha_anual_rendimentos_total_bruto.ano IS NULL )]]>
	</queryString>
	<field name="idEntidadeCjur" class="java.lang.Integer"/>
	<field name="entidade" class="java.lang.String"/>
	<field name="cpf" class="java.lang.String"/>
	<field name="matricula" class="java.lang.Integer"/>
	<field name="nomeCargo" class="java.lang.String"/>
	<field name="TipoDependencia" class="java.lang.String"/>
	<field name="InicioDaPensao" class="java.lang.String"/>
	<field name="TipoDEPensao" class="java.lang.String"/>
	<field name="cargaHorariaMensal" class="java.lang.Integer"/>
	<field name="cargaHorariaSemanal" class="java.lang.Integer"/>
	<field name="TipoCargo" class="java.lang.String"/>
	<field name="escolaridadeDoCargo" class="java.lang.String"/>
	<field name="dataAdmissao" class="java.lang.String"/>
	<field name="tipoVinculo" class="java.lang.String"/>
	<field name="regimePrevidenciario" class="java.lang.String"/>
	<field name="tipoServidor" class="java.lang.String"/>
	<field name="dataUltimaOcorenciaFuncional" class="java.lang.String"/>
	<field name="SituacaoFuncional" class="java.lang.String"/>
	<field name="ano" class="java.lang.Integer"/>
	<field name="JANEIRO" class="java.math.BigDecimal"/>
	<field name="FEVEREIRO" class="java.math.BigDecimal"/>
	<field name="MARÇO" class="java.math.BigDecimal"/>
	<field name="ABRIL" class="java.math.BigDecimal"/>
	<field name="MAIO" class="java.math.BigDecimal"/>
	<field name="JUNHO" class="java.math.BigDecimal"/>
	<field name="JULHO" class="java.math.BigDecimal"/>
	<field name="AGOSTO" class="java.math.BigDecimal"/>
	<field name="SETEMBRO" class="java.math.BigDecimal"/>
	<field name="OUTUBRO" class="java.math.BigDecimal"/>
	<field name="NOVEMBRO" class="java.math.BigDecimal"/>
	<field name="DEZEMBRO" class="java.math.BigDecimal"/>
	<field name="DECIMO" class="java.math.BigDecimal"/>
	<field name="Total" class="java.math.BigDecimal"/>
	<field name="SituacaoBeneficiario" class="java.lang.String"/>
	<detail>
		<band height="150" splitType="Stretch">
			<frame>
				<reportElement x="1" y="1" width="800" height="140" uuid="791905f2-e408-4cb4-81e4-98ab6abc3b21">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField pattern="#,##0.00;#,##0.00" isBlankWhenNull="true">
					<reportElement x="684" y="115" width="57" height="15" uuid="ca4a9196-1d09-4284-a56e-e39b158c4c17">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<box rightPadding="2">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DECIMO}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="190" y="28" width="36" height="18" uuid="b311abcc-c9be-4f8a-91f6-7515a5b71dd2">
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="11" isBold="true"/>
					</textElement>
					<text><![CDATA[Cargo:]]></text>
				</staticText>
				<textField isBlankWhenNull="true">
					<reportElement positionType="Float" x="226" y="28" width="570" height="18" uuid="10c06a47-0ac2-45fa-ad82-67946ebf55b8">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{nomeCargo}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="3" y="51" width="57" height="18" uuid="a996298a-9e61-4abf-a521-dc8837ebebf5">
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="11" isBold="true"/>
					</textElement>
					<text><![CDATA[Admissão:]]></text>
				</staticText>
			</frame>
			<textField isBlankWhenNull="true">
				<reportElement mode="Opaque" x="64" y="4" width="733" height="18" backcolor="#C0C0C0" uuid="e5a9b1ac-2d00-43a2-b52f-9ea2003061f4">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{entidade}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="55" y="28" width="124" height="18" uuid="a26e827e-585d-42a9-9a6b-18bd0303a93c"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{matricula}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="3" y="28" width="52" height="18" uuid="e15c14e2-86bc-4bc8-b7b5-f500dfad35ce">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Matrícula:]]></text>
			</staticText>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement positionType="Float" x="61" y="52" width="118" height="18" uuid="6d0a473e-ba05-41ba-b872-f353db4110f4">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dataAdmissao}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="275" y="52" width="199" height="18" uuid="3ce49e2f-3bb8-4a22-a151-252edfdc7363">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tipoVinculo}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="190" y="52" width="85" height="18" uuid="0d2df472-e350-4db8-b0a2-222cf1169c0e">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Tipo do vínculo:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="753" y="52" width="46" height="18" uuid="923599b3-3fc9-45eb-b8cd-9bcc6715de5e">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cargaHorariaMensal}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="636" y="52" width="117" height="18" uuid="8dd3f22f-b8b1-433f-9668-44eea24bcd1c">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Carga horária mensal:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="753" y="76" width="46" height="18" uuid="0d8df2a2-0594-44c4-bc94-85a576805f03">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cargaHorariaSemanal}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="630" y="76" width="123" height="18" uuid="fb9c5685-a215-4174-9ae5-8aab11bf1313">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Carga horária semanal:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="140" y="76" width="270" height="18" uuid="b743706c-4d76-4f8e-9a5a-d5b3b0418739"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SituacaoFuncional}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="3" y="76" width="137" height="18" uuid="cdc1cfea-1285-4331-afd3-59d31166d439">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Última situação funcional:]]></text>
			</staticText>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="537" y="77" width="90" height="18" uuid="269c110f-bb38-44e3-ac69-58f33a50d239">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dataUltimaOcorenciaFuncional}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="413" y="76" width="124" height="18" uuid="3c225f91-12dc-4028-ba20-d5048ad13493"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Data da movimentação:]]></text>
			</staticText>
			<textField pattern="#,##0.00;#,##0.00" isBlankWhenNull="true">
				<reportElement x="1" y="116" width="57" height="15" uuid="55db4ac8-00a3-4b91-a3e3-fcc1b4c41671">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="5">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{JANEIRO}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Opaque" x="1" y="101" width="57" height="15" backcolor="#C0C0C0" uuid="60136ec6-7748-4dba-80ad-0cb813bc9514">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Janeiro]]></text>
			</staticText>
			<textField pattern="#,##0.00;#,##0.00" isBlankWhenNull="true">
				<reportElement x="58" y="116" width="57" height="15" uuid="03a62f67-0566-4e6c-aa25-0aecd9f2205e">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box rightPadding="2">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{FEVEREIRO}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Opaque" x="58" y="101" width="57" height="15" backcolor="#C0C0C0" uuid="b2fa23c3-60dc-426a-8c5f-30f7fb761a77">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Fevereiro]]></text>
			</staticText>
			<textField pattern="#,##0.00;#,##0.00" isBlankWhenNull="true">
				<reportElement x="115" y="116" width="57" height="15" uuid="404feccb-dd7a-4d4b-85e2-5062b9b6cf4e">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box rightPadding="2">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MARÇO}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Opaque" x="115" y="101" width="57" height="15" backcolor="#C0C0C0" uuid="6e43de97-82a5-4b6e-9d29-448b6e8b44f5">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Março]]></text>
			</staticText>
			<textField pattern="#,##0.00;#,##0.00" isBlankWhenNull="true">
				<reportElement x="172" y="116" width="57" height="15" uuid="6431f9a2-2703-4912-bba6-8e348a15cbfd">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box rightPadding="2">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ABRIL}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Opaque" x="172" y="101" width="57" height="15" backcolor="#C0C0C0" uuid="44c2540e-1b7a-4278-8c81-1aedccd5d5ff">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Abril]]></text>
			</staticText>
			<textField pattern="#,##0.00;#,##0.00" isBlankWhenNull="true">
				<reportElement x="229" y="116" width="57" height="15" uuid="c7769480-75a2-4f64-8c1f-3fa059ca0f04">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box rightPadding="2">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MAIO}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Opaque" x="229" y="101" width="57" height="15" backcolor="#C0C0C0" uuid="256a0c01-48b4-4322-aa2b-41b1010e8fd3">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Maio]]></text>
			</staticText>
			<textField pattern="#,##0.00;#,##0.00" isBlankWhenNull="true">
				<reportElement x="286" y="116" width="57" height="15" uuid="4210d545-b142-4fbd-99b2-b4b16d110c53">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box rightPadding="2">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{JUNHO}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Opaque" x="286" y="101" width="57" height="15" backcolor="#C0C0C0" uuid="a603d922-a6ed-4361-ab75-17a3a4d67573">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box rightPadding="0">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Junho]]></text>
			</staticText>
			<textField pattern="#,##0.00;#,##0.00" isBlankWhenNull="true">
				<reportElement x="343" y="116" width="57" height="15" uuid="2b90a4f9-**************-d3c2c0f77212">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box rightPadding="2">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{JULHO}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Opaque" x="343" y="101" width="57" height="15" backcolor="#C0C0C0" uuid="0799a183-88ba-4e19-8873-c82fe8274ea4">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box rightPadding="0">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Julho]]></text>
			</staticText>
			<textField pattern="#,##0.00;#,##0.00" isBlankWhenNull="true">
				<reportElement x="400" y="116" width="57" height="15" uuid="136ef411-f40d-4b56-beac-e6c4537b786b">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box rightPadding="2">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{AGOSTO}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Opaque" x="400" y="101" width="57" height="15" backcolor="#C0C0C0" uuid="0fa3cd0d-4b34-499e-9bac-0f1409485a26">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Agosto]]></text>
			</staticText>
			<textField pattern="#,##0.00;#,##0.00" isBlankWhenNull="true">
				<reportElement x="457" y="116" width="57" height="15" uuid="d6112647-98c2-4f72-a3c4-4427b1aa2fef">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box rightPadding="2">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SETEMBRO}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Opaque" x="457" y="101" width="57" height="15" backcolor="#C0C0C0" uuid="6076b36b-9e35-43e1-ac15-68249955c022">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Setembro]]></text>
			</staticText>
			<textField pattern="#,##0.00;#,##0.00" isBlankWhenNull="true">
				<reportElement x="514" y="116" width="57" height="15" uuid="67347479-8b94-4e6d-a2ee-cb4d4cb49122">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box rightPadding="2">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{OUTUBRO}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Opaque" x="514" y="101" width="57" height="15" backcolor="#C0C0C0" uuid="0306bdf9-fcf9-40b2-a992-8c9ebedb7909">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Outubro]]></text>
			</staticText>
			<textField pattern="#,##0.00;#,##0.00" isBlankWhenNull="true">
				<reportElement x="571" y="116" width="57" height="15" uuid="6f299277-d5a8-4fb8-a834-e74b4be8cb33">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box rightPadding="2">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NOVEMBRO}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Opaque" x="571" y="101" width="57" height="15" backcolor="#C0C0C0" uuid="5623a4c8-b982-4ef9-9b1f-dee0907b578e">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Novembro]]></text>
			</staticText>
			<textField pattern="#,##0.00;#,##0.00" isBlankWhenNull="true">
				<reportElement x="628" y="116" width="57" height="15" uuid="12953036-d2eb-49c5-9af0-1ab88fb5402f">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box rightPadding="2">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DEZEMBRO}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Opaque" x="628" y="101" width="57" height="15" backcolor="#C0C0C0" uuid="f1cf0555-c037-426a-b5cd-b49e10ba27e8">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Dezembro]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="685" y="101" width="57" height="15" backcolor="#C0C0C0" uuid="807e68d2-e5d0-47fa-83da-8463036522c2">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[13° Salário]]></text>
			</staticText>
			<textField pattern="#,#00.###" isBlankWhenNull="true">
				<reportElement mode="Opaque" x="3" y="4" width="61" height="18" backcolor="#C0C0C0" uuid="fae30ada-e9bb-453e-8d31-bee7f066e12c">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Vínculo " + $V{REPORT_COUNT} + ": "]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Opaque" x="742" y="101" width="59" height="15" backcolor="#C0C0C0" uuid="75282b7a-5738-4c6c-89f9-8f3e22a7da95">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Total]]></text>
			</staticText>
			<textField pattern="#,##0.00;#,##0.00" isBlankWhenNull="true">
				<reportElement x="742" y="116" width="59" height="15" uuid="28418aa8-8c1f-4730-8e14-dddfe7c2eada">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box rightPadding="5">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{Total}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
