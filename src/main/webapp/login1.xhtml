<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:pt="http://xmlns.jcp.org/jsf/passthrough">

<h:head>
	<meta name="viewport"
		content="width=device-width, initial-scale=1, maximum-scale=1, usr-scalable=no" />

	<link rel="SHORTCUT ICON" type="image/png"
		href="#{request.contextPath}/resources/imagens/logo_sicap.png" />
	<link rel="icon" type="image/png"
		href="#{request.contextPath}/resources/imagens/logo_sicap.png" />

	<title>.:: SICAP Análise ::.</title>

	<h:outputStylesheet library="css" name="login.css" />

</h:head>

<h:body styleClass="login">

	<div id="header">
		<h:outputLink id="tceLink" value="http://www.tce.ac.gov.br"
			title="Tribunal de Contas do Estado do Acre">
			<h:graphicImage id="iconTce" url="/resources/imagens/icon_tce.png" />
		</h:outputLink>
	</div>

	<div id="login">
		<h:outputLink id="linkHome" value="#{request.contextPath}/"
			title="Página Inicial do Sistema">
			<h:panelGrid columns="2" cellpadding="5">
				<h:column>
					<h:graphicImage id="iconSistema"
						url="/resources/imagens/logo_sicap.png" />
				</h:column>
				<h:column>
					<h:outputText id="lblHeader" value="SICAP Análise" />
				</h:column>
			</h:panelGrid>
		</h:outputLink>

		<h:form id="loginForm" prependId="false">

			<p:growl id="messages" showDetail="false" sticky="false"
				widgetVar="growl" life="6000" autoUpdate="true" />

			<h:panelGrid columns="2" cellpadding="8">
				<h:column>
					<i class="fa fa-user"></i>
				</h:column>
				<h:column>
					<p:inputText value="#{loginBean.login}" id="idLogin" name="idLogin"
						placeholder="Usuário" required="true"
						requiredMessage="O usuário deve ser informado." />
				</h:column>

				<h:column>
					<i class="fa fa-lock"></i>
				</h:column>
				<h:column>
					<p:password id="idPassword" name="idPassword" placeholder="Senha"
						value="#{loginBean.senha}" required="true"
						requiredMessage="A senha deve ser informado." />
				</h:column>
			</h:panelGrid>
			<h:commandButton id="loginButton" value="Entrar"
				action="#{loginBean.login()}" update="messages">
				<f:ajax render="messages" />
			</h:commandButton>
		</h:form>
	</div>

	<footer> <span> Tribunal de Contas do Estado do Acre <br />
		Av. Ceará, 2994, 7º BEC - Rio Branco-Acre - CEP 69.918-111 <br />
		Telefones: (68) 3025-2010 - 3025-2069 - Fax: (68) 3025-2041 <!-- 		<f:loadBundle -->
		<!-- 			basename="prestacao" var="prestacao" /> --> <!-- 		<div style="float: right;"> -->
		<!-- 			<h:outputText value="v#{prestacao.versao}" --> <!-- 				title="v#{prestacao.versao}" /> -->
		<!-- 		</div> -->
	</span> </footer>

</h:body>
</html>