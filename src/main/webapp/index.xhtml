<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html 
	xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:p="http://primefaces.org/ui"
	xmlns:pe="http://primefaces.org/ui/extensions"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:c="http://java.sun.com/jsp/jstl/core">

<ui:composition template="/resources/template/template1.xhtml">

	<ui:define name="head">

		<script type="text/javascript">
			function start() {
				statusDialog.show();
			}

			function stop() {
				statusDialog.hide();
			}
		</script>

		<style type="text/css">
			.ui-menu-dynamic {
				width: 250px;
			}
		</style>

		<p:ajaxStatus onstart="PF('statusDialog').show();"
			onsuccess="PF('statusDialog').hide();" />

		<p:dialog modal="true" widgetVar="statusDialog" header="Carregando..."
			draggable="false" closable="false" resizable="false">
			<p:graphicImage value="/imagens/ajaxloadingbar.gif" />
		</p:dialog>

	</ui:define>

	<ui:define name="content">
		<h:outputText value="Teste Jesse" />
	</ui:define>

</ui:composition>

</html>