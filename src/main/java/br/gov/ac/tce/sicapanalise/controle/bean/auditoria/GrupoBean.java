package br.gov.ac.tce.sicapanalise.controle.bean.auditoria;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import javax.annotation.PostConstruct;
import javax.faces.view.ViewScoped;
import javax.inject.Inject;
import javax.inject.Named;
import javax.transaction.Transactional;

import org.primefaces.PrimeFaces;

import br.gov.ac.tce.message.MessageType;
import br.gov.ac.tce.message.Messenger;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.responsavel.Grupo;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.responsavel.PerfilGrupo;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.responsavel.Usuario;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.responsavel.UsuarioGrupo;
import br.gov.ac.tce.sicapanalise.auditoria.repositorio.GrupoRepositorio;
import br.gov.ac.tce.sicapanalise.util.Mensagem;
import br.gov.ac.tce.sicapanalise.util.MensagemType;

@Named
@ViewScoped
public class GrupoBean implements Serializable {

	private static final long serialVersionUID = 1527672659373512025L;

	@Inject
	private GrupoRepositorio grupoRepositorio;

	private Grupo grupo = new Grupo();
	
	private Grupo grupoSelecionado;
	
	private Collection<Grupo> grupos;

	private Collection<UsuarioGrupo> usuariosGrupo;
	

	private List<Grupo> gruposSelecionado;

	private List<UsuarioGrupo> usuariosGrupoSelecionado;
	
	private Collection<Usuario> listaUsuarioIncluir;

	private int currentLevel = 1;

	@PostConstruct
	void inicializado() {
		carregaGrupos();
	}

	public void carregaGrupos() {
		this.grupos = grupoRepositorio.listaGrupos();
	}
	
	public void carregaUsuariosGrupo() {
		this.usuariosGrupo = grupoRepositorio.listaUsuariosNoGrupo(grupoSelecionado);
	}

	public Grupo getGrupo() {
		return grupo;
	}

	public void setGrupo(Grupo grupo) {
		this.grupo = grupo;
	}
	

	public Grupo getGrupoSelecionado() {
		return grupoSelecionado;
	}

	public void setGrupoSelecionado(Grupo grupoSelecionado) {
		this.grupoSelecionado = grupoSelecionado;
		carregaUsuariosGrupo();
		
	}

	public Collection<Grupo> getGrupos() {
		return grupos;
	}

	public void setGrupos(Collection<Grupo> grupos) {
		this.grupos = grupos;
	}

	public Collection<UsuarioGrupo> getUsuariosGrupo() {
		return usuariosGrupo;
	}

	public void setUsuariosGrupo(Collection<UsuarioGrupo> usuariosGrupo) {
		this.usuariosGrupo = usuariosGrupo;
	}


	public List<Grupo> getGruposSelecionado() {
		return gruposSelecionado;
	}

	public void setGruposSelecionado(List<Grupo> gruposSelecionado) {
		this.gruposSelecionado = gruposSelecionado;
	}

	public List<UsuarioGrupo> getUsuariosGrupoSelecionado() {
		return usuariosGrupoSelecionado;
	}

	public void setUsuariosGrupoSelecionado(List<UsuarioGrupo> usuariosGrupoSelecionado) {
		this.usuariosGrupoSelecionado = usuariosGrupoSelecionado;
	}
	
	

	public Collection<Usuario> getListaUsuarioIncluir() {
		return listaUsuarioIncluir;
	}

	public void setListaUsuarioIncluir(Collection<Usuario> listaUsuarioIncluir) {
		System.out.println("setando lista de usuarios a incluir: " + listaUsuarioIncluir.size());
		this.listaUsuarioIncluir = listaUsuarioIncluir;
	}

	public int getCurrentLevel() {
		return currentLevel;
	}

	public void setCurrentLevel(int currentLevel) {
		this.currentLevel = currentLevel;
	}
	
	public List<PerfilGrupo> getListaPerfis(){
		List<PerfilGrupo> perfis = Arrays.asList(PerfilGrupo.values());
		Collections.sort(perfis, Comparator.comparing(PerfilGrupo::getDescricao));
		return perfis;
	}

	
//	public void fechaModal() {
//		FacesContext contexto = FacesContext.getCurrentInstance();
//		RequestContext request = RequestContext.getCurrentInstance();
//		if (!contexto.isValidationFailed()) {
//			request.execute("PF('dialogSelecionaUsuarioVar').hide();");
//			
//			//Após usar os filtros e incluir um usuário e reabrir o dialog, o datatable fica bugado
//			DataTable dataTable = (DataTable) contexto.getViewRoot().findComponent("formUsuario:tblUsuario");
//			dataTable.resetValue();
//		}
//	}

	@Transactional
	public void excluirGrupo() {
		if (gruposSelecionado != null && !gruposSelecionado.isEmpty()) {
			grupoRepositorio.desativarGrupo(gruposSelecionado);
			this.gruposSelecionado.clear();
			carregaGrupos();
		} else {
			Messenger.mostrarMensagem(MessageType.ERRO, "Nenhum Grupo foi selecionado.", "");
		}
	}

	@Transactional
	public void excluirUsuarioGrupo() {
		if (usuariosGrupoSelecionado != null && !usuariosGrupoSelecionado.isEmpty()) {
			grupoRepositorio.desativarUsuarioGrupo(usuariosGrupoSelecionado);
			this.usuariosGrupoSelecionado.clear();
			carregaGrupos();
			carregaUsuariosGrupo();
		} else {
			Messenger.mostrarMensagem(MessageType.ERRO, "Nenhum Usuário foi selecionado.", "");
		}
	}

	@Transactional
	public void incluirUsuarioGrupo() {
		if (this.listaUsuarioIncluir != null && !this.listaUsuarioIncluir.isEmpty()) {
			grupoRepositorio.incluirUsuarioGrupo(listaUsuarioIncluir,grupoSelecionado);
			
			//Se adicionou os usu�rios, fecha o modal
//			RequestContext requestContext = RequestContext.getCurrentInstance();
//			requestContext.execute("PF('dlgSelecionarUsuarioVar').hide();");
			PrimeFaces.current().executeScript("PF('dlgSelecionarUsuarioVar').hide();");
			
			carregaGrupos();
			carregaUsuariosGrupo();
		} else {
			Mensagem.setMensagem(MensagemType.ERRO, "Nenhum Usuário foi selecionado.", "");
		}

	}

	@Transactional
	public void salvar() {
		this.grupo.setAtivo(true);
		grupoRepositorio.salvarGrupo(this.grupo);
		this.grupo = new Grupo();
		carregaGrupos();

	}
}
