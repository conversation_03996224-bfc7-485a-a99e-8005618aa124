package br.gov.ac.tce.sicapanalise.controle.bean.entidade;

import java.io.Serializable;
import java.util.Collection;

import javax.annotation.PostConstruct;
import javax.faces.view.ViewScoped;
import javax.inject.Inject;
import javax.inject.Named;

import br.gov.ac.tce.message.MessageType;
import br.gov.ac.tce.message.Messenger;
import br.gov.ac.tce.sicapanalise.controle.bean.LoginBean;
import br.gov.ac.tce.sicapanalise.controle.conversor.FormatUtil;
import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapweb.modelo.Status;
import br.gov.ac.tce.sicapweb.modelo.folha.TipoNatureza;
import br.gov.ac.tce.sicapweb.modelo.folha.TipoReferencia;
import br.gov.ac.tce.sicapweb.modelo.folha.Verba;
import br.gov.ac.tce.sicapweb.modelo.remessa.RemessaEventual;
import br.gov.ac.tce.sicapweb.modelo.remessa.SituacaoRemessa;
import br.gov.ac.tce.sicapweb.repositorio.RemessaEventualRepositorio;
import br.gov.ac.tce.sicapweb.repositorio.entidade.VerbaRepositorio;
import br.gov.ac.tce.sicapweb.xml.validador.TypeArquivo;

@Named
@ViewScoped
public class VerbaBean implements Serializable {

	private static final long serialVersionUID = 1L;

	@Inject
	private LoginBean loginBean;
	@Inject
	private Verba verba;
	@Inject
	private RemessaEventual remessaEventual;
	@Inject
	private FormatUtil formatUtil;

	@Inject
	private RemessaEventualRepositorio remessaEventualRepositorio;
	@Inject
	private VerbaRepositorio verbaRepositorio;

	private Collection<RemessaEventual> listaRemessaEventual;
	private Collection<Verba> listaVerba;

	private String nome;
	private String basePrevidenciaria;
	private String baseIRPF;
	private String baseFGTS;
	private String natureza;
	private Integer tipoReferencia;
	private String compoeVencimentoPadrao;
	private Integer categoriaEconomica;
	private Integer grupoNaturezaDespesa;
	private Integer modalidadeAplicacao;
	private String elementoDespesa;

	private int currentLevel;

	@PostConstruct
	public void init() {
		this.currentLevel = 1;
		if (this.loginBean.existeEntidadeSelecionada("/dados/entidade/verba/verbas.xhtml?faces-redirect=true")) {
			try {
				this.nome = "";
				this.basePrevidenciaria = "";
				this.baseIRPF = "";
				this.baseFGTS = "";
				this.natureza = "";
				this.tipoReferencia = 0;
				this.compoeVencimentoPadrao = "";
				this.categoriaEconomica = null;
				this.grupoNaturezaDespesa = null;
				this.modalidadeAplicacao = null;
				this.elementoDespesa = "";
				this.listaRemessaEventual = this.remessaEventualRepositorio.listarPorEntidadeTipoSituacao(
						this.loginBean.getEntidade(), TypeArquivo.VERBAS, SituacaoRemessa.PROCESSADA);
			} catch (RepositorioException e) {
				Messenger.mostrarMensagem(MessageType.ERRO, "Não foi possível listar as remesas de verbas.");
			}
		}
	}

	public void pesquisar() {
		try {
			this.remessaEventual.setId(Long.valueOf(0));
			this.listaVerba = this.verbaRepositorio.pesquisaVerbas(this.loginBean.getEntidade(), this.remessaEventual,
					this.basePrevidenciaria, this.baseIRPF, this.baseFGTS, this.natureza, this.tipoReferencia,
					this.compoeVencimentoPadrao, this.categoriaEconomica, this.grupoNaturezaDespesa,
					this.modalidadeAplicacao, this.elementoDespesa, this.nome);
			this.currentLevel = 1;
		} catch (RepositorioException e) {
			Messenger.mostrarMensagem(MessageType.ERRO, "Não foi possível listar nenhuma verba.");
		}
	}

	public void buscaDadosVerba(Verba verba) {
		try {
			this.verba = this.verbaRepositorio.pesquisaVerba(this.loginBean.getEntidade(), verba.getId());
		} catch (RepositorioException e) {
			Messenger.mostrarMensagem(MessageType.ERRO, "Não foi possível encontrar nenhuma verba.");
		}
	}

	public Verba getVerba() {
		return verba;
	}

	public Collection<Verba> getListaVerba() {
		return listaVerba;
	}

	public void setListaVerba(Collection<Verba> listaVerba) {
		this.listaVerba = listaVerba;
	}

	public void setVerba(Verba verba) {
		this.verba = verba;
	}

	public int getCurrentLevel() {
		return currentLevel;
	}

	public void setCurrentLevel(int currentLevel) {
		this.currentLevel = currentLevel;
	}

	public Collection<RemessaEventual> getListaRemessaEventual() {
		return listaRemessaEventual;
	}

	public void setListaRemessaEventual(Collection<RemessaEventual> listaRemessaEventual) {
		this.listaRemessaEventual = listaRemessaEventual;
	}

	public RemessaEventual getRemessaEventual() {
		return remessaEventual;
	}

	public void setRemessaEventual(RemessaEventual remessaEventual) {
		this.remessaEventual = remessaEventual;
	}

	public FormatUtil getFormatUtil() {
		return formatUtil;
	}

	public void setFormatUtil(FormatUtil formatUtil) {
		this.formatUtil = formatUtil;
	}

	public String getNome() {
		return nome;
	}

	public void setNome(String nome) {
		this.nome = nome;
	}

	public Status[] getStatus() {
		return Status.values();
	}

	public TipoNatureza[] getTipoNatureza() {
		return TipoNatureza.values();
	}

	public TipoReferencia[] getListaTipoReferencia() {
		return TipoReferencia.values();
	}

	public String getBasePrevidenciaria() {
		return basePrevidenciaria;
	}

	public void setBasePrevidenciaria(String basePrevidenciaria) {
		this.basePrevidenciaria = basePrevidenciaria;
	}

	public String getBaseIRPF() {
		return baseIRPF;
	}

	public void setBaseIRPF(String baseIRPF) {
		this.baseIRPF = baseIRPF;
	}

	public String getBaseFGTS() {
		return baseFGTS;
	}

	public void setBaseFGTS(String baseFGTS) {
		this.baseFGTS = baseFGTS;
	}

	public String getNatureza() {
		return natureza;
	}

	public void setNatureza(String natureza) {
		this.natureza = natureza;
	}

	public Integer getTipoReferencia() {
		return tipoReferencia;
	}

	public String getElementoDespesa() {
		return elementoDespesa;
	}

	public void setTipoReferencia(Integer tipoReferencia) {
		this.tipoReferencia = tipoReferencia;
	}

	public void setElementoDespesa(String elementoDespesa) {
		this.elementoDespesa = elementoDespesa;
	}

	public String getCompoeVencimentoPadrao() {
		return compoeVencimentoPadrao;
	}

	public void setCompoeVencimentoPadrao(String compoeVencimentoPadrao) {
		this.compoeVencimentoPadrao = compoeVencimentoPadrao;
	}

	public Integer getCategoriaEconomica() {
		return categoriaEconomica;
	}

	public void setCategoriaEconomica(Integer categoriaEconomica) {
		this.categoriaEconomica = categoriaEconomica;
	}

	public Integer getGrupoNaturezaDespesa() {
		return grupoNaturezaDespesa;
	}

	public void setGrupoNaturezaDespesa(Integer grupoNaturezaDespesa) {
		this.grupoNaturezaDespesa = grupoNaturezaDespesa;
	}

	public Integer getModalidadeAplicacao() {
		return modalidadeAplicacao;
	}

	public void setModalidadeAplicacao(Integer modalidadeAplicacao) {
		this.modalidadeAplicacao = modalidadeAplicacao;
	}

}
