package br.gov.ac.tce.sicapanalise.relatorios.repositorio;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;


import br.gov.ac.tce.sicapanalise.relatorios.dto.ServidorCompatibilidadeCHDTO;
import br.gov.ac.tce.sicapanalise.relatorios.dto.VinculoCompatibilidadeCHDTO;

@Stateless
public class CompatibilidadeCHRepositorio implements Serializable {

    private static final long serialVersionUID = 1L;

    @PersistenceContext(unitName = "sicapAnalise")
    private EntityManager entityManager;

    @SuppressWarnings("unchecked")
    public Collection<Object[]> listaCompetencias() throws Exception {
        Collection<Object[]> listaCompetencias = null;

        try {
            Query query = this.entityManager.createNativeQuery(
                "SELECT DISTINCT a.ano, a.mes, " +
                "CASE a.mes " +
                "WHEN 1 THEN 'Janeiro' " +
                "WHEN 2 THEN 'Fevereiro' " +
                "WHEN 3 THEN 'Março' " +
                "WHEN 4 THEN 'Abril' " +
                "WHEN 5 THEN 'Maio' " +
                "WHEN 6 THEN 'Junho' " +
                "WHEN 7 THEN 'Julho' " +
                "WHEN 8 THEN 'Agosto' " +
                "WHEN 9 THEN 'Setembro' " +
                "WHEN 10 THEN 'Outubro' " +
                "WHEN 11 THEN 'Novembro' " +
                "WHEN 12 THEN 'Dezembro' " +
                "ELSE 'Nenhum' END AS nomeMes " +
                "FROM auditoria.acumulacao a " +
                "ORDER BY a.ano DESC, a.mes DESC");

            listaCompetencias = query.getResultList();

        } catch (Exception e) {
            throw new Exception("Erro ao listar competências: " + e.getMessage());
        }

        return listaCompetencias;
    }

    @SuppressWarnings("unchecked")
    public Collection<ServidorCompatibilidadeCHDTO> listaServidoresCompatibilidadeCH(
            String competencia, String cpf, String nome, Integer entidade) throws Exception {
        
        Collection<ServidorCompatibilidadeCHDTO> listaServidores = new ArrayList<>();

        try {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT DISTINCT ");
            sql.append("a.cpf, ");
            sql.append("a.nome, ");
            sql.append("a.mes, ");
            sql.append("a.ano, ");
            sql.append("a.cargaHorariaTotal, ");
            sql.append("(a.cargaHorariaTotal / 4.33) as cargaHorariaSemanal, ");
            sql.append("CASE ");
            sql.append("  WHEN (a.cargaHorariaTotal / 4.33) > 60 THEN 'INCOMPATÍVEL' ");
            sql.append("  WHEN (a.cargaHorariaTotal / 4.33) > 40 AND a.agentePolitico = 0 THEN 'ATENÇÃO' ");
            sql.append("  ELSE 'COMPATÍVEL' ");
            sql.append("END as statusCompatibilidade ");
            sql.append("FROM auditoria.acumulacao a ");
            sql.append("WHERE 1=1 ");

            Map<String, Object> parametros = new HashMap<>();
            int paramIndex = 1;

            if (competencia != null && !competencia.trim().isEmpty() && !competencia.equals("0")) {
                String[] comp = competencia.split("/");
                if (comp.length == 2) {
                    sql.append("AND a.mes = ?").append(paramIndex).append(" ");
                    parametros.put(String.valueOf(paramIndex++), Integer.parseInt(comp[0]));
                    sql.append("AND a.ano = ?").append(paramIndex).append(" ");
                    parametros.put(String.valueOf(paramIndex++), Integer.parseInt(comp[1]));
                }
            }

            if (cpf != null && !cpf.trim().isEmpty()) {
                sql.append("AND a.cpf LIKE ?").append(paramIndex).append(" ");
                parametros.put(String.valueOf(paramIndex++), "%" + cpf.trim() + "%");
            }

            if (nome != null && !nome.trim().isEmpty()) {
                sql.append("AND UPPER(a.nome) LIKE ?").append(paramIndex).append(" ");
                parametros.put(String.valueOf(paramIndex++), "%" + nome.trim().toUpperCase() + "%");
            }

            if (entidade != null && entidade > 0) {
                sql.append("AND EXISTS (SELECT 1 FROM auditoria.DetalhamentoAcumulacao da ");
                sql.append("WHERE da.idAcumulacao = a.id AND da.idEntidadeCjur = ?").append(paramIndex).append(") ");
                parametros.put(String.valueOf(paramIndex++), entidade);
            }

            // Filtrar apenas casos com possível incompatibilidade
            sql.append("AND (a.cargaHorariaTotal / 4.33) > 40 ");
            sql.append("ORDER BY a.nome, a.ano DESC, a.mes DESC");

            Query query = this.entityManager.createNativeQuery(sql.toString());

            for (Map.Entry<String, Object> param : parametros.entrySet()) {
                query.setParameter(Integer.parseInt(param.getKey()), param.getValue());
            }

            List<Object[]> resultados = query.getResultList();

            for (Object[] resultado : resultados) {
                ServidorCompatibilidadeCHDTO servidor = new ServidorCompatibilidadeCHDTO(
                    (String) resultado[0],  // cpf
                    (String) resultado[1],  // nome
                    ((Number) resultado[2]).intValue(), // mes
                    ((Number) resultado[3]).intValue(), // ano
                    ((Number) resultado[4]).intValue(), // cargaHorariaTotal
                    ((Number) resultado[5]).intValue(), // cargaHorariaSemanal
                    (String) resultado[6]   // statusCompatibilidade
                );

                // Buscar vínculos do servidor
                servidor.setVinculos(buscarVinculosServidor(servidor.getCpf(), servidor.getMes(), servidor.getAno()));
                
                listaServidores.add(servidor);
            }

        } catch (Exception e) {
            throw new Exception("Erro ao listar servidores com incompatibilidade de CH: " + e.getMessage());
        }

        return listaServidores;
    }

    @SuppressWarnings("unchecked")
    private List<VinculoCompatibilidadeCHDTO> buscarVinculosServidor(String cpf, Integer mes, Integer ano) {
        List<VinculoCompatibilidadeCHDTO> vinculos = new ArrayList<>();

        try {
            String sql = "SELECT " +
                "e.nome as entidade, " +
                "ISNULL(c.nome, 'INDEFINIDO') as cargo, " +
                "ISNULL(c.cargaHorariaMensal, 0) as cargaHorariaMensal, " +
                "(ISNULL(c.cargaHorariaMensal, 0) / 4.33) as cargaHorariaSemanal, " +
                "b.matricula " +
                "FROM auditoria.DetalhamentoAcumulacao da " +
                "INNER JOIN auditoria.acumulacao a ON a.id = da.idAcumulacao " +
                "LEFT JOIN Entidade e ON e.idEntidadeCjur = da.idEntidadeCjur " +
                "LEFT JOIN Cargo c ON c.id = da.idCargo " +
                "LEFT JOIN Beneficiario b ON b.id = da.idBeneficiario " +
                "WHERE a.cpf = ? AND a.mes = ? AND a.ano = ? " +
                "ORDER BY e.nome";

            Query query = this.entityManager.createNativeQuery(sql);
            query.setParameter(1, cpf);
            query.setParameter(2, mes);
            query.setParameter(3, ano);

            List<Object[]> resultados = query.getResultList();

            for (Object[] resultado : resultados) {
                VinculoCompatibilidadeCHDTO vinculo = new VinculoCompatibilidadeCHDTO(
                    (String) resultado[0],  // entidade
                    (String) resultado[1],  // cargo
                    ((Number) resultado[2]).intValue(), // cargaHorariaMensal
                    ((Number) resultado[3]).intValue(), // cargaHorariaSemanal
                    (String) resultado[4]   // matricula
                );
                vinculos.add(vinculo);
            }

        } catch (Exception e) {
            System.err.println("Erro ao buscar vínculos do servidor " + cpf + ": " + e.getMessage());
        }

        return vinculos;
    }
}
