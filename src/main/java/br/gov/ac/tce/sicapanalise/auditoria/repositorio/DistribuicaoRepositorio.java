package br.gov.ac.tce.sicapanalise.auditoria.repositorio;

import java.io.Serializable;
import java.util.Collection;

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.acumulacao.Acumulacao;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.acumulacao.DistribuicaoAcumulacao;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.responsavel.Usuario;

@Stateless
public class DistribuicaoRepositorio implements Serializable {

	private static final long serialVersionUID = 31834010688448576L;

	@PersistenceContext(unitName = "sicapAnalise")
	private EntityManager em;

	public void salvar(DistribuicaoAcumulacao distribuicaoAcumulacao) {
		if (distribuicaoAcumulacao.getId() == null || distribuicaoAcumulacao.getId() == 0L) {
			em.persist(distribuicaoAcumulacao);
		} else {
			em.merge(distribuicaoAcumulacao);
		}

	}

	public Collection<DistribuicaoAcumulacao> listaDistribuicaoAcumulacao(Usuario usuario) {
		Collection<DistribuicaoAcumulacao> listaDistribuicaoAcumulacao = null;
		String jpql = "SELECT da FROM DistribuicaoAcumulacao da " + "JOIN FETCH da.usuarioAuditor "
				+ "JOIN FETCH da.usuario " + "JOIN FETCH da.acumulacao " + "WHERE da.usuarioAuditor = :pUsuario";
		try {
			listaDistribuicaoAcumulacao = this.em.createQuery(jpql, DistribuicaoAcumulacao.class)
					.setParameter("pUsuario", usuario).getResultList();
		} catch (Exception e) {
			e.printStackTrace();
			System.out.println(e.getMessage());
		}
		return listaDistribuicaoAcumulacao;
	}

	public DistribuicaoAcumulacao retornaDistribuicaoAcumulacaoAtiva(Acumulacao acumulacao) {
		DistribuicaoAcumulacao distribuicaoAcumulacao = null;
		String jpql = "SELECT da FROM DistribuicaoAcumulacao da WHERE da.ativo = true AND da.acumulacao = :pAcumulacao";
		try {
			distribuicaoAcumulacao = this.em.createQuery(jpql, DistribuicaoAcumulacao.class)
					.setParameter("pAcumulacao", acumulacao).getSingleResult();
		} catch (Exception e) {
			System.out.println(e.getMessage());
		}

		return distribuicaoAcumulacao;
	}

}
