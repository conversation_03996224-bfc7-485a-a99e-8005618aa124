package br.gov.ac.tce.sicapanalise.auditoria.business;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import javax.ejb.Stateless;
import javax.inject.Inject;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.acumulacao.Acumulacao;
import br.gov.ac.tce.sicapanalise.auditoria.repositorio.AcumulacaoRepositorio;
import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;

@Stateless
public class AcumulacaoBusiness {

	@Inject
	private AcumulacaoRepositorio acumulacaoRepositorio;
	
	public Collection<Object[]> listaCompetencias() throws RepositorioException{
		return acumulacaoRepositorio.listaCompetencias();
	}
	
	public Collection<Acumulacao> listaAcumulacoes(String competencia, String situacao, String cpf, String nome,
			Integer entidade, Integer cargo) throws RepositorioException{
		return acumulacaoRepositorio.listaAcumulacoes2(competencia, situacao, cpf, nome, entidade, cargo);
	}

	public Collection<Acumulacao> listaAcumulacoes(String competencia, String situacao, String cpf, String nome,
			Integer entidade, Integer cargo, String cargaHorariaSemanalFiltro, String numeroVinculosFiltro) throws RepositorioException{
		return acumulacaoRepositorio.listaAcumulacoes2(competencia, situacao, cpf, nome, entidade, cargo, cargaHorariaSemanalFiltro, numeroVinculosFiltro);
	}
	
	public Collection<Object[]> listaCargosAcumulacao(String competencia, Integer idEntidadeCjur) throws RepositorioException{
		return acumulacaoRepositorio.listaCargosAcumulacao(competencia, idEntidadeCjur);
	}
	
	public List<Acumulacao> listaAcumulacoes(List<Long> acumulacaoIds){
		return new ArrayList<Acumulacao>(acumulacaoRepositorio.listaAcumulacoes(acumulacaoIds));
	}
	
	
	public Acumulacao retornaAcumulacaoPorId(Long acumulacaoId) {
		return acumulacaoRepositorio.retornaAcumulacaoPorId(acumulacaoId);
	}
	
	public Acumulacao retornaAcumulacaoPorAnalise(Long analiseId) {
		return acumulacaoRepositorio.retornaAcumulacaoPorAnalise(analiseId);
	}
}
