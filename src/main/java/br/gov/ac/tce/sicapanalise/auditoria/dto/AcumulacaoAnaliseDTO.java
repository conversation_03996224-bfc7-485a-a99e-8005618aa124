package br.gov.ac.tce.sicapanalise.auditoria.dto;

import java.time.LocalDateTime;

public class AcumulacaoAnaliseDTO {

	private Long acumulacaoId;
	private Integer acumulacaoAno;
	private Integer acumulacaoMes;
	private String acumulacaoCpf;
	private String acumulacaoNome;
	private Double acumulacaoPontuacao;
	private String acumulacaoSituacaoCodigo;
	private String acumulacaoSituacaoDescricao;
	private Integer acumulacaoQuantidadeVinculos;
	private Long analiseId;
	private LocalDateTime analiseDataCriacao;
	private String analiseNumeroProcesso;
	private Integer analiseDocumentosSolicitados;
	private Integer analiseDocumentosEntregues;
	private Integer analiseDocumentosNaoEntregues;
	private Integer analiseRelatorios;
	private Long acumulacaoAnaliseId;

	public AcumulacaoAnaliseDTO() {
		super();
	}

	public AcumulacaoAnaliseDTO(Long acumulacaoId, Integer acumulacaoAno, Integer acumulacaoMes, String acumulacaoCpf,
			String acumulacaoNome, Double acumulacaoPontuacao, String acumulacaoSituacaoCodigo,
			String acumulacaoSituacaoDescricao, Integer acumulacaoQuantidadeVinculos, Long analiseId,
			LocalDateTime analiseDataCriacao, String analiseNumeroProcesso, Integer analiseDocumentosSolicitados,
			Integer analiseDocumentosEntregues, Integer analiseDocumentosNaoEntregues, Integer analiseRelatorios,
			Long acumulacaoAnaliseId) {
		super();
		this.acumulacaoId = acumulacaoId;
		this.acumulacaoAno = acumulacaoAno;
		this.acumulacaoMes = acumulacaoMes;
		this.acumulacaoCpf = acumulacaoCpf;
		this.acumulacaoNome = acumulacaoNome;
		this.acumulacaoPontuacao = acumulacaoPontuacao;
		this.acumulacaoSituacaoCodigo = acumulacaoSituacaoCodigo;
		this.acumulacaoSituacaoDescricao = acumulacaoSituacaoDescricao;
		this.acumulacaoQuantidadeVinculos = acumulacaoQuantidadeVinculos;
		this.analiseId = analiseId;
		this.analiseDataCriacao = analiseDataCriacao;
		this.analiseNumeroProcesso = analiseNumeroProcesso;
		this.analiseDocumentosSolicitados = analiseDocumentosSolicitados;
		this.analiseDocumentosEntregues = analiseDocumentosEntregues;
		this.analiseDocumentosNaoEntregues = analiseDocumentosNaoEntregues;
		this.analiseRelatorios = analiseRelatorios;
		this.acumulacaoAnaliseId = acumulacaoAnaliseId;
	}

	public Long getAcumulacaoId() {
		return acumulacaoId;
	}

	public void setAcumulacaoId(Long acumulacaoId) {
		this.acumulacaoId = acumulacaoId;
	}

	public Integer getAcumulacaoAno() {
		return acumulacaoAno;
	}

	public void setAcumulacaoAno(Integer acumulacaoAno) {
		this.acumulacaoAno = acumulacaoAno;
	}

	public Integer getAcumulacaoMes() {
		return acumulacaoMes;
	}

	public void setAcumulacaoMes(Integer acumulacaoMes) {
		this.acumulacaoMes = acumulacaoMes;
	}

	public String getAcumulacaoCpf() {
		return acumulacaoCpf;
	}

	public void setAcumulacaoCpf(String acumulacaoCpf) {
		this.acumulacaoCpf = acumulacaoCpf;
	}

	public String getAcumulacaoNome() {
		return acumulacaoNome;
	}

	public void setAcumulacaoNome(String acumulacaoNome) {
		this.acumulacaoNome = acumulacaoNome;
	}

	public Double getAcumulacaoPontuacao() {
		return acumulacaoPontuacao;
	}

	public void setAcumulacaoPontuacao(Double acumulacaoPontuacao) {
		this.acumulacaoPontuacao = acumulacaoPontuacao;
	}

	public String getAcumulacaoSituacaoCodigo() {
		return acumulacaoSituacaoCodigo;
	}

	public void setAcumulacaoSituacaoCodigo(String acumulacaoSituacaoCodigo) {
		this.acumulacaoSituacaoCodigo = acumulacaoSituacaoCodigo;
	}
	

	public String getAcumulacaoSituacaoDescricao() {
		return acumulacaoSituacaoDescricao;
	}

	public void setAcumulacaoSituacaoDescricao(String acumulacaoSituacaoDescricao) {
		this.acumulacaoSituacaoDescricao = acumulacaoSituacaoDescricao;
	}

	public Integer getAcumulacaoQuantidadeVinculos() {
		return acumulacaoQuantidadeVinculos;
	}

	public void setAcumulacaoQuantidadeVinculos(Integer acumulacaoQuantidadeVinculos) {
		this.acumulacaoQuantidadeVinculos = acumulacaoQuantidadeVinculos;
	}

	public Long getAnaliseId() {
		return analiseId;
	}

	public void setAnaliseId(Long analiseId) {
		this.analiseId = analiseId;
	}

	public LocalDateTime getAnaliseDataCriacao() {
		return analiseDataCriacao;
	}

	public void setAnaliseDataCriacao(LocalDateTime analiseDataCriacao) {
		this.analiseDataCriacao = analiseDataCriacao;
	}

	public String getAnaliseNumeroProcesso() {
		return analiseNumeroProcesso;
	}

	public void setAnaliseNumeroProcesso(String analiseNumeroProcesso) {
		this.analiseNumeroProcesso = analiseNumeroProcesso;
	}

	public Integer getAnaliseDocumentosSolicitados() {
		return analiseDocumentosSolicitados;
	}

	public void setAnaliseDocumentosSolicitados(Integer analiseDocumentosSolicitados) {
		this.analiseDocumentosSolicitados = analiseDocumentosSolicitados;
	}

	public Integer getAnaliseDocumentosEntregues() {
		return analiseDocumentosEntregues;
	}

	public void setAnaliseDocumentosEntregues(Integer analiseDocumentosEntregues) {
		this.analiseDocumentosEntregues = analiseDocumentosEntregues;
	}

	public Integer getAnaliseDocumentosNaoEntregues() {
		return analiseDocumentosNaoEntregues;
	}

	public void setAnaliseDocumentosNaoEntregues(Integer analiseDocumentosNaoEntregues) {
		this.analiseDocumentosNaoEntregues = analiseDocumentosNaoEntregues;
	}

	public Integer getAnaliseRelatorios() {
		return analiseRelatorios;
	}

	public void setAnaliseRelatorios(Integer analiseRelatorios) {
		this.analiseRelatorios = analiseRelatorios;
	}

	public Long getAcumulacaoAnaliseId() {
		return acumulacaoAnaliseId;
	}

	public void setAcumulacaoAnaliseId(Long acumulacaoAnaliseId) {
		this.acumulacaoAnaliseId = acumulacaoAnaliseId;
	}

}
