package br.gov.ac.tce.sicapanalise.controle.bean.entidade;

import java.io.Serializable;
import java.util.Collection;

import javax.annotation.PostConstruct;
import javax.faces.view.ViewScoped;
import javax.inject.Inject;
import javax.inject.Named;

import br.gov.ac.tce.message.MessageType;
import br.gov.ac.tce.message.Messenger;
import br.gov.ac.tce.sicapanalise.controle.bean.LoginBean;
import br.gov.ac.tce.sicapanalise.controle.conversor.FormatUtil;
import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapweb.modelo.cargo.Cargo;
import br.gov.ac.tce.sicapweb.modelo.remessa.RemessaEventual;
import br.gov.ac.tce.sicapweb.modelo.remessa.SituacaoRemessa;
import br.gov.ac.tce.sicapweb.modelo.tabelavencimento.TabelaVencimentos;
import br.gov.ac.tce.sicapweb.repositorio.RemessaEventualRepositorio;
import br.gov.ac.tce.sicapweb.repositorio.entidade.CargoRepositorio;
import br.gov.ac.tce.sicapweb.repositorio.entidade.TabelaVencimentosRepositorio;
import br.gov.ac.tce.sicapweb.xml.validador.TypeArquivo;

@Named
@ViewScoped
public class TabelaVencimentosBean implements Serializable {

	private static final long serialVersionUID = 1L;

	@Inject
	private LoginBean loginBean;
	@Inject
	private TabelaVencimentos tabelaVencimentos;
	@Inject
	private RemessaEventual remessaEventual;
	@Inject
	private Cargo cargo;
	@Inject
	private FormatUtil formatUtil;

	@Inject
	private RemessaEventualRepositorio remessaEventualRepositorio;
	@Inject
	private TabelaVencimentosRepositorio tabelaVencimentosRepositorio;
	@Inject
	private CargoRepositorio cargoRepositorio;

	private Collection<RemessaEventual> listaRemessaEventual;
	private Collection<TabelaVencimentos> listaTabelaVencimentos;
	private Collection<Cargo> listaCargo;

	private String nome;

	private int currentLevel;

	@PostConstruct
	public void init() {
		this.currentLevel = 1;
		if (this.loginBean.existeEntidadeSelecionada(
				"/dados/entidade/tabelaVencimentos/tabelaVencimentos.xhtml?faces-redirect=true")) {
			try {
				this.nome = "";
				this.listaRemessaEventual = this.remessaEventualRepositorio.listarPorEntidadeTipoSituacao(
						this.loginBean.getEntidade(), TypeArquivo.TABELAS_VENCIMENTO, SituacaoRemessa.PROCESSADA);
				this.listaCargo = this.cargoRepositorio.buscarTodosPorEntidade(this.loginBean.getEntidade());
			} catch (RepositorioException e) {
				Messenger.mostrarMensagem(MessageType.ERRO, "Não foi possível listar os dados para a pesquisa.");
			}
		}
	}

	public void pesquisar() {
		try {
			this.remessaEventual.setId(Long.valueOf(0));
			this.listaTabelaVencimentos = this.tabelaVencimentosRepositorio.pesquisaTabelasVencimentos(
					this.loginBean.getEntidade(), this.remessaEventual, this.nome, this.cargo);
			this.currentLevel = 1;
		} catch (RepositorioException e) {
			Messenger.mostrarMensagem(MessageType.ERRO, "Não foi possível listar nenhuma tabela de vencimentos.");
		}
	}

	public void buscaDadosTabelaVencimento(TabelaVencimentos tabelaVencimentos) {
		try {
			this.tabelaVencimentos = this.tabelaVencimentosRepositorio
					.pesquisaTabelaVencimentosPorId(this.loginBean.getEntidade(), tabelaVencimentos.getId());
		} catch (RepositorioException e) {
			Messenger.mostrarMensagem(MessageType.ERRO,
					"Não foi possível encontrar a tabela de vencimentos selecionada.");
		}
	}

	public TabelaVencimentos getTabelaVencimentos() {
		return tabelaVencimentos;
	}

	public void setTabelaVencimentos(TabelaVencimentos tabelaVencimentos) {
		this.tabelaVencimentos = tabelaVencimentos;
	}

	public RemessaEventual getRemessaEventual() {
		return remessaEventual;
	}

	public void setRemessaEventual(RemessaEventual remessaEventual) {
		this.remessaEventual = remessaEventual;
	}

	public Cargo getCargo() {
		return cargo;
	}

	public void setCargo(Cargo cargo) {
		this.cargo = cargo;
	}

	public FormatUtil getFormatUtil() {
		return formatUtil;
	}

	public void setFormatUtil(FormatUtil formatUtil) {
		this.formatUtil = formatUtil;
	}

	public int getCurrentLevel() {
		return currentLevel;
	}

	public void setCurrentLevel(int currentLevel) {
		this.currentLevel = currentLevel;
	}

	public Collection<RemessaEventual> getListaRemessaEventual() {
		return listaRemessaEventual;
	}

	public void setListaRemessaEventual(Collection<RemessaEventual> listaRemessaEventual) {
		this.listaRemessaEventual = listaRemessaEventual;
	}

	public Collection<TabelaVencimentos> getListaTabelaVencimentos() {
		return listaTabelaVencimentos;
	}

	public void setListaTabelaVencimentos(Collection<TabelaVencimentos> listaTabelaVencimentos) {
		this.listaTabelaVencimentos = listaTabelaVencimentos;
	}

	public Collection<Cargo> getListaCargo() {
		return listaCargo;
	}

	public void setListaCargo(Collection<Cargo> listaCargo) {
		this.listaCargo = listaCargo;
	}

	public String getNome() {
		return nome;
	}

	public void setNome(String nome) {
		this.nome = nome;
	}
}
