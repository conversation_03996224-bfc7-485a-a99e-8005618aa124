package br.gov.ac.tce.sicapanalise.auditoria.business;

import java.util.List;

import javax.ejb.Stateless;
import javax.inject.Inject;

import br.gov.ac.tce.sicapanalise.auditoria.dto.FolhaCompetenciaDTO;
import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;
import br.gov.ac.tce.sicapweb.repositorio.entidade.ResumoFolhaRepositorio;

@Stateless
public class FolhaBusiness {

	@Inject
	private ResumoFolhaRepositorio resumoFolhaRepositorio;
	
	public List<FolhaCompetenciaDTO> listaResumoPorEntidade(List<Entidade> entidades, Integer ano){
		return resumoFolhaRepositorio.listaResumoPorEntidade(entidades, ano);
	}
}
