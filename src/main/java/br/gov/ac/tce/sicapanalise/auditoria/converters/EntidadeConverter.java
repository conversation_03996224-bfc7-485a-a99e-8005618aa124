package br.gov.ac.tce.sicapanalise.auditoria.converters;

import java.io.Serializable;
import java.util.Map;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;

@FacesConverter("entidadeConverter")
public class EntidadeConverter implements Converter, Serializable {


	private static final long serialVersionUID = 1L;

	@Override
	public Object getAsObject(FacesContext context, UIComponent component, String id) {
		if (id == null || id.trim().isEmpty()) {
			return null;
		}
		
		return this.getAttributesFrom(component).get(id);
	}

	@Override
	public String getAsString(FacesContext context, UIComponent component, Object entidadeObjeto) {
		if (entidadeObjeto == null) 
			return null;
		
		Entidade entidade = (Entidade) entidadeObjeto;
		
		this.addAttribute(component, entidade);
		return entidade.getIdEntidadeCjur().toString();
	}
	
	protected void addAttribute(UIComponent component, Entidade entidade) {
		String key = entidade.getIdEntidadeCjur().toString();
		this.getAttributesFrom(component).put(key,entidade);
	}

	protected Map<String, Object> getAttributesFrom(UIComponent component) {
		return component.getAttributes();
	}

}
