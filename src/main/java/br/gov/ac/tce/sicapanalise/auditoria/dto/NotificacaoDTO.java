package br.gov.ac.tce.sicapanalise.auditoria.dto;

import java.io.Serializable;
import java.time.LocalDateTime;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.acumulacao.SituacaoBeneficiario;


public class NotificacaoDTO implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 5580771751272452201L;
	
	private Long id;
	private Long idAnalise;
	private Long idAcumulacao;
	private Long idSolicitacaoDocumento;
	
	private LocalDateTime dataSolicitacao;
	private LocalDateTime dataResposta;
	private LocalDateTime dataPrazoResposta;

	private String nomeEntidade;
	private String mensagem;
	private String assunto;
	private String situacaoBeneficiario;
	
	
	
	
	public NotificacaoDTO(Long id, Long idAnalise, Long idAcumulacao, Long idSolicitacaoDocumento, LocalDateTime dataSolicitacao,
			LocalDateTime dataResposta, LocalDateTime dataPrazoResposta, String nomeEntidade, String mensagem,
			String assunto, String situacaoBeneficiario) {
		super();
		this.id = id;
		this.idAnalise = idAnalise;
		this.idAcumulacao = idAcumulacao;
		this.idSolicitacaoDocumento = idSolicitacaoDocumento;
		this.dataSolicitacao = dataSolicitacao;
		this.dataResposta = dataResposta;
		this.dataPrazoResposta = dataPrazoResposta;
		this.nomeEntidade = nomeEntidade;
		this.mensagem = mensagem;
		this.assunto = assunto;
		this.situacaoBeneficiario = situacaoBeneficiario;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getIdAnalise() {
		return idAnalise;
	}
	public void setIdAnalise(Long idAnalise) {
		this.idAnalise = idAnalise;
	}
	public Long getIdAcumulacao() {
		return idAcumulacao;
	}
	public void setIdAcumulacao(Long idAcumulacao) {
		this.idAcumulacao = idAcumulacao;
	}

	public String getNomeEntidade() {
		return nomeEntidade;
	}
	public void setNomeEntidade(String nomeEntidade) {
		this.nomeEntidade = nomeEntidade;
	}
	public String getMensagem() {
		return mensagem;
	}
	public void setMensagem(String mensagem) {
		this.mensagem = mensagem;
	}
	public String getAssunto() {
		return assunto;
	}
	public void setAssunto(String assunto) {
		this.assunto = assunto;
	}
	public String getSituacaoBeneficiario() {
		if (this.situacaoBeneficiario == null || this.situacaoBeneficiario.isEmpty()) 
			return "";
		return SituacaoBeneficiario.valueOf(this.situacaoBeneficiario).getDescricao();
	}

	public void setSituacaoBeneficiario(String situacaoBeneficiario) {
		this.situacaoBeneficiario = situacaoBeneficiario;
	}
	public LocalDateTime getDataSolicitacao() {
		return dataSolicitacao;
	}
	public void setDataSolicitacao(LocalDateTime dataSolicitacao) {
		this.dataSolicitacao = dataSolicitacao;
	}
	public LocalDateTime getDataResposta() {
		return dataResposta;
	}
	public void setDataResposta(LocalDateTime dataResposta) {
		this.dataResposta = dataResposta;
	}
	public LocalDateTime getDataPrazoResposta() {
		return dataPrazoResposta;
	}
	public void setDataPrazoResposta(LocalDateTime dataPrazoResposta) {
		this.dataPrazoResposta = dataPrazoResposta;
	}
	public Long getIdSolicitacaoDocumento() {
		return idSolicitacaoDocumento;
	}
	public void setIdSolicitacaoDocumento(Long idSolicitacaoDocumento) {
		this.idSolicitacaoDocumento = idSolicitacaoDocumento;
	}
	
	public String situacaoNotificacao() {
		if (dataResposta == null && LocalDateTime.now().isAfter(dataPrazoResposta))
			return "vencida";
		return null;
	}
	
}
