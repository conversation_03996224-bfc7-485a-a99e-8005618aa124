package br.gov.ac.tce.sicapanalise.auditoria.business;

import java.io.UnsupportedEncodingException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import javax.ejb.Stateless;
import javax.inject.Inject;

import org.primefaces.model.UploadedFile;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.acumulacao.Acumulacao;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.acumulacao.AnaliseAcumulacao;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise.Analise;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise.HistoricoAnalise;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise.Relatorio;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise.RelatorioAnalise;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise.ResultadoAnalise;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise.TipoAnalise;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise.TipoDocumento;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise.TipoProcedimentoAnalise;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise.TipoResultadoAnalise;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise.TipoSituacaoAnalise;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.responsavel.Usuario;
import br.gov.ac.tce.sicapanalise.auditoria.dto.AcumulacaoAnaliseDTO;
import br.gov.ac.tce.sicapanalise.auditoria.dto.DocumentoAuditoriaDTO;
import br.gov.ac.tce.sicapanalise.auditoria.infra.ArquivoSaver;
import br.gov.ac.tce.sicapanalise.auditoria.repositorio.AcumulacaoRepositorio;
import br.gov.ac.tce.sicapanalise.auditoria.repositorio.AnaliseRepositorio;
import br.gov.ac.tce.sicapanalise.auditoria.repositorio.RelatorioRepositorio;


@Stateless
public class AnaliseBusiness {

	@Inject
	private AnaliseRepositorio analiseRepositorio;

	@Inject
	private AcumulacaoRepositorio acumulacaoRepositorio;

	@Inject
	private RelatorioRepositorio relatorioRepositorio;
	
	
	public Analise retornaAnalisePorId(Long id) {
		return analiseRepositorio.retornaAnalisePorId(id);
	}
	
	public AnaliseAcumulacao retornaAnaliseAcumulacaoPorId(Long id) {
		return analiseRepositorio.retornaAnaliseAcumulacaoPorId(id);
	}
	
	public List<AcumulacaoAnaliseDTO> retornaListaAnaliseAcumulacao(TipoSituacaoAnalise tipoSituacaoAnalise, Usuario usuarioAuditor){
		return analiseRepositorio.listaAnaliseAcumulacao(tipoSituacaoAnalise,usuarioAuditor);
	}

	public Collection<TipoSituacaoAnalise> retornaTiposSituacaoAnalise() {
		return analiseRepositorio.listaTipoSituacaoAnalise();
	}

	public Collection<TipoSituacaoAnalise> retornaTiposSituacaoAnaliseTodas() {
		return analiseRepositorio.listaTipoSituacaoAnaliseTodas();
	}

	public Collection<TipoResultadoAnalise> retornaTiposResultadoAnalise() {
		return analiseRepositorio.listaTipoResultadoAnalise();
	}

//	public Collection<AnaliseAcumulacaoDTO> retornaAnalisesDeAcumulacoes() {
//		return analiseRepositorio.listaAnaliseAcumulacaoDTO();
//	}
//
//	public Collection<AnaliseAcumulacaoDTO> retornaAnalisesDeAcumulacoes(TipoSituacaoAnalise tipoSituacaoAnalise) {
//		return analiseRepositorio.listaAnaliseAcumulacaoDTOPorSituacao(tipoSituacaoAnalise);
//	}
	


	public Collection<DocumentoAuditoriaDTO> retornaAnaliseDocumentos(Long idAnalise) {
		return analiseRepositorio.listaAnaliseDocumentos(idAnalise);
	}
	
	public void atualizarProcesso(Long idAnalise, String numeroProcesso) {
		Analise analise = analiseRepositorio.retornaAnalisePorId(idAnalise);
		analise.setNumeroProcessoEletronico(numeroProcesso);
		analiseRepositorio.atualizar(analise);
	}
	
	public List<AnaliseAcumulacao> iniciarAnalise(Collection<Acumulacao> acumulacoes, String despacho, Usuario usuario) {
		
		List<AnaliseAcumulacao> listaAnalisesAcumulacoes = new ArrayList<AnaliseAcumulacao>();

		try {
			TipoAnalise tipoAnalise = analiseRepositorio.retornaTipoAnalise(1L);
			
			TipoProcedimentoAnalise tipoProcedimentoAnalise = analiseRepositorio.retornaTipoProcedimentoAnalise(1L);

			TipoSituacaoAnalise tipoSituacaoAnalise = analiseRepositorio.retornaTipoSituacaoAnalise(3L);

			for (Acumulacao acumulacao : acumulacoes) {

				Analise analise = new Analise();
				analise.setDataCriacao(LocalDateTime.now());
				analise.setUsuario(usuario);
				analise.setUsuarioResponsavel(usuario.getNome());
				analise.setSituacao(tipoSituacaoAnalise.getCodigo());
				analise.setSituacaoAnalise(tipoSituacaoAnalise);
				analise.setTipoAnalise(tipoAnalise);
				analiseRepositorio.inserir(analise);

				HistoricoAnalise historicoAnalise = new HistoricoAnalise();
				historicoAnalise.setAnalise(analise);
				historicoAnalise.setData(LocalDateTime.now());
				historicoAnalise.setUsuario(usuario);
				historicoAnalise.setDespacho(despacho);
				historicoAnalise.setProcedimentoRealizado(tipoProcedimentoAnalise);
				analiseRepositorio.inserirHistorico(historicoAnalise);

				AnaliseAcumulacao analiseAcumulacao = new AnaliseAcumulacao();
				analiseAcumulacao.setDataCriacao(LocalDateTime.now());
				analiseAcumulacao.setAcumulacao(acumulacao);
				analiseAcumulacao.setAnalise(analise);
				analiseRepositorio.inserirAnaliseAcumulacao(analiseAcumulacao);

				acumulacao.setSituacao(tipoSituacaoAnalise.getCodigo());
				acumulacaoRepositorio.atualizar(acumulacao);
				
				
				listaAnalisesAcumulacoes.add(analiseAcumulacao);
			}
		} catch (Exception e) {
			throw new RuntimeException(e.getMessage());
		}
		
		
		
		return listaAnalisesAcumulacoes;

	}


	public void concluirAnalise(ResultadoAnalise resultadoAnalise, TipoSituacaoAnalise tipoSituacaoAnalise, /*Part*/UploadedFile arqPart,
			Collection<AcumulacaoAnaliseDTO> acumulacoesAnalisesDTO) {

		Relatorio relatorio = null;
		Usuario usuario  = resultadoAnalise.getUsuario();

		// verifica se possui anexo
		if (arqPart != null) {

			String arquivoNome = "";
			
			try {
				arquivoNome = new String(arqPart.getFileName().getBytes("ISO-8859-1"),"UTF-8");
			} catch (UnsupportedEncodingException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			
			
			ArquivoSaver arquivoSaver = new ArquivoSaver();


			String arqDir = "relatorios/" + usuario.getId().toString();
			arquivoSaver.salvar(arqDir,arquivoNome, arqPart);

			TipoDocumento tipoDocumento = new TipoDocumento();
			tipoDocumento.setId(11L); // Relatório de Auditoria

			relatorio = new Relatorio();
			

			relatorio.setArquivoNome(arquivoNome);
			relatorio.setData(LocalDateTime.now());
			relatorio.setUsuario(usuario);
			relatorio.setTipoDocumento(tipoDocumento);
			relatorio.setAtivo(true);
			relatorio.setArquivoCaminho(arqDir);

			relatorioRepositorio.salvar(relatorio);

		}

		TipoProcedimentoAnalise tpa = analiseRepositorio.retornaTipoProcedimentoAnalise(3L);

		
		List<Long> analiseAcumulacaoIds = acumulacoesAnalisesDTO.stream().map(aa -> aa.getAcumulacaoAnaliseId()).collect(Collectors.toList());
		List<AnaliseAcumulacao> analisesAcumulacao = analiseRepositorio.listaAnaliseAcumulacao(analiseAcumulacaoIds);
		// filtrar somente analises nao concluidas
		for (AnaliseAcumulacao aa : analisesAcumulacao) {

			
			Analise analise = analiseRepositorio.retornaAnalisePorId(aa.getAnalise().getId());
			analise.setSituacao(tipoSituacaoAnalise.getCodigo());
			analise.setSituacaoAnalise(tipoSituacaoAnalise);
			analiseRepositorio.atualizar(analise);
			
			if (relatorio != null) {
				RelatorioAnalise relatorioAnalise = new RelatorioAnalise();
				relatorioAnalise.setAnalise(analise);
				relatorioAnalise.setRelatorio(relatorio);
				analiseRepositorio.inserirRelatorio(relatorioAnalise);
			}
			
			HistoricoAnalise ha = new HistoricoAnalise();
			ha.setAnalise(analise);
			ha.setData(LocalDateTime.now());
			ha.setDespacho(resultadoAnalise.getDescricao());
			ha.setUsuario(usuario);
			ha.setProcedimentoRealizado(tpa);
			analiseRepositorio.inserirHistorico(ha);

			ResultadoAnalise resultado = new ResultadoAnalise();
			resultado.setAnalise(analise);
			resultado.setData(LocalDateTime.now());
			resultado.setDescricao(resultadoAnalise.getDescricao());
			resultado.setTipoResultadoAnalise(resultadoAnalise.getTipoResultadoAnalise());
			resultado.setUsuario(usuario);
			analiseRepositorio.inserirResultado(resultado);

			Acumulacao acumulacao = aa.getAcumulacao();
			acumulacao.setSituacao(tipoSituacaoAnalise.getCodigo());
			acumulacaoRepositorio.atualizar(acumulacao);

		}

	}

	public Collection<HistoricoAnalise> retornaHistoricoAnalise(Long idAnalise) {
		return analiseRepositorio.retornaHistoricoAnalise(idAnalise);
	}
}
