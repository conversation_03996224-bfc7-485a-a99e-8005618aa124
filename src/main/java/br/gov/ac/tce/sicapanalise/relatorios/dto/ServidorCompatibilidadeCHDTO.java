package br.gov.ac.tce.sicapanalise.relatorios.dto;

import java.io.Serializable;
import java.util.List;

public class ServidorCompatibilidadeCHDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String cpf;
    private String nome;
    private Integer mes;
    private Integer ano;
    private Integer cargaHorariaTotal;
    private Integer cargaHorariaSemanal;
    private String statusCompatibilidade;
    private List<VinculoCompatibilidadeCHDTO> vinculos;

    public ServidorCompatibilidadeCHDTO() {
    }

    public ServidorCompatibilidadeCHDTO(String cpf, String nome, Integer mes, Integer ano, 
                                       Integer cargaHorariaTotal, Integer cargaHorariaSemanal, 
                                       String statusCompatibilidade) {
        this.cpf = cpf;
        this.nome = nome;
        this.mes = mes;
        this.ano = ano;
        this.cargaHorariaTotal = cargaHorariaTotal;
        this.cargaHorariaSemanal = cargaHorariaSemanal;
        this.statusCompatibilidade = statusCompatibilidade;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getMes() {
        return mes;
    }

    public void setMes(Integer mes) {
        this.mes = mes;
    }

    public Integer getAno() {
        return ano;
    }

    public void setAno(Integer ano) {
        this.ano = ano;
    }

    public Integer getCargaHorariaTotal() {
        return cargaHorariaTotal;
    }

    public void setCargaHorariaTotal(Integer cargaHorariaTotal) {
        this.cargaHorariaTotal = cargaHorariaTotal;
    }

    public Integer getCargaHorariaSemanal() {
        return cargaHorariaSemanal;
    }

    public void setCargaHorariaSemanal(Integer cargaHorariaSemanal) {
        this.cargaHorariaSemanal = cargaHorariaSemanal;
    }

    public String getStatusCompatibilidade() {
        return statusCompatibilidade;
    }

    public void setStatusCompatibilidade(String statusCompatibilidade) {
        this.statusCompatibilidade = statusCompatibilidade;
    }

    public List<VinculoCompatibilidadeCHDTO> getVinculos() {
        return vinculos;
    }

    public void setVinculos(List<VinculoCompatibilidadeCHDTO> vinculos) {
        this.vinculos = vinculos;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((cpf == null) ? 0 : cpf.hashCode());
        result = prime * result + ((mes == null) ? 0 : mes.hashCode());
        result = prime * result + ((ano == null) ? 0 : ano.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        ServidorCompatibilidadeCHDTO other = (ServidorCompatibilidadeCHDTO) obj;
        if (cpf == null) {
            if (other.cpf != null)
                return false;
        } else if (!cpf.equals(other.cpf))
            return false;
        if (mes == null) {
            if (other.mes != null)
                return false;
        } else if (!mes.equals(other.mes))
            return false;
        if (ano == null) {
            if (other.ano != null)
                return false;
        } else if (!ano.equals(other.ano))
            return false;
        return true;
    }
}
