package br.gov.ac.tce.sicapanalise.controle.bean;

import java.io.Serializable;
import java.util.Collection;

import javax.annotation.PostConstruct;
import javax.faces.view.ViewScoped;
import javax.inject.Inject;
import javax.inject.Named;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.trilha.HistoricoProcessamentoTrilha;
import br.gov.ac.tce.sicap.modelo.repositorio.RepositorioException;
import br.gov.ac.tce.sicap.modelo.repositorio.TrilhaProcessamentoRepositorio;
import br.gov.ac.tce.sicapanalise.util.Mensagem;
import br.gov.ac.tce.sicapanalise.util.MensagemType;

@Named
@ViewScoped
public class TrilhaProcessamentoAcumulacaoBean implements Serializable {

	private static final long serialVersionUID = 1L;

//	@Inject
//	private LoginBean loginBean;
	@Inject
	private TrilhaProcessamentoRepositorio trilhaProcessamentoRepositorio;

	private Collection<HistoricoProcessamentoTrilha> listaHistoricoProcessamentoTrilha;

	private int currentLevel;

	@PostConstruct
	public void init() {
		try {
			this.currentLevel = 1;
			this.listaHistoricoProcessamentoTrilha = this.trilhaProcessamentoRepositorio.listarTodas2();
		} catch (RepositorioException e) {
			Mensagem.setMensagem(MensagemType.ERRO, "Ocorreu um erro ao pesquisar dados iniciais para esta página.",
					"");
		}
	}

	public int getCurrentLevel() {
		return currentLevel;
	}

	public void setCurrentLevel(int currentLevel) {
		this.currentLevel = currentLevel;
	}

	public Collection<HistoricoProcessamentoTrilha> getListaHistoricoProcessamentoTrilha() {
		return listaHistoricoProcessamentoTrilha;
	}

	public void setListaHistoricoProcessamentoTrilha(
			Collection<HistoricoProcessamentoTrilha> listaHistoricoProcessamentoTrilha) {
		this.listaHistoricoProcessamentoTrilha = listaHistoricoProcessamentoTrilha;
	}
}
