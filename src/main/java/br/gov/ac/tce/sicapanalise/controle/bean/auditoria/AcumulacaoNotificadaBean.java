package br.gov.ac.tce.sicapanalise.controle.bean.auditoria;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

import javax.annotation.PostConstruct;
import javax.faces.view.ViewScoped;
import javax.inject.Inject;
import javax.inject.Named;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.acumulacao.AnaliseAcumulacao;
import br.gov.ac.tce.sicapanalise.auditoria.repositorio.AcumulacaoRepositorio;

@Named
@ViewScoped
public class AcumulacaoNotificadaBean implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -480427785327551315L;
	
	@Inject
	private AcumulacaoRepositorio acumulacaoRepositorio;
	
	private Collection<AnaliseAcumulacao> acumulacoesNotificadas;
	private List<AnaliseAcumulacao> acumulacoesSelecionadas;
	
	private int currentLevel = 1;
	
	@PostConstruct
	void inicializado() {
		this.acumulacoesNotificadas = acumulacaoRepositorio.listaAcumulacoesNotificadas();
	}
	

	public Collection<AnaliseAcumulacao> getAcumulacoesNotificadas() {
		return acumulacoesNotificadas;
	}


	public void setAcumulacoesNotificadas(Collection<AnaliseAcumulacao> acumulacoesNotificadas) {
		this.acumulacoesNotificadas = acumulacoesNotificadas;
	}


	public List<AnaliseAcumulacao> getAcumulacoesSelecionadas() {
		return acumulacoesSelecionadas;
	}
	public void setAcumulacoesSelecionadas(List<AnaliseAcumulacao> acumulacoesSelecionadas) {
		this.acumulacoesSelecionadas = acumulacoesSelecionadas;
	}

	public int getCurrentLevel() {
		return currentLevel;
	}

	public void setCurrentLevel(int currentLevel) {
		this.currentLevel = currentLevel;
	}
	
	
	
	
	

}
