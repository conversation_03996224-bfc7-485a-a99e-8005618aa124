package br.gov.ac.tce.sicapanalise.auditoria.business;

import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.PostConstruct;
import javax.ejb.Stateless;
import javax.inject.Inject;
import javax.ws.rs.client.Client;
import javax.ws.rs.client.ClientBuilder;
import javax.ws.rs.client.Entity;
import javax.ws.rs.client.Invocation.Builder;
import javax.ws.rs.core.Form;
import javax.ws.rs.core.MediaType;

import com.google.gson.Gson;

import br.gov.ac.tce.sicap.modelo.entidade.Configuracao;
import br.gov.ac.tce.sicapanalise.auditoria.dto.DocumentoAuditoriaDTO;
import br.gov.ac.tce.sicapanalise.auditoria.infra.ArquivoSaver;
import br.gov.ac.tce.sicapanalise.auditoria.processo.ArquivoWS;
import br.gov.ac.tce.sicapanalise.auditoria.processo.ArquivoWSContainer;
import br.gov.ac.tce.sicapanalise.auditoria.processo.ListaStringContainer;
import br.gov.ac.tce.sicapanalise.auditoria.processo.ProcessoSimplesPrestacao;
import br.gov.ac.tce.sicapanalise.auditoria.processo.Protocolo;
import br.gov.ac.tce.sicapanalise.auditoria.processo.RespostaProcessoWS;
import br.gov.ac.tce.sicapanalise.repositorio.ConfiguracaoRepositorio;
import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;
import br.gov.ac.tce.sicapweb.repositorio.EntidadeRepositorio;

@Stateless
public class ProtocoloBusiness {

	private final String URL_SERVICO_CRIAR_PROTOCOLO_PESSOAS_MULTIPLO_ARQUIVOS = "/rest/processos/criarProtocoloRemessaResponsaveisMultiplosArquivos";

	private final String URL_SERVICO_GET_NM_TP_PROCESSO = "/rest/tipoProcesso/";

	private final String URL_SERVICO_GET_NM_TP_DOCUMENTO = "/rest/tipoDocumento/";
	
	private String urlEprocess;

//	private final String URL_SERVICO_GET_NM_ASSUNTO = "/rest/assunto/";
//
//	private final String URL_SERVICO_GET_NM_CLASSE = "/rest/classe/";

	@Inject
	private ConfiguracaoRepositorio configuracaoRepositorio;
	
	@Inject
	private EntidadeRepositorio entidadeRepositorio;

	@Inject
	private AnaliseBusiness analiseBusiness;
	
	@PostConstruct
	void init() {
		List<Configuracao> configuracoes = configuracaoRepositorio.findAllConfiguracao();
		urlEprocess = configuracoes.stream().filter(c -> c.getChave().equals("URL_EPROCESS"))
				.map(Configuracao::getValor).findAny().orElse("");
	}
	

	public List<String> listarTipoProcesso() {
		String target = urlEprocess + URL_SERVICO_GET_NM_TP_PROCESSO;
		return consomeServico(target);
	}

	public List<String> listarTipoDocumento() {
		String target = urlEprocess + URL_SERVICO_GET_NM_TP_DOCUMENTO;
		return consomeServico(target);
	}
//	
//	public List<String> listarAssunto(){
//		String target = URL_EPROCESS + URL_SERVICO_GET_NM_ASSUNTO;
//		return consomeServico(target);
//	}
//	
//	public List<String> listarClasse(){
//		String target = URL_EPROCESS + URL_SERVICO_GET_NM_CLASSE;
//		return consomeServico(target);
//	}

	private List<String> consomeServico(String target) {
		List<String> lista = new ArrayList<>();
		try {
			Client client = ClientBuilder.newClient();
			Builder request = client.target(target).request();
			RespostaProcessoWS response = request.accept(MediaType.APPLICATION_JSON).get(RespostaProcessoWS.class);

			ListaStringContainer listaStringContainer = new Gson().fromJson(response.getObject(),
					ListaStringContainer.class);
			lista = listaStringContainer.getNomes();

		} catch (Exception e) {
			e.printStackTrace();
		}
		return lista;
	}

	public RespostaProcessoWS gerarProtocolo(Protocolo protocolo, Long idAnalise, List<DocumentoAuditoriaDTO> documentos) {
		RespostaProcessoWS resposta = null;
		try {

			Gson gson = new Gson();

			Form form = new Form();
			form.param("cpfUsuarioSistema", "123.456.789-09");

			
			ArquivoWSContainer arquivosPCA = criaArquivoContainer(documentos);
			copiaArquivosDocumentosPCA(arquivosPCA);
			
			String arquivos = gson.toJson(arquivosPCA);
			
			form.param("arquivosPCA", arquivos);
			form.param("exercicio", protocolo.getExercicio().toString());

			String responsaveis = gson.toJson(protocolo.getResponsaveisContainer());
			String interessado = gson.toJson(protocolo.getInteressado());

			form.param("responsaveis", encodeString(responsaveis));
			form.param("interessado", encodeString(interessado));
			form.param("idCjurEnte", protocolo.getEnte().getId().toString());
			form.param("idCjurEntidade", protocolo.getEntidade().getIdEntidadeCjur().toString());

			if (protocolo.getNumRemessa() != null && protocolo.getNumRemessa() > 0) {
				form.param("numRemessa", protocolo.getNumRemessa().toString());
			} else {
				form.param("numRemessa", "0");
			}

			form.param("assunto", encodeString(protocolo.getAssunto()));
			form.param("classe", encodeString(protocolo.getClasse()));
			form.param("objeto", encodeString(protocolo.getObjeto()));
			form.param("nomeEnte", encodeString(protocolo.getEnte().getDescricao()));
			form.param("nomeEntidade", encodeString(protocolo.getEntidade().getNome()));
			form.param("nomeTipoProcesso", encodeString(protocolo.getNomeTipoProcesso()));
			form.param("tipoDocumento", encodeString(protocolo.getTipoDocumento()));

			Client client = ClientBuilder.newClient();
			String target = urlEprocess + URL_SERVICO_CRIAR_PROTOCOLO_PESSOAS_MULTIPLO_ARQUIVOS;


			resposta = client.target(target).request().post(Entity.form(form), RespostaProcessoWS.class);

			if (resposta.getStatus().equals("RESPONSE")) {
				ProcessoSimplesPrestacao processo = gson.fromJson(resposta.getObject(), ProcessoSimplesPrestacao.class);
				analiseBusiness.atualizarProcesso(idAnalise, processo.getNumeroProcesso());
			}

		} catch (Exception e) {
			System.out.println(e.getMessage());

		}
		return resposta;

	}

	private void copiaArquivosDocumentosPCA(ArquivoWSContainer arquivoContainer) {
		// Copiar arquivos para a pasta acessivel pelo eProcess
		// Codifica o nome do arquivo caso haja caracter especial
		ArquivoSaver arqSaver = new ArquivoSaver();
		for (ArquivoWS arquivo : arquivoContainer.getArquivos()) {
			String arqPCA = arqSaver.salvarDocumentosPCA(arquivo.getUrlArquivo(), arquivo.getNome());
			arquivo.setUrlArquivo("/sicap/" + arquivo.getUrlArquivo());
			arquivo.setNome(arqPCA);

		}
	}

	private String encodeString(String texto) {
		String strEncoded = null;
		try {
			strEncoded = String.format("%040x", new BigInteger(1, texto.getBytes("ISO-8859-1")));
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		return strEncoded;
	}

	private ArquivoWSContainer criaArquivoContainer(List<DocumentoAuditoriaDTO> documentos) {
		ArquivoWSContainer arquivoContainer = new ArquivoWSContainer();

		try {
			
			for (int i = 0; i < documentos.size(); i++) {

				DocumentoAuditoriaDTO da = documentos.get(i);

				ArquivoWS anexo = new ArquivoWS();
				anexo.setUrlArquivo(da.getCaminhoDocumento());
				anexo.setNome(da.getNomeDocumento());
				anexo.setTipoArquivo("application/pdf");
				anexo.setOrdem(i + 1);
				
				anexo.setTipoDocumento(encodeString(da.getNomeTipoDocumento()));

				arquivoContainer.getArquivos().add(anexo);

			}
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
		return arquivoContainer;
	}

	public List<Entidade> retornaEntidades() {
		return entidadeRepositorio.lista();
	}

}
