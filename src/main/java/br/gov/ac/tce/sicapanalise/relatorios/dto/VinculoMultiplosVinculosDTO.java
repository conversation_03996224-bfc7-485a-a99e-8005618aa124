package br.gov.ac.tce.sicapanalise.relatorios.dto;

import java.io.Serializable;
import java.math.BigDecimal;

public class VinculoMultiplosVinculosDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String entidade;
    private String municipio;
    private String matricula;
    private String cargo;
    private String tipoCargo;
    private Integer cargaHorariaMensal;
    private BigDecimal proventos;

    public VinculoMultiplosVinculosDTO() {
    }

    public VinculoMultiplosVinculosDTO(String entidade, String municipio, String matricula, 
                                      String cargo, String tipoCargo, Integer cargaHorariaMensal, 
                                      BigDecimal proventos) {
        this.entidade = entidade;
        this.municipio = municipio;
        this.matricula = matricula;
        this.cargo = cargo;
        this.tipoCargo = tipoCargo;
        this.cargaHorariaMensal = cargaHorariaMensal;
        this.proventos = proventos;
    }

    public String getEntidade() {
        return entidade;
    }

    public void setEntidade(String entidade) {
        this.entidade = entidade;
    }

    public String getMunicipio() {
        return municipio;
    }

    public void setMunicipio(String municipio) {
        this.municipio = municipio;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getCargo() {
        return cargo;
    }

    public void setCargo(String cargo) {
        this.cargo = cargo;
    }

    public String getTipoCargo() {
        return tipoCargo;
    }

    public void setTipoCargo(String tipoCargo) {
        this.tipoCargo = tipoCargo;
    }

    public Integer getCargaHorariaMensal() {
        return cargaHorariaMensal;
    }

    public void setCargaHorariaMensal(Integer cargaHorariaMensal) {
        this.cargaHorariaMensal = cargaHorariaMensal;
    }

    public BigDecimal getProventos() {
        return proventos;
    }

    public void setProventos(BigDecimal proventos) {
        this.proventos = proventos;
    }
}
