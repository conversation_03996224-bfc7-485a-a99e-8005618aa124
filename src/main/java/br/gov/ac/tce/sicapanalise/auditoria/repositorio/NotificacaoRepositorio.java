package br.gov.ac.tce.sicapanalise.auditoria.repositorio;

import java.io.Serializable;
import java.util.Collection;

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise.DetalhamentoSolicitacaoDocumento;
import br.gov.ac.tce.sicap.modelo.entidade.notificacao.Notificacao;
import br.gov.ac.tce.sicapanalise.auditoria.dto.NotificacaoDTO;

@Stateless
public class NotificacaoRepositorio implements Serializable {

	private static final long serialVersionUID = 3309905704685019535L;

	@PersistenceContext(unitName = "sicapAnalise")
	private EntityManager entityManager;
	
	public void salvar(Notificacao notificacao) {
		entityManager.persist(notificacao);
	}

	public Collection<DetalhamentoSolicitacaoDocumento> listaDetalhamentoSolicitacaoDocumentos(
			Long idSolicitacaoDocumento) {
		Collection<DetalhamentoSolicitacaoDocumento> listaDetalhamentoSolicitacaoDocumentos = null;
		String jpql = "SELECT dsd FROM DetalhamentoSolicitacaoDocumento dsd JOIN FETCH dsd.tipoDocumento where dsd.solicitacaoDocumento.id = :pId";
		try {
			listaDetalhamentoSolicitacaoDocumentos = this.entityManager
					.createQuery(jpql, DetalhamentoSolicitacaoDocumento.class)
					.setParameter("pId", idSolicitacaoDocumento).getResultList();
		} catch (Exception e) {
			e.printStackTrace();
			System.out.println(e.getMessage());
		}

		return listaDetalhamentoSolicitacaoDocumentos;
	}

	public Collection<NotificacaoDTO> listaNotificacoes() {
		Collection<NotificacaoDTO> listaNotificacoes = null;
		String sql = "SELECT n.id, n.idanalise, aa.idAcumulacao, sd.id AS idSolicitacaoDocumento, sd.datasolicitacao, " +
				"n.dataresposta, sd.dataprazoresposta, e.nome AS nomeEntidade, n.assunto, n.mensagem, sda.situacaoAtualBeneficiario " + 
				"FROM notificacao n " +
				"INNER JOIN auditoria.AnaliseAcumulacao aa ON aa.idAnalise = n.idAnalise " + 
				"INNER JOIN entidade e ON e.identidadecjur = n.identidadecjur " +
				"INNER JOIN auditoria.solicitacaodocumento sd ON sd.id = n.idsolicitacaodocumento " + 
				"INNER JOIN auditoria.solicitacaodocumentoacumulacao sda ON sda.idsolicitacaodocumento = sd.id order by aa.id desc ";
		try {
			listaNotificacoes = entityManager.createNativeQuery(sql, "NotificacaoDTOMapping").getResultList();
		} catch (Exception e) {
			e.printStackTrace();
			System.out.println(e.getMessage());
		}

		return listaNotificacoes;
	}

}
