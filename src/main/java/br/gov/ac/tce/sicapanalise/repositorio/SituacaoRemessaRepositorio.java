package br.gov.ac.tce.sicapanalise.repositorio;

import java.io.Serializable;
import java.util.Collection;

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;

import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;

@Stateless
public class SituacaoRemessaRepositorio implements Serializable {

	private static final long serialVersionUID = 1L;

	@PersistenceContext(unitName = "sicapAnalise")
	private EntityManager em;

	@SuppressWarnings("unchecked")
	public Collection<Object[]> consultaSituacaoRemessa(Entidade entidade, Integer ano) throws RepositorioException {
		Collection<Object[]> listaSituacaoRemessa = null;
		try {
			if (entidade == null) {
				Query query = this.em
						.createNativeQuery("select * from auditoria.fn_situacao_remessas(?) ORDER BY entidade");

				query.setParameter(1, ano);
				listaSituacaoRemessa = (Collection<Object[]>) query.getResultList();
			} else {
				Query query = this.em
						.createNativeQuery("select * from auditoria.fn_situacao_remessas(?) where codigo_cjur = ?");

				query.setParameter(1, ano);
				query.setParameter(2, entidade.getIdEntidadeCjur());
				listaSituacaoRemessa = (Collection<Object[]>) query.getResultList();
			}
		} catch (Exception e) {
			e.printStackTrace();
			throw new RepositorioException("Erro ao consultaSituacaoRemessa.", e.getCause());
		}
		return listaSituacaoRemessa;
	}

}
