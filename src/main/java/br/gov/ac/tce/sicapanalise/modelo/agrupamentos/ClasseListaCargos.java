package br.gov.ac.tce.sicapanalise.modelo.agrupamentos;

import java.io.Serializable;
import java.time.LocalDateTime;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;

@Entity
@Table(schema = "auditoria", uniqueConstraints = { @UniqueConstraint(name = "ClasseListaCargos_unique", columnNames = {
		"codigoCargo", "idEntidadeCjur", "idClasseCargos", "idSubClasseCargos" }) })
public class ClasseListaCargos implements Serializable {

	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;
	@Column(nullable = false)
	private Long codigoCargo;
	@ManyToOne
	@JoinColumn(name = "idEntidadeCjur", nullable = false)
	private Entidade entidade;
	@ManyToOne
	@JoinColumn(name = "idClasseCargos", nullable = false)
	private ClasseCargos classeCargos;
	@ManyToOne
	@JoinColumn(name = "idSubClasseCargos", nullable = false)
	private SubClasseCargos subClasseCargos;
	@Column(nullable = false)
	private Long idUsuario;
	@Column(nullable = false)
	private LocalDateTime dataCadastro;
	@Column(nullable = false)
	private Boolean registroAtivo;
	private LocalDateTime dataInativacao;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getCodigoCargo() {
		return codigoCargo;
	}

	public void setCodigoCargo(Long codigoCargo) {
		this.codigoCargo = codigoCargo;
	}

	public Entidade getEntidade() {
		return entidade;
	}

	public void setEntidade(Entidade entidade) {
		this.entidade = entidade;
	}

	public ClasseCargos getClasseCargos() {
		return classeCargos;
	}

	public void setClasseCargos(ClasseCargos classeCargos) {
		this.classeCargos = classeCargos;
	}

	public SubClasseCargos getSubClasseCargos() {
		return subClasseCargos;
	}

	public void setSubClasseCargos(SubClasseCargos subClasseCargos) {
		this.subClasseCargos = subClasseCargos;
	}

	public Long getIdUsuario() {
		return idUsuario;
	}

	public void setIdUsuario(Long idUsuario) {
		this.idUsuario = idUsuario;
	}

	public LocalDateTime getDataCadastro() {
		return dataCadastro;
	}

	public void setDataCadastro(LocalDateTime dataCadastro) {
		this.dataCadastro = dataCadastro;
	}

	public Boolean getRegistroAtivo() {
		return registroAtivo;
	}

	public void setRegistroAtivo(Boolean registroAtivo) {
		this.registroAtivo = registroAtivo;
	}

	public LocalDateTime getDataInativacao() {
		return dataInativacao;
	}

	public void setDataInativacao(LocalDateTime dataInativacao) {
		this.dataInativacao = dataInativacao;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((classeCargos == null) ? 0 : classeCargos.hashCode());
		result = prime * result + ((codigoCargo == null) ? 0 : codigoCargo.hashCode());
		result = prime * result + ((dataCadastro == null) ? 0 : dataCadastro.hashCode());
		result = prime * result + ((dataInativacao == null) ? 0 : dataInativacao.hashCode());
		result = prime * result + ((entidade == null) ? 0 : entidade.hashCode());
		result = prime * result + ((idUsuario == null) ? 0 : idUsuario.hashCode());
		result = prime * result + ((registroAtivo == null) ? 0 : registroAtivo.hashCode());
		result = prime * result + ((subClasseCargos == null) ? 0 : subClasseCargos.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ClasseListaCargos other = (ClasseListaCargos) obj;
		if (classeCargos == null) {
			if (other.classeCargos != null)
				return false;
		} else if (!classeCargos.equals(other.classeCargos))
			return false;
		if (codigoCargo == null) {
			if (other.codigoCargo != null)
				return false;
		} else if (!codigoCargo.equals(other.codigoCargo))
			return false;
		if (dataCadastro == null) {
			if (other.dataCadastro != null)
				return false;
		} else if (!dataCadastro.equals(other.dataCadastro))
			return false;
		if (dataInativacao == null) {
			if (other.dataInativacao != null)
				return false;
		} else if (!dataInativacao.equals(other.dataInativacao))
			return false;
		if (entidade == null) {
			if (other.entidade != null)
				return false;
		} else if (!entidade.equals(other.entidade))
			return false;
		if (idUsuario == null) {
			if (other.idUsuario != null)
				return false;
		} else if (!idUsuario.equals(other.idUsuario))
			return false;
		if (registroAtivo == null) {
			if (other.registroAtivo != null)
				return false;
		} else if (!registroAtivo.equals(other.registroAtivo))
			return false;
		if (subClasseCargos == null) {
			if (other.subClasseCargos != null)
				return false;
		} else if (!subClasseCargos.equals(other.subClasseCargos))
			return false;
		return true;
	}

}
