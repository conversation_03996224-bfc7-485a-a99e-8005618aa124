package br.gov.ac.tce.sicapanalise.controle.conversor;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

@FacesConverter("stringDateTimeConverter")
public class StringLocalDateTimeConverter implements Converter {

	@Override
	public Object getAsObject(FacesContext facesContext, UIComponent uiComponent, String value) {
		Date date = new Date();
		if (value == null || value.equals("")) {
			return null;
		} else {
			SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
			try {
				date = sdf.parse(value);
			} catch (Exception e) {

			}
			return date;
		}
	}

	@Override
	public String getAsString(FacesContext context, UIComponent component, Object object) {
		String data = null;
		if (object == null) {
			return null;
		} else {
			try {
				String dataObject = (String) object;
				LocalDateTime dateTime = LocalDateTime.parse(dataObject.replace(" ", "T"));
				DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss");
				data = dateTime.format(formatter);
				return data;
			} catch (Exception e) {

			}
		}
		return data;
	}
}
