package br.gov.ac.tce.sicapanalise.auditoria.processo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class ArquivoWSContainer implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -987555375443546140L;
	
	private List<ArquivoWS> arquivos = new ArrayList<>();

	public ArquivoWSContainer() {}
	public ArquivoWSContainer(List<ArquivoWS> arquivos) {
		super();
		this.arquivos = arquivos;
	}
	public List<ArquivoWS> getArquivos() {
		return arquivos;
	}
	public void setArquivos(List<ArquivoWS> arquivos) {
		this.arquivos = arquivos;
	}

	
	
	
	
	
}
