package br.gov.ac.tce.sicapanalise.auditoria.processo;

import java.io.Serializable;
import java.util.Date;

public class ProcessoSimplesPrestacao implements Serializable {

	private static final long serialVersionUID = 3294673207179515479L;
	
	private String numeroProcesso;
	
	private Date dataCriacao;
	
	private String nomeRelator;
	
	private String nomeClasse;
	
	public ProcessoSimplesPrestacao(){}
	
	public ProcessoSimplesPrestacao(String numeroProcesso, Date dataCriacao,
			String nomeRelator, String nomeClasse) {
		super();
		this.numeroProcesso = numeroProcesso;
		this.dataCriacao = dataCriacao;
		this.nomeRelator = nomeRelator;
		this.nomeClasse = nomeClasse;
	}

	public String getNumeroProcesso() {
		return numeroProcesso;
	}

	public void setNumeroProcesso(String numeroProcesso) {
		this.numeroProcesso = numeroProcesso;
	}

	public Date getDataCriacao() {
		return dataCriacao;
	}

	public void setDataCriacao(Date dataCriacao) {
		this.dataCriacao = dataCriacao;
	}

	public String getNomeRelator() {
		return nomeRelator;
	}

	public void setNomeRelator(String nomeRelator) {
		this.nomeRelator = nomeRelator;
	}

	public String getNomeClasse() {
		return nomeClasse;
	}

	public void setNomeClasse(String nomeClasse) {
		this.nomeClasse = nomeClasse;
	}
	
}
