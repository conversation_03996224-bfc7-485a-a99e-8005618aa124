package br.gov.ac.tce.sicapanalise.auditoria.repositorio;

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.acumulacao.SolicitacaoDocumentoAcumulacao;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise.SolicitacaoDocumento;

@Stateless
public class SolicitacaoDocumentoRepositorio {

	@PersistenceContext(unitName = "sicapAnalise")
	private EntityManager manager;
	
	
	public void salvarSolicitacaoDocumento(SolicitacaoDocumento solicitacaoDocumento) {
		manager.persist(solicitacaoDocumento);
	}
	
	public void salvarSolicitacaoDocumentoAcumulacao(SolicitacaoDocumentoAcumulacao solicitacaoDocumentoAcumulacao) {
		manager.persist(solicitacaoDocumentoAcumulacao);
	}
}
