package br.gov.ac.tce.sicapanalise.controle.bean;

import java.io.Serializable;
import java.text.Normalizer;

import javax.annotation.PostConstruct;
import javax.faces.view.ViewScoped;
import javax.inject.Named;

@Named
@ViewScoped
public class UtilBean implements Serializable {

	private static final long serialVersionUID = 1L;

	@PostConstruct
	public void init() {
		System.out.println("teste");
	}

	public static String removeAcentos(String str) {
//		System.out.println("txt: " + str);
		str = Normalizer.normalize(str, Normalizer.Form.NFD);
		str = str.replaceAll("\\p{InCombiningDiacriticalMarks}+", "");
//		System.out.println("txt alterado: " + str);
		return str;
	}
}
