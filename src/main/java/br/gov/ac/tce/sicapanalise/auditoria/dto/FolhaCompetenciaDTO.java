package br.gov.ac.tce.sicapanalise.auditoria.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

public class FolhaCompetenciaDTO implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private Long entidadeId;
	private String entidadeNome;
	private Long beneficiarioId;
	private Integer beneficiarioMatricula;
	private String beneficiarioCpf;
	private String beneficiarioNome;
	private String beneficiarioSexo;
	private LocalDate beneficiarioDataNascimento;
	private String beneficiarioTipo;
	private String beneficiarioCargo;
	private String beneficiarioSituacao;

	private String beneficiarioCargoTipo;
	private String beneficiarioCargoEscolaridade;
	private Integer beneficiarioCargoCargaHorariaMensal;

	private String vinculoTipo;
	private String situacaoFuncional;
	private LocalDate dataSituacaoFuncional;
	private String unidadeLotacao;
	private String municipioLotacao;
	private LocalDate dataAdmissaoInicioPensao;
	private String pensaoTipo;
	private String dependenciaTipo;
	private String regimePrevidenciario;

	private Integer ano;
	private BigDecimal valorJaneiro;
	private BigDecimal valorFevereiro;
	private BigDecimal valorMarco;
	private BigDecimal valorAbril;
	private BigDecimal valorMaio;
	private BigDecimal valorJunho;
	private BigDecimal valorJulho;
	private BigDecimal valorAgosto;
	private BigDecimal valorSetembro;
	private BigDecimal valorOutubro;
	private BigDecimal valorNovembro;
	private BigDecimal valorDezembro;
	private BigDecimal valorDecimo;

	public FolhaCompetenciaDTO(Long entidadeId, String entidadeNome, Long beneficiarioId, Integer beneficiarioMatricula,
			String beneficiarioCpf, String beneficiarioNome, String beneficiarioSexo,
			LocalDate beneficiarioDataNascimento, String beneficiarioTipo, String beneficiarioCargo,
			String beneficiarioSituacao, String beneficiarioCargoTipo, String beneficiarioCargoEscolaridade,
			Integer beneficiarioCargoCargaHorariaMensal, String vinculoTipo, String situacaoFuncional,
			LocalDate dataSituacaoFuncional, String unidadeLotacao, String municipioLotacao,
			LocalDate dataAdmissaoInicioPensao, String pensaoTipo, String dependenciaTipo, String regimePrevidenciario,
			Integer ano, BigDecimal valorJaneiro, BigDecimal valorFevereiro, BigDecimal valorMarco,
			BigDecimal valorAbril, BigDecimal valorMaio, BigDecimal valorJunho, BigDecimal valorJulho,
			BigDecimal valorAgosto, BigDecimal valorSetembro, BigDecimal valorOutubro, BigDecimal valorNovembro,
			BigDecimal valorDezembro, BigDecimal valorDecimo) {

		this.entidadeId = entidadeId;
		this.entidadeNome = entidadeNome;
		this.beneficiarioId = beneficiarioId;
		this.beneficiarioMatricula = beneficiarioMatricula;
		this.beneficiarioCpf = beneficiarioCpf;
		this.beneficiarioNome = beneficiarioNome;
		this.beneficiarioSexo = beneficiarioSexo;
		this.beneficiarioDataNascimento = beneficiarioDataNascimento;
		this.beneficiarioTipo = beneficiarioTipo;
		this.beneficiarioCargo = beneficiarioCargo;
		this.beneficiarioSituacao = beneficiarioSituacao;
		this.beneficiarioCargoTipo = beneficiarioCargoTipo;
		this.beneficiarioCargoEscolaridade = beneficiarioCargoEscolaridade;
		this.beneficiarioCargoCargaHorariaMensal = beneficiarioCargoCargaHorariaMensal;
		this.vinculoTipo = vinculoTipo;
		this.situacaoFuncional = situacaoFuncional;
		this.dataSituacaoFuncional = dataSituacaoFuncional;
		this.unidadeLotacao = unidadeLotacao;
		this.municipioLotacao = municipioLotacao;
		this.dataAdmissaoInicioPensao = dataAdmissaoInicioPensao;
		this.pensaoTipo = pensaoTipo;
		this.dependenciaTipo = dependenciaTipo;
		this.regimePrevidenciario = regimePrevidenciario;
		this.ano = ano;
		this.valorJaneiro = valorJaneiro;
		this.valorFevereiro = valorFevereiro;
		this.valorMarco = valorMarco;
		this.valorAbril = valorAbril;
		this.valorMaio = valorMaio;
		this.valorJunho = valorJunho;
		this.valorJulho = valorJulho;
		this.valorAgosto = valorAgosto;
		this.valorSetembro = valorSetembro;
		this.valorOutubro = valorOutubro;
		this.valorNovembro = valorNovembro;
		this.valorDezembro = valorDezembro;
		this.valorDecimo = valorDecimo;
	}

	public Long getEntidadeId() {
		return entidadeId;
	}

	public void setEntidadeId(Long entidadeId) {
		this.entidadeId = entidadeId;
	}

	public String getEntidadeNome() {
		return entidadeNome;
	}

	public void setEntidadeNome(String entidadeNome) {
		this.entidadeNome = entidadeNome;
	}

	public Long getBeneficiarioId() {
		return beneficiarioId;
	}

	public void setBeneficiarioId(Long beneficiarioId) {
		this.beneficiarioId = beneficiarioId;
	}

	public Integer getBeneficiarioMatricula() {
		return beneficiarioMatricula;
	}

	public void setBeneficiarioMatricula(Integer beneficiarioMatricula) {
		this.beneficiarioMatricula = beneficiarioMatricula;
	}

	public String getBeneficiarioCpf() {
		return beneficiarioCpf;
	}

	public void setBeneficiarioCpf(String beneficiarioCpf) {
		this.beneficiarioCpf = beneficiarioCpf;
	}

	public String getBeneficiarioNome() {
		return beneficiarioNome;
	}

	public void setBeneficiarioNome(String beneficiarioNome) {
		this.beneficiarioNome = beneficiarioNome;
	}

	public String getBeneficiarioSexo() {
		return beneficiarioSexo;
	}

	public void setBeneficiarioSexo(String beneficiarioSexo) {
		this.beneficiarioSexo = beneficiarioSexo;
	}

	public LocalDate getBeneficiarioDataNascimento() {
		return beneficiarioDataNascimento;
	}

	public void setBeneficiarioDataNascimento(LocalDate beneficiarioDataNascimento) {
		this.beneficiarioDataNascimento = beneficiarioDataNascimento;
	}

	public String getBeneficiarioTipo() {
		return beneficiarioTipo;
	}

	public void setBeneficiarioTipo(String beneficiarioTipo) {
		this.beneficiarioTipo = beneficiarioTipo;
	}

	public String getBeneficiarioCargo() {
		return beneficiarioCargo;
	}

	public void setBeneficiarioCargo(String beneficiarioCargo) {
		this.beneficiarioCargo = beneficiarioCargo;
	}

	public String getBeneficiarioSituacao() {
		return beneficiarioSituacao;
	}

	public void setBeneficiarioSituacao(String beneficiarioSituacao) {
		this.beneficiarioSituacao = beneficiarioSituacao;
	}

	public String getBeneficiarioCargoTipo() {
		return beneficiarioCargoTipo;
	}

	public void setBeneficiarioCargoTipo(String beneficiarioCargoTipo) {
		this.beneficiarioCargoTipo = beneficiarioCargoTipo;
	}

	public String getBeneficiarioCargoEscolaridade() {
		return beneficiarioCargoEscolaridade;
	}

	public void setBeneficiarioCargoEscolaridade(String beneficiarioCargoEscolaridade) {
		this.beneficiarioCargoEscolaridade = beneficiarioCargoEscolaridade;
	}

	public Integer getBeneficiarioCargoCargaHorariaMensal() {
		return beneficiarioCargoCargaHorariaMensal;
	}

	public void setBeneficiarioCargoCargaHorariaMensal(Integer beneficiarioCargoCargaHorariaMensal) {
		this.beneficiarioCargoCargaHorariaMensal = beneficiarioCargoCargaHorariaMensal;
	}

	public String getVinculoTipo() {
		return vinculoTipo;
	}

	public void setVinculoTipo(String vinculoTipo) {
		this.vinculoTipo = vinculoTipo;
	}

	public String getSituacaoFuncional() {
		return situacaoFuncional;
	}

	public void setSituacaoFuncional(String situacaoFuncional) {
		this.situacaoFuncional = situacaoFuncional;
	}

	public LocalDate getDataSituacaoFuncional() {
		return dataSituacaoFuncional;
	}

	public void setDataSituacaoFuncional(LocalDate dataSituacaoFuncional) {
		this.dataSituacaoFuncional = dataSituacaoFuncional;
	}

	public LocalDate getDataAdmissaoInicioPensao() {
		return dataAdmissaoInicioPensao;
	}

	public void setDataAdmissaoInicioPensao(LocalDate dataAdmissaoInicioPensao) {
		this.dataAdmissaoInicioPensao = dataAdmissaoInicioPensao;
	}

	public String getPensaoTipo() {
		return pensaoTipo;
	}

	public void setPensaoTipo(String pensaoTipo) {
		this.pensaoTipo = pensaoTipo;
	}

	public String getDependenciaTipo() {
		return dependenciaTipo;
	}

	public void setDependenciaTipo(String dependenciaTipo) {
		this.dependenciaTipo = dependenciaTipo;
	}

	public String getRegimePrevidenciario() {
		return regimePrevidenciario;
	}

	public void setRegimePrevidenciario(String regimePrevidenciario) {
		this.regimePrevidenciario = regimePrevidenciario;
	}

	public Integer getAno() {
		return ano;
	}

	public void setAno(Integer ano) {
		this.ano = ano;
	}

	public BigDecimal getValorJaneiro() {
		return valorJaneiro;
	}

	public void setValorJaneiro(BigDecimal valorJaneiro) {
		this.valorJaneiro = valorJaneiro;
	}

	public BigDecimal getValorFevereiro() {
		return valorFevereiro;
	}

	public void setValorFevereiro(BigDecimal valorFevereiro) {
		this.valorFevereiro = valorFevereiro;
	}

	public BigDecimal getValorMarco() {
		return valorMarco;
	}

	public void setValorMarco(BigDecimal valorMarco) {
		this.valorMarco = valorMarco;
	}

	public BigDecimal getValorAbril() {
		return valorAbril;
	}

	public void setValorAbril(BigDecimal valorAbril) {
		this.valorAbril = valorAbril;
	}

	public BigDecimal getValorMaio() {
		return valorMaio;
	}

	public void setValorMaio(BigDecimal valorMaio) {
		this.valorMaio = valorMaio;
	}

	public BigDecimal getValorJunho() {
		return valorJunho;
	}

	public void setValorJunho(BigDecimal valorJunho) {
		this.valorJunho = valorJunho;
	}

	public BigDecimal getValorJulho() {
		return valorJulho;
	}

	public void setValorJulho(BigDecimal valorJulho) {
		this.valorJulho = valorJulho;
	}

	public BigDecimal getValorAgosto() {
		return valorAgosto;
	}

	public void setValorAgosto(BigDecimal valorAgosto) {
		this.valorAgosto = valorAgosto;
	}

	public BigDecimal getValorSetembro() {
		return valorSetembro;
	}

	public void setValorSetembro(BigDecimal valorSetembro) {
		this.valorSetembro = valorSetembro;
	}

	public BigDecimal getValorOutubro() {
		return valorOutubro;
	}

	public void setValorOutubro(BigDecimal valorOutubro) {
		this.valorOutubro = valorOutubro;
	}

	public BigDecimal getValorNovembro() {
		return valorNovembro;
	}

	public void setValorNovembro(BigDecimal valorNovembro) {
		this.valorNovembro = valorNovembro;
	}

	public BigDecimal getValorDezembro() {
		return valorDezembro;
	}

	public void setValorDezembro(BigDecimal valorDezembro) {
		this.valorDezembro = valorDezembro;
	}

	public BigDecimal getValorDecimo() {
		return valorDecimo;
	}

	public void setValorDecimo(BigDecimal valorDecimo) {
		this.valorDecimo = valorDecimo;
	}

	public BigDecimal getValorTotal() {
		return this.valorJaneiro.add(this.valorFevereiro).add(this.valorMarco).add(this.valorAbril).add(this.valorMaio)
				.add(this.valorJunho).add(this.valorJulho).add(this.valorAgosto).add(this.valorSetembro)
				.add(this.valorOutubro).add(this.valorNovembro).add(this.valorDezembro);
	}

	public String getUnidadeLotacao() {
		return unidadeLotacao;
	}

	public void setUnidadeLotacao(String unidadeLotacao) {
		this.unidadeLotacao = unidadeLotacao;
	}

	public String getMunicipioLotacao() {
		return municipioLotacao;
	}

	public void setMunicipioLotacao(String municipioLotacao) {
		this.municipioLotacao = municipioLotacao;
	}

}
