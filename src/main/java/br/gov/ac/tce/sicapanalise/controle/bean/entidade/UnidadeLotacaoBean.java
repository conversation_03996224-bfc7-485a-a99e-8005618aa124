package br.gov.ac.tce.sicapanalise.controle.bean.entidade;

import java.io.Serializable;
import java.util.Collection;

import javax.annotation.PostConstruct;
import javax.faces.view.ViewScoped;
import javax.inject.Inject;
import javax.inject.Named;

import br.gov.ac.tce.message.MessageType;
import br.gov.ac.tce.message.Messenger;
import br.gov.ac.tce.sicapanalise.controle.bean.LoginBean;
import br.gov.ac.tce.sicapanalise.controle.conversor.FormatUtil;
import br.gov.ac.tce.sicapanalise.repositorio.MunicipioRepositorio;
import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapanalise.util.Mensagem;
import br.gov.ac.tce.sicapanalise.util.MensagemType;
import br.gov.ac.tce.sicapweb.modelo.unidadelotacao.Municipio;
import br.gov.ac.tce.sicapweb.modelo.unidadelotacao.Uf;
import br.gov.ac.tce.sicapweb.modelo.unidadelotacao.UnidadeLotacao;
import br.gov.ac.tce.sicapweb.repositorio.UfRepositorio;
import br.gov.ac.tce.sicapweb.repositorio.entidade.UnidadeLotacaoRepositorio;

@Named
@ViewScoped
public class UnidadeLotacaoBean implements Serializable {

	private static final long serialVersionUID = 1L;

	@Inject
	private LoginBean loginBean;
	@Inject
	private UnidadeLotacao unidadeLotacaoFiltro;
	@Inject
	private UnidadeLotacao unidadeLotacao;
	@Inject
	private Municipio municipio;
	@Inject
	private Uf uf;
	@Inject
	private FormatUtil formatUtil;

	@Inject
	private UnidadeLotacaoRepositorio unidadeLotacaoRepositorio;
	@Inject
	private UfRepositorio ufRepositorio;
	@Inject
	private MunicipioRepositorio municipioRepositorio;

	private Collection<Object[]> listaUnidadesLotacaoPorCompetencia;
	private Collection<UnidadeLotacao> listaUnidadesSelectOneMenu;
	private Collection<UnidadeLotacao> listaUnidadesLotacao;
	private Collection<Object[]> listaServidoresPorCompetencia;
	private Collection<Uf> listaUfs;
	private Collection<Municipio> listaMunicipios;
	private String nome;

	private int currentLevel;

	@PostConstruct
	public void init() {
		this.currentLevel = 1;
		if (this.loginBean
				.existeEntidadeSelecionada("/dados/entidade/unidadeLotacao/unidadeLotacao.xhtml?faces-redirect=true")) {
			try {
				this.nome = "";
				this.uf = this.ufRepositorio.buscarPorId(12);
				this.listaMunicipios = this.municipioRepositorio.buscarPorUf(this.uf);
				this.listaUfs = this.ufRepositorio.buscarTodos();
				this.listaUnidadesSelectOneMenu = this.unidadeLotacaoRepositorio
						.pesquisaTodos(this.loginBean.getEntidade());
			} catch (RepositorioException e) {
				Messenger.mostrarMensagem(MessageType.ERRO, "Não foi possível listar as remesas de tipos de folha.");
			}
		}
	}

	public void pesquisar() {
		try {
			this.listaUnidadesLotacao = this.unidadeLotacaoRepositorio.buscarPorFiltros(this.loginBean.getEntidade(),
					this.unidadeLotacaoFiltro, this.uf, this.municipio, this.nome);
		} catch (RepositorioException e) {
			Mensagem.setMensagem(MensagemType.ERRO, "Ocorreu um erro ao pesquisar as remessas.", "");
		}
	}

	public void buscarUnidadePorCompetencia(UnidadeLotacao unidadeLotacao) {
		try {
			this.listaUnidadesLotacaoPorCompetencia = this.unidadeLotacaoRepositorio
					.buscarPorIdUnidadesPorCompetencia(unidadeLotacao);
		} catch (RepositorioException e) {
			Mensagem.setMensagem(MensagemType.ERRO, "Ocorreu um erro ao pesquisar municípios.", "");
		}
	}

	public void buscarServidoresPorCompetencia(Long idRemessa, Long idUnidadeLotacao) {
		try {
			this.listaServidoresPorCompetencia = this.unidadeLotacaoRepositorio.buscarServidoresPorRemessa(idRemessa,
					idUnidadeLotacao);
		} catch (RepositorioException e) {
			Mensagem.setMensagem(MensagemType.ERRO, "Ocorreu um erro ao pesquisar servidores.", "");
		}
	}

	public void perquisarMunicipio() {
		try {
			this.listaMunicipios = this.municipioRepositorio.buscarPorUf(this.uf);
		} catch (RepositorioException e) {
			Mensagem.setMensagem(MensagemType.ERRO, "Ocorreu um erro ao pesquisar municípios.", "");
		}
	}

	public UnidadeLotacao getUnidadeLotacao() {
		return unidadeLotacao;
	}

	public void setUnidadeLotacao(UnidadeLotacao unidadeLotacao) {
		this.unidadeLotacao = unidadeLotacao;
	}

	public Municipio getMunicipio() {
		return municipio;
	}

	public void setMunicipio(Municipio municipio) {
		this.municipio = municipio;
	}

	public FormatUtil getFormatUtil() {
		return formatUtil;
	}

	public void setFormatUtil(FormatUtil formatUtil) {
		this.formatUtil = formatUtil;
	}

	public Uf getUf() {
		return uf;
	}

	public void setUf(Uf uf) {
		this.uf = uf;
	}

	public Collection<Object[]> getListaUnidadesLotacaoPorCompetencia() {
		return listaUnidadesLotacaoPorCompetencia;
	}

	public void setListaUnidadesLotacaoPorCompetencia(Collection<Object[]> listaUnidadesLotacaoPorCompetencia) {
		this.listaUnidadesLotacaoPorCompetencia = listaUnidadesLotacaoPorCompetencia;
	}

	public Collection<UnidadeLotacao> getListaUnidadesSelectOneMenu() {
		return listaUnidadesSelectOneMenu;
	}

	public void setListaUnidadesSelectOneMenu(Collection<UnidadeLotacao> listaUnidadesSelectOneMenu) {
		this.listaUnidadesSelectOneMenu = listaUnidadesSelectOneMenu;
	}

	public Collection<UnidadeLotacao> getListaUnidadesLotacao() {
		return listaUnidadesLotacao;
	}

	public void setListaUnidadesLotacao(Collection<UnidadeLotacao> listaUnidadesLotacao) {
		this.listaUnidadesLotacao = listaUnidadesLotacao;
	}

	public Collection<Uf> getListaUfs() {
		return listaUfs;
	}

	public void setListaUfs(Collection<Uf> listaUfs) {
		this.listaUfs = listaUfs;
	}

	public Collection<Municipio> getListaMunicipios() {
		return listaMunicipios;
	}

	public void setListaMunicipios(Collection<Municipio> listaMunicipios) {
		this.listaMunicipios = listaMunicipios;
	}

	public Collection<Object[]> getListaServidoresPorCompetencia() {
		return listaServidoresPorCompetencia;
	}

	public void setListaServidoresPorCompetencia(Collection<Object[]> listaServidoresPorCompetencia) {
		this.listaServidoresPorCompetencia = listaServidoresPorCompetencia;
	}

	public String getNome() {
		return nome;
	}

	public void setNome(String nome) {
		this.nome = nome;
	}

	public int getCurrentLevel() {
		return currentLevel;
	}

	public void setCurrentLevel(int currentLevel) {
		this.currentLevel = currentLevel;
	}

	public UnidadeLotacao getUnidadeLotacaoFiltro() {
		return unidadeLotacaoFiltro;
	}

	public void setUnidadeLotacaoFiltro(UnidadeLotacao unidadeLotacaoFiltro) {
		this.unidadeLotacaoFiltro = unidadeLotacaoFiltro;
	}

}
