package br.gov.ac.tce.sicapanalise.controle.bean.auditoria;

import java.io.Serializable;
import java.util.Collection;

import javax.annotation.PostConstruct;
import javax.faces.view.ViewScoped;
import javax.inject.Inject;
import javax.inject.Named;

import br.gov.ac.tce.message.MessageType;
import br.gov.ac.tce.message.Messenger;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.acumulacao.DetalhamentoAcumulacao;
import br.gov.ac.tce.sicapanalise.auditoria.dto.EntidadeAcumulacaoDTO;
import br.gov.ac.tce.sicapanalise.auditoria.repositorio.AcumulacaoRepositorio;
import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapanalise.util.Mensagem;
import br.gov.ac.tce.sicapanalise.util.MensagemType;

@Named
@ViewScoped
public class AcumulacaoEntidadeBean implements Serializable {

	private static final long serialVersionUID = 7739948613001119159L;

	@Inject
	private AcumulacaoRepositorio acumulacaoRepositorio;
	
	private Collection<EntidadeAcumulacaoDTO> listaEntidades;
	private Collection<DetalhamentoAcumulacao> listaVinculos;
	private Collection<Object[]> listaCompetencias;
	
	private String cpf;
	private String competencia;
	
	private int currentLevel;

	
	@PostConstruct
	public void init() {
		try {
			this.currentLevel = 1;
			this.listaCompetencias = this.acumulacaoRepositorio.listaCompetencias();
			pesquisar();
		} catch (RepositorioException e) {
			Mensagem.setMensagem(MensagemType.ERRO, e.getMessage(), "");
		}
	}
	
	public void pesquisar() {
		try {
			this.listaEntidades = acumulacaoRepositorio.listaEntidadesAcumulacoes(this.competencia, this.cpf);
			this.currentLevel = 1;
		} catch (RepositorioException e) {
			Messenger.mostrarMensagem(MessageType.ERRO, "Não foi possível listar as acumulações.");
		}
	}
	
	public void detalharEntidade(EntidadeAcumulacaoDTO entidade) {
		try {
			this.listaVinculos = this.acumulacaoRepositorio.listaVinculos(this.competencia, entidade.getId(), this.cpf);
		} catch (RepositorioException e) {
			Messenger.mostrarMensagem(MessageType.ERRO, "Não foi possível listar as acumulações.");
		}
	}

	public String getCpf() {
		return cpf;
	}

	public void setCpf(String cpf) {
		this.cpf = cpf;
	}

	public Collection<EntidadeAcumulacaoDTO> getListaEntidades() {
		return listaEntidades;
	}

	public void setListaEntidades(Collection<EntidadeAcumulacaoDTO> listaEntidades) {
		this.listaEntidades = listaEntidades;
	}

	public Collection<Object[]> getListaCompetencias() {
		return listaCompetencias;
	}

	public void setListaCompetencias(Collection<Object[]> listaCompetencias) {
		this.listaCompetencias = listaCompetencias;
	}

	public String getCompetencia() {
		return competencia;
	}

	public void setCompetencia(String competencia) {
		this.competencia = competencia;
	}


	public int getCurrentLevel() {
		return currentLevel;
	}

	public void setCurrentLevel(int currentLevel) {
		this.currentLevel = currentLevel;
	}

	public Collection<DetalhamentoAcumulacao> getListaVinculos() {
		return listaVinculos;
	}

	public void setListaVinculos(Collection<DetalhamentoAcumulacao> listaVinculos) {
		this.listaVinculos = listaVinculos;
	}


	
}
