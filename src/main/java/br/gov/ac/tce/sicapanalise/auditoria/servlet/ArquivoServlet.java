package br.gov.ac.tce.sicapanalise.auditoria.servlet;

import java.io.IOException;
import java.net.FileNameMap;
import java.net.URLConnection;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import br.gov.ac.tce.sicapanalise.auditoria.infra.ArquivoSaver;

@WebServlet("/file/*")
public class ArquivoServlet extends HttpServlet {

	/**
	 * 
	 */
	private static final long serialVersionUID = 5766011790526340960L;

	@Override
	protected void service(HttpServletRequest req, HttpServletResponse res) throws IOException, ServletException {

		String caminho = req.getRequestURI().split("/file")[1];
		
		caminho = URLDecoder.decode(caminho, StandardCharsets.UTF_8.toString());

		Path fonte = Paths.get(ArquivoSaver.SERVER_PATH + "/" + caminho);

		FileNameMap fileNameMap = URLConnection.getFileNameMap();
		String contentType = fileNameMap.getContentTypeFor("file:" + fonte);

		res.reset();
		res.setContentType(contentType);
		res.setHeader("Content-Length", String.valueOf(Files.size(fonte)));
		res.setHeader("Content-Disposition", "filename=\"" + fonte.getFileName().toString() + "\"");
		ArquivoSaver.transferir(fonte, res.getOutputStream());

	}

}
