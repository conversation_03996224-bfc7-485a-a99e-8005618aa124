package br.gov.ac.tce.sicapanalise.relatorios.business;

import java.util.Collection;

import javax.ejb.Stateless;
import javax.inject.Inject;


import br.gov.ac.tce.sicapanalise.relatorios.dto.ServidorCompatibilidadeCHDTO;
import br.gov.ac.tce.sicapanalise.relatorios.repositorio.CompatibilidadeCHRepositorio;

@Stateless
public class CompatibilidadeCHBusiness {

    @Inject
    private CompatibilidadeCHRepositorio compatibilidadeCHRepositorio;

    public Collection<Object[]> listaCompetencias() throws Exception {
        return compatibilidadeCHRepositorio.listaCompetencias();
    }

    public Collection<ServidorCompatibilidadeCHDTO> listaServidoresCompatibilidadeCH(
            String competencia, String cpf, String nome, Integer entidade) throws Exception {
        return compatibilidadeCHRepositorio.listaServidoresCompatibilidadeCH(competencia, cpf, nome, entidade);
    }
}
