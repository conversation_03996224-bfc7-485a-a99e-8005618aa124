package br.gov.ac.tce.sicapanalise.relatorios.repositorio;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;


import br.gov.ac.tce.sicapanalise.relatorios.dto.ServidorMultiplosVinculosDTO;
import br.gov.ac.tce.sicapanalise.relatorios.dto.VinculoMultiplosVinculosDTO;

@Stateless
public class ServidoresMultiplosVinculosRepositorio implements Serializable {

    private static final long serialVersionUID = 1L;

    @PersistenceContext(unitName = "sicapAnalise")
    private EntityManager entityManager;

    @SuppressWarnings("unchecked")
    public Collection<Object[]> listaCompetencias() throws Exception {
        Collection<Object[]> listaCompetencias = null;

        try {
            Query query = this.entityManager.createNativeQuery(
                "SELECT DISTINCT a.ano, a.mes, " +
                "CASE a.mes " +
                "WHEN 1 THEN 'Janeiro' " +
                "WHEN 2 THEN 'Fevereiro' " +
                "WHEN 3 THEN 'Março' " +
                "WHEN 4 THEN 'Abril' " +
                "WHEN 5 THEN 'Maio' " +
                "WHEN 6 THEN 'Junho' " +
                "WHEN 7 THEN 'Julho' " +
                "WHEN 8 THEN 'Agosto' " +
                "WHEN 9 THEN 'Setembro' " +
                "WHEN 10 THEN 'Outubro' " +
                "WHEN 11 THEN 'Novembro' " +
                "WHEN 12 THEN 'Dezembro' " +
                "ELSE 'Nenhum' END AS nomeMes " +
                "FROM auditoria.acumulacao a " +
                "ORDER BY a.ano DESC, a.mes DESC");

            listaCompetencias = query.getResultList();

        } catch (Exception e) {
            throw new Exception("Erro ao listar competências: " + e.getMessage());
        }

        return listaCompetencias;
    }

    @SuppressWarnings("unchecked")
    public Collection<ServidorMultiplosVinculosDTO> listaServidoresMultiplosVinculos(
            String competencia, String cpf, String nome, Integer entidade, Integer minimoVinculos)
            throws Exception {
        
        Collection<ServidorMultiplosVinculosDTO> listaServidores = new ArrayList<>();

        try {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT ");
            sql.append("a.cpf, ");
            sql.append("a.nome, ");
            sql.append("a.mes, ");
            sql.append("a.ano, ");
            sql.append("a.quantidadeVinculos, ");
            sql.append("a.cargaHorariaTotal, ");
            sql.append("a.montanteProventos, ");
            sql.append("a.quantidadeMunicipiosLotacao ");
            sql.append("FROM auditoria.acumulacao a ");
            sql.append("WHERE a.quantidadeVinculos >= ? ");

            Map<String, Object> parametros = new HashMap<>();
            int paramIndex = 1;
            parametros.put(String.valueOf(paramIndex++), minimoVinculos);

            if (competencia != null && !competencia.trim().isEmpty() && !competencia.equals("0")) {
                String[] comp = competencia.split("/");
                if (comp.length == 2) {
                    sql.append("AND a.mes = ?").append(paramIndex).append(" ");
                    parametros.put(String.valueOf(paramIndex++), Integer.parseInt(comp[0]));
                    sql.append("AND a.ano = ?").append(paramIndex).append(" ");
                    parametros.put(String.valueOf(paramIndex++), Integer.parseInt(comp[1]));
                }
            }

            if (cpf != null && !cpf.trim().isEmpty()) {
                sql.append("AND a.cpf LIKE ?").append(paramIndex).append(" ");
                parametros.put(String.valueOf(paramIndex++), "%" + cpf.trim() + "%");
            }

            if (nome != null && !nome.trim().isEmpty()) {
                sql.append("AND UPPER(a.nome) LIKE ?").append(paramIndex).append(" ");
                parametros.put(String.valueOf(paramIndex++), "%" + nome.trim().toUpperCase() + "%");
            }

            if (entidade != null && entidade > 0) {
                sql.append("AND EXISTS (SELECT 1 FROM auditoria.DetalhamentoAcumulacao da ");
                sql.append("WHERE da.idAcumulacao = a.id AND da.idEntidadeCjur = ?").append(paramIndex).append(") ");
                parametros.put(String.valueOf(paramIndex++), entidade);
            }

            sql.append("ORDER BY a.quantidadeVinculos DESC, a.nome");

            Query query = this.entityManager.createNativeQuery(sql.toString());

            for (Map.Entry<String, Object> param : parametros.entrySet()) {
                query.setParameter(Integer.parseInt(param.getKey()), param.getValue());
            }

            List<Object[]> resultados = query.getResultList();

            for (Object[] resultado : resultados) {
                ServidorMultiplosVinculosDTO servidor = new ServidorMultiplosVinculosDTO(
                    (String) resultado[0],  // cpf
                    (String) resultado[1],  // nome
                    ((Number) resultado[2]).intValue(), // mes
                    ((Number) resultado[3]).intValue(), // ano
                    ((Number) resultado[4]).intValue(), // quantidadeVinculos
                    ((Number) resultado[5]).intValue(), // cargaHorariaTotal
                    (BigDecimal) resultado[6], // montanteProventos
                    ((Number) resultado[7]).intValue()  // quantidadeMunicipios
                );

                // Buscar vínculos do servidor
                servidor.setVinculos(buscarVinculosServidor(servidor.getCpf(), servidor.getMes(), servidor.getAno()));
                
                listaServidores.add(servidor);
            }

        } catch (Exception e) {
            throw new Exception("Erro ao listar servidores com múltiplos vínculos: " + e.getMessage());
        }

        return listaServidores;
    }

    @SuppressWarnings("unchecked")
    private List<VinculoMultiplosVinculosDTO> buscarVinculosServidor(String cpf, Integer mes, Integer ano) {
        List<VinculoMultiplosVinculosDTO> vinculos = new ArrayList<>();

        try {
            String sql = "SELECT " +
                "e.nome as entidade, " +
                "ISNULL(m.nome, 'INDEFINIDO') as municipio, " +
                "ISNULL(b.matricula, 'N/A') as matricula, " +
                "ISNULL(c.nome, 'INDEFINIDO') as cargo, " +
                "CASE c.tipo WHEN 1 THEN 'Efetivo' WHEN 2 THEN 'Comissionado' " +
                "WHEN 3 THEN 'Temporário' ELSE 'Indefinido' END as tipoCargo, " +
                "ISNULL(c.cargaHorariaMensal, 0) as cargaHorariaMensal, " +
                "CAST(0.00 as DECIMAL(10,2)) as proventos " +
                "FROM auditoria.DetalhamentoAcumulacao da " +
                "INNER JOIN auditoria.acumulacao a ON a.id = da.idAcumulacao " +
                "LEFT JOIN Entidade e ON e.idEntidadeCjur = da.idEntidadeCjur " +
                "LEFT JOIN Municipio m ON m.id = da.idMunicipio " +
                "LEFT JOIN Cargo c ON c.id = da.idCargo " +
                "LEFT JOIN Beneficiario b ON b.id = da.idBeneficiario " +
                "WHERE a.cpf = ? AND a.mes = ? AND a.ano = ? " +
                "ORDER BY e.nome";

            Query query = this.entityManager.createNativeQuery(sql);
            query.setParameter(1, cpf);
            query.setParameter(2, mes);
            query.setParameter(3, ano);

            List<Object[]> resultados = query.getResultList();

            for (Object[] resultado : resultados) {
                VinculoMultiplosVinculosDTO vinculo = new VinculoMultiplosVinculosDTO(
                    (String) resultado[0],  // entidade
                    (String) resultado[1],  // municipio
                    (String) resultado[2],  // matricula
                    (String) resultado[3],  // cargo
                    (String) resultado[4],  // tipoCargo
                    ((Number) resultado[5]).intValue(), // cargaHorariaMensal
                    (BigDecimal) resultado[6] // proventos
                );
                vinculos.add(vinculo);
            }

        } catch (Exception e) {
            // Log do erro, mas não interrompe o processamento
            System.err.println("Erro ao buscar vínculos do servidor " + cpf + ": " + e.getMessage());
        }

        return vinculos;
    }
}
