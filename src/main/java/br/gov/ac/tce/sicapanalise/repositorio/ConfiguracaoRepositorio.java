package br.gov.ac.tce.sicapanalise.repositorio;

import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import br.gov.ac.tce.sicap.modelo.entidade.Configuracao;

public class ConfiguracaoRepositorio {

	@PersistenceContext(unitName = "sicapAnalise")
	private EntityManager entityManager;
	
	
	public List<Configuracao> findAllConfiguracao(){
		return entityManager.createQuery("select c from Configuracao c", Configuracao.class).getResultList();
	}
}
