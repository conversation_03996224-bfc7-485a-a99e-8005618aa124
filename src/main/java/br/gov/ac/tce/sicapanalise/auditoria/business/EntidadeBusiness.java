package br.gov.ac.tce.sicapanalise.auditoria.business;

import java.util.Collection;
import java.util.List;

import javax.ejb.Stateless;
import javax.inject.Inject;

import br.gov.ac.tce.sicapanalise.auditoria.dto.CargoEntidadeDTO;
import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;
import br.gov.ac.tce.sicapweb.repositorio.EntidadeRepositorio;

@Stateless
public class EntidadeBusiness {

	@Inject
	private EntidadeRepositorio entidadeRepositorio;
	
	public Entidade retornaUltimaEntidadeAdmissao(Long idAcumulacao) {
		return entidadeRepositorio.retornaUltimaEntidadeAdmissao(idAcumulacao);
	}
	
	public List<Entidade> listaEntidades(){
		return entidadeRepositorio.lista();
	}
	
	public Collection<Object[]> listaCompetenciasContraCheque(Integer entidade) throws RepositorioException{
		return entidadeRepositorio.listaCompetenciasContraCheque(entidade);
	}
	
	public Collection<CargoEntidadeDTO> listaCargosPorEntidade(Integer entidade, Integer ano , Integer mes){
		return entidadeRepositorio.listaCargosPorEntidade(entidade, ano, mes);
	}
	
	public Collection<Integer> listaCompetenciaAno() {
		return entidadeRepositorio.listaCompetenciaAno();
	}
}
