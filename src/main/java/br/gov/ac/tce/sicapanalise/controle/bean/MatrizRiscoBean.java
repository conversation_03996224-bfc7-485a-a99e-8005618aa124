package br.gov.ac.tce.sicapanalise.controle.bean;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Collection;

import javax.annotation.PostConstruct;
import javax.faces.view.ViewScoped;
import javax.inject.Inject;
import javax.inject.Named;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.MatrizRisco;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.MetricasMatrizRisco;
import br.gov.ac.tce.sicap.modelo.repositorio.MatrizRiscoRepositorio;
import br.gov.ac.tce.sicap.modelo.repositorio.MetricasMatrizRiscoRepositorio;
import br.gov.ac.tce.sicap.modelo.repositorio.RepositorioException;
import br.gov.ac.tce.sicapanalise.util.Mensagem;
import br.gov.ac.tce.sicapanalise.util.MensagemType;

@Named
@ViewScoped
public class MatrizRiscoBean implements Serializable {

	private static final long serialVersionUID = 1L;

	@Inject
	private LoginBean loginBean;
	@Inject
	private MatrizRiscoRepositorio matrizRiscoRepositorio;
	@Inject
	private MetricasMatrizRiscoRepositorio metricasMatrizRiscoRepositorio;

	private Collection<MatrizRisco> listaMatrizesRisco;
	private MatrizRisco matrizRisco;
	private MatrizRisco matrizRiscoSelecionada;
	private MetricasMatrizRisco metricasMatrizRisco;

	private Boolean renderedCadastrarNovaMatriz;
	private Boolean renderedListarMatrizesCadastradas;
	private int currentLevel;

	@PostConstruct
	public void init() {
		try {
			this.setCurrentLevel(1);
			this.renderedCadastrarNovaMatriz = false;
			this.renderedListarMatrizesCadastradas = true;
			this.listaMatrizesRisco = this.matrizRiscoRepositorio.listarTodas();
		} catch (RepositorioException e) {
			Mensagem.setMensagem(MensagemType.ERRO, "Ocorreu um erro ao pesquisar dados iniciais para esta página.",
					"");
		}
	}

	public void novaMatriz() {
		this.matrizRisco = new MatrizRisco();
		// this.matrizRisco.setDataCadastro(LocalDateTime.now());
//		this.matrizRisco.setIdUsuario(this.loginBean.getIdUsuario());
		this.matrizRisco.setIdUsuario(this.loginBean.getUsuario().getId());
		this.metricasMatrizRisco = new MetricasMatrizRisco();
		this.metricasMatrizRisco.setDataCadastro(LocalDateTime.now());
//		this.metricasMatrizRisco.setIdUsuario(this.loginBean.getIdUsuario());
		this.metricasMatrizRisco.setIdUsuario(this.loginBean.getUsuario().getId());
		this.renderedCadastrarNovaMatriz = true;
		this.renderedListarMatrizesCadastradas = false;
	}

	public void listarMatriz() {
		this.renderedCadastrarNovaMatriz = false;
		this.renderedListarMatrizesCadastradas = true;
	}

	public void salvarMatriz() {
		try {
			System.out.println("Entrou no metodo salvar.");
			this.matrizRiscoRepositorio.salvar(this.matrizRisco);
			this.metricasMatrizRisco.setMatrizRisco(this.matrizRisco);
			this.metricasMatrizRiscoRepositorio.salvar(this.metricasMatrizRisco);
			this.renderedCadastrarNovaMatriz = false;
			this.renderedListarMatrizesCadastradas = true;
			this.listaMatrizesRisco = this.matrizRiscoRepositorio.listarTodas();
			Mensagem.setMensagem(MensagemType.INFORMACAO, "Dados gravados com sucesso.", "");
		} catch (RepositorioException e) {
			e.printStackTrace();
			Mensagem.setMensagem(MensagemType.ERRO, "Ocorreu um erro ao cadastrar as informações.", "");
		}
	}

	public void visualizarMetricasCadastradas(Long idMatrizRisco) {
		try {
			this.matrizRiscoSelecionada = this.matrizRiscoRepositorio.pesquisarPorId(idMatrizRisco);
		} catch (RepositorioException e) {
			e.printStackTrace();
			Mensagem.setMensagem(MensagemType.ERRO,
					"Ocorreu um erro ao pesquisar as métricas cadastradas para está matriz de risco.", "");
		}
	}

	public int getCurrentLevel() {
		return currentLevel;
	}

	public void setCurrentLevel(int currentLevel) {
		this.currentLevel = currentLevel;
	}

	public Collection<MatrizRisco> getListaMatrizesRisco() {
		return listaMatrizesRisco;
	}

	public void setListaMatrizesRisco(Collection<MatrizRisco> listaMatrizesRisco) {
		this.listaMatrizesRisco = listaMatrizesRisco;
	}

	public MatrizRisco getMatrizRisco() {
		return matrizRisco;
	}

	public void setMatrizRisco(MatrizRisco matrizRisco) {
		this.matrizRisco = matrizRisco;
	}

	public MatrizRisco getMatrizRiscoSelecionada() {
		return matrizRiscoSelecionada;
	}

	public void setMatrizRiscoSelecionada(MatrizRisco matrizRiscoSelecionada) {
		this.matrizRiscoSelecionada = matrizRiscoSelecionada;
	}

	public MetricasMatrizRisco getMetricasMatrizRisco() {
		return metricasMatrizRisco;
	}

	public void setMetricasMatrizRisco(MetricasMatrizRisco metricasMatrizRisco) {
		this.metricasMatrizRisco = metricasMatrizRisco;
	}

	public Boolean getRenderedCadastrarNovaMatriz() {
		return renderedCadastrarNovaMatriz;
	}

	public void setRenderedCadastrarNovaMatriz(Boolean renderedCadastrarNovaMatriz) {
		this.renderedCadastrarNovaMatriz = renderedCadastrarNovaMatriz;
	}

	public Boolean getRenderedListarMatrizesCadastradas() {
		return renderedListarMatrizesCadastradas;
	}

	public void setRenderedListarMatrizesCadastradas(Boolean renderedListarMatrizesCadastradas) {
		this.renderedListarMatrizesCadastradas = renderedListarMatrizesCadastradas;
	}
}
