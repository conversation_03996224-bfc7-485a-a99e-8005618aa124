package br.gov.ac.tce.sicapanalise.controle.bean.auditoria;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

import javax.annotation.PostConstruct;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Inject;
import javax.inject.Named;

import org.primefaces.component.datatable.DataTable;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.responsavel.Usuario;
import br.gov.ac.tce.sicapanalise.auditoria.repositorio.UsuarioAuditoriaRepositorio;

@Named
@ViewScoped
public class UsuarioInternoBean implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 3403747108504259142L;

	@Inject
	private UsuarioAuditoriaRepositorio usuarioInternoRepositorio;
	
	private Collection<Usuario> listaUsuario;
	
	private List<Usuario> listaUsuarioSelecionado;
	
	private Long grupoId;
	
	private String operacao;
	
	@PostConstruct
	void inicializa() {
		listaUsuario = usuarioInternoRepositorio.listaUsuarios();
	}

	public Collection<Usuario> getListaUsuario() {
		return listaUsuario;
	}

	public void setListaUsuario(Collection<Usuario> listaUsuario) {
		this.listaUsuario = listaUsuario;
	}

	public List<Usuario> getListaUsuarioSelecionado() {
		return listaUsuarioSelecionado;
	}

	public void setListaUsuarioSelecionado(List<Usuario> listaUsuarioSelecionado) {
		this.listaUsuarioSelecionado = listaUsuarioSelecionado;
	}

	public Long getGrupoId() {
		return grupoId;
	}

	public void setGrupoId(Long grupoId) {
		this.grupoId = grupoId;
		
		if (operacao != null && !operacao.isEmpty() && grupoId != null && grupoId > 0) {
			if (operacao.equals("INCLUIR_USUARIO")) {
				this.listaUsuario = usuarioInternoRepositorio.listaUsuarioSemGrupo(grupoId);
			} else if(operacao.equals("LISTA_USUARIO_GRUPO")){
				this.listaUsuario = usuarioInternoRepositorio.listaUsuarioNoGrupo(grupoId);
			}
		}
				
		UIComponent table = FacesContext.getCurrentInstance().getViewRoot().findComponent("formUsuario:tblUsuario");
        ((DataTable) table).reset();
	
	}

	public String getOperacao() {
		return operacao;
	}

	public void setOperacao(String operacao) {
		this.operacao = operacao;
	}

	
	
}
