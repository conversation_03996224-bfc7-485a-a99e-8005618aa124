package br.gov.ac.tce.sicapanalise.controle.bean.auditoria;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Inject;
import javax.inject.Named;
import javax.transaction.Transactional;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.acumulacao.Acumulacao;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.acumulacao.AnaliseAcumulacao;
import br.gov.ac.tce.sicap.modelo.entidade.notificacao.NotificacaoInformacao;
import br.gov.ac.tce.sicapanalise.auditoria.business.AcumulacaoBusiness;
import br.gov.ac.tce.sicapanalise.auditoria.business.AnaliseBusiness;
import br.gov.ac.tce.sicapanalise.auditoria.business.NotificacaoBusiness;
import br.gov.ac.tce.sicapanalise.auditoria.dto.AcumulacaoAnaliseDTO;
import br.gov.ac.tce.sicapanalise.controle.bean.LoginBean;

@Named
@ViewScoped
public class NotificarAnaliseBean implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 8907730148117689989L;

	@Inject
	private LoginBean loginBean;
	
	@Inject
	private NotificacaoBusiness notificacaoBusiness;
	
	@Inject
	private AnaliseBusiness analiseBusiness;
	
	@Inject
	private AcumulacaoBusiness acumulacaoBusiness;

	@Inject
	private FacesContext contexto;

	private NotificacaoInformacao notificacao = new NotificacaoInformacao();

	
	private List<AcumulacaoAnaliseDTO> acumulacoes;

	public NotificacaoInformacao getNotificacao() {
		return notificacao;
	}

	public void setNotificacaoInformacao(NotificacaoInformacao notificacao) {
		this.notificacao = notificacao;
	}


	
	public List<AcumulacaoAnaliseDTO> getAcumulacoes() {
		return acumulacoes;
	}

	public void setAcumulacoes(List<AcumulacaoAnaliseDTO> acumulacoes) {
		this.acumulacoes = acumulacoes;
	}

	@Transactional
	public String notificarAnalise() {
		
		List<Long> acumulacaoIds = acumulacoes.stream().map(a -> a.getAcumulacaoId()).collect(Collectors.toList());
		List<Acumulacao> listaAcumulacoes = this.acumulacaoBusiness.listaAcumulacoes(acumulacaoIds);
		
		//Inicia a an�lise das acumulacoes selecionadas
		List<AnaliseAcumulacao> analises = this.analiseBusiness.iniciarAnalise(listaAcumulacoes, "Iniciado análise da acumulação.", this.loginBean.getUsuario());
		
		//Notifica as acumula��es ap�s iniciada a analise
		notificacaoBusiness.notificarAnalise(analises, notificacao);
		
		contexto.getExternalContext().getFlash().setKeepMessages(true);
		contexto.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO,"Notificação Gerada","As notificações para as acumulações selecionadas foram geradas. (" + analises.size() + ")"));
		
		return "index?faces-redirect=true";

	}



}
