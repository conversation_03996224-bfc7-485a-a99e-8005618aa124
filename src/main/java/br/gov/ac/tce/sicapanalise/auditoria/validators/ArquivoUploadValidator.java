package br.gov.ac.tce.sicapanalise.auditoria.validators;

import javax.faces.application.FacesMessage;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.validator.FacesValidator;
import javax.faces.validator.Validator;
import javax.faces.validator.ValidatorException;
import javax.servlet.http.Part;

@FacesValidator(value = "arquivoUploadValidator")
public class ArquivoUploadValidator implements Validator {

	@Override
	public void validate(FacesContext contexto, UIComponent componente, Object objeto) throws ValidatorException {
		Part arquivo = (Part) objeto;

		FacesMessage mensagem = null;

		try {
			if (arquivo != null) {
				if (!arquivo.getContentType().endsWith("pdf"))
					mensagem = new FacesMessage("Selecione um arquivo PDF");
				else if (arquivo.getSize() > 5000000)
					mensagem = new FacesMessage(
							"Tamanho do arquivo muito grande. O tamanho do arquivo permitido é menor ou igual a 5 MB.");

				if (mensagem != null && !mensagem.getDetail().isEmpty()) {
					mensagem.setSeverity(FacesMessage.SEVERITY_ERROR);

					throw new ValidatorException(mensagem);
				}
			}
		} catch (Exception e) {
			throw new ValidatorException(new FacesMessage(e.getMessage()));
		}

	}

}
