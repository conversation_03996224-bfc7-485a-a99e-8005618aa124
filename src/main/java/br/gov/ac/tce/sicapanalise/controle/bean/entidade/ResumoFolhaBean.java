package br.gov.ac.tce.sicapanalise.controle.bean.entidade;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.Objects;

import javax.annotation.PostConstruct;
import javax.faces.view.ViewScoped;
import javax.inject.Inject;
import javax.inject.Named;

import br.gov.ac.tce.sicapanalise.controle.bean.LoginBean;
import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapweb.modelo.folha.TipoFolha;
import br.gov.ac.tce.sicapweb.modelo.remessa.Remessa;
import br.gov.ac.tce.sicapweb.modelo.remessa.SituacaoRemessa;
import br.gov.ac.tce.sicapweb.repositorio.RemessaPeriodicaRepositorio;
import br.gov.ac.tce.sicapweb.repositorio.RemessaRepositorio;
import br.gov.ac.tce.sicapweb.repositorio.entidade.ResumoFolhaRepositorio;
import br.gov.ac.tce.sicapweb.repositorio.entidade.TipoFolhaRepositorio;
import br.gov.ac.tce.sicapweb.util.Mensagem;
import br.gov.ac.tce.sicapweb.util.MensagemType;

@Named
@ViewScoped
public class ResumoFolhaBean implements Serializable {

	private static final long serialVersionUID = 1L;

	@Inject
	private LoginBean loginBean;

	@Inject
	private RemessaPeriodicaRepositorio remessaPeriodicaRepositorio;
	@Inject
	private TipoFolhaRepositorio tipoFolhaRepositorio;
	@Inject
	private ResumoFolhaRepositorio resumoFolhaRepositorio;

	@Inject
	private Remessa remessa;
	@Inject
	private TipoFolha tipoFolha;
	@Inject
	private RemessaRepositorio remessaRepositorio;

	private Collection<Remessa> listaProcessadas;
	private Collection<TipoFolha> listaTipoFolha;

	private Collection<TipoFolha> listaTipoFolhaSelecionado;
	private Collection<Object[]> resumoGeral;
	private Collection<Object[]> resumoPorTipoFolha;
	private Collection<Object[]> resumoPorSituacaoBeneficiario;
	private Collection<Object[]> resumoPorCargo;
	private Collection<Object[]> resumoPorVerba;
	private Collection<Object[]> dadosFolhaPagamentoAnual;

	@PostConstruct
	public void init() {
		if (this.loginBean.existeEntidadeSelecionada("/dados/entidade/folha/resumoFolha.xhtml?faces-redirect=true")) {
			try {
				this.listaProcessadas = this.remessaPeriodicaRepositorio
						.listaRemessasPorSituacaoEntidade(SituacaoRemessa.CONFIRMADA, this.loginBean.getEntidade());
				this.listaTipoFolha = this.tipoFolhaRepositorio.listaTipoFolha(this.loginBean.getEntidade());
			} catch (RepositorioException e) {
				Mensagem.setMensagem(MensagemType.ERRO, "Ocorreu um erro ao selecionar os filtros da pesquisa.", "");
				e.printStackTrace();
			} catch (Exception ex) {
				ex.printStackTrace();
			}
		}
	}

	public void pesquisar() {
		try {
			if (this.resumoGeral != null) {
				this.resumoGeral.clear();
			}
			if (this.resumoPorTipoFolha != null) {
				this.resumoPorTipoFolha.clear();
			}
			if (this.resumoPorSituacaoBeneficiario != null) {
				this.resumoPorSituacaoBeneficiario.clear();
			}
			if (this.resumoPorCargo != null) {
				this.resumoPorCargo.clear();
			}
			if (this.resumoPorVerba != null) {
				this.resumoPorVerba.clear();
			}
			if (this.dadosFolhaPagamentoAnual != null) {
				this.dadosFolhaPagamentoAnual.clear();
			}

			this.resumoGeral = this.resumoFolhaRepositorio.listaResumoGeral(this.remessa, this.tipoFolha);
			this.resumoPorTipoFolha = this.resumoFolhaRepositorio.listaResumoPorTipoFolha(this.remessa, this.tipoFolha);
			this.resumoPorSituacaoBeneficiario = this.resumoFolhaRepositorio
					.listaResumoPorSituacaoBeneficiario(this.remessa, this.tipoFolha);
			this.resumoPorCargo = this.resumoFolhaRepositorio.listaResumoPorCargo(this.remessa, this.tipoFolha);
			this.resumoPorVerba = this.resumoFolhaRepositorio.listaResumoPorVerba(this.remessa, this.tipoFolha);
			this.remessa = this.remessaRepositorio.buscarPorId(this.remessa.getId());
			this.dadosFolhaPagamentoAnual = this.resumoFolhaRepositorio.listaDadosFolhaPagamentoAnual(this.remessa);
		} catch (Exception e) {
			e.printStackTrace();
			Mensagem.setMensagem(MensagemType.ERRO, "Não foi possível executar a pesquisa.", "");
		}
	}

	public Remessa getRemessa() {
		return remessa;
	}

	public void setRemessa(Remessa remessa) {
		this.remessa = remessa;
	}

	public void setDadosFolhaPagamentoAnual(Collection<Object[]> dadosFolhaPagamentoAnual) {
		this.dadosFolhaPagamentoAnual = dadosFolhaPagamentoAnual;
	}

	public Collection<Object[]> getDadosFolhaPagamentoAnual() {
		return dadosFolhaPagamentoAnual;
	}

	public TipoFolha getTipoFolha() {
		return tipoFolha;
	}

	public void setTipoFolha(TipoFolha tipoFolha) {
		this.tipoFolha = tipoFolha;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public Collection<TipoFolha> getListaTipoFolhaSelecionado() {
		return listaTipoFolhaSelecionado;
	}

	public Collection<Remessa> getListaProcessadas() {
		return listaProcessadas;
	}

	public Collection<TipoFolha> getListaTipoFolha() {
		return listaTipoFolha;
	}

	public Collection<Object[]> getResumoGeral() {
		return resumoGeral;
	}

	public Collection<Object[]> getResumoPorTipoFolha() {
		return resumoPorTipoFolha;
	}

	public Collection<Object[]> getResumoPorSituacaoBeneficiario() {
		return resumoPorSituacaoBeneficiario;
	}

	public Collection<Object[]> getResumoPorCargo() {
		return resumoPorCargo;
	}

	public Collection<Object[]> getResumoPorVerba() {
		return resumoPorVerba;
	}

	public Integer getTotalGeralQuantidade() {
		if (this.resumoGeral != null && !this.resumoGeral.isEmpty())
			return this.resumoGeral.stream().mapToInt(resumo -> (Integer) resumo[3]).sum();
		return 0;
	}

	public Double getTotalGeralDescontos() {
		if (this.resumoGeral != null && !this.resumoGeral.isEmpty())
			return this.resumoGeral.stream().filter(resumo -> Objects.nonNull(resumo[4]))
					.mapToDouble(resumo -> ((BigDecimal) resumo[4]).doubleValue()).sum();
		return 0D;
	}

	public Double getTotalGeralVencimentos() {
		if (this.resumoGeral != null && !this.resumoGeral.isEmpty())
			return this.resumoGeral.stream().filter(resumo -> Objects.nonNull(resumo[5]))
					.mapToDouble(resumo -> ((BigDecimal) resumo[5]).doubleValue()).sum();
		return 0D;
	}

	public Integer getTotalTipoFolhaQuantidade() {
		if (this.resumoPorTipoFolha != null && !this.resumoPorTipoFolha.isEmpty())
			return this.resumoPorTipoFolha.stream().mapToInt(resumo -> (Integer) resumo[1]).sum();
		return 0;
	}

	public Double getTotalTipoFolhaDescontos() {
		if (this.resumoPorTipoFolha != null && !this.resumoPorTipoFolha.isEmpty())
			return this.resumoPorTipoFolha.stream().filter(resumo -> Objects.nonNull(resumo[2]))
					.mapToDouble(resumo -> ((BigDecimal) resumo[2]).doubleValue()).sum();
		return 0D;
	}

	public Double getTotalTipoFolhaVencimentos() {
		if (this.resumoPorTipoFolha != null && !this.resumoPorTipoFolha.isEmpty())
			return this.resumoPorTipoFolha.stream().filter(resumo -> Objects.nonNull(resumo[3]))
					.mapToDouble(resumo -> ((BigDecimal) resumo[3]).doubleValue()).sum();
		return 0D;
	}

	public Integer getTotalTipoBeneficiarioQuantidade() {
		if (this.resumoPorSituacaoBeneficiario != null && !this.resumoPorSituacaoBeneficiario.isEmpty())
			return this.resumoPorSituacaoBeneficiario.stream().mapToInt(resumo -> (Integer) resumo[1]).sum();
		return 0;
	}

	public Double getTotalTipoBeneficiarioDescontos() {
		if (this.resumoPorSituacaoBeneficiario != null && !this.resumoPorSituacaoBeneficiario.isEmpty())
			return this.resumoPorSituacaoBeneficiario.stream().filter(resumo -> Objects.nonNull(resumo[2]))
					.mapToDouble(resumo -> ((BigDecimal) resumo[2]).doubleValue()).sum();
		return 0D;
	}

	public Double getTotalTipoBeneficiarioVencimentos() {
		if (this.resumoPorSituacaoBeneficiario != null && !this.resumoPorSituacaoBeneficiario.isEmpty())
			return this.resumoPorSituacaoBeneficiario.stream().filter(resumo -> Objects.nonNull(resumo[3]))
					.mapToDouble(resumo -> ((BigDecimal) resumo[3]).doubleValue()).sum();
		return 0D;
	}

	public Integer getTotalCargoQuantidade() {
		if (this.resumoPorCargo != null && !this.resumoPorCargo.isEmpty())
			return this.resumoPorCargo.stream().mapToInt(resumo -> (Integer) resumo[1]).sum();
		return 0;
	}

	public Double getTotalCargoDescontos() {
		if (this.resumoPorCargo != null && !this.resumoPorCargo.isEmpty())
			return this.resumoPorCargo.stream().filter(resumo -> Objects.nonNull(resumo[2]))
					.mapToDouble(resumo -> ((BigDecimal) resumo[2]).doubleValue()).sum();
		return 0D;
	}

	public Double getTotalCargoVencimentos() {
		if (this.resumoPorCargo != null && !this.resumoPorCargo.isEmpty())
			return this.resumoPorCargo.stream().filter(resumo -> Objects.nonNull(resumo[3]))
					.mapToDouble(resumo -> ((BigDecimal) resumo[3]).doubleValue()).sum();
		return 0D;
	}

	public Double getTotalVerbaDescontos() {
		if (this.resumoPorVerba != null && !this.resumoPorVerba.isEmpty())
			return this.resumoPorVerba.stream().mapToDouble(resumo -> ((BigDecimal) resumo[1]).doubleValue()).sum();
		return 0D;
	}

	public Double getTotalVerbaVencimentos() {
		if (this.resumoPorVerba != null && !this.resumoPorVerba.isEmpty())
			return this.resumoPorVerba.stream().mapToDouble(resumo -> ((BigDecimal) resumo[2]).doubleValue()).sum();
		return 0D;
	}

	public Double getTotalJaneiro() {
		if (this.dadosFolhaPagamentoAnual != null && !this.dadosFolhaPagamentoAnual.isEmpty())
			return this.dadosFolhaPagamentoAnual.stream().mapToDouble(resumo -> ((BigDecimal) resumo[11]).doubleValue())
					.sum();
		return Double.valueOf(0D);
	}

	public Double getTotalFevereiro() {
		if (this.dadosFolhaPagamentoAnual != null && !this.dadosFolhaPagamentoAnual.isEmpty())
			return this.dadosFolhaPagamentoAnual.stream().mapToDouble(resumo -> ((BigDecimal) resumo[12]).doubleValue())
					.sum();
		return Double.valueOf(0D);
	}

	public Double getTotalMarco() {
		if (this.dadosFolhaPagamentoAnual != null && !this.dadosFolhaPagamentoAnual.isEmpty())
			return this.dadosFolhaPagamentoAnual.stream().mapToDouble(resumo -> ((BigDecimal) resumo[13]).doubleValue())
					.sum();
		return Double.valueOf(0D);
	}

	public Double getTotalAbril() {
		if (this.dadosFolhaPagamentoAnual != null && !this.dadosFolhaPagamentoAnual.isEmpty())
			return this.dadosFolhaPagamentoAnual.stream().mapToDouble(resumo -> ((BigDecimal) resumo[14]).doubleValue())
					.sum();
		return Double.valueOf(0D);
	}

	public Double getTotalMaio() {
		if (this.dadosFolhaPagamentoAnual != null && !this.dadosFolhaPagamentoAnual.isEmpty())
			return this.dadosFolhaPagamentoAnual.stream().mapToDouble(resumo -> ((BigDecimal) resumo[15]).doubleValue())
					.sum();
		return Double.valueOf(0D);
	}

	public Double getTotalJunho() {
		if (this.dadosFolhaPagamentoAnual != null && !this.dadosFolhaPagamentoAnual.isEmpty())
			return this.dadosFolhaPagamentoAnual.stream().mapToDouble(resumo -> ((BigDecimal) resumo[16]).doubleValue())
					.sum();
		return Double.valueOf(0D);
	}

	public Double getTotalJulho() {
		if (this.dadosFolhaPagamentoAnual != null && !this.dadosFolhaPagamentoAnual.isEmpty())
			return this.dadosFolhaPagamentoAnual.stream().mapToDouble(resumo -> ((BigDecimal) resumo[17]).doubleValue())
					.sum();
		return Double.valueOf(0D);
	}

	public Double getTotalAgosto() {
		if (this.dadosFolhaPagamentoAnual != null && !this.dadosFolhaPagamentoAnual.isEmpty())
			return this.dadosFolhaPagamentoAnual.stream().mapToDouble(resumo -> ((BigDecimal) resumo[18]).doubleValue())
					.sum();
		return Double.valueOf(0D);
	}

	public Double getTotalSetembro() {
		if (this.dadosFolhaPagamentoAnual != null && !this.dadosFolhaPagamentoAnual.isEmpty())
			return this.dadosFolhaPagamentoAnual.stream().mapToDouble(resumo -> ((BigDecimal) resumo[19]).doubleValue())
					.sum();
		return Double.valueOf(0D);
	}

	public Double getTotalOutubro() {
		if (this.dadosFolhaPagamentoAnual != null && !this.dadosFolhaPagamentoAnual.isEmpty())
			return this.dadosFolhaPagamentoAnual.stream().mapToDouble(resumo -> ((BigDecimal) resumo[20]).doubleValue())
					.sum();
		return Double.valueOf(0D);
	}

	public Double getTotalNovembro() {
		if (this.dadosFolhaPagamentoAnual != null && !this.dadosFolhaPagamentoAnual.isEmpty())
			return this.dadosFolhaPagamentoAnual.stream().mapToDouble(resumo -> ((BigDecimal) resumo[21]).doubleValue())
					.sum();
		return Double.valueOf(0D);
	}

	public Double getTotalDezembro() {
		if (this.dadosFolhaPagamentoAnual != null && !this.dadosFolhaPagamentoAnual.isEmpty())
			return this.dadosFolhaPagamentoAnual.stream().mapToDouble(resumo -> ((BigDecimal) resumo[22]).doubleValue())
					.sum();
		return Double.valueOf(0D);
	}

	public Double getTotalDecimo() {
		if (this.dadosFolhaPagamentoAnual != null && !this.dadosFolhaPagamentoAnual.isEmpty())
			return this.dadosFolhaPagamentoAnual.stream().mapToDouble(resumo -> ((BigDecimal) resumo[23]).doubleValue())
					.sum();
		return Double.valueOf(0D);
	}

	public Double getTotalGeral() {
		if (this.dadosFolhaPagamentoAnual != null && !this.dadosFolhaPagamentoAnual.isEmpty())
			return this.dadosFolhaPagamentoAnual.stream().mapToDouble(resumo -> ((BigDecimal) resumo[24]).doubleValue())
					.sum();
		return Double.valueOf(0D);
	}

}
