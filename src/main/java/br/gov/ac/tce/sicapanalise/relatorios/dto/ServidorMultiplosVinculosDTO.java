package br.gov.ac.tce.sicapanalise.relatorios.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class ServidorMultiplosVinculosDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String cpf;
    private String nome;
    private Integer mes;
    private Integer ano;
    private Integer quantidadeVinculos;
    private Integer cargaHorariaTotal;
    private BigDecimal montanteProventos;
    private Integer quantidadeMunicipios;
    private List<VinculoMultiplosVinculosDTO> vinculos;

    public ServidorMultiplosVinculosDTO() {
    }

    public ServidorMultiplosVinculosDTO(String cpf, String nome, Integer mes, Integer ano, 
                                       Integer quantidadeVinculos, Integer cargaHorariaTotal, 
                                       BigDecimal montanteProventos, Integer quantidadeMunicipios) {
        this.cpf = cpf;
        this.nome = nome;
        this.mes = mes;
        this.ano = ano;
        this.quantidadeVinculos = quantidadeVinculos;
        this.cargaHorariaTotal = cargaHorariaTotal;
        this.montanteProventos = montanteProventos;
        this.quantidadeMunicipios = quantidadeMunicipios;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getMes() {
        return mes;
    }

    public void setMes(Integer mes) {
        this.mes = mes;
    }

    public Integer getAno() {
        return ano;
    }

    public void setAno(Integer ano) {
        this.ano = ano;
    }

    public Integer getQuantidadeVinculos() {
        return quantidadeVinculos;
    }

    public void setQuantidadeVinculos(Integer quantidadeVinculos) {
        this.quantidadeVinculos = quantidadeVinculos;
    }

    public Integer getCargaHorariaTotal() {
        return cargaHorariaTotal;
    }

    public void setCargaHorariaTotal(Integer cargaHorariaTotal) {
        this.cargaHorariaTotal = cargaHorariaTotal;
    }

    public BigDecimal getMontanteProventos() {
        return montanteProventos;
    }

    public void setMontanteProventos(BigDecimal montanteProventos) {
        this.montanteProventos = montanteProventos;
    }

    public Integer getQuantidadeMunicipios() {
        return quantidadeMunicipios;
    }

    public void setQuantidadeMunicipios(Integer quantidadeMunicipios) {
        this.quantidadeMunicipios = quantidadeMunicipios;
    }

    public List<VinculoMultiplosVinculosDTO> getVinculos() {
        return vinculos;
    }

    public void setVinculos(List<VinculoMultiplosVinculosDTO> vinculos) {
        this.vinculos = vinculos;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((cpf == null) ? 0 : cpf.hashCode());
        result = prime * result + ((mes == null) ? 0 : mes.hashCode());
        result = prime * result + ((ano == null) ? 0 : ano.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        ServidorMultiplosVinculosDTO other = (ServidorMultiplosVinculosDTO) obj;
        if (cpf == null) {
            if (other.cpf != null)
                return false;
        } else if (!cpf.equals(other.cpf))
            return false;
        if (mes == null) {
            if (other.mes != null)
                return false;
        } else if (!mes.equals(other.mes))
            return false;
        if (ano == null) {
            if (other.ano != null)
                return false;
        } else if (!ano.equals(other.ano))
            return false;
        return true;
    }
}
