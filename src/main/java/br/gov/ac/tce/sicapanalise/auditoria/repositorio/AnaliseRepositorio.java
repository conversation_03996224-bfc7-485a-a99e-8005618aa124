package br.gov.ac.tce.sicapanalise.auditoria.repositorio;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.function.Predicate;

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.acumulacao.AnaliseAcumulacao;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise.Analise;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise.HistoricoAnalise;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise.RelatorioAnalise;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise.ResultadoAnalise;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise.TipoAnalise;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise.TipoProcedimentoAnalise;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise.TipoResultadoAnalise;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise.TipoSituacaoAnalise;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.responsavel.Usuario;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.responsavel.UsuarioGrupo;
import br.gov.ac.tce.sicapanalise.auditoria.dto.AcumulacaoAnaliseDTO;
import br.gov.ac.tce.sicapanalise.auditoria.dto.DocumentoAuditoriaDTO;

@Stateless
public class AnaliseRepositorio implements Serializable {

	private static final long serialVersionUID = 6496647481912715414L;

	@PersistenceContext(unitName = "sicapAnalise")
	private EntityManager entityManager;


	public Analise retornaAnalisePorId(Long id) {
		return entityManager.find(Analise.class, id);
	}
	
	public AnaliseAcumulacao retornaAnaliseAcumulacaoPorId(Long id) {
		String jpql = "select aa from AnaliseAcumulacao aa join fetch aa.analise join fetch aa.acumulacao where aa.id = :pId";
		return entityManager.createQuery(jpql, AnaliseAcumulacao.class).setParameter("pId", id).getSingleResult();
	}
	
	public void inserir(Analise analise) {
		entityManager.persist(analise);
	}
	
	public void atualizar(Analise analise) {
		entityManager.merge(analise);
	}
	
	public void inserirAnaliseAcumulacao(AnaliseAcumulacao analiseAcumulacao) {
		entityManager.persist(analiseAcumulacao);
	}

	public void inserirHistorico(HistoricoAnalise historicoAnalise) {
		entityManager.persist(historicoAnalise);
	}

	public void inserirResultado(ResultadoAnalise resultadoAnalise) {
		entityManager.persist(resultadoAnalise);
	}

	public void inserirRelatorio(RelatorioAnalise relatorioAnalise) {
		entityManager.persist(relatorioAnalise);
	}
	

	public TipoAnalise retornaTipoAnalise(Long id) {
		return entityManager.find(TipoAnalise.class, id);
	}

	public TipoProcedimentoAnalise retornaTipoProcedimentoAnalise(Long id) {
		return entityManager.find(TipoProcedimentoAnalise.class, id);
	}

	public TipoSituacaoAnalise retornaTipoSituacaoAnalise(Long id) {
		return entityManager.find(TipoSituacaoAnalise.class, id);
	}

	public AnaliseAcumulacao retornaAnaliseAcumulacao(Long id) {
		String jpql = "select aa from AnaliseAcumulacao aa " + "join fetch aa.analise an join fetch an.tipoAnalise "
				+ "join fetch aa.acumulacao ac join fetch ac.trilha " + "join fetch ac.listaDetalhamentoAcumulacao da "
				+ "join fetch da.beneficiario b " + "join fetch b.cadastroUnico cu " + "where aa.id = :id";
		return entityManager.createQuery(jpql, AnaliseAcumulacao.class).setParameter("id", id).getSingleResult();
	}

	public Collection<DocumentoAuditoriaDTO> listaAnaliseDocumentos(Long idAnalise) {
		Collection<DocumentoAuditoriaDTO> listaAnaliseDocumentos = null;
		String sql = "select * from (SELECT sd.idanalise, " + "n.id AS idNotificacao , " + "sd.identidadecjur, "
				+ "td.id AS idTipoDocumento, " + "td.nome AS nomeTipoDocumento, "
				+ "Concat(a.id, '/', sd.idEntidadeCjur) AS caminhoDocumento, " + "dsd.documento as nomeDocumento "
				+ "FROM auditoria.analise a " + "INNER JOIN auditoria.solicitacaodocumento sd "
				+ "ON sd.idanalise = a.id " + "INNER JOIN auditoria.detalhamentosolicitacaodocumento dsd "
				+ "ON dsd.idsolicitacaodocumento = sd.id " + "INNER JOIN auditoria.tipodocumento td "
				+ "ON td.id = dsd.idtipodocumento " + "INNER JOIN notificacao n "
				+ "ON n.idsolicitacaodocumento = sd.id " + "UNION " + "SELECT ra.idanalise, " + "0, " + "9999999, "
				+ "td.id, " + "td.nome, " + "r.arquivocaminho, " + "r.arquivoNome "
				+ "FROM   auditoria.relatorioanalise ra " + "INNER JOIN auditoria.relatorio r "
				+ "ON r.id = ra.idrelatorio " + "INNER JOIN auditoria.tipodocumento td "
				+ "ON r.idtipodocumento = td.id ) d " + "WHERE d.idAnalise = :pId " + "ORDER  BY idanalise, "
				+ "idnotificacao, " + "idtipodocumento ";

		try {
			listaAnaliseDocumentos = this.entityManager.createNativeQuery(sql, "DocumentoAuditoriaDTOMapping")
					.setParameter("pId", idAnalise).getResultList();
		} catch (Exception e) {
			e.printStackTrace();
			System.out.println(e.getMessage());
		}

		return listaAnaliseDocumentos;

	}
	

	


	public Collection<HistoricoAnalise> retornaHistoricoAnalise(Long idAnalise) {
		Collection<HistoricoAnalise> listaHistoricoAnalise = null;
		String jpql = "SELECT h FROM HistoricoAnalise h " + "JOIN FETCH h.procedimentoRealizado p "
				+ "JOIN FETCH h.usuario u " + "JOIN FETCH h.analise a " + "WHERE a.id = :pId";
		try {
			listaHistoricoAnalise = entityManager.createQuery(jpql, HistoricoAnalise.class)
					.setParameter("pId", idAnalise).getResultList();
		} catch (Exception e) {
			e.printStackTrace();
			System.out.println(e.getMessage());
		}
		return listaHistoricoAnalise;
	}

	public List<AcumulacaoAnaliseDTO> listaAnaliseAcumulacao(TipoSituacaoAnalise tipoSituacaoAnalise, Usuario usuarioAuditor) {
		List<AcumulacaoAnaliseDTO> listaAcumulacaoAnalise = null;
		
		Predicate<UsuarioGrupo> filtroDistribuidor = ug -> ug.getAtivo() && ug.getGrupo().getId().equals(6L);
		Optional<UsuarioGrupo> perfilDistribuicao = usuarioAuditor.getListaUsuarioGrupo().stream().filter(filtroDistribuidor).findAny();
		int ehDistribuidor = 0 ;
		if (perfilDistribuicao.isPresent()) {
			ehDistribuidor = 1;
		}

		
		String filtroSituacao = "";
		if(tipoSituacaoAnalise != null && !tipoSituacaoAnalise.getCodigo().isEmpty()) {
			filtroSituacao = " and an.situacaoAnalise = :pSituacao ";
		}
		
		String sql = "select distinct ac.id as acumulacaoId, "
				+ "ac.ano as acumulacaoAno, "
				+ "ac.mes as acumulacaoMes, "
				+ "ac.cpf as acumulacaoCpf, "
				+ "ac.nome as acumulacaoNome, "
				+ "ac.pontuacao as acumulacaoPontuacao, "
				+ "ac.situacao as acumulacaoSituacaoCodigo, "
				+ "tsa.descricao as acumulacaoSituacaoDescricao, "
				+ "ac.quantidadeVinculos as acumulacaoQuantidadeVinculos, "
				+ "an.id as analiseId, "
				+ "an.dataCriacao as analiseDataCriacao, "
				+ "an.numeroProcessoEletronico as analiseNumeroProcesso, "  
				+ "count(sd.id) over (partition by sd.idAnalise) as analiseDocumentosSolicitados," 
				+ "count(case sd.situacao when 'RE' then 1 end) over (partition by sd.idAnalise) as analiseDocumentosEntregues, "
				+ "count(case WHEN sd.dataprazoresposta < Getdate() AND sd.dataPrazoResposta is null then 1 end) over (partition by sd.idAnalise) as analiseDocumentosNaoEntregues, " 
				+ "(select count(1) from auditoria.RelatorioAnalise ra where ra.idanalise = an.id) as analiseRelatorios, "
				+ "aa.id as acumulacaoAnaliseId " 
				+ "from auditoria.Acumulacao ac " 
				+ "inner join auditoria.distribuicaoacumulacao da " 
				+ "on ac.id = da.idacumulacao "
				+ "and da.ativo = 1 " 
				+ "left join auditoria.AnaliseAcumulacao aa " 
				+ "on aa.idAcumulacao = ac.id " 
				+ "left join auditoria.Analise an " 
				+ "on aa.idAnalise = an.id "
				+ "left join auditoria.TipoSituacaoAnalise tsa " 
				+ "on tsa.codigo = ac.situacao " 
				+ "left join auditoria.SolicitacaoDocumento sd "
				+ "on aa.idAnalise = sd.idAnalise " 
				+ "left join auditoria.relatorioanalise ra "
				+ "on aa.idAnalise = ra.idanalise " 
				+ "where (da.idusuarioauditor = :pAuditor or 1 = :pDistribuidor )"
				+ filtroSituacao;

		try {
			 Query query = this.entityManager.createNativeQuery(sql, "acumulacaoAnaliseMapping");
			 
			 query.setParameter("pAuditor", usuarioAuditor);
			 query.setParameter("pDistribuidor", ehDistribuidor);
			 
			 if(!filtroSituacao.isEmpty()) {
				 query.setParameter("pSituacao", tipoSituacaoAnalise);
			 }
			 
			 listaAcumulacaoAnalise = query.getResultList();
		} catch (Exception e) {
			e.printStackTrace();
			e.getMessage();
		}
		return listaAcumulacaoAnalise;
	}

	

	public Collection<TipoSituacaoAnalise> listaTipoSituacaoAnalise() {
		Collection<TipoSituacaoAnalise> listaTipoSituacaoAnalise = null;
		String jpql = "SELECT tsa FROM TipoSituacaoAnalise tsa WHERE tsa.fase = 'Fim da Análise'";
		try {
			listaTipoSituacaoAnalise = entityManager.createQuery(jpql, TipoSituacaoAnalise.class).getResultList();
		} catch (Exception e) {
			System.out.println(e.getMessage());
		}
		return listaTipoSituacaoAnalise;
	}

	public Collection<TipoSituacaoAnalise> listaTipoSituacaoAnaliseTodas() {
		Collection<TipoSituacaoAnalise> listaTipoSituacaoAnalise = null;
		String jpql = "SELECT tsa FROM TipoSituacaoAnalise tsa WHERE tsa.fase <> 'Em espera'";
		try {
			listaTipoSituacaoAnalise = entityManager.createQuery(jpql, TipoSituacaoAnalise.class).getResultList();
		} catch (Exception e) {
			System.out.println(e.getMessage());
		}
		return listaTipoSituacaoAnalise;
	}

	public Collection<TipoResultadoAnalise> listaTipoResultadoAnalise() {
		Collection<TipoResultadoAnalise> listaTipoResultadoAnalise = null;
		String jpql = "SELECT tra FROM TipoResultadoAnalise tra";
		try {
			listaTipoResultadoAnalise = entityManager.createQuery(jpql, TipoResultadoAnalise.class).getResultList();
		} catch (Exception e) {
			System.out.println(e.getMessage());
		}
		return listaTipoResultadoAnalise;
	}

	public List<AnaliseAcumulacao> listaAnaliseAcumulacao(List<Long> analiseAcumulacaoIds) {
		List<AnaliseAcumulacao> listaAnaliseAcumulacao = null;
		String jpql = "select aa from AnaliseAcumulacao aa where aa.id in :pIds";
		try {
			listaAnaliseAcumulacao = entityManager.createQuery(jpql, AnaliseAcumulacao.class).setParameter("pIds", analiseAcumulacaoIds).getResultList();
		} catch (Exception e) {
			System.out.println(e.getMessage());
		}
		return listaAnaliseAcumulacao;
	}

}
