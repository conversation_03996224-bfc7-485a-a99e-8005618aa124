package br.gov.ac.tce.sicapanalise.auditoria.dto;

import java.io.Serializable;

public class DocumentoAuditoriaDTO implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -40488622062777326L;
	
	private Long idAnalise;
	private Long idNotificacao;
	private Long idEntidadeCjur;
	private Long idTipoDocumento;
	private String nomeTipoDocumento;
	private String caminhoDocumento;
	private String nomeDocumento;

	
	public DocumentoAuditoriaDTO() {

	}
	public DocumentoAuditoriaDTO(Long idAnalise, Long idNotificacao, Long idEntidadeCjur, Long idTipoDocumento,
			String nomeTipoDocumento, String caminhoDocumento, String nomeDocumento) {

		this.idAnalise = idAnalise;
		this.idNotificacao = idNotificacao;
		this.idEntidadeCjur = idEntidadeCjur;
		this.idTipoDocumento = idTipoDocumento;
		this.nomeTipoDocumento = nomeTipoDocumento;
		this.caminhoDocumento = caminhoDocumento;
		this.nomeDocumento = nomeDocumento;
	}
	public Long getIdAnalise() {
		return idAnalise;
	}
	public void setIdAnalise(Long idAnalise) {
		this.idAnalise = idAnalise;
	}
	public Long getIdNotificacao() {
		return idNotificacao;
	}
	public void setIdNotificacao(Long idNotificacao) {
		this.idNotificacao = idNotificacao;
	}
	public Long getIdEntidadeCjur() {
		return idEntidadeCjur;
	}
	public void setIdEntidadeCjur(Long idEntidadeCjur) {
		this.idEntidadeCjur = idEntidadeCjur;
	}
	public Long getIdTipoDocumento() {
		return idTipoDocumento;
	}
	public void setIdTipoDocumento(Long idTipoDocumento) {
		this.idTipoDocumento = idTipoDocumento;
	}
	public String getNomeTipoDocumento() {
		return nomeTipoDocumento;
	}
	public void setNomeTipoDocumento(String nomeTipoDocumento) {
		this.nomeTipoDocumento = nomeTipoDocumento;
	}
	public String getCaminhoDocumento() {
		return caminhoDocumento;
	}
	public void setCaminhoDocumento(String caminhoDocumento) {
		this.caminhoDocumento = caminhoDocumento;
	}
	
	public String getNomeDocumento() {
		return nomeDocumento;
	}
	public void setNomeDocumento(String nomeDocumento) {
		this.nomeDocumento = nomeDocumento;
	}
	@Override
	public String toString() {
		return "DocumentoAuditoria [idAnalise=" + idAnalise + ", idNotificacao=" + idNotificacao + ", idEntidadeCjur="
				+ idEntidadeCjur + ", idTipoDocumento=" + idTipoDocumento + ", nomeTipoDocumento=" + nomeTipoDocumento
				+ ", caminhoDocumento=" + caminhoDocumento + "]";
	}
	
//	
//	public String getNomeArquivo() {
//		String caminhoRelativo = getCaminhoDocumento();
//		int idxNomArq = caminhoRelativo.lastIndexOf("/") + 1;
//		return caminhoRelativo.substring(idxNomArq , caminhoRelativo.length());
//		
//	}

	
	
}
