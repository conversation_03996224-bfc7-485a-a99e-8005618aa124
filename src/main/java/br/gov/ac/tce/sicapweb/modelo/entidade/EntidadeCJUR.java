package br.gov.ac.tce.sicapweb.modelo.entidade;

import java.io.Serializable;

import javax.persistence.Entity;
import javax.persistence.Id;

@Entity
public class EntidadeCJUR implements Serializable {

	private static final long serialVersionUID = 1L;
	@Id
	private Integer idEntidadeCjur;
	private String nome;
	private String ente;
	private String poder;
	private String esfera;
	private String classificacaoAdministrativa;

	public EntidadeCJUR() {

	}

	public EntidadeCJUR(Integer idEntiadeCjur, String nome, String ente, String poder, String esfera,
			String classificacaoAdministrativa) {
		this.idEntidadeCjur = idEntiadeCjur;
		this.nome = nome;
		this.ente = ente;
		this.poder = poder;
		this.esfera = esfera;
		this.classificacaoAdministrativa = classificacaoAdministrativa;
	}

	public Integer getIdEntidadeCjur() {
		return idEntidadeCjur;
	}

	public void setIdEntidadeCjur(Integer idEntidadeCjur) {
		this.idEntidadeCjur = idEntidadeCjur;
	}

	public String getNome() {
		return nome;
	}

	public void setNome(String nome) {
		this.nome = nome;
	}

	public Ente getEnte() {
		return Ente.parse(this.ente);
	}

	public void setEnte(String ente) {
		this.ente = ente;
	}

	public Poder getPoder() {
		return Poder.parse(this.poder);
	}

	public void setPoder(String poder) {
		this.poder = poder;
	}

	public Esfera getEsfera() {
		return Esfera.parse(this.esfera);
	}

	public void setEsfera(String esfera) {
		this.esfera = esfera;
	}

	public ClassificacaoAdministrativa getClassificacaoAdministrativa() {
		return ClassificacaoAdministrativa.parse(this.classificacaoAdministrativa);
	}

	public void setClassificacaoAdministrativa(String classificacaoAdministrativa) {
		this.classificacaoAdministrativa = classificacaoAdministrativa;
	}
}
