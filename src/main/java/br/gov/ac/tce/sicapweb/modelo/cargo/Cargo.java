package br.gov.ac.tce.sicapweb.modelo.cargo;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;

import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;
import br.gov.ac.tce.sicapweb.modelo.remessa.RemessaEventual;

@Entity
@NamedQueries({
		@NamedQuery(name = Cargo.buscarTodosPorEntidadeCJUR, query = "select c from Cargo c where c.entidade = :entidade order by c.remessaEventual.id, c.nome, c.codigo"),
		@NamedQuery(name = Cargo.buscarTodosPorEntidadeCJURRegistroAtivo, query = "select c from Cargo c where c.entidade = :entidade and c.registroAtivo = true order by c.remessaEventual.id, c.nome , c.codigo" ) })
public class Cargo implements Serializable {

	private static final long serialVersionUID = 1L;

	public static final String buscarTodosPorEntidadeCJUR = "Cargo.buscarTodosPorEntidadeCJUR";
	public static final String buscarTodosPorEntidadeCJURRegistroAtivo = "Cargo.buscarTodosPorEntidadeCJURRegistroAtivo";

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idEntidadeCjur", nullable = false)
	private Entidade entidade;
	@Column(nullable = false)
	private Integer codigo;
	@Column(length = 60, nullable = false)
	private String nome;
	@Column(nullable = false)
	private Integer escolaridade;
	@Column(nullable = false)
	private Integer tipoAcumulavel;
	@Column(nullable = false)
	private Integer cargaHorariaMensal;
	private Integer intervaloPromocao;
	@Column(nullable = false)
	private Integer tipo;
	private LocalDate dataCriacao;
	@Column(nullable = false)
	private Integer situacao;
	private LocalDate dataExtincao;
	@Column(nullable = false)
	private Boolean registroAtivo;
	private LocalDateTime dataInativacao;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idRemessaEventual", nullable = false)
	private RemessaEventual remessaEventual;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "subGrupoClassificacaoFuncional", nullable = true)
	private CargoReferenciaSub referenciaSub;
	
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Entidade getEntidade() {
		return entidade;
	}

	public void setEntidade(Entidade entidade) {
		this.entidade = entidade;
	}

	public Integer getCodigo() {
		return codigo;
	}

	public void setCodigo(Integer codigo) {
		this.codigo = codigo;
	}

	public String getNome() {
		return nome;
	}

	public void setNome(String nome) {
		this.nome = nome;
	}

	public Escolaridade getEscolaridade() {
		return Escolaridade.parse(this.escolaridade);
	}

	public void setEscolaridade(Escolaridade escolaridade) {
		this.escolaridade = escolaridade.getId();
	}

	public TipoAcumulavel getTipoAcumulavel() {
		return TipoAcumulavel.parse(this.tipoAcumulavel);
	}

	public void setTipoAcumulavel(TipoAcumulavel tipoAcumulavel) {
		this.tipoAcumulavel = tipoAcumulavel.getId();
	}

	public Integer getCargaHorariaMensal() {
		return cargaHorariaMensal;
	}

	public void setCargaHorariaMensal(Integer cargaHorariaMensal) {
		this.cargaHorariaMensal = cargaHorariaMensal;
	}

	public Integer getIntervaloPromocao() {
		return intervaloPromocao;
	}

	public void setIntervaloPromocao(Integer intervaloPromocao) {
		this.intervaloPromocao = intervaloPromocao;
	}

	public TipoCargo getTipo() {
		return TipoCargo.parse(this.tipo);
	}

	public void setTipo(TipoCargo tipoCargo) {
		this.tipo = tipoCargo.getId();
	}

	public LocalDate getDataCriacao() {
		return dataCriacao;
	}

	public void setDataCriacao(LocalDate dataCriacao) {
		this.dataCriacao = dataCriacao;
	}

	public SituacaoCargo getSituacao() {
		return SituacaoCargo.parse(this.situacao);
	}

	public void setSituacao(SituacaoCargo situacaoCargo) {
		this.situacao = situacaoCargo.getId();
	}

	public LocalDate getDataExtincao() {
		return dataExtincao;
	}

	public void setDataExtincao(LocalDate dataExtincao) {
		this.dataExtincao = dataExtincao;
	}

	public Boolean getRegistroAtivo() {
		return registroAtivo;
	}

	public void setRegistroAtivo(Boolean registroAtivo) {
		this.registroAtivo = registroAtivo;
	}

	public LocalDateTime getDataInativacao() {
		return dataInativacao;
	}

	public void setDataInativacao(LocalDateTime dataInativacao) {
		this.dataInativacao = dataInativacao;
	}

	public RemessaEventual getRemessaEventual() {
		return remessaEventual;
	}

	public void setRemessaEventual(RemessaEventual remessaEventual) {
		this.remessaEventual = remessaEventual;
	}
	
	

	public CargoReferenciaSub getReferenciaSub() {
		return referenciaSub;
	}

	public void setReferenciaSub(CargoReferenciaSub referenciaSub) {
		this.referenciaSub = referenciaSub;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((cargaHorariaMensal == null) ? 0 : cargaHorariaMensal.hashCode());
		result = prime * result + ((codigo == null) ? 0 : codigo.hashCode());
		result = prime * result + ((dataCriacao == null) ? 0 : dataCriacao.hashCode());
		result = prime * result + ((dataExtincao == null) ? 0 : dataExtincao.hashCode());
		result = prime * result + ((dataInativacao == null) ? 0 : dataInativacao.hashCode());
		result = prime * result + ((entidade == null) ? 0 : entidade.hashCode());
		result = prime * result + ((escolaridade == null) ? 0 : escolaridade.hashCode());
		result = prime * result + ((intervaloPromocao == null) ? 0 : intervaloPromocao.hashCode());
		result = prime * result + ((nome == null) ? 0 : nome.hashCode());
		result = prime * result + ((registroAtivo == null) ? 0 : registroAtivo.hashCode());
		result = prime * result + ((remessaEventual == null) ? 0 : remessaEventual.hashCode());
		result = prime * result + ((situacao == null) ? 0 : situacao.hashCode());
		result = prime * result + ((tipo == null) ? 0 : tipo.hashCode());
		result = prime * result + ((tipoAcumulavel == null) ? 0 : tipoAcumulavel.hashCode());
		result = prime * result + ((referenciaSub == null) ? 0 : referenciaSub.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		Cargo other = (Cargo) obj;
		if (cargaHorariaMensal == null) {
			if (other.cargaHorariaMensal != null)
				return false;
		} else if (!cargaHorariaMensal.equals(other.cargaHorariaMensal))
			return false;
		if (codigo == null) {
			if (other.codigo != null)
				return false;
		} else if (!codigo.equals(other.codigo))
			return false;
		if (dataCriacao == null) {
			if (other.dataCriacao != null)
				return false;
		} else if (!dataCriacao.equals(other.dataCriacao))
			return false;
		if (dataExtincao == null) {
			if (other.dataExtincao != null)
				return false;
		} else if (!dataExtincao.equals(other.dataExtincao))
			return false;
		if (dataInativacao == null) {
			if (other.dataInativacao != null)
				return false;
		} else if (!dataInativacao.equals(other.dataInativacao))
			return false;
		if (entidade == null) {
			if (other.entidade != null)
				return false;
		} else if (!entidade.equals(other.entidade))
			return false;
		if (escolaridade == null) {
			if (other.escolaridade != null)
				return false;
		} else if (!escolaridade.equals(other.escolaridade))
			return false;
		if (intervaloPromocao == null) {
			if (other.intervaloPromocao != null)
				return false;
		} else if (!intervaloPromocao.equals(other.intervaloPromocao))
			return false;
		if (nome == null) {
			if (other.nome != null)
				return false;
		} else if (!nome.equals(other.nome))
			return false;
		if (registroAtivo == null) {
			if (other.registroAtivo != null)
				return false;
		} else if (!registroAtivo.equals(other.registroAtivo))
			return false;
		if (remessaEventual == null) {
			if (other.remessaEventual != null)
				return false;
		} else if (!remessaEventual.equals(other.remessaEventual))
			return false;
		if (situacao == null) {
			if (other.situacao != null)
				return false;
		} else if (!situacao.equals(other.situacao))
			return false;
		if (tipo == null) {
			if (other.tipo != null)
				return false;
		} else if (!tipo.equals(other.tipo))
			return false;
		if (tipoAcumulavel == null) {
			if (other.tipoAcumulavel != null)
				return false;
		} else if (!tipoAcumulavel.equals(other.tipoAcumulavel))
			return false;
		if (referenciaSub == null) {
			if(other.referenciaSub != null)
				return false;
		} else if(!referenciaSub.equals(other.referenciaSub))
			return false;
		return true;
	}

}
