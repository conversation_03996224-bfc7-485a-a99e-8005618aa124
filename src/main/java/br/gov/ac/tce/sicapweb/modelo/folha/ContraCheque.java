package br.gov.ac.tce.sicapweb.modelo.folha;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collection;

import javax.persistence.Column;
import javax.persistence.ColumnResult;
import javax.persistence.ConstructorResult;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.SqlResultSetMapping;
import javax.persistence.SqlResultSetMappings;

import br.gov.ac.tce.sicapanalise.auditoria.dto.ContraChequeDTO;
import br.gov.ac.tce.sicapanalise.auditoria.dto.FolhaCompetenciaDTO;
import br.gov.ac.tce.sicapweb.modelo.cargo.Cargo;
import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;
import br.gov.ac.tce.sicapweb.modelo.pessoa.Beneficiario;
import br.gov.ac.tce.sicapweb.modelo.remessa.Remessa;
import br.gov.ac.tce.sicapweb.modelo.tabelavencimento.TabelaVencimentos;
import br.gov.ac.tce.sicapweb.modelo.unidadelotacao.UnidadeLotacao;

@Entity

@SqlResultSetMappings({ @SqlResultSetMapping(name = "ContraChequeDTOMapping", classes = {
		@ConstructorResult(targetClass = ContraChequeDTO.class, columns = {
				@ColumnResult(name = "cpf", type = String.class), @ColumnResult(name = "nome", type = String.class),
				@ColumnResult(name = "mes", type = Integer.class), @ColumnResult(name = "ano", type = Integer.class),
				@ColumnResult(name = "entidade_nome", type = String.class),
				@ColumnResult(name = "matricula", type = Integer.class),
				@ColumnResult(name = "tipo_folha", type = String.class),
				@ColumnResult(name = "total_vencimentos", type = BigDecimal.class),
				@ColumnResult(name = "total_descontos", type = BigDecimal.class),
				@ColumnResult(name = "verba_codigo", type = String.class),
				@ColumnResult(name = "verba_descricao", type = String.class),
				@ColumnResult(name = "verba_natureza", type = String.class),
				@ColumnResult(name = "verba_referencia", type = String.class),
				@ColumnResult(name = "verba_valor", type = BigDecimal.class) }) }),
		@SqlResultSetMapping(name = "FolhaCompetenciaDTOMapping", classes = {
				@ConstructorResult(targetClass = FolhaCompetenciaDTO.class, columns =  {
						@ColumnResult(name = "entidadeId", type = Long.class),
						@ColumnResult(name = "entidadeNome", type = String.class),
						@ColumnResult(name = "beneficiarioId", type = Long.class),
						@ColumnResult(name = "beneficiarioMatricula", type = Integer.class),
						@ColumnResult(name = "beneficiarioCpf", type = String.class),
						@ColumnResult(name = "beneficiarioNome", type = String.class),
						@ColumnResult(name = "beneficiarioSexo", type = String.class),
						@ColumnResult(name = "beneficiarioDataNascimento", type = LocalDate.class),
						@ColumnResult(name = "beneficiarioTipo", type = String.class),
						@ColumnResult(name = "beneficiarioCargo", type = String.class),
						@ColumnResult(name = "beneficiarioSituacao", type = String.class),
						@ColumnResult(name = "beneficiarioCargoTipo", type = String.class),
						@ColumnResult(name = "beneficiarioCargoEscolaridade", type = String.class),
						@ColumnResult(name = "beneficiarioCargoCargaHorariaMensal", type = Integer.class),
						@ColumnResult(name = "vinculoTipo", type = String.class),
						@ColumnResult(name = "situacaoFuncional", type = String.class),
						@ColumnResult(name = "dataSituacaoFuncional", type = LocalDate.class),
						@ColumnResult(name = "unidadeLotacao", type = String.class),
						@ColumnResult(name = "municipioLotacao", type = String.class),
						@ColumnResult(name = "dataAdmissaoInicioPensao", type = LocalDate.class),
						@ColumnResult(name = "pensaoTipo", type = String.class),
						@ColumnResult(name = "dependenciaTipo", type = String.class),
						@ColumnResult(name = "regimePrevidenciario", type = String.class),
						@ColumnResult(name = "ano", type = Integer.class),
						@ColumnResult(name = "valorJaneiro", type = BigDecimal.class),
						@ColumnResult(name = "valorFevereiro", type = BigDecimal.class),
						@ColumnResult(name = "valorMarco", type = BigDecimal.class),
						@ColumnResult(name = "valorAbril", type = BigDecimal.class),
						@ColumnResult(name = "valorMaio", type = BigDecimal.class),
						@ColumnResult(name = "valorJunho", type = BigDecimal.class),
						@ColumnResult(name = "valorJulho", type = BigDecimal.class),
						@ColumnResult(name = "valorAgosto", type = BigDecimal.class),
						@ColumnResult(name = "valorSetembro", type = BigDecimal.class),
						@ColumnResult(name = "valorOutubro", type = BigDecimal.class),
						@ColumnResult(name = "valorNovembro", type = BigDecimal.class),
						@ColumnResult(name = "valorDezembro", type = BigDecimal.class),
						@ColumnResult(name = "valorDecimo", type = BigDecimal.class)
				})
		}) })
public class ContraCheque implements Serializable {

	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idEntidadeCjur", nullable = false)
	private Entidade entidade;
	@Column(nullable = false)
	private Integer ano;
	@Column(nullable = false)
	private Integer mes;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idTipoFolha", nullable = false)
	private TipoFolha tipoFolha;
	@Column(nullable = false, precision = 10, scale = 2)
	private BigDecimal baseFgts;
	@Column(nullable = false, precision = 10, scale = 2)
	private BigDecimal basePrevidenciariaSegurado;
	@Column(nullable = false, precision = 10, scale = 2)
	private BigDecimal basePrevidenciariaPatronal;
	@Column(nullable = false, precision = 10, scale = 2)
	private BigDecimal baseIrpf;
	@Column(nullable = false, precision = 10, scale = 2)
	private BigDecimal totalVencimentos;
	@Column(nullable = false, precision = 10, scale = 2)
	private BigDecimal totalDescontos;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idTabelaVenvimentos")
	private TabelaVencimentos tabelaVencimento;
	@Column(length = 60)
	private String referenciaNivel;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idBeneficiario", nullable = false)
	private Beneficiario beneficiario;
	@Column(nullable = false)
	private String situacaoBeneficiario;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idCargo")
	private Cargo cargo;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idUnidadeLotacao")
	private UnidadeLotacao unidadeLotacao;
	@OneToMany(mappedBy = "contraCheque", fetch = FetchType.LAZY)
	private Collection<VerbasContraCheque> listaVerbasContraCheque;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idRemessa", nullable = false)
	private Remessa remessa;
	private String hash;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Entidade getEntidade() {
		return entidade;
	}

	public void setEntidade(Entidade entidade) {
		this.entidade = entidade;
	}

	public Integer getAno() {
		return ano;
	}

	public void setAno(Integer ano) {
		this.ano = ano;
	}

	public Integer getMes() {
		return mes;
	}

	public void setMes(Integer mes) {
		this.mes = mes;
	}

	public TipoFolha getTipoFolha() {
		return tipoFolha;
	}

	public void setTipoFolha(TipoFolha tipoFolha) {
		this.tipoFolha = tipoFolha;
	}

	public BigDecimal getBaseFgts() {
		return baseFgts;
	}

	public void setBaseFgts(BigDecimal baseFgts) {
		this.baseFgts = baseFgts;
	}

	public BigDecimal getBasePrevidenciariaSegurado() {
		return basePrevidenciariaSegurado;
	}

	public void setBasePrevidenciariaSegurado(BigDecimal basePrevidenciariaSegurado) {
		this.basePrevidenciariaSegurado = basePrevidenciariaSegurado;
	}

	public BigDecimal getBasePrevidenciariaPatronal() {
		return basePrevidenciariaPatronal;
	}

	public void setBasePrevidenciariaPatronal(BigDecimal basePrevidenciariaPatronal) {
		this.basePrevidenciariaPatronal = basePrevidenciariaPatronal;
	}

	public BigDecimal getBaseIrpf() {
		return baseIrpf;
	}

	public void setBaseIrpf(BigDecimal baseIrpf) {
		this.baseIrpf = baseIrpf;
	}

	public BigDecimal getTotalVencimentos() {
		return totalVencimentos;
	}

	public void setTotalVencimentos(BigDecimal totalVencimentos) {
		this.totalVencimentos = totalVencimentos;
	}

	public BigDecimal getTotalDescontos() {
		return totalDescontos;
	}

	public void setTotalDescontos(BigDecimal totalDescontos) {
		this.totalDescontos = totalDescontos;
	}

	public TabelaVencimentos getTabelaVencimento() {
		return tabelaVencimento;
	}

	public void setTabelaVencimento(TabelaVencimentos tabelaVencimento) {
		this.tabelaVencimento = tabelaVencimento;
	}

	public String getReferenciaNivel() {
		return referenciaNivel;
	}

	public void setReferenciaNivel(String referenciaNivel) {
		this.referenciaNivel = referenciaNivel;
	}

	public Beneficiario getBeneficiario() {
		return beneficiario;
	}

	public void setBeneficiario(Beneficiario beneficiario) {
		this.beneficiario = beneficiario;
	}

	public SituacaoBeneficiario getSituacaoBeneficiario() {
		return SituacaoBeneficiario.parse(this.situacaoBeneficiario);
	}

	public void setSituacaoBeneficiario(SituacaoBeneficiario situacaoBeneficiario) {
		this.situacaoBeneficiario = situacaoBeneficiario.getId();
	}

	public Cargo getCargo() {
		return cargo;
	}

	public void setCargo(Cargo cargo) {
		this.cargo = cargo;
	}

	public UnidadeLotacao getUnidadeLotacao() {
		return unidadeLotacao;
	}

	public void setUnidadeLotacao(UnidadeLotacao unidadeLotacao) {
		this.unidadeLotacao = unidadeLotacao;
	}

	public Remessa getRemessa() {
		return remessa;
	}

	public void setRemessa(Remessa remessa) {
		this.remessa = remessa;
	}

	public Collection<VerbasContraCheque> getListaVerbasContraCheque() {
		return listaVerbasContraCheque;
	}

	public void setListaVerbasContraCheque(Collection<VerbasContraCheque> listaVerbasContraCheque) {
		this.listaVerbasContraCheque = listaVerbasContraCheque;
	}

	public String getHash() {
		return hash;
	}

	public void setHash(String hash) {
		this.hash = hash;
	}

	public BigDecimal getTotalLiquido() {
		return this.totalVencimentos.subtract(this.totalDescontos);
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((ano == null) ? 0 : ano.hashCode());
		result = prime * result + ((baseFgts == null) ? 0 : baseFgts.hashCode());
		result = prime * result + ((baseIrpf == null) ? 0 : baseIrpf.hashCode());
		result = prime * result + ((basePrevidenciariaPatronal == null) ? 0 : basePrevidenciariaPatronal.hashCode());
		result = prime * result + ((basePrevidenciariaSegurado == null) ? 0 : basePrevidenciariaSegurado.hashCode());
		result = prime * result + ((beneficiario == null) ? 0 : beneficiario.hashCode());
		result = prime * result + ((cargo == null) ? 0 : cargo.hashCode());
		result = prime * result + ((entidade == null) ? 0 : entidade.hashCode());
		result = prime * result + ((hash == null) ? 0 : hash.hashCode());
		result = prime * result + ((listaVerbasContraCheque == null) ? 0 : listaVerbasContraCheque.hashCode());
		result = prime * result + ((mes == null) ? 0 : mes.hashCode());
		result = prime * result + ((referenciaNivel == null) ? 0 : referenciaNivel.hashCode());
		result = prime * result + ((remessa == null) ? 0 : remessa.hashCode());
		result = prime * result + ((situacaoBeneficiario == null) ? 0 : situacaoBeneficiario.hashCode());
		result = prime * result + ((tabelaVencimento == null) ? 0 : tabelaVencimento.hashCode());
		result = prime * result + ((tipoFolha == null) ? 0 : tipoFolha.hashCode());
		result = prime * result + ((totalDescontos == null) ? 0 : totalDescontos.hashCode());
		result = prime * result + ((totalVencimentos == null) ? 0 : totalVencimentos.hashCode());
		result = prime * result + ((unidadeLotacao == null) ? 0 : unidadeLotacao.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ContraCheque other = (ContraCheque) obj;
		if (ano == null) {
			if (other.ano != null)
				return false;
		} else if (!ano.equals(other.ano))
			return false;
		if (baseFgts == null) {
			if (other.baseFgts != null)
				return false;
		} else if (!baseFgts.equals(other.baseFgts))
			return false;
		if (baseIrpf == null) {
			if (other.baseIrpf != null)
				return false;
		} else if (!baseIrpf.equals(other.baseIrpf))
			return false;
		if (basePrevidenciariaPatronal == null) {
			if (other.basePrevidenciariaPatronal != null)
				return false;
		} else if (!basePrevidenciariaPatronal.equals(other.basePrevidenciariaPatronal))
			return false;
		if (basePrevidenciariaSegurado == null) {
			if (other.basePrevidenciariaSegurado != null)
				return false;
		} else if (!basePrevidenciariaSegurado.equals(other.basePrevidenciariaSegurado))
			return false;
		if (beneficiario == null) {
			if (other.beneficiario != null)
				return false;
		} else if (!beneficiario.equals(other.beneficiario))
			return false;
		if (cargo == null) {
			if (other.cargo != null)
				return false;
		} else if (!cargo.equals(other.cargo))
			return false;
		if (entidade == null) {
			if (other.entidade != null)
				return false;
		} else if (!entidade.equals(other.entidade))
			return false;
		if (hash == null) {
			if (other.hash != null)
				return false;
		} else if (!hash.equals(other.hash))
			return false;
		if (listaVerbasContraCheque == null) {
			if (other.listaVerbasContraCheque != null)
				return false;
		} else if (!listaVerbasContraCheque.equals(other.listaVerbasContraCheque))
			return false;
		if (mes == null) {
			if (other.mes != null)
				return false;
		} else if (!mes.equals(other.mes))
			return false;
		if (referenciaNivel == null) {
			if (other.referenciaNivel != null)
				return false;
		} else if (!referenciaNivel.equals(other.referenciaNivel))
			return false;
		if (remessa == null) {
			if (other.remessa != null)
				return false;
		} else if (!remessa.equals(other.remessa))
			return false;
		if (situacaoBeneficiario == null) {
			if (other.situacaoBeneficiario != null)
				return false;
		} else if (!situacaoBeneficiario.equals(other.situacaoBeneficiario))
			return false;
		if (tabelaVencimento == null) {
			if (other.tabelaVencimento != null)
				return false;
		} else if (!tabelaVencimento.equals(other.tabelaVencimento))
			return false;
		if (tipoFolha == null) {
			if (other.tipoFolha != null)
				return false;
		} else if (!tipoFolha.equals(other.tipoFolha))
			return false;
		if (totalDescontos == null) {
			if (other.totalDescontos != null)
				return false;
		} else if (!totalDescontos.equals(other.totalDescontos))
			return false;
		if (totalVencimentos == null) {
			if (other.totalVencimentos != null)
				return false;
		} else if (!totalVencimentos.equals(other.totalVencimentos))
			return false;
		if (unidadeLotacao == null) {
			if (other.unidadeLotacao != null)
				return false;
		} else if (!unidadeLotacao.equals(other.unidadeLotacao))
			return false;
		return true;
	}

}
