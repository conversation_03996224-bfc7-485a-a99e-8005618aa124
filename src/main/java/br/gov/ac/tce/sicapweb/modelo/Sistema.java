package br.gov.ac.tce.sicapweb.modelo;

public enum Sistema {
	
	SAGRES(1, "SAGRES"),
	ELEGIS(2, "ELEGIS"),
	PLANEJAMENTO(3, "PLANEJAMENTO"),
	PRESTACAO(4, "PRESTACAO"),
	LICON(5, "LICON"),
	SICAP(6, "SICAP");
	
	
	private Integer id;
	private String nome;

	private Sistema (Integer id, String nome) {
		this.id = id;
		this.nome = nome;
	}
	
	public Integer getId() {
		return id;
	}

	public String getNome() {
		return nome;
	}
	
	public Sistema parse(Integer id) {
		Sistema sistema = null;

		for (Sistema item : Sistema.values()) {
			if (item.getId() == id) {
				sistema = item;
				break;
			}
		}
		return sistema;
	}
}
