package br.gov.ac.tce.sicapweb.repositorio.entidade;

import java.io.Serializable;
import java.util.Collection;

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import com.uaihebert.uaicriteria.UaiCriteria;
import com.uaihebert.uaicriteria.UaiCriteriaFactory;

import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapweb.modelo.pensionista.TipoDependencia;

@Stateless
public class TipoDependenciaRepositorio implements Serializable {

	private static final long serialVersionUID = 1L;

	@PersistenceContext(unitName = "sicapAnalise")
	private EntityManager entityManager;

	public Collection<TipoDependencia> listaTodos() throws RepositorioException {
		Collection<TipoDependencia> listaTipoDependencia = null;
		try {
			UaiCriteria<TipoDependencia> uaiCriteria = UaiCriteriaFactory.createQueryCriteria(this.entityManager,
					TipoDependencia.class);
			listaTipoDependencia = uaiCriteria.getResultList();
		} catch (Exception e) {
			throw new RepositorioException("Erro tipoDependenciaRepositorio.buscarTodos.", e.getCause());
		}
		return listaTipoDependencia;
	}
}
