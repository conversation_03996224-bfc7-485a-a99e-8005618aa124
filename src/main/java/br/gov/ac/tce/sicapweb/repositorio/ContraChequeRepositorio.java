package br.gov.ac.tce.sicapweb.repositorio;

import java.io.Serializable;
import java.util.Collection;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;

import br.gov.ac.tce.sicapanalise.auditoria.dto.ContraChequeDTO;

public class ContraChequeRepositorio implements Serializable {

	private static final long serialVersionUID = -4149180191815834742L;

	@PersistenceContext(unitName = "sicapAnalise")
	private EntityManager entityManager;

	public Collection<ContraChequeDTO> retornaContraCheque(Long idBeneficiario, Integer ano, Integer mes) {
		Collection<ContraChequeDTO> listaContraCheque = null;
		String sql = "SELECT a.cpf, " + 
				"a.nome, " + 
				"cc.mes, " + 
				"cc.ano, " + 
				"e.nome AS entidade_nome, " + 
				"b.matricula, " + 
				"tf.descricao AS tipo_folha, " + 
				"cc.totalVencimentos AS total_vencimentos, " + 
				"cc.totalDescontos AS total_descontos, " + 
				"v.codigo AS verba_codigo, " + 
				"v.descricao AS verba_descricao, " + 
				"v.natureza AS verba_natureza, " + 
				"vcc.referencia AS verba_referencia, " + 
				"vcc.valor AS verba_valor " + 
				"FROM ContraCheque cc " + 
				"INNER JOIN VerbasContraCheque vcc ON vcc.idContraCheque = cc.id " + 
				"INNER JOIN Verba v ON v.id = vcc.idVerba " + 
				"INNER JOIN TipoFolha tf ON tf.id = cc.idTipoFolha " + 
				"INNER JOIN Entidade e " + 
				"ON (e.idEntidadeCjur = cc.idEntidadeLotacao and not cc.idEntidadeLotacao is null) or (e.idEntidadeCjur = cc.idEntidadeCjur and cc.idEntidadeLotacao is null) " + 
				"INNER JOIN Beneficiario b ON b.id = cc.idBeneficiario " + 
				"INNER JOIN auditoria.DetalhamentoAcumulacao da ON da.idBeneficiario = b.id " + 
				"AND da.ano = cc.ano " + 
				"AND da.mes = cc.mes " + 
				"INNER JOIN auditoria.Acumulacao a ON a.id = da.idAcumulacao " + 
				"WHERE cc.ano = :pAno " + 
				"AND cc.mes = :pMes " + 
				"AND b.id = :pBeneficiario";
		try {

			Query query = entityManager.createNativeQuery(sql, "ContraChequeDTOMapping");
			query.setParameter("pAno", ano).setParameter("pMes", mes).setParameter("pBeneficiario", idBeneficiario);
			
			listaContraCheque = query.getResultList();

		} catch (Exception e) {
			e.printStackTrace();
			System.out.println(e.getMessage());
		}
		return listaContraCheque;
	}

}
