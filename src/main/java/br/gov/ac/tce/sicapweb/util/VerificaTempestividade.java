package br.gov.ac.tce.sicapweb.util;

import java.io.Serializable;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import br.gov.ac.tce.sicapweb.modelo.remessa.TempestividadeRemessa;

public class VerificaTempestividade implements Serializable {

	private static final long serialVersionUID = 1L;

//	private static final int MINUTOS_POR_HORA = 60;
//	private static final int SEGUNDOS_POR_MINUTOS = 60;
//	private static final int SEGUNDOS_POR_HORA = SEGUNDOS_POR_MINUTOS * MINUTOS_POR_HORA;

	private Integer[] listaFundos = { 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480, 481, 482, 483, 484,
			485, 486, 487, 488, 489, 490, 491, 492, 493, 494, 496, 541, 542, 543, 544, 545, 546, 547, 548, 549, 550,
			551, 552, 553, 554, 555, 556, 557, 558, 559, 560, 561, 562, 564, 565, 572, 573, 575, 576, 577, 578, 579,
			580, 581, 582, 583, 584, 585, 586, 587, 588, 589, 590, 591, 592, 593, 594, 595, 596, 597, 598, 599, 600,
			622, 623, 624, 629, 630, 631, 664, 675, 679, 718 };

	private Integer anoRemessaAtual = null;
	private Integer primeiraRemessa = null;
	private Integer segundaRemessa = null;
	private Integer mesPrazoRmessaAtual = null;
	private Map<Integer, Integer> legenda = new HashMap<Integer, Integer>();
	private LocalDateTime prazoEnvioRemessaAtual = null;

	public VerificaTempestividade() {

	}

	public VerificaTempestividade(LocalDateTime prazoEnvioRemessaAtual) {
		this.prazoEnvioRemessaAtual = prazoEnvioRemessaAtual;
	}

	public TempestividadeRemessa verificarRemessa(Object[] object) {
		TempestividadeRemessa tempestividadeRemessa = new TempestividadeRemessa();
		String situacao = null;

		definirRemessaAtual();
		// this.legenda.clear();
		// setLegendaRelatorio(object[11], object[16], object[21], object[26],
		// object[31], object[36], object[41],
		// object[46], object[51], object[56], object[61], object[66],
		// object[71]);

		tempestividadeRemessa.setIdEntidadeCjur((Integer) object[0]);
		tempestividadeRemessa.setEnte((String) object[1]);
		tempestividadeRemessa.setPoder((String) object[2]);
		tempestividadeRemessa.setClassificacao((String) object[3]);
		tempestividadeRemessa.setEsfera((String) object[4]);
		tempestividadeRemessa.setEntidade((String) object[6]);
		if (object[5] == null)
			if (isFundo(tempestividadeRemessa.getIdEntidadeCjur())) {
				tempestividadeRemessa.setAno(2017);
			} else {
				tempestividadeRemessa.setAno(2016);
			}
		else
			tempestividadeRemessa.setAno((Integer) object[5]);


		// Janeiro
		situacao = situacaoRemessa(tempestividadeRemessa.getAno(), 1, object[9], object[7]);
		tempestividadeRemessa.setTextoJaneiro(textoRemessa(situacao, object[9]));
		tempestividadeRemessa.setCorJaneiro(corTexto(situacao));
//		tempestividadeRemessa.setDataConfirmacaoJaneiro((String) object[9]);
		tempestividadeRemessa.setDataConfirmacaoJaneiro(converteTimestampParaTexto(object[9]));
//		tempestividadeRemessa.setDataEnvioJaneiro((String) object[8]);
		tempestividadeRemessa.setDataEnvioJaneiro(converteTimestampParaTexto( object[8]));
		tempestividadeRemessa.setProtocoloJaneiro((String) object[10]);
		tempestividadeRemessa.setTextoRelatorioJan(setTextoRelatorio(situacao));
		tempestividadeRemessa.setIdUsuarioJan((Integer) object[11]);
		// tempestividadeRemessa.setAssinaturaJaneiro(converterNumeroLegenda(legenda.get((Integer)
		// object[11])));

		// Fevereiro
		situacao = situacaoRemessa(tempestividadeRemessa.getAno(), 2, object[14], object[12]);
		tempestividadeRemessa.setTextoFevereiro(textoRemessa(situacao, object[14]));
		tempestividadeRemessa.setCorFevereiro(corTexto(situacao));
//		tempestividadeRemessa.setDataConfirmacaoFevereiro((String) object[14]);
		tempestividadeRemessa.setDataConfirmacaoFevereiro(converteTimestampParaTexto(object[14]));
//		tempestividadeRemessa.setDataEnvioFevereiro((String) object[13]);
		tempestividadeRemessa.setDataEnvioFevereiro(converteTimestampParaTexto(object[13]));
		tempestividadeRemessa.setProtocoloFevereiro((String) object[15]);
		tempestividadeRemessa.setTextoRelatorioFev(setTextoRelatorio(situacao));
		tempestividadeRemessa.setIdUsuarioFev((Integer) object[16]);
		// tempestividadeRemessa.setAssinaturaFevereiro(converterNumeroLegenda(legenda.get((Integer)
		// object[16])));

		// Mar�o
		situacao = situacaoRemessa(tempestividadeRemessa.getAno(), 3, object[19], object[17]);
		tempestividadeRemessa.setTextoMarco(textoRemessa(situacao, object[19]));
		tempestividadeRemessa.setCorMarco(corTexto(situacao));
//		tempestividadeRemessa.setDataConfirmacaoMarco((String) object[19]);
		tempestividadeRemessa.setDataConfirmacaoMarco(converteTimestampParaTexto(object[19]));
//		tempestividadeRemessa.setDataEnvioMarco((String) object[18]);
		tempestividadeRemessa.setDataEnvioMarco(converteTimestampParaTexto(object[18]));
		tempestividadeRemessa.setProtocoloMarco((String) object[20]);
		tempestividadeRemessa.setTextoRelatorioMar(setTextoRelatorio(situacao));
		tempestividadeRemessa.setIdUsuarioMar((Integer) object[21]);
		// tempestividadeRemessa.setAssinaturaMarco(converterNumeroLegenda(legenda.get((Integer)
		// object[21])));

		// Abril
		situacao = situacaoRemessa(tempestividadeRemessa.getAno(), 4, object[24], object[22]);
		tempestividadeRemessa.setTextoAbril(textoRemessa(situacao, object[24]));
		tempestividadeRemessa.setCorAbril(corTexto(situacao));
//		tempestividadeRemessa.setDataConfirmacaoAbril((String) object[24]);
//		tempestividadeRemessa.setDataEnvioAbril((String) object[23]);
		tempestividadeRemessa.setDataConfirmacaoAbril(converteTimestampParaTexto(object[24]));
		tempestividadeRemessa.setDataEnvioAbril(converteTimestampParaTexto(object[23]));
		tempestividadeRemessa.setProtocoloAbril((String) object[25]);
		tempestividadeRemessa.setTextoRelatorioAbr(setTextoRelatorio(situacao));
		tempestividadeRemessa.setIdUsuarioAbr((Integer) object[26]);
		// tempestividadeRemessa.setAssinaturaAbril(converterNumeroLegenda(legenda.get((Integer)
		// object[26])));

		// Maio
		situacao = situacaoRemessa(tempestividadeRemessa.getAno(), 5, object[29], object[27]);
		tempestividadeRemessa.setTextoMaio(textoRemessa(situacao, object[29]));
		tempestividadeRemessa.setCorMaio(corTexto(situacao));
//		tempestividadeRemessa.setDataConfirmacaoMaio((String) object[29]);
//		tempestividadeRemessa.setDataEnvioMaio((String) object[28]);
		tempestividadeRemessa.setDataConfirmacaoMaio(converteTimestampParaTexto(object[29]));
		tempestividadeRemessa.setDataEnvioMaio(converteTimestampParaTexto(object[28]));
		tempestividadeRemessa.setProtocoloMaio((String) object[30]);
		tempestividadeRemessa.setTextoRelatorioMai(setTextoRelatorio(situacao));
		tempestividadeRemessa.setIdUsuarioMai((Integer) object[31]);
		// tempestividadeRemessa.setAssinaturaMaio(converterNumeroLegenda(legenda.get((Integer)
		// object[31])));

		// Junho
		situacao = situacaoRemessa(tempestividadeRemessa.getAno(), 6, object[34], object[32]);
		tempestividadeRemessa.setTextoJunho(textoRemessa(situacao, object[34]));
		tempestividadeRemessa.setCorJunho(corTexto(situacao));
//		tempestividadeRemessa.setDataConfirmacaoJunho((String) object[34]);
//		tempestividadeRemessa.setDataEnvioJunho((String) object[33]);
		tempestividadeRemessa.setDataConfirmacaoJunho(converteTimestampParaTexto(object[34]));
		tempestividadeRemessa.setDataEnvioJunho(converteTimestampParaTexto(object[33]));
		tempestividadeRemessa.setProtocoloJunho((String) object[35]);
		tempestividadeRemessa.setTextoRelatorioJun(setTextoRelatorio(situacao));
		tempestividadeRemessa.setIdUsuarioJun((Integer) object[36]);
		// tempestividadeRemessa.setAssinaturaJunho(converterNumeroLegenda(legenda.get((Integer)
		// object[36])));

		// Julho
		situacao = situacaoRemessa(tempestividadeRemessa.getAno(), 7, object[39], object[37]);
		tempestividadeRemessa.setTextoJulho(textoRemessa(situacao, object[39]));
		tempestividadeRemessa.setCorJulho(corTexto(situacao));
//		tempestividadeRemessa.setDataConfirmacaoJulho((String) object[39]);
//		tempestividadeRemessa.setDataEnvioJulho((String) object[38]);
		tempestividadeRemessa.setDataConfirmacaoJulho(converteTimestampParaTexto(object[39]));
		tempestividadeRemessa.setDataEnvioJulho(converteTimestampParaTexto(object[38]));
		tempestividadeRemessa.setProtocoloJulho((String) object[40]);
		tempestividadeRemessa.setTextoRelatorioJul(setTextoRelatorio(situacao));
		tempestividadeRemessa.setIdUsuarioJul((Integer) object[41]);
		// tempestividadeRemessa.setAssinaturaJulho(converterNumeroLegenda(legenda.get((Integer)
		// object[41])));

		// Agosto
		situacao = situacaoRemessa(tempestividadeRemessa.getAno(), 8, object[44], object[42]);
		tempestividadeRemessa.setTextoAgosto(textoRemessa(situacao, object[44]));
		tempestividadeRemessa.setCorAgosto(corTexto(situacao));
//		tempestividadeRemessa.setDataConfirmacaoAgosto((String) object[44]);
//		tempestividadeRemessa.setDataEnvioAgosto((String) object[43]);
		tempestividadeRemessa.setDataConfirmacaoAgosto(converteTimestampParaTexto(object[44]));
		tempestividadeRemessa.setDataEnvioAgosto(converteTimestampParaTexto(object[43]));
		tempestividadeRemessa.setProtocoloAgosto((String) object[45]);
		tempestividadeRemessa.setTextoRelatorioAgo(setTextoRelatorio(situacao));
		tempestividadeRemessa.setIdUsuarioAgo((Integer) object[46]);
		// tempestividadeRemessa.setAssinaturaAgosto(converterNumeroLegenda(legenda.get((Integer)
		// object[46])));

		// Setembro
		situacao = situacaoRemessa(tempestividadeRemessa.getAno(), 9, object[49], object[47]);
		tempestividadeRemessa.setTextoSetembro(textoRemessa(situacao, object[49]));
		tempestividadeRemessa.setCorSetembro(corTexto(situacao));
//		tempestividadeRemessa.setDataConfirmacaoSetembro((String) object[49]);
//		tempestividadeRemessa.setDataEnvioSetembro((String) object[48]);
		tempestividadeRemessa.setDataConfirmacaoSetembro(converteTimestampParaTexto(object[49]));
		tempestividadeRemessa.setDataEnvioSetembro(converteTimestampParaTexto(object[48]));
		tempestividadeRemessa.setProtocoloSetembro((String) object[50]);
		tempestividadeRemessa.setTextoRelatorioSet(setTextoRelatorio(situacao));
		tempestividadeRemessa.setIdUsuarioSet((Integer) object[51]);
		// tempestividadeRemessa.setAssinaturaSetembro(converterNumeroLegenda(legenda.get((Integer)
		// object[51])));

		// Outubro
		situacao = situacaoRemessa(tempestividadeRemessa.getAno(), 10, object[54], object[52]);
		tempestividadeRemessa.setTextoOutubro(textoRemessa(situacao, object[54]));
		tempestividadeRemessa.setCorOutrubro(corTexto(situacao));
//		tempestividadeRemessa.setDataConfirmacaoOutubro((String) object[54]);
//		tempestividadeRemessa.setDataEnvioOutubro((String) object[53]);
		tempestividadeRemessa.setDataConfirmacaoOutubro(converteTimestampParaTexto(object[54]));
		tempestividadeRemessa.setDataEnvioOutubro(converteTimestampParaTexto(object[53]));
		tempestividadeRemessa.setProtocoloOutubro((String) object[55]);
		tempestividadeRemessa.setTextoRelatorioOut(setTextoRelatorio(situacao));
		tempestividadeRemessa.setIdUsuarioOut((Integer) object[56]);
		// tempestividadeRemessa.setAssinaturaOutubro(converterNumeroLegenda(legenda.get((Integer)
		// object[56])));

		// Novembro
		situacao = situacaoRemessa(tempestividadeRemessa.getAno(), 11, object[59], object[57]);
		tempestividadeRemessa.setTextoNovembro(textoRemessa(situacao, object[59]));
		tempestividadeRemessa.setCorNovembro(corTexto(situacao));
//		tempestividadeRemessa.setDataConfirmacaoNovembro((String) object[59]);
//		tempestividadeRemessa.setDataEnvioNovembro((String) object[58]);
		tempestividadeRemessa.setDataConfirmacaoNovembro(converteTimestampParaTexto(object[59]));
		tempestividadeRemessa.setDataEnvioNovembro(converteTimestampParaTexto(object[58]));
		tempestividadeRemessa.setProtocoloNovembro((String) object[60]);
		tempestividadeRemessa.setTextoRelatorioNov(setTextoRelatorio(situacao));
		tempestividadeRemessa.setIdUsuarioNov((Integer) object[61]);
		// tempestividadeRemessa.setAssinaturaNovembro(converterNumeroLegenda(legenda.get((Integer)
		// object[61])));

		// Dezembro
		situacao = situacaoRemessa(tempestividadeRemessa.getAno(), 12, object[64], object[62]);
		tempestividadeRemessa.setTextoDezembro(textoRemessa(situacao, object[64]));
		tempestividadeRemessa.setCorDezembro(corTexto(situacao));
//		tempestividadeRemessa.setDataConfirmacaoDezembro((String) object[64]);
//		tempestividadeRemessa.setDataEnvioDezembro((String) object[63]);
		tempestividadeRemessa.setDataConfirmacaoDezembro(converteTimestampParaTexto(object[64]));
		tempestividadeRemessa.setDataEnvioDezembro(converteTimestampParaTexto(object[63]));
		tempestividadeRemessa.setProtocoloDezembro((String) object[65]);
		tempestividadeRemessa.setTextoRelatorioDez(setTextoRelatorio(situacao));
		tempestividadeRemessa.setIdUsuarioDez((Integer) object[66]);
		// tempestividadeRemessa.setAssinaturaDezembro(converterNumeroLegenda(legenda.get((Integer)
		// object[66])));

		// Decimo
		situacao = situacaoRemessa(tempestividadeRemessa.getAno(), 12, object[69], object[67]);
		tempestividadeRemessa.setTextoDecimo(textoRemessa(situacao, object[69]));
		tempestividadeRemessa.setCorDecimo(corTexto(situacao));
//		tempestividadeRemessa.setDataConfirmacaoDecimo((String) object[69]);
//		tempestividadeRemessa.setDataEnvioDecimo((String) object[68]);
		tempestividadeRemessa.setDataConfirmacaoDecimo(converteTimestampParaTexto(object[69]));
		tempestividadeRemessa.setDataEnvioDecimo(converteTimestampParaTexto(object[68]));
		tempestividadeRemessa.setProtocoloDecimo((String) object[70]);
		tempestividadeRemessa.setTextoRelatorioDecimo(setTextoRelatorio(situacao));
		tempestividadeRemessa.setIdUsuarioDecimo((Integer) object[71]);
		// tempestividadeRemessa.setAssinaturaDecimo(converterNumeroLegenda(legenda.get((Integer)
		// object[71])));

		return tempestividadeRemessa;
	}

	public void gerarLegendaAssinaturas(TempestividadeRemessa remessa) {
		this.legenda.clear();
		setLegendaRelatorio(remessa.getIdUsuarioJan(), remessa.getIdUsuarioFev(), remessa.getIdUsuarioMar(),
				remessa.getIdUsuarioAbr(), remessa.getIdUsuarioMai(), remessa.getIdUsuarioJun(),
				remessa.getIdUsuarioJul(), remessa.getIdUsuarioAgo(), remessa.getIdUsuarioSet(),
				remessa.getIdUsuarioOut(), remessa.getIdUsuarioNov(), remessa.getIdUsuarioDez(),
				remessa.getIdUsuarioDecimo());

		if (!legenda.isEmpty()) {
			remessa.setAssinaturaJaneiro(converterNumeroLegenda(legenda.get(remessa.getIdUsuarioJan())));
			remessa.setAssinaturaFevereiro(converterNumeroLegenda(legenda.get(remessa.getIdUsuarioFev())));
			remessa.setAssinaturaMarco(converterNumeroLegenda(legenda.get(remessa.getIdUsuarioMar())));
			remessa.setAssinaturaAbril(converterNumeroLegenda(legenda.get(remessa.getIdUsuarioAbr())));
			remessa.setAssinaturaMaio(converterNumeroLegenda(legenda.get(remessa.getIdUsuarioMai())));
			remessa.setAssinaturaJunho(converterNumeroLegenda(legenda.get(remessa.getIdUsuarioJun())));
			remessa.setAssinaturaJulho(converterNumeroLegenda(legenda.get(remessa.getIdUsuarioJul())));
			remessa.setAssinaturaAgosto(converterNumeroLegenda(legenda.get(remessa.getIdUsuarioAgo())));
			remessa.setAssinaturaSetembro(converterNumeroLegenda(legenda.get(remessa.getIdUsuarioSet())));
			remessa.setAssinaturaOutubro(converterNumeroLegenda(legenda.get(remessa.getIdUsuarioOut())));
			remessa.setAssinaturaNovembro(converterNumeroLegenda(legenda.get(remessa.getIdUsuarioNov())));
			remessa.setAssinaturaDezembro(converterNumeroLegenda(legenda.get(remessa.getIdUsuarioDez())));
			remessa.setAssinaturaDecimo(converterNumeroLegenda(legenda.get(remessa.getIdUsuarioDecimo())));

//			remessa.setDataEnvioJaneiro(FormatarTexto.dataTime(remessa.getDataEnvioJaneiro()));
//			remessa.setDataEnvioFevereiro(FormatarTexto.dataTime(remessa.getDataEnvioFevereiro()));
//			remessa.setDataEnvioMarco(FormatarTexto.dataTime(remessa.getDataEnvioMarco()));
//			remessa.setDataEnvioAbril(FormatarTexto.dataTime(remessa.getDataEnvioAbril()));
//			remessa.setDataEnvioMaio(FormatarTexto.dataTime(remessa.getDataEnvioMaio()));
//			remessa.setDataEnvioJunho(FormatarTexto.dataTime(remessa.getDataEnvioJunho()));
//			remessa.setDataEnvioJulho(FormatarTexto.dataTime(remessa.getDataEnvioJulho()));
//			remessa.setDataEnvioAgosto(FormatarTexto.dataTime(remessa.getDataEnvioAgosto()));
//			remessa.setDataEnvioSetembro(FormatarTexto.dataTime(remessa.getDataEnvioSetembro()));
//			remessa.setDataEnvioOutubro(FormatarTexto.dataTime(remessa.getDataEnvioOutubro()));
//			remessa.setDataEnvioNovembro(FormatarTexto.dataTime(remessa.getDataEnvioNovembro()));
//			remessa.setDataEnvioDezembro(FormatarTexto.dataTime(remessa.getDataEnvioDezembro()));
//			remessa.setDataEnvioDecimo(FormatarTexto.dataTime(remessa.getDataEnvioDecimo()));
//
//			remessa.setDataConfirmacaoJaneiro(FormatarTexto.dataTime(remessa.getDataConfirmacaoJaneiro()));
//			remessa.setDataConfirmacaoFevereiro(FormatarTexto.dataTime(remessa.getDataConfirmacaoFevereiro()));
//			remessa.setDataConfirmacaoMarco(FormatarTexto.dataTime(remessa.getDataConfirmacaoMarco()));
//			remessa.setDataConfirmacaoAbril(FormatarTexto.dataTime(remessa.getDataConfirmacaoAbril()));
//			remessa.setDataConfirmacaoMaio(FormatarTexto.dataTime(remessa.getDataConfirmacaoMaio()));
//			remessa.setDataConfirmacaoJunho(FormatarTexto.dataTime(remessa.getDataConfirmacaoJunho()));
//			remessa.setDataConfirmacaoJulho(FormatarTexto.dataTime(remessa.getDataConfirmacaoJulho()));
//			remessa.setDataConfirmacaoAgosto(FormatarTexto.dataTime(remessa.getDataConfirmacaoAgosto()));
//			remessa.setDataConfirmacaoSetembro(FormatarTexto.dataTime(remessa.getDataConfirmacaoSetembro()));
//			remessa.setDataConfirmacaoOutubro(FormatarTexto.dataTime(remessa.getDataConfirmacaoOutubro()));
//			remessa.setDataConfirmacaoNovembro(FormatarTexto.dataTime(remessa.getDataConfirmacaoNovembro()));
//			remessa.setDataConfirmacaoDezembro(FormatarTexto.dataTime(remessa.getDataConfirmacaoDezembro()));
//			remessa.setDataConfirmacaoDecimo(FormatarTexto.dataTime(remessa.getDataConfirmacaoDecimo()));
		}
	}

	private String situacaoRemessa(Integer ano, Integer mes, Object objDataAssinatura, Object objPrazoEnvio) {
		String situacao = "A";
		LocalDateTime assinatura = null;
		LocalDateTime prazo = null;
		
		Timestamp dataAssinatura = (Timestamp) objDataAssinatura;
		Timestamp prazoEnvio = (Timestamp) objPrazoEnvio;
		
		if(dataAssinatura != null)
			assinatura = dataAssinatura.toLocalDateTime();
		
		if(prazoEnvio != null)
			prazo = prazoEnvio.toLocalDateTime();
		
//		String dataAssinatura = (String) objDataAssinatura;
//		String prazoEnvio = (String) objPrazoEnvio;
		
//		if (dataAssinatura != null) {
//			if (dataAssinatura.isEmpty() || dataAssinatura.equals("null")) {
//				assinatura = null;
//			} else {
//				dataAssinatura = dataAssinatura.replace(" ", "T");
//				assinatura = LocalDateTime.parse(dataAssinatura);
//			}
//		} else {
//			assinatura = null;
//		}
//
//		if (prazoEnvio != null) {
//			if (prazoEnvio.isEmpty() || prazoEnvio.equals("null")) {
//				prazo = null;
//			} else {
//				prazoEnvio = prazoEnvio.replace(" ", "T");
//				prazo = LocalDateTime.parse(prazoEnvio);
//			}
//		} else {
//			prazo = null;
//		}
		

		if (ano == null) {
			// sem remessas para o ano
			situacao = "A";
		} else {
			if (assinatura == null) {
				if (ano > this.anoRemessaAtual) {
					situacao = "L";
				} else {
					if (ano.equals(this.anoRemessaAtual)) {
						if ((mes == this.primeiraRemessa) || (mes == this.segundaRemessa)) {
							situacao = "L";
						} else if (mes > this.primeiraRemessa) {
							situacao = "L";
						} else if ((mes == 1) && (this.primeiraRemessa == 11)) {
							situacao = "A";
						}
					} else {
						situacao = "A";
					}
				}
			} else {
				if ((assinatura.isBefore(prazo)) || (assinatura.isEqual(prazo))) {
					// remessa tempestiva
					situacao = "T";
				} else {
					// remessa intempestiva
					situacao = "I";
				}
			}
		}

		return situacao;
	}

	private String corTexto(String situacao) {
		String cor = null;
		switch (situacao) {
		case "T":
			cor = "corTextoVerde";
			break;
		case "I":
			cor = "corTextoLaranja";
			break;
		case "A":
			cor = "corTextoVermelho";
			break;
		case "L":
			cor = "corTextoVerde";
			break;
		default:
			cor = "";
			break;
		}

		return cor;
	}

	private String setTextoRelatorio(String situacao) {
		String texto = null;
		switch (situacao) {
		case "T":
			texto = "Tempestivo";
			break;
		case "I":
			texto = "<b>Intempestivo</b>";
			break;
		case "A":
			texto = "<b>Não Enviado</b>";
			break;
		case "L":
			texto = "Aguardando";
			break;
		default:
			texto = "";
			break;
		}

		return texto;
	}

	private void setLegendaRelatorio(Integer jan, Integer fev, Integer mar, Integer abr, Integer mai, Integer jun,
			Integer jul, Integer ago, Integer set, Integer out, Integer nov, Integer dez, Integer decimo) {

		List<Integer> listaIdUsuario = new ArrayList<Integer>();
		Integer numero = 1;

		if (jan != null) {
			listaIdUsuario.add((Integer) jan);
		}
		if (fev != null) {
			listaIdUsuario.add((Integer) fev);
		}
		if (mar != null) {
			listaIdUsuario.add((Integer) mar);
		}
		if (abr != null) {
			listaIdUsuario.add((Integer) abr);
		}
		if (mai != null) {
			listaIdUsuario.add((Integer) mai);
		}
		if (jun != null) {
			listaIdUsuario.add((Integer) jun);
		}
		if (jul != null) {
			listaIdUsuario.add((Integer) jul);
		}
		if (ago != null) {
			listaIdUsuario.add((Integer) ago);
		}
		if (set != null) {
			listaIdUsuario.add((Integer) set);
		}
		if (out != null) {
			listaIdUsuario.add((Integer) out);
		}
		if (nov != null) {
			listaIdUsuario.add((Integer) nov);
		}
		if (dez != null) {
			listaIdUsuario.add((Integer) dez);
		}
		if (decimo != null) {
			listaIdUsuario.add((Integer) decimo);
		}

		if (!listaIdUsuario.isEmpty()) {
			this.legenda.put(listaIdUsuario.get(0), numero);
			for (Integer id : listaIdUsuario) {
				if (!this.legenda.containsKey(id)) {
					this.legenda.put(id, ++numero);
				}
			}
		}

	}

	private String converterNumeroLegenda(Integer num) {
		String letra = "";
		if (num != null) {
			switch (num) {
			case 1:
				letra = "A";
				break;
			case 2:
				letra = "B";
				break;
			case 3:
				letra = "C";
				break;
			case 4:
				letra = "D";
				break;
			case 5:
				letra = "E";
				break;
			case 6:
				letra = "F";
				break;
			case 7:
				letra = "G";
				break;
			case 8:
				letra = "H";
				break;
			case 9:
				letra = "I";
				break;
			case 10:
				letra = "J";
				break;
			case 11:
				letra = "K";
				break;
			case 12:
				letra = "L";
				break;
			default:
				letra = "";
				break;
			}
		}
		return letra;
	}

	private String textoRemessa(String situacao, Object objDataAssinatura) {
		String texto = "";
		
		Timestamp dataAssinatura = (Timestamp) objDataAssinatura;
		
		DateTimeFormatter formato = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss");
		
//		System.out.println("Situacao Remessa: " + situacao);
//		System.out.println("Data Assinatura: " + dataAssinatura.toLocalDateTime().format(formato));
		
//		String dataAssinatura = (String) objDataAssinatura;
		
//		if (dataAssinatura != null) {
//			if (dataAssinatura.isEmpty() || dataAssinatura.equals("null")) {
//				dataAssinatura = "";
//			}
//		}
		
		switch (situacao) {
		case "T":
		case "I":
//			texto = dataAssinatura;
			texto = dataAssinatura.toLocalDateTime().format(formato);
			break;
		case "A":
			texto = "Sem Remessa";
			break;
		case "L":
			texto = "---";
			break;
		default:
			texto = "";
			break;
		}

		return texto;
	}

	public void definirRemessaAtual() {
		LocalDate hoje = LocalDate.now();
		Integer anoAtual = null;
		Integer mesAtual = null;

		anoAtual = hoje.getYear();
		mesAtual = hoje.getMonthValue();

		LocalDate dataPrazoEnvio = this.prazoEnvioRemessaAtual.toLocalDate();

		if (hoje.isEqual(dataPrazoEnvio)) {
			anoAtual = hoje.getYear();
			mesAtual = hoje.getMonthValue();
		} else {
			if (hoje.isAfter(dataPrazoEnvio)) {
				if (hoje.getMonthValue() == 12) {
					anoAtual = hoje.getYear() + 1;
					mesAtual = 1;
				} else {
					anoAtual = hoje.getYear();
					mesAtual = hoje.getMonthValue() + 1;
				}
			} else {
				anoAtual = hoje.getYear();
				mesAtual = hoje.getMonthValue();
			}
		}

		this.anoRemessaAtual = anoAtual;

		switch (mesAtual) {
		case 1:
			this.primeiraRemessa = 11;
			this.segundaRemessa = 12;
			this.anoRemessaAtual = anoAtual - 1;
			this.mesPrazoRmessaAtual = 1;
			break;

		case 2:
		case 3:
			this.primeiraRemessa = 1;
			this.segundaRemessa = 2;
			this.mesPrazoRmessaAtual = 3;
			break;

		case 4:
		case 5:
			this.primeiraRemessa = 3;
			this.segundaRemessa = 4;
			this.mesPrazoRmessaAtual = 5;
			break;

		case 6:
		case 7:
			this.primeiraRemessa = 5;
			this.segundaRemessa = 6;
			this.mesPrazoRmessaAtual = 7;
			break;

		case 8:
		case 9:
			this.primeiraRemessa = 7;
			this.segundaRemessa = 8;
			this.mesPrazoRmessaAtual = 9;
			break;

		case 10:
		case 11:
			this.primeiraRemessa = 9;
			this.segundaRemessa = 10;
			this.mesPrazoRmessaAtual = 11;
			break;

		case 12:
			this.primeiraRemessa = 11;
			this.segundaRemessa = 12;
			this.mesPrazoRmessaAtual = 1;
			break;

		default:
			this.primeiraRemessa = 0;
			this.segundaRemessa = 0;
			this.mesPrazoRmessaAtual = 0;
			break;
		}
		System.out.println("anoAtual: " + anoAtual);
		System.out.println("mesAtual: " + mesAtual);
		System.out.println("primeiraRemessa: " + primeiraRemessa);
		System.out.println("segundaRemessa: " + segundaRemessa);
	}

	public static String calcularTempoAtraso(LocalDateTime assinatura, LocalDateTime prazo) {
		String tempoAtraso = null;
		LocalDateTime dataAssinatura = null;

		if (assinatura == null) {
			dataAssinatura = LocalDateTime.now();
		} else {
			dataAssinatura = assinatura;
		}

		try {
			if ((dataAssinatura != null) && (prazo != null)) {
				if (dataAssinatura.isAfter(prazo)) {

					long dateTime[] = getDiferenceDateTime(prazo, dataAssinatura);

					tempoAtraso = tempoPorExtenso(dateTime[0], dateTime[1], dateTime[2], dateTime[3], dateTime[4],
							dateTime[5]);
				} else {
					tempoAtraso = "Tempestiva";
				}
			}
		} catch (Exception e) {
		}

		return tempoAtraso;
	}

	private static long[] getDiferenceDateTime(LocalDateTime dateTimeInicial, LocalDateTime dateTimeFinal) {
		LocalDateTime tempDateTime = LocalDateTime.from(dateTimeInicial);

		long years = tempDateTime.until(dateTimeFinal, ChronoUnit.YEARS);
		tempDateTime = tempDateTime.plusYears(years);

		long months = tempDateTime.until(dateTimeFinal, ChronoUnit.MONTHS);
		tempDateTime = tempDateTime.plusMonths(months);

		long days = tempDateTime.until(dateTimeFinal, ChronoUnit.DAYS);
		tempDateTime = tempDateTime.plusDays(days);

		long hours = tempDateTime.until(dateTimeFinal, ChronoUnit.HOURS);
		tempDateTime = tempDateTime.plusHours(hours);

		long minutes = tempDateTime.until(dateTimeFinal, ChronoUnit.MINUTES);
		tempDateTime = tempDateTime.plusMinutes(minutes);

		long seconds = tempDateTime.until(dateTimeFinal, ChronoUnit.SECONDS);

		return new long[] { years, months, days, hours, minutes, seconds };
	}

	private static String tempoPorExtenso(long ano, long mes, long dia, long hora, long minuto, long segundo) {
		List<String> tempo = new LinkedList<>();

		if (ano != 0) {
			tempo.add((ano == 1) ? ano + " ano" : ano + " anos");
		}

		if (mes != 0) {
			tempo.add((mes == 1) ? mes + " m�s" : mes + " meses");
		}

		if (dia != 0) {
			tempo.add((dia == 1) ? dia + " dia" : dia + " dias");
		}

		if (hora != 0) {
			tempo.add((hora == 1) ? hora + " hora" : hora + " horas");
		}

		if (minuto != 0) {
			tempo.add((minuto == 1) ? minuto + " minuto" : minuto + " minutos");
		}

		if (segundo != 0) {
			tempo.add((segundo == 1) ? segundo + " segundo" : segundo + " segundos");
		}

		if (tempo.size() == 0) {
			return "Tempestiva";
		} else {
			return String.join(" ", tempo);
		}
	}

	public Boolean isFundo(Integer idEntidadeCjur) {
		List<Integer> fundos = Arrays.asList(this.listaFundos);

		if (fundos.contains(idEntidadeCjur)) {
			return true;
		} else {
			return false;
		}
	}

	public Integer getPrimeiraRemessa() {
		return primeiraRemessa;
	}

	public LocalDateTime getPrazoEnvioRemessaAtual() {
		return prazoEnvioRemessaAtual;
	}

	public void setPrazoEnvioRemessaAtual(LocalDateTime prazoEnvioRemessaAtual) {
		this.prazoEnvioRemessaAtual = prazoEnvioRemessaAtual;
	}

	private String converteTimestampParaTexto(Object objeto) {
		String textoData = "";
		
		Timestamp data = (Timestamp) objeto;
		
		if(data != null) {
			DateTimeFormatter formato = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss");
			textoData = data.toLocalDateTime().format(formato);	
		}
		
		
		return textoData;
	}

}
