package br.gov.ac.tce.sicapweb.modelo.cargo;

public enum TipoAcumulavel {
	PROFESSOR(Integer.valueOf(1), "Professor"), PROFISSIONAL_SAUDE(Integer.valueOf(2),
			"Profissional de Saúde"), NAO_APLICAVEL(Integer.valueOf(9), "Não Aplicável");

	private Integer id;
	private String descricao;

	private TipoAcumulavel(Integer id, String descricao) {
		this.id = id;
		this.descricao = descricao;
	}

	public Integer getId() {
		return id;
	}

	public String getDescricao() {
		return descricao;
	}

	public static TipoAcumulavel parse(Integer id) {
		TipoAcumulavel tipoAcumulavel = null;
		for (TipoAcumulavel item : TipoAcumulavel.values()) {
			if (item.getId() == id) {
				tipoAcumulavel = item;
				break;
			}
		}
		return tipoAcumulavel;
	}
}
