package br.gov.ac.tce.sicapweb.repositorio;

import java.io.Serializable;
import java.util.Collection;

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.TypedQuery;

import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;
import br.gov.ac.tce.sicapweb.modelo.remessa.RemessaEventual;
import br.gov.ac.tce.sicapweb.modelo.remessa.SituacaoRemessa;
import br.gov.ac.tce.sicapweb.xml.validador.TypeArquivo;

@Stateless
public class RemessaEventualRepositorio implements Serializable {

	private static final long serialVersionUID = 1L;

	@PersistenceContext(unitName = "sicapAnalise")
	private EntityManager entityManager;

	public Collection<RemessaEventual> listarTodasPorEntidade(Entidade entidade) throws RepositorioException {
		Collection<RemessaEventual> listaRemessaEventual = null;
		try {
			if (entidade.getIdEntidadeCjur() != null) {
				TypedQuery<RemessaEventual> query = this.entityManager
						.createNamedQuery(RemessaEventual.listarTodasPorEntidade, RemessaEventual.class);
				query.setParameter("entidade", entidade);
				listaRemessaEventual = query.getResultList();
			}
		} catch (Exception e) {
			throw new RepositorioException("Erro listarPorEntidade.", e.getCause());
		}
		return listaRemessaEventual;
	}

	public Collection<RemessaEventual> listarPorEntidadeTipo(Entidade entidade, TypeArquivo tipoRemessa)
			throws RepositorioException {
		Collection<RemessaEventual> listaRemessaEventual = null;
		try {
			if (entidade.getIdEntidadeCjur() != null) {
				TypedQuery<RemessaEventual> query = this.entityManager
						.createNamedQuery(RemessaEventual.listarPorEntidadeTipo, RemessaEventual.class);
				query.setParameter("entidade", entidade);
				query.setParameter("tipoRemessa", tipoRemessa.getId());
				listaRemessaEventual = query.getResultList();
			}
		} catch (Exception e) {
			throw new RepositorioException("Erro RemessaEventualRepositorio.listarPorEntidadeTipo.", e.getCause());
		}
		return listaRemessaEventual;
	}

	public Collection<RemessaEventual> listarPorEntidadeTipoSituacao(Entidade entidade, TypeArquivo tipoRemessa,
			SituacaoRemessa situacaoRemessa) throws RepositorioException {
		Collection<RemessaEventual> listaRemessaEventual = null;
		try {
			if (entidade.getIdEntidadeCjur() != null) {
				TypedQuery<RemessaEventual> query = this.entityManager
						.createNamedQuery(RemessaEventual.listarPorEntidadeTipoSituacao, RemessaEventual.class);
				query.setParameter("entidade", entidade);
				query.setParameter("tipoRemessa", tipoRemessa.getId());
				query.setParameter("situacao", situacaoRemessa.getId());
				listaRemessaEventual = query.getResultList();
			}
		} catch (Exception e) {
			throw new RepositorioException("Erro RemessaEventualRepositorio.listarPorEntidadeTipoSituacao.",
					e.getCause());
		}
		return listaRemessaEventual;
	}

	public RemessaEventual buscarPorId(Long id) throws RepositorioException {
		RemessaEventual remessaEventual = null;
		try {
			TypedQuery<RemessaEventual> query = this.entityManager.createNamedQuery(RemessaEventual.buscarPorId,
					RemessaEventual.class);
			query.setParameter("id", id);
			remessaEventual = query.getSingleResult();
		} catch (Exception e) {
			throw new RepositorioException("Erro RemessaEventualRepositorio.buscarPorId.", e.getCause());
		}
		return remessaEventual;
	}
}
