package br.gov.ac.tce.sicapweb.util;

import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;

public class Mensagem {

	public static void setMensagem(String titulo) {
		FacesContext.getCurrentInstance().addMessage(null,
				new FacesMessage(titulo));
	}

	public static void setMensagem(String titulo, String mensagem) {
		FacesContext.getCurrentInstance().addMessage(null,
				new FacesMessage(titulo, mensagem));
	}

	public static void setMensagem(MensagemType tipo, String titulo,
			String mensagem) {
		FacesContext.getCurrentInstance().addMessage(null,
				new FacesMessage(tipo.getSeverity(), titulo, mensagem));
	}

}
