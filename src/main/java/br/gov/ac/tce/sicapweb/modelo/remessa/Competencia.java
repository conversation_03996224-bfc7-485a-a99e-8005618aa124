package br.gov.ac.tce.sicapweb.modelo.remessa;

import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjuster;
import java.time.temporal.TemporalAdjusters;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import javax.persistence.Transient;

@Embeddable
public class Competencia {

	@Transient
	private final Integer MES_LIMITE_INFERIOR = 1;
	@Transient
	private final Integer MES_LIMITE_SUPERIOR = 12;

	@Column(nullable = false)
	private Integer mes;
	@Column(nullable = false)
	private Integer ano;

	public Competencia(Integer mes, Integer ano) throws Exception {
		if (mes < MES_LIMITE_INFERIOR || mes > MES_LIMITE_SUPERIOR) {
			throw new Exception(
					"MêS INFORMADO FORA DOS LIMITES - " + MES_LIMITE_INFERIOR + " a " + MES_LIMITE_SUPERIOR);
		} else {
			this.mes = mes;
			this.ano = ano;
		}
	}

	public Competencia() {

	}

	public Competencia proxima() throws Exception {

		if (this.mes == null || this.ano == null) {
			throw new Exception("MÊS OU ANO EM BRANCO");
		} else {

			Competencia competencia = new Competencia();

			if (this.mes == MES_LIMITE_SUPERIOR) {
				competencia.setMes(MES_LIMITE_INFERIOR);
				competencia.setAno(this.ano + 1);
			} else {
				competencia.setMes(this.mes + 1);
				competencia.setAno(this.ano);
			}

			return competencia;
		}
	}

	public Competencia anterior() throws Exception {
		if (this.mes == null || this.ano == null) {
			throw new Exception("MÊS OU ANO EM BRANCO");
		} else {
			Competencia competencia = new Competencia();

			if (this.mes == MES_LIMITE_INFERIOR) {
				competencia.setMes(MES_LIMITE_SUPERIOR);
				competencia.setAno(this.ano - 1);
			} else {
				competencia.setMes(this.mes - 1);
				competencia.setAno(this.ano);
			}

			return competencia;
		}
	}

	public Integer getMes() {
		return mes;
	}

	private void setMes(Integer mes) {
		this.mes = mes;
	}

	public Integer getAno() {
		return ano;
	}

	private void setAno(Integer ano) {
		this.ano = ano;
	}

	public LocalDateTime getPrazoLimite() throws Exception {
		if (this.mes == null || this.ano == null) {
			throw new Exception("MÊS OU ANO EM BRANCO");
		} else {
			Integer fimBimestre = 0;

			if (this.mes == 1 || this.mes == 2) {
				fimBimestre = 2;
			} else if (this.mes == 3 || this.mes == 4) {
				fimBimestre = 4;
			} else if (this.mes == 5 || this.mes == 6) {
				fimBimestre = 6;
			} else if (this.mes == 7 || this.mes == 8) {
				fimBimestre = 8;
			} else if (this.mes == 9 || this.mes == 10) {
				fimBimestre = 10;
			} else if (this.mes == 11 || this.mes == 12) {
				fimBimestre = 12;
			}

			TemporalAdjuster ajusteUltimoDia = TemporalAdjusters.lastDayOfMonth();
			LocalDateTime prazoLimite = LocalDateTime.of(this.ano, fimBimestre, 1, 23, 59, 59).with(ajusteUltimoDia);
			prazoLimite = prazoLimite.plusDays(30);
			return prazoLimite;
		}
	}

	public LocalDateTime getDataInicioCompetencia() throws Exception {
		if (this.mes == null || this.ano == null) {
			throw new Exception("MÊS OU ANO EM BRANCO");
		} else {
			TemporalAdjuster ajuste = TemporalAdjusters.firstDayOfMonth();
			return LocalDateTime.of(this.ano, this.mes, 1, 23, 59, 59).with(ajuste);
		}
	}

	public LocalDateTime getDataTerminoCompetencia() throws Exception {
		if (this.mes == null || this.ano == null) {
			throw new Exception("MÊS OU ANO EM BRANCO");
		} else {
			TemporalAdjuster ajuste = TemporalAdjusters.lastDayOfMonth();
			return LocalDateTime.of(this.ano, this.mes, 1, 23, 59, 59).with(ajuste);
		}
	}

	@Override
	public String toString() {
		return this.mes + "/" + this.ano;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((ano == null) ? 0 : ano.hashCode());
		result = prime * result + ((mes == null) ? 0 : mes.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		Competencia other = (Competencia) obj;
		if (ano == null) {
			if (other.ano != null)
				return false;
		} else if (!ano.equals(other.ano))
			return false;
		if (mes == null) {
			if (other.mes != null)
				return false;
		} else if (!mes.equals(other.mes))
			return false;
		return true;
	}

}