package br.gov.ac.tce.sicapweb.modelo;

public enum NumeroVinculosFiltro {
	TODOS("0", "Todos"),
	DOIS_VINCULOS("2", "2 vínculos"),
	TRES_OU_MAIS_VINCULOS("3", "3+ vínculos (Em violação direta ao artigo 37, XVII, da CF/88)");

	private String id;
	private String descricao;

	private NumeroVinculosFiltro(String id, String descricao) {
		this.id = id;
		this.descricao = descricao;
	}

	public String getId() {
		return id;
	}

	public String getDescricao() {
		return descricao;
	}

	public static NumeroVinculosFiltro parse(String id) {
		NumeroVinculosFiltro filtro = null;
		for (NumeroVinculosFiltro item : NumeroVinculosFiltro.values()) {
			if (item.getId().equals(id)) {
				filtro = item;
				break;
			}
		}
		return filtro;
	}
}
