package br.gov.ac.tce.sicapweb.modelo;

public enum Status {
	SIM(String.valueOf("S"), "Sim"), NAO(String.valueOf("N"), "Não");

	private String id;
	private String descricao;

	private Status(String id, String descricao) {
		this.id = id;
		this.descricao = descricao;
	}

	public String getId() {
		return id;
	}

	public String getDescricao() {
		return descricao;
	}

	public static Status parse(String id) {
		Status status = null;
		for (Status item : Status.values()) {
			if (item.getId().equals(id)) {
				status = item;
				break;
			}
		}
		return status;
	}
}
