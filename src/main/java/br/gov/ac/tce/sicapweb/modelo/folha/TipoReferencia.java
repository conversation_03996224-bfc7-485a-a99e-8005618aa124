package br.gov.ac.tce.sicapweb.modelo.folha;

public enum TipoReferencia {
	DIA(Integer.valueOf(1), "Dia"), HORA(Integer.valueOf(2), "Hora"), PERCENTUAL(Integer.valueOf(3),
			"Percentual"), VALORFIXO(Integer.valueOf(4),
					"Valor Fixo"), SEM_REFERENCIA(Integer.valueOf(99), "Sem Referência");

	private Integer id;
	private String descricao;

	private TipoReferencia(Integer id, String descricao) {
		this.id = id;
		this.descricao = descricao;
	}

	public Integer getId() {
		return id;
	}

	public String getDescricao() {
		return descricao;
	}

	public static TipoReferencia parse(Integer id) {
		TipoReferencia tipoReferencia = null;
		for (TipoReferencia item : TipoReferencia.values()) {
			if (item.getId() == id) {
				tipoReferencia = item;
				break;
			}
		}
		return tipoReferencia;
	}
}
