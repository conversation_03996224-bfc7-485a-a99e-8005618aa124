package br.gov.ac.tce.sicapweb.repositorio;

import java.io.Serializable;
import java.util.Collection;

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.TypedQuery;

import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapweb.modelo.servidor.SituacaoFuncional;

@Stateless
public class SituacaoFuncionalRepositorio implements Serializable {

	private static final long serialVersionUID = 1L;

	@PersistenceContext(unitName = "sicapAnalise")
	private EntityManager entityManager;

	public Collection<SituacaoFuncional> buscarTodos() throws RepositorioException {
		Collection<SituacaoFuncional> listaSituacaoFuncional = null;
		try {
			TypedQuery<SituacaoFuncional> query = this.entityManager.createNamedQuery(SituacaoFuncional.buscarTodos,
					SituacaoFuncional.class);
			listaSituacaoFuncional = query.getResultList();
		} catch (Exception e) {
			throw new RepositorioException("Erro ao SituacaoFuncionalRepositoriobuscarTodos.", e.getCause());
		}
		return listaSituacaoFuncional;
	}
}
