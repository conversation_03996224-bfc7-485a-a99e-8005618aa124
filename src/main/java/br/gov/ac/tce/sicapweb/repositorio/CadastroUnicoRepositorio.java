package br.gov.ac.tce.sicapweb.repositorio;

import java.io.Serializable;
import java.util.Collection;

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.TypedQuery;

import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;
import br.gov.ac.tce.sicapweb.modelo.pessoa.CadastroUnico;

@Stateless
public class CadastroUnicoRepositorio implements Serializable {

	private static final long serialVersionUID = 1L;

	@PersistenceContext(unitName = "sicapAnalise")
	private EntityManager entityManager;

	public Collection<CadastroUnico> buscarTodosPorEntidadeServidores(Entidade entidade) throws RepositorioException {
		Collection<CadastroUnico> listaCadastroUnico = null;
		try {
			if (entidade.getIdEntidadeCjur() != null) {
				TypedQuery<CadastroUnico> query = this.entityManager
						.createNamedQuery(CadastroUnico.buscarTodosPorEntidadeServidores, CadastroUnico.class);
				query.setParameter("entidade", entidade);
				listaCadastroUnico = query.getResultList();

			}
		} catch (Exception e) {
			throw new RepositorioException("Erro ao buscarTodosPorEntidade.", e.getCause());
		}
		return listaCadastroUnico;
	}

	public Collection<CadastroUnico> buscarTodosPorEntidadePensionistas(Entidade entidade) throws RepositorioException {
		Collection<CadastroUnico> listaCadastroUnico = null;
		try {
			if (entidade.getIdEntidadeCjur() != null) {
				TypedQuery<CadastroUnico> query = this.entityManager
						.createNamedQuery(CadastroUnico.buscarTodosPorEntidadePensionistas, CadastroUnico.class);
				query.setParameter("entidade", entidade);
				listaCadastroUnico = query.getResultList();
			}
		} catch (Exception e) {
			throw new RepositorioException("Erro ao buscarTodosPorEntidade.", e.getCause());
		}
		return listaCadastroUnico;
	}

}
