package br.gov.ac.tce.sicapweb.modelo.pensionista;

public enum TipoPensao {
	TEMPORARIA(Integer.valueOf(1), "Temporária"), VITALICIA(Integer.valueOf(2), "Vitalícia");

	private Integer id;
	private String descricao;

	private TipoPensao(Integer id, String descricao) {
		this.id = id;
		this.descricao = descricao;
	}

	public Integer getId() {
		return id;
	}

	public String getDescricao() {
		return descricao;
	}

	public static TipoPensao parse(Integer id) {
		TipoPensao tipoPensao = null;
		for (TipoPensao item : TipoPensao.values()) {
			if (item.getId() == id) {
				tipoPensao = item;
				break;
			}
		}
		return tipoPensao;
	}
}
