package br.gov.ac.tce.sicapweb.repositorio.entidade;

import java.io.Serializable;
import java.util.Collection;

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import com.uaihebert.uaicriteria.UaiCriteria;
import com.uaihebert.uaicriteria.UaiCriteriaFactory;

import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;
import br.gov.ac.tce.sicapweb.modelo.folha.ContraCheque;
import br.gov.ac.tce.sicapweb.modelo.folha.VerbasContraCheque;
import br.gov.ac.tce.sicapweb.modelo.pessoa.Beneficiario;

@Stateless
public class ContraChequeRepositorio implements Serializable {

	private static final long serialVersionUID = 1L;

	@PersistenceContext(unitName = "sicapAnalise")
	private EntityManager entityManager;

	public Collection<ContraCheque> pesquisaPorBeneficiario(Entidade entidade, Beneficiario beneficiario)
			throws RepositorioException {
		Collection<ContraCheque> listaContraCheque = null;

		try {
			UaiCriteria<ContraCheque> uaiCriteria = UaiCriteriaFactory.createQueryCriteria(this.entityManager,
					ContraCheque.class);

			uaiCriteria.andEquals("entidade", entidade);
			uaiCriteria.andEquals("beneficiario", beneficiario);
			uaiCriteria.orderByDesc("ano");
			uaiCriteria.orderByDesc("mes");
			uaiCriteria.orderByDesc("tipoFolha");

			listaContraCheque = uaiCriteria.getResultList();
		} catch (Exception e) {
			throw new RepositorioException("Erro ContraChequeRepositorio.pesquisaPorServidores", e.getCause());
		}

		return listaContraCheque;
	}

	public ContraCheque pesquisaPorId(ContraCheque contraCheque) throws RepositorioException {
		ContraCheque resultadoContraCheque = null;

		try {
			UaiCriteria<ContraCheque> uaiCriteria = UaiCriteriaFactory.createQueryCriteria(this.entityManager,
					ContraCheque.class);

			uaiCriteria.andEquals("id", contraCheque.getId());
			uaiCriteria.leftJoin("listaVerbasContraCheque");
			uaiCriteria.leftJoin("listaVerbasContraCheque.verba");
			uaiCriteria.orderByAsc("listaVerbasContraCheque.verba.natureza");

			resultadoContraCheque = uaiCriteria.getSingleResult();
		} catch (Exception e) {
			throw new RepositorioException("Erro ContraChequeRepositorio.pesquisaPorId", e.getCause());
		}

		return resultadoContraCheque;
	}

	public Collection<VerbasContraCheque> pesquisaPorVerbasContraCheque(ContraCheque contraCheque) throws RepositorioException {
		Collection<VerbasContraCheque> listaVerbasContraCheque = null;

		try {
			UaiCriteria<VerbasContraCheque> uaiCriteria = UaiCriteriaFactory.createQueryCriteria(this.entityManager,
					VerbasContraCheque.class);

			uaiCriteria.andEquals("contraCheque", contraCheque);
			uaiCriteria.leftJoin("verba");
			uaiCriteria.orderByAsc("verba.natureza");

			listaVerbasContraCheque = uaiCriteria.getResultList();
		} catch (Exception e) {
			throw new RepositorioException("Erro ContraChequeRepositorio.pesquisaPorVerbasContraCheque", e.getCause());
		}

		return listaVerbasContraCheque;
	}
	
}
