package br.gov.ac.tce.sicapweb.repositorio;

import java.io.Serializable;
import java.math.BigDecimal;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;

import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;

public class UsuarioInternoRespositorio implements Serializable {

	private static final long serialVersionUID = 1L;

	@PersistenceContext(unitName = "sicapAnalise")
	private EntityManager entityManager;

	public String buscarNomePorLogin(String login) throws RepositorioException {
		String nome = null;
		try {
			Query query = entityManager
					.createNativeQuery("SELECT s.nome FROM tce.dbo.Servidor s WHERE s.login = :login AND s.ativo = 1 ");
			query.setParameter("login", login);

			nome = (String) query.getSingleResult();
		} catch (Exception e) {
			throw new RepositorioException("Erro ao buscar usuario por id.", e.getCause());
		}
		return nome;
	}

	public Long buscarIdUsuarioPorLogin(String login) throws RepositorioException {
		BigDecimal idBD = null;
		Long id = null;
		try {
			Query query = entityManager
					.createNativeQuery("SELECT s.id FROM tce.dbo.Servidor s WHERE s.login = :login AND s.ativo = 1 ");
			query.setParameter("login", login);
			idBD = (BigDecimal) query.getSingleResult();
			id = idBD.longValue();
		} catch (Exception e) {
			e.printStackTrace();
			throw new RepositorioException("Erro ao buscar id do usuario por login.", e.getCause());
		}
		return id;
	}
	
	
}
