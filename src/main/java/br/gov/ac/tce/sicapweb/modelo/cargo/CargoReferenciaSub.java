package br.gov.ac.tce.sicapweb.modelo.cargo;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.ColumnResult;
import javax.persistence.ConstructorResult;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SqlResultSetMapping;
import javax.persistence.Table;

import br.gov.ac.tce.sicapanalise.auditoria.dto.CargoEntidadeDTO;

@Entity
@Table(name = "CargoRefSub")
@SqlResultSetMapping(name = "CargoEntidadeDTOMapping", classes = @ConstructorResult(targetClass = CargoEntidadeDTO.class, columns = {
		@ColumnResult(name="entidade_id", type = Integer.class),
		@ColumnResult(name="entidade_nome", type = String.class),
		@ColumnResult(name="cargo_tipo", type = String.class),
		@ColumnResult(name="cargo_referencia", type = String.class),
		@ColumnResult(name="cargo_nome", type = String.class),
		@ColumnResult(name="cargo_quantidade_servidores", type = Integer.class),
		@ColumnResult(name="entidade_quantidade_servidores",type = Integer.class )
}))
public class CargoReferenciaSub implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;
	@Column(name = "cargoRef_id", nullable = false)
	private Long cargoReferencia;
	@Column(length = 1256, nullable = false)
	private String descricao;
	@Column(nullable = false)
	private Boolean ativo;
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getCargoReferencia() {
		return cargoReferencia;
	}
	public void setCargoReferencia(Long cargoReferencia) {
		this.cargoReferencia = cargoReferencia;
	}
	public String getDescricao() {
		return descricao;
	}
	public void setDescricao(String descricao) {
		this.descricao = descricao;
	}
	public Boolean getAtivo() {
		return ativo;
	}
	public void setAtivo(Boolean ativo) {
		this.ativo = ativo;
	}
	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((ativo == null) ? 0 : ativo.hashCode());
		result = prime * result + ((cargoReferencia == null) ? 0 : cargoReferencia.hashCode());
		result = prime * result + ((descricao == null) ? 0 : descricao.hashCode());
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}
	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		CargoReferenciaSub other = (CargoReferenciaSub) obj;
		if (ativo == null) {
			if (other.ativo != null)
				return false;
		} else if (!ativo.equals(other.ativo))
			return false;
		if (cargoReferencia == null) {
			if (other.cargoReferencia != null)
				return false;
		} else if (!cargoReferencia.equals(other.cargoReferencia))
			return false;
		if (descricao == null) {
			if (other.descricao != null)
				return false;
		} else if (!descricao.equals(other.descricao))
			return false;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

	
}
