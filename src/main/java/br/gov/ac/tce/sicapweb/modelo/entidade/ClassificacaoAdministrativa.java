package br.gov.ac.tce.sicapweb.modelo.entidade;

public enum ClassificacaoAdministrativa {
	DIRETA("ADMINISTRAÇÃO DIRETA", 1),
	AUTARQUIA("AUTARQUIA", 2),
	EMPRESAPUBLICA("EMPRESA PÚBLICA", 3),
	SOCIEDADEDEECONOMIAMISTA("SOCIEDADE DE ECONOMIA MISTA", 4),
	FUNDACAO("FUNDAÇÃO PÚBLICA", 5),
	FUNDO("FUNDO", 6);
	
	private String descricao;
	private Integer id;
	
	private ClassificacaoAdministrativa(String descricao, Integer id) {
		this.descricao = descricao;
		this.id = id;
	}
	
	public Integer getId() {
		return id;
	}

	public String getDescricao() {
		return descricao;
	}
	
	public static ClassificacaoAdministrativa parse(Integer id) {
		ClassificacaoAdministrativa classificacaoAdministrativa = null;

		for (ClassificacaoAdministrativa item : ClassificacaoAdministrativa.values()) {
			if (item.getId() == id) {
				classificacaoAdministrativa = item;
				break;
			}
		}
		return classificacaoAdministrativa;
	}
	
	public static ClassificacaoAdministrativa parse(String chaveEnum) {
		return ClassificacaoAdministrativa.valueOf(chaveEnum);
	}
}
