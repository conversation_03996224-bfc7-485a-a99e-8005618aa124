package br.gov.ac.tce.sicapweb.modelo.pessoa;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Collection;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;

import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;
import br.gov.ac.tce.sicapweb.modelo.folha.TipoBeneficiario;
import br.gov.ac.tce.sicapweb.modelo.pensionista.Pensao;
import br.gov.ac.tce.sicapweb.modelo.remessa.RemessaEventual;
import br.gov.ac.tce.sicapweb.modelo.servidor.VinculoFuncional;

@Entity
@NamedQueries({
		@NamedQuery(name = Beneficiario.buscarTodosPorEntidade, query = "select b from Beneficiario b where b.entidade = :entidade"),
		@NamedQuery(name = Beneficiario.buscarTodosPorEntidadeRegistroAtivo, query = "select b from Beneficiario b where b.entidade = :entidade and b.registroAtivo = true") })
public class Beneficiario implements Serializable {

	private static final long serialVersionUID = 1L;

	public static final String buscarTodosPorEntidade = "Beneficiario.buscarTodosPorEntidade";
	public static final String buscarTodosPorEntidadeRegistroAtivo = "Beneficiario.buscarTodosPorEntidadeRegistroAtivo";
	public static final String buscarTodosPorEntidadePensionistas = "beneficiario.buscarTodosPorEntidadePensionistas";

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idEntidadeCjur", nullable = false)
	private Entidade entidade;
	@Column(nullable = false)
	private Integer matricula;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idCadastroUnico", nullable = false)
	private CadastroUnico cadastroUnico;
	@Column(length = 1, nullable = false)
	private String tipoBeneficiario;
	@OneToMany(mappedBy = "beneficiario", fetch = FetchType.LAZY)
	private Collection<VinculoFuncional> listaVinculosFuncionais;
	@OneToMany(mappedBy = "pensionista", fetch = FetchType.LAZY)
	private Collection<Pensao> listaPensoes;
	@Column(nullable = false)
	private Boolean registroAtivo;
	private LocalDateTime dataInativacao;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idRemessaEventual", nullable = false)
	private RemessaEventual remessaEventual;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Entidade getEntidade() {
		return entidade;
	}

	public void setEntidade(Entidade entidade) {
		this.entidade = entidade;
	}

	public Integer getMatricula() {
		return matricula;
	}

	public void setMatricula(Integer matricula) {
		this.matricula = matricula;
	}

	public CadastroUnico getCadastroUnico() {
		return cadastroUnico;
	}

	public void setCadastroUnico(CadastroUnico cadastroUnico) {
		this.cadastroUnico = cadastroUnico;
	}

	public TipoBeneficiario getTipoBeneficiario() {
		return TipoBeneficiario.parse(this.tipoBeneficiario);
	}

	public void setTipoBeneficiario(TipoBeneficiario tipoBeneficiario) {
		this.tipoBeneficiario = tipoBeneficiario.getId();
	}

	public Boolean getRegistroAtivo() {
		return registroAtivo;
	}

	public void setRegistroAtivo(Boolean registroAtivo) {
		this.registroAtivo = registroAtivo;
	}

	public LocalDateTime getDataInativacao() {
		return dataInativacao;
	}

	public void setDataInativacao(LocalDateTime dataInativacao) {
		this.dataInativacao = dataInativacao;
	}

	public RemessaEventual getRemessaEventual() {
		return remessaEventual;
	}

	public void setRemessaEventual(RemessaEventual remessaEventual) {
		this.remessaEventual = remessaEventual;
	}

	public Collection<VinculoFuncional> getListaVinculosFuncionais() {
		return listaVinculosFuncionais;
	}

	public void setListaVinculosFuncionais(Collection<VinculoFuncional> listaVinculosFuncionais) {
		this.listaVinculosFuncionais = listaVinculosFuncionais;
	}

	public Collection<Pensao> getListaPensoes() {
		return listaPensoes;
	}

	public void setListaPensoes(Collection<Pensao> listaPensoes) {
		this.listaPensoes = listaPensoes;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((cadastroUnico == null) ? 0 : cadastroUnico.hashCode());
		result = prime * result + ((dataInativacao == null) ? 0 : dataInativacao.hashCode());
		result = prime * result + ((entidade == null) ? 0 : entidade.hashCode());
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		result = prime * result + ((matricula == null) ? 0 : matricula.hashCode());
		result = prime * result + ((registroAtivo == null) ? 0 : registroAtivo.hashCode());
		result = prime * result + ((remessaEventual == null) ? 0 : remessaEventual.hashCode());
		result = prime * result + ((tipoBeneficiario == null) ? 0 : tipoBeneficiario.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		Beneficiario other = (Beneficiario) obj;
		if (cadastroUnico == null) {
			if (other.cadastroUnico != null)
				return false;
		} else if (!cadastroUnico.equals(other.cadastroUnico))
			return false;
		if (dataInativacao == null) {
			if (other.dataInativacao != null)
				return false;
		} else if (!dataInativacao.equals(other.dataInativacao))
			return false;
		if (entidade == null) {
			if (other.entidade != null)
				return false;
		} else if (!entidade.equals(other.entidade))
			return false;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		if (matricula == null) {
			if (other.matricula != null)
				return false;
		} else if (!matricula.equals(other.matricula))
			return false;
		if (registroAtivo == null) {
			if (other.registroAtivo != null)
				return false;
		} else if (!registroAtivo.equals(other.registroAtivo))
			return false;
		if (remessaEventual == null) {
			if (other.remessaEventual != null)
				return false;
		} else if (!remessaEventual.equals(other.remessaEventual))
			return false;
		if (tipoBeneficiario == null) {
			if (other.tipoBeneficiario != null)
				return false;
		} else if (!tipoBeneficiario.equals(other.tipoBeneficiario))
			return false;
		return true;
	}

}
