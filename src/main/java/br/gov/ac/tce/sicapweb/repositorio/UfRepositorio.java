package br.gov.ac.tce.sicapweb.repositorio;

import java.io.Serializable;
import java.util.Collection;

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import com.uaihebert.uaicriteria.UaiCriteria;
import com.uaihebert.uaicriteria.UaiCriteriaFactory;

import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapweb.modelo.unidadelotacao.Uf;

@Stateless
public class UfRepositorio implements Serializable {

	private static final long serialVersionUID = 1L;

	@PersistenceContext(unitName = "sicapAnalise")
	private EntityManager entityManager;

	public Collection<Uf> buscarTodos() throws RepositorioException {
		Collection<Uf> listaUf = null;
		try {
			UaiCriteria<Uf> uaiCriteria = UaiCriteriaFactory.createQueryCriteria(this.entityManager, Uf.class);
			uaiCriteria.orderByAsc("nome");

			listaUf = uaiCriteria.getResultList();
		} catch (Exception e) {
			throw new RepositorioException("Erro ao UfRepositorio.buscarPorId.", e.getCause());
		}
		return listaUf;
	}

	public Uf buscarPorId(Integer id) throws RepositorioException {
		Uf uf = null;
		try {
			UaiCriteria<Uf> uaiCriteria = UaiCriteriaFactory.createQueryCriteria(this.entityManager, Uf.class);
			uaiCriteria.andEquals("id", id);
			uaiCriteria.orderByAsc("nome");

			uf = uaiCriteria.getSingleResult();
		} catch (Exception e) {
			throw new RepositorioException("Erro ao UfRepositorio.buscarPorId.", e.getCause());
		}
		return uf;
	}
}
