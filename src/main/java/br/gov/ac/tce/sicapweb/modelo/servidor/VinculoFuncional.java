package br.gov.ac.tce.sicapweb.modelo.servidor;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;

import br.gov.ac.tce.sicapweb.modelo.cargo.Cargo;
import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;
import br.gov.ac.tce.sicapweb.modelo.pessoa.Beneficiario;
import br.gov.ac.tce.sicapweb.modelo.remessa.RemessaEventual;

@Entity
@NamedQueries({
		@NamedQuery(name = VinculoFuncional.buscarTodosPorEntidadeCJUR, query = "select v from VinculoFuncional v where v.entidade = :entidade"),
		@NamedQuery(name = VinculoFuncional.buscarTodosPorEntidadeCJURRegistroAtivo, query = "select v from VinculoFuncional v where v.entidade = :entidade and v.registroAtivo = true") })
public class VinculoFuncional implements Serializable {

	private static final long serialVersionUID = 1L;

	public static final String buscarTodosPorEntidadeCJUR = "VinculoFuncional.buscarTodosPorEntidadeCJUR";
	public static final String buscarTodosPorEntidadeCJURRegistroAtivo = "VinculoFuncional.buscarTodosPorEntidadeCJURRegistroAtivo";

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idEntidadeCjur", nullable = false)
	private Entidade entidade;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idBeneficiario", nullable = false)
	private Beneficiario beneficiario;
	@Column(nullable = false)
	private LocalDate dataAdmissao;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idCargo", nullable = false)
	private Cargo cargo;
	@Column(length = 1, nullable = false)
	private Integer tipoServidor;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idTipoVinculo", nullable = false)
	private TipoVinculo tipoVinculo;
	@Column(nullable = false)
	private Integer regimePrevidenciario;
	@Column(nullable = false)
	private Boolean registroAtivo;
	private LocalDateTime dataInativacao;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idRemessaEventual", nullable = false)
	private RemessaEventual remessaEventual;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Entidade getEntidade() {
		return entidade;
	}

	public void setEntidade(Entidade entidade) {
		this.entidade = entidade;
	}

	public Beneficiario getBeneficiario() {
		return beneficiario;
	}

	public void setBeneficiario(Beneficiario beneficiario) {
		this.beneficiario = beneficiario;
	}

	public LocalDate getDataAdmissao() {
		return dataAdmissao;
	}

	public void setDataAdmissao(LocalDate dataAdmissao) {
		this.dataAdmissao = dataAdmissao;
	}

	public Cargo getCargo() {
		return cargo;
	}

	public void setCargo(Cargo cargo) {
		this.cargo = cargo;
	}

	public TipoServidor getTipoServidor() {
		return TipoServidor.parse(this.tipoServidor);
	}

	public void setTipoServidor(TipoServidor tipoServidor) {
		this.tipoServidor = tipoServidor.getId();
	}

	public TipoVinculo getTipoVinculo() {
		return tipoVinculo;
	}

	public void setTipoVinculo(TipoVinculo tipoVinculo) {
		this.tipoVinculo = tipoVinculo;
	}

	public RegimePrevidenciario getRegimePrevidenciario() {
		return RegimePrevidenciario.parse(this.regimePrevidenciario);
	}

	public void setRegimePrevidenciario(RegimePrevidenciario regimePrevidenciario) {
		this.regimePrevidenciario = regimePrevidenciario.getId();
	}

	public Boolean getRegistroAtivo() {
		return registroAtivo;
	}

	public void setRegistroAtivo(Boolean registroAtivo) {
		this.registroAtivo = registroAtivo;
	}

	public LocalDateTime getDataInativacao() {
		return dataInativacao;
	}

	public void setDataInativacao(LocalDateTime dataInativacao) {
		this.dataInativacao = dataInativacao;
	}

	public RemessaEventual getRemessaEventual() {
		return remessaEventual;
	}

	public void setRemessaEventual(RemessaEventual remessaEventual) {
		this.remessaEventual = remessaEventual;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((beneficiario == null) ? 0 : beneficiario.hashCode());
		result = prime * result + ((cargo == null) ? 0 : cargo.hashCode());
		result = prime * result + ((dataAdmissao == null) ? 0 : dataAdmissao.hashCode());
		result = prime * result + ((dataInativacao == null) ? 0 : dataInativacao.hashCode());
		result = prime * result + ((entidade == null) ? 0 : entidade.hashCode());
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		result = prime * result + ((regimePrevidenciario == null) ? 0 : regimePrevidenciario.hashCode());
		result = prime * result + ((registroAtivo == null) ? 0 : registroAtivo.hashCode());
		result = prime * result + ((remessaEventual == null) ? 0 : remessaEventual.hashCode());
		result = prime * result + ((tipoServidor == null) ? 0 : tipoServidor.hashCode());
		result = prime * result + ((tipoVinculo == null) ? 0 : tipoVinculo.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		VinculoFuncional other = (VinculoFuncional) obj;
		if (beneficiario == null) {
			if (other.beneficiario != null)
				return false;
		} else if (!beneficiario.equals(other.beneficiario))
			return false;
		if (cargo == null) {
			if (other.cargo != null)
				return false;
		} else if (!cargo.equals(other.cargo))
			return false;
		if (dataAdmissao == null) {
			if (other.dataAdmissao != null)
				return false;
		} else if (!dataAdmissao.equals(other.dataAdmissao))
			return false;
		if (dataInativacao == null) {
			if (other.dataInativacao != null)
				return false;
		} else if (!dataInativacao.equals(other.dataInativacao))
			return false;
		if (entidade == null) {
			if (other.entidade != null)
				return false;
		} else if (!entidade.equals(other.entidade))
			return false;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		if (regimePrevidenciario == null) {
			if (other.regimePrevidenciario != null)
				return false;
		} else if (!regimePrevidenciario.equals(other.regimePrevidenciario))
			return false;
		if (registroAtivo == null) {
			if (other.registroAtivo != null)
				return false;
		} else if (!registroAtivo.equals(other.registroAtivo))
			return false;
		if (remessaEventual == null) {
			if (other.remessaEventual != null)
				return false;
		} else if (!remessaEventual.equals(other.remessaEventual))
			return false;
		if (tipoServidor == null) {
			if (other.tipoServidor != null)
				return false;
		} else if (!tipoServidor.equals(other.tipoServidor))
			return false;
		if (tipoVinculo == null) {
			if (other.tipoVinculo != null)
				return false;
		} else if (!tipoVinculo.equals(other.tipoVinculo))
			return false;
		return true;
	}

}
