package br.gov.ac.tce.sicapweb.repositorio.entidade;

import java.io.Serializable;
import java.util.Collection;

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.TypedQuery;

import com.uaihebert.uaicriteria.UaiCriteria;
import com.uaihebert.uaicriteria.UaiCriteriaFactory;

import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapweb.modelo.cargo.Cargo;
import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;
import br.gov.ac.tce.sicapweb.modelo.remessa.RemessaEventual;

@Stateless
public class CargoRepositorio implements Serializable {

	private static final long serialVersionUID = 1L;

	@PersistenceContext(unitName = "sicapAnalise")
	private EntityManager entityManager;

	public Collection<Cargo> pesquisaCargos(Entidade entidade, RemessaEventual remessaEventual, Integer tipoCargo,
			Integer tipoEscolaridade, Integer tipoAcumulavel, Integer situacao, String nome)
			throws RepositorioException {
		Collection<Cargo> listaCargo = null;

		try {
			UaiCriteria<Cargo> uaiCriteria = UaiCriteriaFactory.createQueryCriteria(this.entityManager, Cargo.class);
			uaiCriteria.andEquals("entidade", entidade);

			if (remessaEventual.getId() != 0) {
				uaiCriteria.andEquals("remessaEventual", remessaEventual);
			}

			if (tipoCargo != 0) {
				uaiCriteria.andEquals("tipo", tipoCargo);
			}

			if (tipoEscolaridade != 0) {
				uaiCriteria.andEquals("escolaridade", tipoEscolaridade);
			}

			if (tipoAcumulavel != 0) {
				uaiCriteria.andEquals("tipoAcumulavel", tipoAcumulavel);
			}

			switch (situacao) {
			case 1:
				uaiCriteria.andEquals("registroAtivo", true);
				break;
			case 2:
				uaiCriteria.andEquals("registroAtivo", false);
				break;
			default:
				break;
			}

			if ((nome != null) && (!nome.isEmpty())) {
				uaiCriteria.andStringLike("nome", "%" + nome + "%");
			}

			listaCargo = uaiCriteria.getResultList();

		} catch (Exception e) {
			throw new RepositorioException("Erro cargoRepositorio.pesquisaCargos.", e.getCause());
		}
		return listaCargo;
	}

	public Collection<Cargo> listaTodosPorEntidade(Entidade entidade) throws RepositorioException {
		Collection<Cargo> listaCargo = null;

		try {
			UaiCriteria<Cargo> uaiCriteria = UaiCriteriaFactory.createQueryCriteria(this.entityManager, Cargo.class);
			uaiCriteria.andEquals("entidade", entidade);

			listaCargo = uaiCriteria.getResultList();

		} catch (Exception e) {
			throw new RepositorioException("Erro cargoRepositorio.listaTodos.", e.getCause());
		}
		return listaCargo;
	}

	public Cargo buscaCargoPorId(Entidade entidade, Cargo cargo) throws RepositorioException {
		Cargo cargoResult = null;

		try {
			UaiCriteria<Cargo> uaiCriteria = UaiCriteriaFactory.createQueryCriteria(this.entityManager, Cargo.class);
			uaiCriteria.andEquals("entidade", cargo.getEntidade());
			uaiCriteria.andEquals("id", cargo.getId());

			cargoResult = uaiCriteria.getSingleResult();
		} catch (Exception e) {
			throw new RepositorioException("Erro buscaCargoPorId.", e.getCause());
		}
		return cargoResult;
	}

	public Collection<Cargo> buscaCargoPorCodigo(Cargo cargo) throws RepositorioException {
		Collection<Cargo> listaCargos = null;

		try {
			UaiCriteria<Cargo> uaiCriteria = UaiCriteriaFactory.createQueryCriteria(this.entityManager, Cargo.class);
			uaiCriteria.andEquals("entidade", cargo.getEntidade());
			uaiCriteria.andEquals("codigo", cargo.getCodigo());
			uaiCriteria.orderByAsc("id");
			listaCargos = uaiCriteria.getResultList();
		} catch (Exception e) {
			throw new RepositorioException("Erro buscaCargoPorCodigo.", e.getCause());
		}
		return listaCargos;
	}

	public Collection<Cargo> buscarTodosPorEntidade(Entidade entidade) throws RepositorioException {
		Collection<Cargo> listaCargos = null;
		try {
			if (entidade.getIdEntidadeCjur() != null) {
				TypedQuery<Cargo> query = this.entityManager.createNamedQuery(Cargo.buscarTodosPorEntidadeCJUR,
						Cargo.class);
				query.setParameter("entidade", entidade);
				listaCargos = query.getResultList();
			}
		} catch (Exception e) {
			throw new RepositorioException("Erro cargoRepositorio.buscarTodosPorEntidade.", e.getCause());
		}
		return listaCargos;
	}

	public Collection<Cargo> buscarTodosPorEntidadeRegistroAtivo(Entidade entidade) throws RepositorioException {
		Collection<Cargo> listaCargos = null;
		try {
			if (entidade.getIdEntidadeCjur() != null) {
				TypedQuery<Cargo> query = this.entityManager
						.createNamedQuery(Cargo.buscarTodosPorEntidadeCJURRegistroAtivo, Cargo.class);
				query.setParameter("entidade", entidade);
				listaCargos = query.getResultList();
			}
		} catch (Exception e) {
			throw new RepositorioException("Erro cargoRepositorio.buscarTodosPorEntidadeRegistroAtivo.", e.getCause());
		}
		return listaCargos;
	}
}
