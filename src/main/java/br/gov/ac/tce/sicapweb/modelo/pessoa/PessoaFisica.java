package br.gov.ac.tce.sicapweb.modelo.pessoa;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;

import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;
import br.gov.ac.tce.sicapweb.modelo.remessa.RemessaEventual;

@Entity
@NamedQueries({
		@NamedQuery(name = PessoaFisica.buscarTodosPorEntidadeCJUR, query = "select p from PessoaFisica p where p.entidade = :entidade"),
		@NamedQuery(name = PessoaFisica.buscarTodosPorEntidadeCJURRegistroAtivo, query = "select p from PessoaFisica p where p.entidade = :entidade and p.registroAtivo = true") })
public class PessoaFisica implements Serializable {

	private static final long serialVersionUID = 1L;

	public static final String buscarTodosPorEntidadeCJUR = "Servidor.buscarTodosPorEntidadeCJUR";
	public static final String buscarTodosPorEntidadeCJURRegistroAtivo = "Servidor.buscarTodosPorEntidadeCJURRegistroAtivo";

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idEntidadeCjur", nullable = false)
	private Entidade entidade;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idCadastroUnico", nullable = false)
	private CadastroUnico cadastroUnico;
	@Column(length = 100, nullable = false)
	private String nome;
	@Column(nullable = false)
	private LocalDate dataNascimento;
	@Column(nullable = false)
	private String nomeMae;
	@Column(length = 1, nullable = false)
	private String sexo;
	@Column(nullable = false)
	private Boolean registroAtivo;
	private LocalDateTime dataInativacao;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idRemessaEventual", nullable = false)
	private RemessaEventual remessaEventual;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Entidade getEntidade() {
		return entidade;
	}

	public void setEntidade(Entidade entidade) {
		this.entidade = entidade;
	}

	public String getNome() {
		return nome;
	}

	public void setNome(String nome) {
		this.nome = nome;
	}

	public LocalDate getDataNascimento() {
		return dataNascimento;
	}

	public void setDataNascimento(LocalDate dataNascimento) {
		this.dataNascimento = dataNascimento;
	}

	public String getNomeMae() {
		return nomeMae;
	}

	public void setNomeMae(String nomeMae) {
		this.nomeMae = nomeMae;
	}

	public Sexo getSexo() {
		return Sexo.parse(this.sexo);
	}

	public void setSexo(Sexo sexo) {
		this.sexo = sexo.getId();
	}

	public Boolean getRegistroAtivo() {
		return registroAtivo;
	}

	public void setRegistroAtivo(Boolean registroAtivo) {
		this.registroAtivo = registroAtivo;
	}

	public LocalDateTime getDataInativacao() {
		return dataInativacao;
	}

	public void setDataInativacao(LocalDateTime dataInativacao) {
		this.dataInativacao = dataInativacao;
	}

	public RemessaEventual getRemessaEventual() {
		return remessaEventual;
	}

	public void setRemessaEventual(RemessaEventual remessaEventual) {
		this.remessaEventual = remessaEventual;
	}

	public CadastroUnico getCadastroUnico() {
		return cadastroUnico;
	}

	public void setCadastroUnico(CadastroUnico cadastroUnico) {
		this.cadastroUnico = cadastroUnico;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((cadastroUnico == null) ? 0 : cadastroUnico.hashCode());
		result = prime * result + ((dataInativacao == null) ? 0 : dataInativacao.hashCode());
		result = prime * result + ((dataNascimento == null) ? 0 : dataNascimento.hashCode());
		result = prime * result + ((entidade == null) ? 0 : entidade.hashCode());
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		result = prime * result + ((nome == null) ? 0 : nome.hashCode());
		result = prime * result + ((nomeMae == null) ? 0 : nomeMae.hashCode());
		result = prime * result + ((registroAtivo == null) ? 0 : registroAtivo.hashCode());
		result = prime * result + ((remessaEventual == null) ? 0 : remessaEventual.hashCode());
		result = prime * result + ((sexo == null) ? 0 : sexo.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		PessoaFisica other = (PessoaFisica) obj;
		if (cadastroUnico == null) {
			if (other.cadastroUnico != null)
				return false;
		} else if (!cadastroUnico.equals(other.cadastroUnico))
			return false;
		if (dataInativacao == null) {
			if (other.dataInativacao != null)
				return false;
		} else if (!dataInativacao.equals(other.dataInativacao))
			return false;
		if (dataNascimento == null) {
			if (other.dataNascimento != null)
				return false;
		} else if (!dataNascimento.equals(other.dataNascimento))
			return false;
		if (entidade == null) {
			if (other.entidade != null)
				return false;
		} else if (!entidade.equals(other.entidade))
			return false;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		if (nome == null) {
			if (other.nome != null)
				return false;
		} else if (!nome.equals(other.nome))
			return false;
		if (nomeMae == null) {
			if (other.nomeMae != null)
				return false;
		} else if (!nomeMae.equals(other.nomeMae))
			return false;
		if (registroAtivo == null) {
			if (other.registroAtivo != null)
				return false;
		} else if (!registroAtivo.equals(other.registroAtivo))
			return false;
		if (remessaEventual == null) {
			if (other.remessaEventual != null)
				return false;
		} else if (!remessaEventual.equals(other.remessaEventual))
			return false;
		if (sexo == null) {
			if (other.sexo != null)
				return false;
		} else if (!sexo.equals(other.sexo))
			return false;
		return true;
	}

}