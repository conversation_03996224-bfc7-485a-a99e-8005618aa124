package br.gov.ac.tce.sicapweb.repositorio;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import javax.persistence.TypedQuery;

import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;
import br.gov.ac.tce.sicapweb.modelo.folha.TipoFolha;
import br.gov.ac.tce.sicapweb.modelo.remessa.Remessa;

@Stateless
public class RemessaRepositorio implements Serializable {

	private static final long serialVersionUID = 1L;

	@PersistenceContext(unitName = "sicapAnalise")
	private EntityManager em;

	public void salva(Remessa remessa) {
		this.em.merge(remessa);
	}

	public void atualizar(Remessa remessa) throws RepositorioException {
		try {
			this.em.merge(remessa);
		} catch (Exception e) {
			throw new RepositorioException("Erro ao atualizar remessa.", e.getCause());
		}
	}

	public Remessa buscarPorId(Long id) throws RepositorioException {
		Remessa remessa = null;
		try {
			TypedQuery<Remessa> query = this.em.createNamedQuery(Remessa.buscarPorId, Remessa.class);
			query.setParameter("id", id);
			remessa = query.getSingleResult();
		} catch (Exception e) {
			throw new RepositorioException("Remessa - Erro buscarPorId.", e.getCause());
		}
		return remessa;
	}

	@SuppressWarnings("unchecked")
	public List<Remessa> lista(Entidade entidade) {
		Query query = this.em.createQuery(
				"SELECT r FROM Remessa r WHERE r.entidade.idEntidadeCjur = :idEntidadeCjur ORDER BY r.id desc");

		query.setParameter("idEntidadeCjur", entidade.getIdEntidadeCjur());

		return (List<Remessa>) query.getResultList();
	}

	@SuppressWarnings("unchecked")
	public List<Remessa> listaProcessadas(Entidade entidade) {
		Query query = this.em.createQuery(
				"SELECT r FROM Remessa r WHERE r.entidade.idEntidadeCjur = :idEntidadeCjur AND r.situacao = 'PR'");

		query.setParameter("idEntidadeCjur", entidade.getIdEntidadeCjur());

		return (List<Remessa>) query.getResultList();
	}

	@SuppressWarnings("unchecked")
	public List<Remessa> listaRemessaResumo(Entidade entidade) {
		Query query = this.em.createQuery(
				"SELECT r FROM Remessa r WHERE r.entidade.idEntidadeCjur = :idEntidadeCjur AND r.situacao in ('PR', 'CO')");

		query.setParameter("idEntidadeCjur", entidade.getIdEntidadeCjur());

		return (List<Remessa>) query.getResultList();
	}

	public Collection<Remessa> listarTodasPorEntidade(Entidade entidade) throws RepositorioException {
		Collection<Remessa> listaRemessas = null;
		try {
			TypedQuery<Remessa> query = this.em.createNamedQuery(Remessa.listarTodasPorEntidade, Remessa.class);

			query.setParameter("entidade", entidade);
			listaRemessas = query.getResultList();
		} catch (Exception e) {
			throw new RepositorioException("Erro RemessaRepositorio.listarTodasPorEntidade.", e.getCause());
		}
		return listaRemessas;
	}

	@SuppressWarnings("unchecked")
	public List<Object[]> consultaResumoProcessamento(Remessa remessa, TipoFolha tipoFolha) {
		if (tipoFolha == null || tipoFolha.getId() == null) {
			Query query = this.em.createNativeQuery("select tf.codigo AS CODIGO_TIPO_FOLHA, "
					+ "tf.descricao AS DESCRICAO_TIPO_FOLHA, cc.situacaoBeneficiario as CODIGO_SITUACAO, "
					+ "CASE WHEN cc.situacaoBeneficiario =  'A' THEN 'ATIVO' WHEN  cc.situacaoBeneficiario = 'I' THEN 'INATIVO' WHEN cc.situacaoBeneficiario =  'P' THEN 'PENSIONISTA' END as SITUACAO, "
					+ "ca.codigo as CODIGO_CARGO, "
					+ "CASE WHEN ca.codigo is null THEN 'PENSIONISTA' ELSE ca.nome END as DESCRICAO_CARGO, "
					+ "count(1) as QUANTIDADE,      SUM(cc.totalDescontos) as TOTAL_DESCONTOS_CABECALHO, "
					+ "SUM(cc.totalVencimentos) as TOTAL_VENCIMENTOS_CABECALHO, "
					+ "SUM(verbas.descontos) as TOTAL_DESCONTOS_VERBAS, "
					+ "SUM(verbas.vencimentos) AS TOTAL_VENCIMENTOS_VERBAS from    dbo.contracheque cc "
					+ "join dbo.tipofolha tf   on cc.idtipofolha = tf.id    left join dbo.cargo ca "
					+ "on cc.idcargo = ca.id "
					+ "LEFT JOIN (SELECT re.idcontracheque, SUM(re.descontos) as descontos, SUM(re.vencimentos) as vencimentos "
					+ "from "
					+ "(SELECT vc.idContraCheque as idContracheque, CASE WHEN ve.natureza = 'D' THEN vc.valor ELSE 0 END as descontos, CASE WHEN ve.natureza = 'C' THEN vc.valor ELSE 0 END as vencimentos from dbo.VerbasContraCheque vc, dbo.verba ve where vc.idVerba = ve.id) re "
					+ "group by re.idcontracheque) verbas ON cc.id = verbas.idcontracheque " + "where cc.idremessa = ? "
					+ "group by cc.situacaoBeneficiario, CASE WHEN cc.situacaoBeneficiario =  'A' THEN 'ATIVO' WHEN  cc.situacaoBeneficiario = 'I' THEN 'INATIVO' WHEN cc.situacaoBeneficiario = 'P' THEN 'PENSIONISTA' END, ca.codigo, CASE WHEN ca.codigo = null THEN 'Pensionista' ELSE ca.nome END, "
					+ "ca.codigo, CASE WHEN ca.codigo = null THEN 'Pensionista' ELSE ca.nome END, tf.codigo, tf.descricao "
					+ "order by cc.situacaoBeneficiario");

			query.setParameter(1, remessa.getId());
			return (List<Object[]>) query.getResultList();
		} else {
			Query query = this.em.createNativeQuery("select tf.codigo AS CODIGO_TIPO_FOLHA, "
					+ "tf.descricao AS DESCRICAO_TIPO_FOLHA, cc.situacaoBeneficiario as CODIGO_SITUACAO, "
					+ "CASE WHEN cc.situacaoBeneficiario =  'A' THEN 'ATIVO' WHEN  cc.situacaoBeneficiario = 'I' THEN 'INATIVO' WHEN cc.situacaoBeneficiario =  'P' THEN 'PENSIONISTA' END as SITUACAO, "
					+ "ca.codigo as CODIGO_CARGO, "
					+ "CASE WHEN ca.codigo is null THEN 'PENSIONISTA' ELSE ca.nome END as DESCRICAO_CARGO, "
					+ "count(1) as QUANTIDADE,      SUM(cc.totalDescontos) as TOTAL_DESCONTOS_CABECALHO, "
					+ "SUM(cc.totalVencimentos) as TOTAL_VENCIMENTOS_CABECALHO, "
					+ "SUM(verbas.descontos) as TOTAL_DESCONTOS_VERBAS, "
					+ "SUM(verbas.vencimentos) AS TOTAL_VENCIMENTOS_VERBAS " + "from    dbo.contracheque cc "
					+ "join dbo.tipofolha tf   on cc.idtipofolha = tf.id    left join dbo.cargo ca "
					+ "on cc.idcargo = ca.id "
					+ "LEFT JOIN (SELECT re.idcontracheque, SUM(re.descontos) as descontos, SUM(re.vencimentos) as vencimentos "
					+ "from "
					+ "(SELECT vc.idContraCheque as idContracheque, CASE WHEN ve.natureza = 'D' THEN vc.valor ELSE 0 END as descontos, CASE WHEN ve.natureza = 'C' THEN vc.valor ELSE 0 END as vencimentos from dbo.VerbasContraCheque vc, dbo.verba ve where vc.idVerba = ve.id) re "
					+ "group by re.idcontracheque) verbas ON cc.id = verbas.idcontracheque "
					+ "where cc.idremessa = ? and tf.id = ? "
					+ "group by cc.situacaoBeneficiario, CASE WHEN cc.situacaoBeneficiario =  'A' THEN 'ATIVO' WHEN  cc.situacaoBeneficiario = 'I' THEN 'INATIVO' WHEN cc.situacaoBeneficiario = 'P' THEN 'PENSIONISTA' END, ca.codigo, CASE WHEN ca.codigo = null THEN 'Pensionista' ELSE ca.nome END, "
					+ "ca.codigo, CASE WHEN ca.codigo = null THEN 'Pensionista' ELSE ca.nome END, tf.codigo, tf.descricao "
					+ "order by cc.situacaoBeneficiario");

			query.setParameter(1, remessa.getId());
			query.setParameter(2, tipoFolha.getId());
			return (List<Object[]>) query.getResultList();
		}
	}

	public List<Object[]> consultaResumoProcessamentoPorTipoFolha(Remessa remessa, TipoFolha tipoFolha) {
		if (tipoFolha == null || tipoFolha.getId() == null) {
			Query query = this.em.createNativeQuery(" select "
					+ " tf.codigo AS CODIGO_TIPO_FOLHA,    tf.descricao AS DESCRICAO_TIPO_FOLHA, count(1) as QUANTIDADE, SUM(cc.totalDescontos) as TOTAL_DESCONTOS, SUM(cc.totalVencimentos) as TOTAL_VENCIMENTOS, sum(verbas.descontos) as total_descontos_verbas, sum(verbas.vencimentos) as total_vencimentos_verbas"
					+ " from    dbo.contracheque cc" + ""
					+ " join dbo.tipofolha tf   	on cc.idtipofolha = tf.id    left join dbo.cargo ca" + ""
					+ " on cc.idcargo = ca.id" + "  "
					+ " LEFT JOIN (SELECT re.idcontracheque, SUM(re.descontos) as descontos, SUM(re.vencimentos) as vencimentos"
					+ " from"
					+ " (SELECT vc.idContraCheque as idContracheque, CASE WHEN ve.natureza = 'D' THEN vc.valor ELSE 0 END as descontos, CASE WHEN ve.natureza = 'C' THEN vc.valor ELSE 0 END as vencimentos from dbo.VerbasContraCheque vc, dbo.verba ve where vc.idVerba = ve.id) re"
					+ " group by re.idcontracheque) verbas" + "" + " ON cc.id = verbas.idcontracheque"
					+ " where cc.idremessa = ? " + " group by tf.codigo, tf.descricao   order by tf.descricao");

			query.setParameter(1, remessa.getId());

			return (List<Object[]>) query.getResultList();
		} else {
			Query query = this.em.createNativeQuery(" select "
					+ " tf.codigo AS CODIGO_TIPO_FOLHA,    tf.descricao AS DESCRICAO_TIPO_FOLHA, count(1) as QUANTIDADE, SUM(cc.totalDescontos) as TOTAL_DESCONTOS, SUM(cc.totalVencimentos) as TOTAL_VENCIMENTOS, sum(verbas.descontos) as total_descontos_verbas, sum(verbas.vencimentos) as total_vencimentos_verbas"
					+ " from    dbo.contracheque cc" + ""
					+ " join dbo.tipofolha tf   	on cc.idtipofolha = tf.id    left join dbo.cargo ca" + ""
					+ " on cc.idcargo = ca.id" + "  "
					+ " LEFT JOIN (SELECT re.idcontracheque, SUM(re.descontos) as descontos, SUM(re.vencimentos) as vencimentos"
					+ " from"
					+ " (SELECT vc.idContraCheque as idContracheque, CASE WHEN ve.natureza = 'D' THEN vc.valor ELSE 0 END as descontos, CASE WHEN ve.natureza = 'C' THEN vc.valor ELSE 0 END as vencimentos from dbo.VerbasContraCheque vc, dbo.verba ve where vc.idVerba = ve.id) re"
					+ " group by re.idcontracheque) verbas" + "" + " ON cc.id = verbas.idcontracheque"
					+ " where cc.idremessa = ? and tf.id = ? "
					+ " group by tf.codigo, tf.descricao   order by tf.descricao");

			query.setParameter(1, remessa.getId());
			query.setParameter(2, tipoFolha.getId());

			return (List<Object[]>) query.getResultList();
		}
	}

	public List<Object[]> consultaResumoProcessamentoPorSituacao(Remessa remessa, TipoFolha tipoFolha) {
		if (tipoFolha == null || tipoFolha.getId() == null) {
			Query query = this.em.createNativeQuery(
					"select cc.situacaoBeneficiario as CODIGO_SITUACAO,  CASE WHEN cc.situacaoBeneficiario =  'A' THEN 'ATIVO' WHEN  cc.situacaoBeneficiario = 'I' THEN 'INATIVO' WHEN cc.situacaoBeneficiario =  'P' THEN 'PENSIONISTA' END as SITUACAO, "
							+ " count(1) as QUANTIDADE,      SUM(cc.totalDescontos) as TOTAL_DESCONTOS,"
							+ " SUM(cc.totalVencimentos) as TOTAL_VENCIMENTOS,"
							+ " SUM(verbas.descontos) as descontos_verbas,"
							+ " SUM(verbas.vencimentos) as vencimentos_verbas" + " from    dbo.contracheque cc"
							+ " join dbo.tipofolha tf   	on cc.idtipofolha = tf.id    left join dbo.cargo ca"
							+ " on cc.idcargo = ca.id"
							+ " LEFT JOIN (SELECT re.idcontracheque, SUM(re.descontos) as descontos, SUM(re.vencimentos) as vencimentos"
							+ " from"
							+ " (SELECT vc.idContraCheque as idContracheque, CASE WHEN ve.natureza = 'D' THEN vc.valor ELSE 0 END as descontos, CASE WHEN ve.natureza = 'C' THEN vc.valor ELSE 0 END as vencimentos from dbo.VerbasContraCheque vc, dbo.verba ve where vc.idVerba = ve.id) re"
							+ " group by re.idcontracheque) verbas" + " ON cc.id = verbas.idcontracheque"
							+ " where cc.idremessa = ?"
							+ " group by cc.situacaoBeneficiario, CASE WHEN cc.situacaoBeneficiario =  'A' THEN 'ATIVO' WHEN  cc.situacaoBeneficiario = 'I' THEN 'INATIVO' WHEN cc.situacaoBeneficiario = 'P' THEN 'PENSIONISTA' END"
							+ " order by cc.situacaoBeneficiario");

			query.setParameter(1, remessa.getId());

			return (List<Object[]>) query.getResultList();
		} else {
			Query query = this.em.createNativeQuery(
					"select cc.situacaoBeneficiario as CODIGO_SITUACAO,  CASE WHEN cc.situacaoBeneficiario =  'A' THEN 'ATIVO' WHEN  cc.situacaoBeneficiario = 'I' THEN 'INATIVO' WHEN cc.situacaoBeneficiario =  'P' THEN 'PENSIONISTA' END as SITUACAO, "
							+ " count(1) as QUANTIDADE,      SUM(cc.totalDescontos) as TOTAL_DESCONTOS,"
							+ " SUM(cc.totalVencimentos) as TOTAL_VENCIMENTOS,"
							+ " SUM(verbas.descontos) as descontos_verbas,"
							+ " SUM(verbas.vencimentos) as vencimentos_verbas" + " from    dbo.contracheque cc"
							+ " join dbo.tipofolha tf   	on cc.idtipofolha = tf.id    left join dbo.cargo ca"
							+ " on cc.idcargo = ca.id"
							+ " LEFT JOIN (SELECT re.idcontracheque, SUM(re.descontos) as descontos, SUM(re.vencimentos) as vencimentos"
							+ " from"
							+ " (SELECT vc.idContraCheque as idContracheque, CASE WHEN ve.natureza = 'D' THEN vc.valor ELSE 0 END as descontos, CASE WHEN ve.natureza = 'C' THEN vc.valor ELSE 0 END as vencimentos from dbo.VerbasContraCheque vc, dbo.verba ve where vc.idVerba = ve.id) re"
							+ " group by re.idcontracheque) verbas" + " ON cc.id = verbas.idcontracheque"
							+ " where cc.idremessa = ? and tf.id = ? "
							+ " group by cc.situacaoBeneficiario, CASE WHEN cc.situacaoBeneficiario =  'A' THEN 'ATIVO' WHEN  cc.situacaoBeneficiario = 'I' THEN 'INATIVO' WHEN cc.situacaoBeneficiario = 'P' THEN 'PENSIONISTA' END"
							+ " order by cc.situacaoBeneficiario");

			query.setParameter(1, remessa.getId());
			query.setParameter(2, tipoFolha.getId());

			return (List<Object[]>) query.getResultList();
		}
	}

	public List<Object[]> consultaResumoProcessamentoPorCargo(Remessa remessa, TipoFolha tipoFolha) {
		if (tipoFolha == null || tipoFolha.getId() == null) {
			Query query = this.em.createNativeQuery("select ca.codigo as CODIGO_CARGO, "
					+ " CASE WHEN ca.codigo is null THEN 'PENSIONISTA' ELSE ca.nome END as DESCRICAO_CARGO,"
					+ " count(1) as QUANTIDADE,      SUM(cc.totalDescontos) as TOTAL_DESCONTOS,"
					+ " SUM(cc.totalVencimentos) as TOTAL_VENCIMENTOS," + " SUM(verbas.descontos) as descontos_verbas,"
					+ " SUM(verbas.vencimentos) as vencimentos_verbas" + " from    dbo.contracheque cc"
					+ " join dbo.tipofolha tf   	on cc.idtipofolha = tf.id    left join dbo.cargo ca"
					+ "	on cc.idcargo = ca.id"
					+ "	LEFT JOIN (SELECT re.idcontracheque, SUM(re.descontos) as descontos, SUM(re.vencimentos) as vencimentos"
					+ "	from"
					+ "	(SELECT vc.idContraCheque as idContracheque, CASE WHEN ve.natureza = 'D' THEN vc.valor ELSE 0 END as descontos, CASE WHEN ve.natureza = 'C' THEN vc.valor ELSE 0 END as vencimentos from dbo.VerbasContraCheque vc, dbo.verba ve where vc.idVerba = ve.id) re"
					+ "	group by re.idcontracheque) verbas" + "	ON cc.id = verbas.idcontracheque"
					+ "	where cc.idremessa = ? "
					+ "	group by ca.codigo, CASE WHEN ca.codigo = null THEN 'Pensionista' ELSE ca.nome END"
					+ "	order by CASE WHEN ca.codigo = null THEN 'Pensionista' ELSE ca.nome END");

			query.setParameter(1, remessa.getId());

			return (List<Object[]>) query.getResultList();
		} else {
			Query query = this.em.createNativeQuery("select ca.codigo as CODIGO_CARGO, "
					+ " CASE WHEN ca.codigo is null THEN 'PENSIONISTA' ELSE ca.nome END as DESCRICAO_CARGO,"
					+ " count(1) as QUANTIDADE,      SUM(cc.totalDescontos) as TOTAL_DESCONTOS,"
					+ " SUM(cc.totalVencimentos) as TOTAL_VENCIMENTOS," + " SUM(verbas.descontos) as descontos_verbas,"
					+ " SUM(verbas.vencimentos) as vencimentos_verbas" + "  from    dbo.contracheque cc"
					+ " join dbo.tipofolha tf   	on cc.idtipofolha = tf.id    left join dbo.cargo ca"
					+ "	on cc.idcargo = ca.id"
					+ "	LEFT JOIN (SELECT re.idcontracheque, SUM(re.descontos) as descontos, SUM(re.vencimentos) as vencimentos"
					+ "	from"
					+ "	(SELECT vc.idContraCheque as idContracheque, CASE WHEN ve.natureza = 'D' THEN vc.valor ELSE 0 END as descontos, CASE WHEN ve.natureza = 'C' THEN vc.valor ELSE 0 END as vencimentos from dbo.VerbasContraCheque vc, dbo.verba ve where vc.idVerba = ve.id) re"
					+ "	group by re.idcontracheque) verbas" + "	ON cc.id = verbas.idcontracheque"
					+ "	where cc.idremessa = ? and tf.id = ? "
					+ "	group by ca.codigo, CASE WHEN ca.codigo = null THEN 'Pensionista' ELSE ca.nome END"
					+ "	order by CASE WHEN ca.codigo = null THEN 'Pensionista' ELSE ca.nome END");

			query.setParameter(1, remessa.getId());
			query.setParameter(2, tipoFolha.getId());

			return (List<Object[]>) query.getResultList();
		}
	}

	public List<Object[]> consultaResumoProcessamentoPorVerba(Remessa remessa, TipoFolha tipoFolha) {
		if (tipoFolha == null || tipoFolha.getId() == null) {
			Query query = this.em.createNativeQuery(
					"SELECT resultado.codigo, resultado.descricao, SUM(resultado.debito) as descontos, SUM(resultado.credito) as vencimentos from (select ve.codigo, ve.descricao, CASE WHEN ve.natureza = 'C' THEN vc.valor ELSE 0 END AS CREDITO, CASE WHEN ve.natureza = 'D' THEN vc.valor ELSE 0 END AS DEBITO from dbo.contracheque cc join dbo.verbascontracheque vc on cc.id = vc.idcontracheque join dbo.verba ve on vc.idverba = ve.id where cc.idRemessa = ?) resultado group by resultado.codigo, resultado.descricao order by resultado.descricao");

			query.setParameter(1, remessa.getId());

			return (List<Object[]>) query.getResultList();
		} else {
			Query query = this.em.createNativeQuery(
					"SELECT resultado.codigo, resultado.descricao, SUM(resultado.debito) as descontos, SUM(resultado.credito) as vencimentos from (select ve.codigo, ve.descricao, CASE WHEN ve.natureza = 'C' THEN vc.valor ELSE 0 END AS CREDITO, CASE WHEN ve.natureza = 'D' THEN vc.valor ELSE 0 END AS DEBITO from dbo.contracheque cc join dbo.verbascontracheque vc on cc.id = vc.idcontracheque join dbo.verba ve on vc.idverba = ve.id where cc.idRemessa = ? and cc.idTipoFolha = ?) resultado group by resultado.codigo, resultado.descricao order by resultado.descricao");

			query.setParameter(1, remessa.getId());
			query.setParameter(2, tipoFolha.getId());

			return (List<Object[]>) query.getResultList();
		}
	}

	@SuppressWarnings("unchecked")
	public void descarta(Remessa remessa) throws Exception {
		Query query = this.em.createNativeQuery(
				"DELETE FROM dbo.verbascontracheque WHERE idcontracheque IN (SELECT id FROM dbo.contracheque  WHERE idRemessa = ?)");
		query.setParameter(1, remessa.getId());
		query.executeUpdate();

		query = this.em.createNativeQuery("DELETE FROM dbo.contracheque WHERE idRemessa = ?");
		query.setParameter(1, remessa.getId());
		query.executeUpdate();

		query = this.em.createNativeQuery("DELETE FROM remessa.errosProcessamentoRemessaPeriodica WHERE idRemessa = ?");
		query.setParameter(1, remessa.getId());
		query.executeUpdate();
	}
}
