package br.gov.ac.tce.sicapweb.xml.validador;

public enum TypeArquivo {
	CARGO(Integer.valueOf(1), "cargo", "cargo.xsd"), 
	SERVIDOR(Integer.valueOf(2), "servidor", "servidor.xsd"),
	PENSIONISTA(Integer.valueOf(3), "pensionista", "pensionista.xsd"),
	VERBAS(Integer.valueOf(4), "verbas", "verbas.xsd"),
	TIPOS_FOLHA(Integer.valueOf(5), "tiposFolha", "tiposFolha.xsd"), 
	TABELAS_VENCIMENTO(Integer.valueOf(6), "tabelasVencimento", "tabelasVencimento.xsd"), 
	UNIDADES_LOTACAO(Integer.valueOf(7), "unidadesLotacao", "unidadesLotacao.xsd"),
	HISTORICO_FUNCIONAL(Integer.valueOf(8), "historicoFuncional", "historicoFuncional.xsd"),
	CONTRACHEQUE(Integer.valueOf(9), "contracheque",	"contracheque.xsd"), 
	SICAP(Integer.valueOf(0), "sicap", "sicap.xsd");

	private final Integer id;
	private final String arquivoXml;
	private final String arquivoXsd;

	private TypeArquivo(Integer id, String arquivoXml, String arquivoXsd) {
		this.id = id;
		this.arquivoXml = arquivoXml;
		this.arquivoXsd = arquivoXsd;
	}

	public Integer getId() {
		return id;
	}

	public String getArquivoXsd() {
		return arquivoXsd;
	}

	public String getArquivoXml() {
		return arquivoXml;
	}

	public static TypeArquivo parse(Integer id) {
		TypeArquivo typeArquivo = null;
		for (TypeArquivo item : TypeArquivo.values()) {
			if (item.getId() == id) {
				typeArquivo = item;
				break;
			}
		}
		return typeArquivo;
	}
}
