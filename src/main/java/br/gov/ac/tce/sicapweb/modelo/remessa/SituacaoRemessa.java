package br.gov.ac.tce.sicapweb.modelo.remessa;

public enum SituacaoRemessa {

	BLOQUEADA(String.valueOf("BL"), "Bloqueada para envio"),
	LIBERADA(String.valueOf("LI"), "Liberada para envio"), 
	AGUARDANDO(String.valueOf("AG"), "Aguardando Processamento"), 
	ERRO_FILA_PROCESSAMENTO(String.valueOf("EF"), "Erro na Fila Processamento"), 
	ERRO_INTERNO_BANCO_DE_DADOS(String.valueOf("EI"), "Erro no Processamento"), 
	PROCESSADA(String.valueOf("PR"), "Processada"), 
	CONFIRMADA(String.valueOf("CO"), "Confirmada"), 
	DESCARTADA(String.valueOf("DE"), "Descartada");

	private String id;
	private String descricao;

	private SituacaoRemessa(String id, String descricao) {
		this.id = id;
		this.descricao = descricao;
	}

	public String getId() {
		return id;
	}

	public String getDescricao() {
		return descricao;
	}

	public static SituacaoRemessa parse(String id) {
		SituacaoRemessa tipoRemessa = null;
		for (SituacaoRemessa item : SituacaoRemessa.values()) {
			if (item.getId().equals(id)) {
				tipoRemessa = item;
				break;
			}
		}
		return tipoRemessa;
	}

}
