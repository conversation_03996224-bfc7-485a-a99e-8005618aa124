package br.gov.ac.tce.sicapweb.modelo.entidade;

public enum Poder {

	EXECUTIVO("EXECUTIVO", 1), LEGISLATIVO("LEGISLATIVO", 2), JUDICIARIO("JUDICIÁRIO", 3);

	private String descricao;
	private Integer id;

	private Poder(String descricao, Integer id) {
		this.descricao = descricao;
		this.id = id;
	}

	public String getDescricao() {
		return descricao;
	}

	
	public Integer getId() {
		return id;
	}


	public static Poder parse(Integer id) {
		Poder poder = null;

		for (Poder item : Poder.values()) {
			if (item.getId() == id) {
				poder = item;
				break;
			}
		}
		return poder;
	}
	
	public static Poder parse(String chaveEnum) {
		return Poder.valueOf(chaveEnum);
	}
}
