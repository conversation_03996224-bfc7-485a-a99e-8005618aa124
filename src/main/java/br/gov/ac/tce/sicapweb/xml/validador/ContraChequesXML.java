package br.gov.ac.tce.sicapweb.xml.validador;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "contraCheques")
public class ContraChequesXML implements Serializable {

	private static final long serialVersionUID = 1L;

	private Integer mes;
	private Integer ano;

	public Integer getMes() {
		return mes;
	}

	@XmlAttribute(name = "mesCompetencia", required = true)
	public void setMes(Integer mes) {
		this.mes = mes;
	}

	public Integer getAno() {
		return ano;
	}

	@XmlAttribute(name = "anoCompetencia", required = true)
	public void setAno(Integer ano) {
		this.ano = ano;
	}
}
