package br.gov.ac.tce.sicapweb.modelo.folha;

import java.io.Serializable;
import java.time.LocalDateTime;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;

import br.gov.ac.tce.sicapweb.modelo.Status;
import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;
import br.gov.ac.tce.sicapweb.modelo.remessa.RemessaEventual;

@Entity
@NamedQueries({
		@NamedQuery(name = Verba.buscarTodosPorEntidadeCJUR, query = "select v from Verba v where v.entidade = :entidade"),
		@NamedQuery(name = Verba.buscarTodosPorEntidadeCJURRegistroAtivo, query = "select v from Verba v where v.entidade = :entidade and v.registroAtivo = true") })
public class Verba implements Serializable {

	private static final long serialVersionUID = 1L;

	public static final String buscarTodosPorEntidadeCJUR = "Verba.buscarTodosPorEntidadeCJUR";
	public static final String buscarTodosPorEntidadeCJURRegistroAtivo = "Verba.buscarTodosPorEntidadeCJURRegistroAtivo";

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idEntidadeCjur", nullable = false)
	private Entidade entidade;
	@Column(length = 25, nullable = false)
	private String codigo;
	@Column(length = 60, nullable = false)
	private String descricao;
	@Column(length = 40)
	private String baseLegal;
	@Column(length = 1, nullable = false)
	private String basePrevidencia;
	@Column(length = 1, nullable = false)
	private String baseIRPF;
	@Column(length = 1, nullable = false)
	private String baseFGTS;
	@Column(length = 1, nullable = false)
	private String natureza;
	@Column(nullable = false)
	private Integer tipoReferencia;
	@Column(length = 1, nullable = false)
	private String compoeVencimentoPadrao;
	private Integer categoriaEconomica;
	private Integer grupoNaturezaDespesa;
	private Integer modalidadeAplicacao;
	@Column(length = 2)
	private String elementoDespesa;
	@Column(nullable = false)
	private Boolean registroAtivo;
	private LocalDateTime dataInativacao;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idRemessaEventual", nullable = false)
	private RemessaEventual remessaEventual;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getCodigo() {
		return codigo;
	}

	public void setCodigo(String codigo) {
		this.codigo = codigo;
	}

	public String getDescricao() {
		return descricao;
	}

	public void setDescricao(String descricao) {
		this.descricao = descricao;
	}

	public String getBaseLegal() {
		return baseLegal;
	}

	public void setBaseLegal(String baseLegal) {
		this.baseLegal = baseLegal;
	}

	public Status getBasePrevidencia() {
		return Status.parse(this.basePrevidencia);
	}

	public void setBasePrevidencia(Status basePrevidencia) {
		this.basePrevidencia = basePrevidencia.getId();
	}

	public Status getBaseIRPF() {
		return Status.parse(this.baseIRPF);
	}

	public void setBaseIRPF(Status baseIRPF) {
		this.baseIRPF = baseIRPF.getId();
	}

	public Status getBaseFGTS() {
		return Status.parse(this.baseFGTS);
	}

	public void setBaseFGTS(Status baseFGTS) {
		this.baseFGTS = baseFGTS.getId();
	}

	public TipoNatureza getNatureza() {
		return TipoNatureza.parse(this.natureza);
	}

	public void setNatureza(TipoNatureza natureza) {
		this.natureza = natureza.getId();
	}

	public TipoReferencia getTipoReferencia() {
		return TipoReferencia.parse(this.tipoReferencia);
	}

	public void setTipoReferencia(TipoReferencia tipoReferencia) {
		this.tipoReferencia = tipoReferencia.getId();
	}

	public Status getCompoeVencimentoPadrao() {
		return Status.parse(this.compoeVencimentoPadrao);
	}

	public void setCompoeVencimentoPadrao(Status compoeVencimentoPadrao) {
		this.compoeVencimentoPadrao = compoeVencimentoPadrao.getId();
	}

	public Integer getGrupoNaturezaDespesa() {
		return grupoNaturezaDespesa;
	}

	public void setGrupoNaturezaDespesa(Integer grupoNaturezaDespesa) {
		this.grupoNaturezaDespesa = grupoNaturezaDespesa;
	}

	public Integer getModalidadeAplicacao() {
		return modalidadeAplicacao;
	}

	public void setModalidadeAplicacao(Integer modalidadeAplicacao) {
		this.modalidadeAplicacao = modalidadeAplicacao;
	}

	public String getElementoDespesa() {
		return elementoDespesa;
	}

	public void setElementoDespesa(String elementoDespesa) {
		this.elementoDespesa = elementoDespesa;
	}

	public void setNatureza(String natureza) {
		this.natureza = natureza;
	}

	public Entidade getEntidade() {
		return entidade;
	}

	public void setEntidade(Entidade entidade) {
		this.entidade = entidade;
	}

	public Boolean getRegistroAtivo() {
		return registroAtivo;
	}

	public void setRegistroAtivo(Boolean registroAtivo) {
		this.registroAtivo = registroAtivo;
	}

	public LocalDateTime getDataInativacao() {
		return dataInativacao;
	}

	public void setDataInativacao(LocalDateTime dataInativacao) {
		this.dataInativacao = dataInativacao;
	}

	public RemessaEventual getRemessaEventual() {
		return remessaEventual;
	}

	public void setRemessaEventual(RemessaEventual remessaEventual) {
		this.remessaEventual = remessaEventual;
	}

	public Integer getCategoriaEconomica() {
		return categoriaEconomica;
	}

	public void setCategoriaEconomica(Integer categoriaEconomica) {
		this.categoriaEconomica = categoriaEconomica;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((baseFGTS == null) ? 0 : baseFGTS.hashCode());
		result = prime * result + ((baseIRPF == null) ? 0 : baseIRPF.hashCode());
		result = prime * result + ((baseLegal == null) ? 0 : baseLegal.hashCode());
		result = prime * result + ((basePrevidencia == null) ? 0 : basePrevidencia.hashCode());
		result = prime * result + ((categoriaEconomica == null) ? 0 : categoriaEconomica.hashCode());
		result = prime * result + ((codigo == null) ? 0 : codigo.hashCode());
		result = prime * result + ((compoeVencimentoPadrao == null) ? 0 : compoeVencimentoPadrao.hashCode());
		result = prime * result + ((dataInativacao == null) ? 0 : dataInativacao.hashCode());
		result = prime * result + ((descricao == null) ? 0 : descricao.hashCode());
		result = prime * result + ((elementoDespesa == null) ? 0 : elementoDespesa.hashCode());
		result = prime * result + ((entidade == null) ? 0 : entidade.hashCode());
		result = prime * result + ((grupoNaturezaDespesa == null) ? 0 : grupoNaturezaDespesa.hashCode());
		result = prime * result + ((modalidadeAplicacao == null) ? 0 : modalidadeAplicacao.hashCode());
		result = prime * result + ((natureza == null) ? 0 : natureza.hashCode());
		result = prime * result + ((registroAtivo == null) ? 0 : registroAtivo.hashCode());
		result = prime * result + ((remessaEventual == null) ? 0 : remessaEventual.hashCode());
		result = prime * result + ((tipoReferencia == null) ? 0 : tipoReferencia.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		Verba other = (Verba) obj;
		if (baseFGTS == null) {
			if (other.baseFGTS != null)
				return false;
		} else if (!baseFGTS.equals(other.baseFGTS))
			return false;
		if (baseIRPF == null) {
			if (other.baseIRPF != null)
				return false;
		} else if (!baseIRPF.equals(other.baseIRPF))
			return false;
		if (baseLegal == null) {
			if (other.baseLegal != null)
				return false;
		} else if (!baseLegal.equals(other.baseLegal))
			return false;
		if (basePrevidencia == null) {
			if (other.basePrevidencia != null)
				return false;
		} else if (!basePrevidencia.equals(other.basePrevidencia))
			return false;
		if (categoriaEconomica == null) {
			if (other.categoriaEconomica != null)
				return false;
		} else if (!categoriaEconomica.equals(other.categoriaEconomica))
			return false;
		if (codigo == null) {
			if (other.codigo != null)
				return false;
		} else if (!codigo.equals(other.codigo))
			return false;
		if (compoeVencimentoPadrao == null) {
			if (other.compoeVencimentoPadrao != null)
				return false;
		} else if (!compoeVencimentoPadrao.equals(other.compoeVencimentoPadrao))
			return false;
		if (dataInativacao == null) {
			if (other.dataInativacao != null)
				return false;
		} else if (!dataInativacao.equals(other.dataInativacao))
			return false;
		if (descricao == null) {
			if (other.descricao != null)
				return false;
		} else if (!descricao.equals(other.descricao))
			return false;
		if (elementoDespesa == null) {
			if (other.elementoDespesa != null)
				return false;
		} else if (!elementoDespesa.equals(other.elementoDespesa))
			return false;
		if (entidade == null) {
			if (other.entidade != null)
				return false;
		} else if (!entidade.equals(other.entidade))
			return false;
		if (grupoNaturezaDespesa == null) {
			if (other.grupoNaturezaDespesa != null)
				return false;
		} else if (!grupoNaturezaDespesa.equals(other.grupoNaturezaDespesa))
			return false;
		if (modalidadeAplicacao == null) {
			if (other.modalidadeAplicacao != null)
				return false;
		} else if (!modalidadeAplicacao.equals(other.modalidadeAplicacao))
			return false;
		if (natureza == null) {
			if (other.natureza != null)
				return false;
		} else if (!natureza.equals(other.natureza))
			return false;
		if (registroAtivo == null) {
			if (other.registroAtivo != null)
				return false;
		} else if (!registroAtivo.equals(other.registroAtivo))
			return false;
		if (remessaEventual == null) {
			if (other.remessaEventual != null)
				return false;
		} else if (!remessaEventual.equals(other.remessaEventual))
			return false;
		if (tipoReferencia == null) {
			if (other.tipoReferencia != null)
				return false;
		} else if (!tipoReferencia.equals(other.tipoReferencia))
			return false;
		return true;
	}

}
