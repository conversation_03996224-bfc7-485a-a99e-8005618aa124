package br.gov.ac.tce.sicapweb.modelo.remessa;

import java.io.Serializable;
import java.time.LocalDateTime;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Transient;

import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;
import br.gov.ac.tce.sicapweb.modelo.usuario.Usuario;

@Entity
@Table(schema = "remessa")
@NamedQueries({
		@NamedQuery(name = AssinaturaRemessa.buscarPorIdRemessa, query = "select a from AssinaturaRemessa a where a.remessa = :remessa") })
public class AssinaturaRemessa implements Serializable {

	private static final long serialVersionUID = 1L;

	public static final String buscarPorIdRemessa = "AssinaturaRemessa.buscarPorIdRemessa";

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idEntidadeCjur", nullable = false)
	private Entidade entidade;
	@OneToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idRemessa", nullable = false)
	private Remessa remessa;
	@Column(name = "idUsuario", nullable = false)
	private Integer idUsuario;
	@Column(nullable = false)
	private LocalDateTime dataAssinatura;
	private Long hashAssinatura;
	@Transient
	private Usuario usuario;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Entidade getEntidade() {
		return entidade;
	}

	public void setEntidade(Entidade entidade) {
		this.entidade = entidade;
	}

	public Remessa getRemessa() {
		return remessa;
	}

	public void setRemessa(Remessa remessa) {
		this.remessa = remessa;
	}

	public Integer getIdUsuario() {
		return idUsuario;
	}

	private void setIdUsuario(Integer idUsuario) {
		this.idUsuario = idUsuario;
	}

	public LocalDateTime getDataAssinatura() {
		return dataAssinatura;
	}

	public void setDataAssinatura(LocalDateTime dataAssinatura) {
		this.dataAssinatura = dataAssinatura;
	}

	public Long getHashAssinatura() {
		return hashAssinatura;
	}

	public void setHashAssinatura(Long hashAssinatura) {
		this.hashAssinatura = hashAssinatura;
	}

	public Usuario getUsuario() {
		return usuario;
	}

	public void setUsuario(Usuario usuario) {
		this.usuario = usuario;
		this.setIdUsuario(usuario.getId());
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = super.hashCode();
		result = prime * result + ((dataAssinatura == null) ? 0 : dataAssinatura.hashCode());
		result = prime * result + ((entidade == null) ? 0 : entidade.hashCode());
		result = prime * result + ((remessa == null) ? 0 : remessa.hashCode());
		result = prime * result + ((usuario == null) ? 0 : usuario.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		AssinaturaRemessa other = (AssinaturaRemessa) obj;
		if (dataAssinatura == null) {
			if (other.dataAssinatura != null)
				return false;
		} else if (!dataAssinatura.equals(other.dataAssinatura))
			return false;
		if (entidade == null) {
			if (other.entidade != null)
				return false;
		} else if (!entidade.equals(other.entidade))
			return false;
		if (remessa == null) {
			if (other.remessa != null)
				return false;
		} else if (!remessa.equals(other.remessa))
			return false;
		if (usuario == null) {
			if (other.usuario != null)
				return false;
		} else if (!usuario.equals(other.usuario))
			return false;
		return true;
	}

}
