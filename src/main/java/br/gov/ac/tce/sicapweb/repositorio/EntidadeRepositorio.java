package br.gov.ac.tce.sicapweb.repositorio;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;

import com.uaihebert.uaicriteria.UaiCriteria;
import com.uaihebert.uaicriteria.UaiCriteriaFactory;

import br.gov.ac.tce.sicapanalise.auditoria.dto.CargoEntidadeDTO;
import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapweb.modelo.Sistema;
import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;
import br.gov.ac.tce.sicapweb.modelo.entidade.EntidadeCJUR;
import br.gov.ac.tce.sicapweb.modelo.usuario.Usuario;

@Stateless
public class EntidadeRepositorio implements Serializable {

	private static final long serialVersionUID = 1L;

	@PersistenceContext(unitName = "sicapAnalise")
	private EntityManager em;
	@PersistenceContext(unitName = "cjur")
	private EntityManager emCjur;

//	public Entidade consultaPorCodigoCjur(Integer codigoCjur) {
//		Query query = this.emCjur
//				.createNativeQuery(
//						"SELECT ug.id as idEntidadeCjur, pe.nome as nome, ug.sede as ente, ug.poder as poder,  ug.tipoUg as classificacaoAdministrativa "
//								+ "FROM comum.pessoa pe, comum.unidadegestora ug "
//								+ "WHERE pe.id = ug.pessoaJuridica_id AND ug.extinta = 0 AND ug.id = ?",
//						Entidade.class);
//
//		query.setParameter(1, codigoCjur);
//
//		return (Entidade) query.getSingleResult();
//	}

	@SuppressWarnings("unchecked")
	public List<Entidade> lista() {
		// Query query = this.emCjur.createNativeQuery(
		// "SELECT ug.id as idEntidadeCjur, pe.nome as nome, ug.sede as ente,
		// ug.poder as poder, ug.tipoUg as classificacaoAdministrativa "
		// + "FROM comum.pessoa pe, comum.unidadegestora ug "
		// + "WHERE pe.id = ug.pessoaJuridica_id AND ug.envioSicap = 1 AND
		// ug.extinta = 0 "
		// + " ORDER BY pe.nome",
		// Entidade.class);
		// Em 16.03.2018 - Odair

		Query query = this.emCjur.createNativeQuery(
				"SELECT ug.id as idEntidadeCjur, pe.nome as nome, ug.sede as ente, ug.poder as poder,  ug.tipoUg as classificacaoAdministrativa "
						+ "FROM comum.pessoa pe, comum.unidadegestora ug "
						+ "WHERE pe.id = ug.pessoaJuridica_id " + " ORDER BY pe.nome",
				Entidade.class);

		return (List<Entidade>) query.getResultList();
	}

	@SuppressWarnings("unchecked")
	public List<Entidade> lista(Usuario usuario, Sistema sistema) {
		Query query = this.emCjur.createNativeQuery(
				"select ug.id as idEntidadeCjur, pe.nome as nome, ug.sede as ente, ug.poder as poder,  ug.tipoUg as classificacaoAdministrativa "
						+ " FROM comum.pessoa pe, comum.PessoaJuridica pj, comum.UnidadeGestora ug, comum.usuario u, comum.PessoaFisica pf, cjur.Responsavel re, comum.Autorizacao au "
						+ "WHERE pe.id = pj.id AND pj.id = ug.pessoaJuridica_id AND ug.id = re.unidadeGestora_id AND u.id = pf.id AND pf.id = re.usuario_id AND re.id = au.responsavel_id AND pf.numeroCpf = ? "
						+ "AND au.sistema_id = ? AND au.grupo_id = 1 AND au.situacao = 'HOMOLOGADA' AND u.bloqueado = 0",
				Entidade.class);

		query.setParameter(1, usuario.getLogin());
		query.setParameter(2, sistema.getId());

		return (List<Entidade>) query.getResultList();
	}


	public EntidadeCJUR pesquisaEntidade(Integer idEntidadeCjur) {
		EntidadeCJUR entidade = null;
		try {
			Query query = this.emCjur.createNativeQuery(
					"SELECT ug.id as idEntidadeCjur, pe.nome as nome, ug.sede as ente, ug.poder as poder,  ug.tipoUg as classificacaoAdministrativa, ug.esfera "
							+ "FROM comum.pessoa pe, comum.unidadegestora ug "
							+ "WHERE pe.id = ug.pessoaJuridica_id AND ug.envioSicap = 1 AND ug.extinta = 0 AND ug.id = ?",
					EntidadeCJUR.class);

			query.setParameter(1, idEntidadeCjur);

			entidade = (EntidadeCJUR) query.getSingleResult();

			// entidade = new Entidade();
			//
			// Object[] object = (Object[]) query.getSingleResult();
			//
			// entidade.setIdEntidadeCjur((Integer) object[0]);
			// entidade.setNome((String) object[1]);
			// entidade.setEnte((String) object[2]);
			// entidade.setPoder((String) object[3]);
			// entidade.setClassificacaoAdministrativa((String) object[4]);
			// System.out.println("esfera: " + object[5]);
			// entidade.setEsfera((String) object[5]);
			//
			// System.out.println("entidade.esfera: " + entidade.getEsfera());
			// System.out.println("entidade.ente: " + entidade.getEnte());

		} catch (Exception e) {

		}
		return entidade;
	}

	public Collection<Entidade> listaTodasRepositorioSicap() throws RepositorioException {
		Collection<Entidade> listaEntidade = null;

		try {
			UaiCriteria<Entidade> uaiCriteria = UaiCriteriaFactory.createQueryCriteria(this.em, Entidade.class);

			listaEntidade = uaiCriteria.getResultList();

		} catch (Exception e) {
			throw new RepositorioException("Erro entidadeRepositorio.listaTodasRepositorioSicap.", e.getCause());
		}
		return listaEntidade;
	}
	
	public Collection<CargoEntidadeDTO> listaCargosPorEntidade(Integer entidade, Integer ano, Integer mes){
		Collection<CargoEntidadeDTO> listaCargosEntidade = null;
		String sql = "SELECT DISTINCT cc.idEntidadeLotacao AS entidade_id,"	+ 
				" e.nome as entidade_nome," + 
				" CASE c.tipo" + 
				" WHEN 1 THEN 'Efetivo'" + 
				" WHEN 2 THEN 'Comissionado'" + 
				" WHEN 3 THEN 'Temporário'" + 
				" END AS cargo_tipo," +
				" crs.descricao AS cargo_referencia," +
				" c.nome AS cargo_nome, " +
				" Count(cc.id) OVER(PARTITION BY cc.idEntidadeLotacao,c.tipo, crs.id, c.nome) AS cargo_quantidade_servidores," + 
				" Count(cc.id) OVER(PARTITION BY cc.idEntidadeLotacao) AS entidade_quantidade_servidores" + 
				" FROM contracheque cc" + 
				" INNER JOIN Entidade e" + 
				" ON e.idEntidadeCjur = cc.idEntidadeLotacao" + 
				" LEFT JOIN cargo c" + 
				" ON c.id = cc.idcargoatual" + 
				" LEFT JOIN CargoRefSub crs" + 
				" on c.subGrupoClassificacaoFuncional = crs.id" + 
				" WHERE  ano = :ano" + 
				" AND mes = :mes" + 
				" AND (cc.identidadelotacao = :entidade OR :entidade = 0)" + 
				" ORDER  BY e.nome ,crs.descricao ,c.nome";
		try {
			Query query = this.em.createNativeQuery(sql,"CargoEntidadeDTOMapping")
					.setParameter("entidade", entidade)
					.setParameter("ano", ano)
					.setParameter("mes", mes);
			listaCargosEntidade = query.getResultList();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return listaCargosEntidade;
	}
	

	public Entidade retornaUltimaEntidadeAdmissao(Long idAcumulacao) {
		Entidade entidade = null;
		String sql = "select e.* " + 
				"from auditoria.DetalhamentoAcumulacao da " + 
				"left join VinculoFuncional vf " + 
				"on vf.idBeneficiario = da.idBeneficiario " + 
				"left join Entidade e " + 
				"on e.idEntidadeCjur = vf.idEntidadeCjur " + 
				"where idAcumulacao = :pIdAcumulacao " + 
				"and vf.dataInativacao is null " + 
				"order by vf.dataAdmissao desc";
		try {
			entidade = (Entidade) em.createNativeQuery(sql, Entidade.class)
									.setParameter("pIdAcumulacao", idAcumulacao)
									.setMaxResults(1)
									.getSingleResult();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return entidade;
	}

	public Collection<Object[]> listaCompetenciasContraCheque(Integer entidade) throws RepositorioException {
		Collection<Object[]> listaCompetencias = null;
		
		try {
			Query query = this.em.createNativeQuery("SELECT DISTINCT cc.ano, cc.mes, " + "CASE cc.mes "
					+ "WHEN 1 THEN 'Janeiro' " + "WHEN 2 THEN 'Fevereiro' " + "WHEN 3 THEN 'Março' "
					+ "WHEN 4 THEN 'Abril' " + "WHEN 5 THEN 'Maio' " + "WHEN 6 THEN 'Junho' " + "WHEN 7 THEN 'Julho' "
					+ "WHEN 8 THEN 'Agosto' " + "WHEN 9 THEN 'Setembro' " + "WHEN 10 THEN 'Outubro' "
					+ "WHEN 11 THEN 'Novembro' " + "WHEN 12 THEN 'Dezembro' " + "ELSE 'Nenhum' END AS nomeMes "
					+ "FROM ContraCheque cc where (cc.idEntidadeLotacao = :entidade or :entidade = 0) order by cc.ano desc, cc.mes desc");

			query.setParameter("entidade", entidade);
			listaCompetencias = query.getResultList();

		} catch (Exception e) {
			throw new RepositorioException("Erro acumulacaoRepositorio.listaCompetenciasContraCheque.", e.getCause());
		}
		return listaCompetencias;
	}
	
	
	public Collection<Integer> listaCompetenciaAno()  {
		Collection<Integer> listaCompetencia = null;
		try {
			listaCompetencia = this.em.createNativeQuery("select distinct ano from remessa.Remessa order by ano desc").getResultList();
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		return listaCompetencia;
	}
	

}
