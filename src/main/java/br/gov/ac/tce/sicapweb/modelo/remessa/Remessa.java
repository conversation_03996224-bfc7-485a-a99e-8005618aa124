package br.gov.ac.tce.sicapweb.modelo.remessa;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Collection;

import javax.persistence.Column;
import javax.persistence.Embedded;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;

import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;
import br.gov.ac.tce.sicapweb.modelo.usuario.Usuario;

@Entity
@Table(schema = "remessa")
@NamedQueries({ @NamedQuery(name = Remessa.buscarPorId, query = "select r from Remessa r where r.id = :id"),
		@NamedQuery(name = Remessa.listarTodasPorEntidade, query = "select r from Remessa r where r.entidade = :entidade order by r.id desc"),
		@NamedQuery(name = Remessa.listaRemessasPorSituacaoEntidade, query = "select r from Remessa r where r.entidade = :entidade and r.situacao = :situacao order by r.id desc") })
public class Remessa implements Serializable {

	private static final long serialVersionUID = 1L;

	public static final String buscarPorId = "Remessa.buscarPorId";
	public static final String listarTodasPorEntidade = "Remessa.listarTodasPorEntidade";
	public static final String listaRemessasPorSituacaoEntidade = "Remessa.listaRemessasPorSituacaoEntidade";

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;
	@ManyToOne(optional = false, fetch = FetchType.EAGER)
	@JoinColumn(name = "idEntidadeCjur")
	private Entidade entidade;
	@Embedded
	private Competencia competencia;
	@Column(nullable = false)
	private String situacao;
	@Column(nullable = false)
	private LocalDateTime prazoEnvio;
	private LocalDateTime dataEnvio;
	private LocalDateTime dataConfirmacao;
	private String protocoloEntrega;
	private String caminhoArquivoCompactado;
	private String caminhoArquivoXMLContraCheque;
	private String hashArquivoCompactado;
	@Column(name = "idUsuario", nullable = false)
	private Integer usuario;
	@OneToOne(mappedBy = "remessa", fetch = FetchType.LAZY)
	private AssinaturaRemessa assinaturaRemessa;
	@OneToMany(mappedBy = "remessa", fetch = FetchType.LAZY)
	private Collection<ErrosProcessamentoRemessaPeriodica> listaErrosProcessamentoRemessaPeriodica;

	public Remessa() {

	}

	public Remessa(Entidade entidade, Competencia competencia, SituacaoRemessa situacao, LocalDateTime prazoEnvio,
			Usuario usuario) {
		this.entidade = entidade;
		this.competencia = competencia;
		this.situacao = situacao.getId();
		this.prazoEnvio = prazoEnvio;
		this.usuario = usuario.getId();
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Entidade getEntidade() {
		return entidade;
	}

	public void setEntidade(Entidade entidade) {
		this.entidade = entidade;
	}

	public Competencia getCompetencia() {
		return competencia;
	}

	public void setCompetencia(Competencia competencia) {
		this.competencia = competencia;
	}

	public SituacaoRemessa getSituacao() {
		return SituacaoRemessa.parse(this.situacao);
	}

	public void setSituacao(SituacaoRemessa situacao) {
		this.situacao = situacao.getId();
	}

	public LocalDateTime getPrazoEnvio() {
		return prazoEnvio;
	}

	public void setPrazoEnvio(LocalDateTime prazoEnvio) {
		this.prazoEnvio = prazoEnvio;
	}

	public LocalDateTime getDataEnvio() {
		return dataEnvio;
	}

	public void setDataEnvio(LocalDateTime dataEnvio) {
		this.dataEnvio = dataEnvio;
	}

	public LocalDateTime getDataConfirmacao() {
		return dataConfirmacao;
	}

	public void setDataConfirmacao(LocalDateTime dataConfirmacao) {
		this.dataConfirmacao = dataConfirmacao;
	}

	public String getProtocoloEntrega() {
		return protocoloEntrega;
	}

	public void setProtocoloEntrega(String protocoloEntrega) {
		this.protocoloEntrega = protocoloEntrega;
	}

	public String getCaminhoArquivoCompactado() {
		return caminhoArquivoCompactado;
	}

	public void setCaminhoArquivoCompactado(String caminhoArquivoCompactado) {
		this.caminhoArquivoCompactado = caminhoArquivoCompactado;
	}

	public String getCaminhoArquivoXMLContraCheque() {
		return caminhoArquivoXMLContraCheque;
	}

	public void setCaminhoArquivoXMLContraCheque(String caminhoArquivoXMLContraCheque) {
		this.caminhoArquivoXMLContraCheque = caminhoArquivoXMLContraCheque;
	}

	public String getHashArquivoCompactado() {
		return hashArquivoCompactado;
	}

	public void setHashArquivoCompactado(String hashArquivoCompactado) {
		this.hashArquivoCompactado = hashArquivoCompactado;
	}

	public Boolean isBloqueada() {
		return SituacaoRemessa.parse(this.situacao) == SituacaoRemessa.BLOQUEADA;
	}

	public Boolean isLiberada() {
		return SituacaoRemessa.parse(this.situacao) == SituacaoRemessa.LIBERADA;
	}

	public Boolean isAguardando() {
		return SituacaoRemessa.parse(this.situacao) == SituacaoRemessa.AGUARDANDO;
	}

	public Boolean isProcessada() {
		return SituacaoRemessa.parse(this.situacao) == SituacaoRemessa.PROCESSADA;
	}

	public Boolean isConfirmada() {
		return SituacaoRemessa.parse(this.situacao) == SituacaoRemessa.CONFIRMADA;
	}

	public Boolean hasErroProcessamento() {
		return (SituacaoRemessa.parse(this.situacao) == SituacaoRemessa.ERRO_FILA_PROCESSAMENTO
				|| SituacaoRemessa.parse(this.situacao) == SituacaoRemessa.ERRO_INTERNO_BANCO_DE_DADOS);
	}

	public Integer getUsuario() {
		return usuario;
	}

	public void setUsuario(Integer usuario) {
		this.usuario = usuario;
	}

	public Collection<ErrosProcessamentoRemessaPeriodica> getListaErrosProcessamentoRemessaPeriodica() {
		return listaErrosProcessamentoRemessaPeriodica;
	}

	public void setListaErrosProcessamentoRemessaPeriodica(
			Collection<ErrosProcessamentoRemessaPeriodica> listaErrosProcessamentoRemessaPeriodica) {
		this.listaErrosProcessamentoRemessaPeriodica = listaErrosProcessamentoRemessaPeriodica;
	}

	public AssinaturaRemessa getAssinaturaRemessa() {
		return assinaturaRemessa;
	}

	public void setAssinaturaRemessa(AssinaturaRemessa assinaturaRemessa) {
		this.assinaturaRemessa = assinaturaRemessa;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((caminhoArquivoCompactado == null) ? 0 : caminhoArquivoCompactado.hashCode());
		result = prime * result
				+ ((caminhoArquivoXMLContraCheque == null) ? 0 : caminhoArquivoXMLContraCheque.hashCode());
		result = prime * result + ((competencia == null) ? 0 : competencia.hashCode());
		result = prime * result + ((dataConfirmacao == null) ? 0 : dataConfirmacao.hashCode());
		result = prime * result + ((dataEnvio == null) ? 0 : dataEnvio.hashCode());
		result = prime * result + ((entidade == null) ? 0 : entidade.hashCode());
		result = prime * result + ((hashArquivoCompactado == null) ? 0 : hashArquivoCompactado.hashCode());
		result = prime * result + ((prazoEnvio == null) ? 0 : prazoEnvio.hashCode());
		result = prime * result + ((protocoloEntrega == null) ? 0 : protocoloEntrega.hashCode());
		result = prime * result + ((situacao == null) ? 0 : situacao.hashCode());
		result = prime * result + ((usuario == null) ? 0 : usuario.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		Remessa other = (Remessa) obj;
		if (caminhoArquivoCompactado == null) {
			if (other.caminhoArquivoCompactado != null)
				return false;
		} else if (!caminhoArquivoCompactado.equals(other.caminhoArquivoCompactado))
			return false;
		if (caminhoArquivoXMLContraCheque == null) {
			if (other.caminhoArquivoXMLContraCheque != null)
				return false;
		} else if (!caminhoArquivoXMLContraCheque.equals(other.caminhoArquivoXMLContraCheque))
			return false;
		if (competencia == null) {
			if (other.competencia != null)
				return false;
		} else if (!competencia.equals(other.competencia))
			return false;
		if (dataConfirmacao == null) {
			if (other.dataConfirmacao != null)
				return false;
		} else if (!dataConfirmacao.equals(other.dataConfirmacao))
			return false;
		if (dataEnvio == null) {
			if (other.dataEnvio != null)
				return false;
		} else if (!dataEnvio.equals(other.dataEnvio))
			return false;
		if (entidade == null) {
			if (other.entidade != null)
				return false;
		} else if (!entidade.equals(other.entidade))
			return false;
		if (hashArquivoCompactado == null) {
			if (other.hashArquivoCompactado != null)
				return false;
		} else if (!hashArquivoCompactado.equals(other.hashArquivoCompactado))
			return false;
		if (prazoEnvio == null) {
			if (other.prazoEnvio != null)
				return false;
		} else if (!prazoEnvio.equals(other.prazoEnvio))
			return false;
		if (protocoloEntrega == null) {
			if (other.protocoloEntrega != null)
				return false;
		} else if (!protocoloEntrega.equals(other.protocoloEntrega))
			return false;
		if (situacao == null) {
			if (other.situacao != null)
				return false;
		} else if (!situacao.equals(other.situacao))
			return false;
		if (usuario == null) {
			if (other.usuario != null)
				return false;
		} else if (!usuario.equals(other.usuario))
			return false;
		return true;
	}

}
