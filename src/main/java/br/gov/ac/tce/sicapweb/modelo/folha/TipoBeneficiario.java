package br.gov.ac.tce.sicapweb.modelo.folha;

public enum TipoBeneficiario {
	SERVIDOR(String.valueOf("S"), "Servidor"), PENSIONISTA(String.valueOf("P"), "Pensionista");

	private String id;
	private String descricao;

	private TipoBeneficiario(String id, String descricao) {
		this.id = id;
		this.descricao = descricao;
	}

	public String getId() {
		return id;
	}

	public String getDescricao() {
		return descricao;
	}

	public static TipoBeneficiario parse(String id) {
		TipoBeneficiario tipoBeneficiario = null;
		for (TipoBeneficiario item : TipoBeneficiario.values()) {
			if (item.getId().equals(id)) {
				tipoBeneficiario = item;
				break;
			}
		}
		return tipoBeneficiario;
	}
}
