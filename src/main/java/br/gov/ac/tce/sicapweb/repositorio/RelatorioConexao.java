package br.gov.ac.tce.sicapweb.repositorio;

import java.io.Serializable;
import java.sql.Connection;

import javax.naming.InitialContext;
import javax.sql.DataSource;


public class RelatorioConexao implements Serializable {

	private static final long serialVersionUID = 1L;

	public Connection getConnection() throws Exception {
		InitialContext initialContext;
		Connection connection = null;
		
		initialContext = new InitialContext();
		DataSource dataSource = (DataSource) initialContext.lookup("java:jboss/datasources/sicapAnaliseDS");
		connection = dataSource.getConnection();
		return connection;
	}
}
