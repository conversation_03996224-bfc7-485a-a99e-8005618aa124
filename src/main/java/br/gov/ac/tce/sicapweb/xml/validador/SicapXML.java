package br.gov.ac.tce.sicapweb.xml.validador;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "sicap")
public class SicapXML implements Serializable {

	private static final long serialVersionUID = 1L;

	private String arquivo;
	private ContraChequesXML contraChequesXML;

	@XmlAttribute(name = "arquivo", required = true)
	public void setArquivo(String arquivo) {
		this.arquivo = arquivo;
	}

	public String getArquivo() {
		return arquivo;
	}

	public ContraChequesXML getContraChequesXML() {
		return contraChequesXML;
	}

	@XmlElement(name = "contraCheques", required = false)
	public void setContraChequesXML(ContraChequesXML contraChequesXML) {
		this.contraChequesXML = contraChequesXML;
	}

}
