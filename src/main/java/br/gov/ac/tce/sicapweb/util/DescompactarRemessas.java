package br.gov.ac.tce.sicapweb.util;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.Serializable;
import java.util.Enumeration;
import java.util.zip.ZipEntry;
import java.util.zip.ZipException;
import java.util.zip.ZipFile;

import br.gov.ac.tce.sicapweb.xml.exception.ArquivoRemessaException;
import br.gov.ac.tce.sicapweb.xml.exception.DiretorioCreateException;
import br.gov.ac.tce.sicapweb.xml.exception.EstruturaXMLException;

public class DescompactarRemessas implements Serializable {

	private static final long serialVersionUID = 1L;

	public static Boolean descompactarArquivoZip(String nomeArquivoXML, File arquivoZipDescompactar)
			throws ZipException, IOException, DiretorioCreateException, ArquivoRemessaException, EstruturaXMLException,
			Exception {

		ZipFile arquivoZip = null;

		Boolean descompactou = false;

		try {
			File diretorioTemporarioXML = new File(PropriedadeSistema.diretorioTemporarioXML);

			if (!diretorioTemporarioXML.exists() || !diretorioTemporarioXML.isDirectory()) {
				throw new DiretorioCreateException("O diretório para descompactar os arquivos não existe.");
			}

			arquivoZip = new ZipFile(arquivoZipDescompactar);

			@SuppressWarnings("unchecked")
			Enumeration<ZipEntry> listaZIP = (Enumeration<ZipEntry>) arquivoZip.entries();

			if ((listaZIP.hasMoreElements()) && (arquivoZip.size() == 1)) {
				ZipEntry zipEntry = (ZipEntry) listaZIP.nextElement();

				String nomeCompletoArquivo = PropriedadeSistema.diretorioTemporarioXML + nomeArquivoXML;

				File arquivo = new File(nomeCompletoArquivo);
				escreverXMLDisco(arquivoZip, zipEntry, new FileOutputStream(arquivo));

				arquivoZip.close();
				descompactou = true;
			} else {
				descompactou = false;
				throw new ArquivoRemessaException(
						"A remessa " + nomeArquivoXML + " não possui a quantidade de arquivos exigida.");
			}
		} catch (Exception e) {
			descompactou = false;
			e.printStackTrace();
			throw new Exception(
					"Ocorreu um erro durante o processamento da ação. Se o erro persistir entre em contato com o suporte técnico.");
		} finally {
			arquivoZip.close();
		}
		return descompactou;
	}

	private static void escreverXMLDisco(ZipFile zipFile, ZipEntry zipEntry, OutputStream outputStream)
			throws IOException, ZipException {
		byte[] buffer = new byte[2048];
		int bytesLido = 0;
		InputStream inputStream = null;

		inputStream = zipFile.getInputStream(zipEntry);
		if (inputStream == null) {
			throw new ZipException("Erro ao ler entrada do zip: " + zipEntry.getName());
		} else {
			while ((bytesLido = inputStream.read(buffer)) > 0) {
				outputStream.write(buffer, 0, bytesLido);
			}
		}
		inputStream.close();
		outputStream.close();
	}
}
