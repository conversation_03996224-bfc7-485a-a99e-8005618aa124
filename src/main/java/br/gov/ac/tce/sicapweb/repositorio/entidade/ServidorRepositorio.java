package br.gov.ac.tce.sicapweb.repositorio.entidade;

import java.io.Serializable;
import java.util.Collection;

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import com.uaihebert.uaicriteria.UaiCriteria;
import com.uaihebert.uaicriteria.UaiCriteriaFactory;

import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapweb.modelo.cargo.Cargo;
import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;
import br.gov.ac.tce.sicapweb.modelo.pessoa.Beneficiario;
import br.gov.ac.tce.sicapweb.modelo.remessa.RemessaEventual;
import br.gov.ac.tce.sicapweb.modelo.servidor.TipoVinculo;

@Stateless
public class ServidorRepositorio implements Serializable {

	private static final long serialVersionUID = 1L;

	@PersistenceContext(unitName = "sicapAnalise")
	private EntityManager entityManager;

	public Collection<Beneficiario> pesquisaServidores(Entidade entidade, RemessaEventual remessaEventual,
			Integer tipoServidor, TipoVinculo tipoVinculo, Integer regimePrevidenciario, Cargo cargo, Integer matricula,
			String nome, String cpf) throws RepositorioException {
		Collection<Beneficiario> listaBeneficiario = null;

		try {
			UaiCriteria<Beneficiario> uaiCriteria = UaiCriteriaFactory.createQueryCriteria(this.entityManager,
					Beneficiario.class);
			uaiCriteria.andEquals("entidade", entidade);
			uaiCriteria.andEquals("tipoBeneficiario", "S");
			uaiCriteria.innerJoin("listaVinculosFuncionais");
			uaiCriteria.andEquals("listaVinculosFuncionais.registroAtivo", true);
			uaiCriteria.innerJoin("cadastroUnico");
			uaiCriteria.innerJoin("cadastroUnico.listaPessoaFisica");
			uaiCriteria.andEquals("cadastroUnico.listaPessoaFisica.registroAtivo", true);
			uaiCriteria.innerJoin("listaVinculosFuncionais.cargo");
			uaiCriteria.innerJoin("listaVinculosFuncionais.tipoVinculo");

			if (remessaEventual.getId() != 0) {
				uaiCriteria.andEquals("remessaEventual", remessaEventual);
			}

			if (tipoServidor != 0) {
				uaiCriteria.andEquals("listaVinculosFuncionais.tipoServidor", tipoServidor);
			}

			if (tipoVinculo.getId() != 0) {
				uaiCriteria.andEquals("listaVinculosFuncionais.tipoVinculo", tipoVinculo);
			}

			if (regimePrevidenciario != 0) {
				uaiCriteria.andEquals("listaVinculosFuncionais.regimePrevidenciario", regimePrevidenciario);
			}

			if (cargo.getId() != 0) {
				uaiCriteria.andEquals("listaVinculosFuncionais.cargo", cargo);
			}

			if (matricula != null) {
				uaiCriteria.andEquals("matricula", matricula);
			}

			if ((nome != null) && (!nome.isEmpty())) {
				uaiCriteria.andStringLike("cadastroUnico.listaPessoaFisica.nome", "%" + nome + "%");
			}

			if ((cpf != null) && (!cpf.isEmpty())) {
				uaiCriteria.andStringLike("cadastroUnico.cpf", "%" + cpf + "%");
			}

			listaBeneficiario = uaiCriteria.getResultList();
		} catch (Exception e) {
			throw new RepositorioException("Erro servidorRepositorio.pesquisaServidores", e.getCause());
		}

		return listaBeneficiario;
	}

	public Beneficiario pesquisaServidor(Entidade entidade, Integer matricula) throws RepositorioException {
		Beneficiario beneficiario = null;

		try {
			UaiCriteria<Beneficiario> uaiCriteria = UaiCriteriaFactory.createQueryCriteria(this.entityManager,
					Beneficiario.class);
			uaiCriteria.andEquals("entidade", entidade);
			uaiCriteria.andEquals("tipoBeneficiario", "S");
			uaiCriteria.innerJoin("listaVinculosFuncionais");
			uaiCriteria.andEquals("listaVinculosFuncionais.registroAtivo", true);
			uaiCriteria.innerJoin("cadastroUnico");
			uaiCriteria.innerJoin("cadastroUnico.listaPessoaFisica");
			uaiCriteria.innerJoin("listaVinculosFuncionais.cargo");
			uaiCriteria.innerJoin("listaVinculosFuncionais.tipoVinculo");
			uaiCriteria.andEquals("matricula", matricula);

			beneficiario = uaiCriteria.getSingleResult();
		} catch (Exception e) {
			throw new RepositorioException("Erro servidorRepositorio.pesquisaServidor", e.getCause());
		}

		return beneficiario;
	}
}
