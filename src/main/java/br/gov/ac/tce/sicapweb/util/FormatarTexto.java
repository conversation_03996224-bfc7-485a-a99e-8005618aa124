package br.gov.ac.tce.sicapweb.util;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import org.apache.commons.lang3.text.WordUtils;

public class FormatarTexto implements Serializable {

	private static final long serialVersionUID = 1L;

	public static String primeiraLetraCadaPalavraMaiuscula(String texto) {
		return WordUtils.capitalizeFully(texto);
	}

	public static String dataTime(String texto) {
		String data = null;
		if (texto == null) {
			return null;
		} else {
			try {
				LocalDateTime dateTime = LocalDateTime.parse(texto.replace(" ", "T"));
				DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss");
				data = dateTime.format(formatter);
				return data;
			} catch (Exception e) {
			}
		}
		return data;
	}
}
