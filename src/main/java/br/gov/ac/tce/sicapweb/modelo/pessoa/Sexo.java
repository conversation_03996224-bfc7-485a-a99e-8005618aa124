package br.gov.ac.tce.sicapweb.modelo.pessoa;

public enum Sexo {
	MASCULINO(String.valueOf("M"), "<PERSON><PERSON><PERSON><PERSON>"), FEMININO(String.valueOf("F"), "Feminino");

	private String id;
	private String descricao;

	private Sexo(String id, String descricao) {
		this.id = id;
		this.descricao = descricao;
	}

	public String getId() {
		return id;
	}

	public String getDescricao() {
		return descricao;
	}

	public static Sexo parse(String id) {
		Sexo sexo = null;
		for (Sexo item : Sexo.values()) {
			if (item.getId().equals(id)) {
				sexo = item;
				break;
			}
		}
		return sexo;
	}
}
