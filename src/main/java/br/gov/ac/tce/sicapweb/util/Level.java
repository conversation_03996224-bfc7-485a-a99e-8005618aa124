package br.gov.ac.tce.sicapweb.util;


public enum Level {
	EASY("abcdefghijklmnopqrstuvwxyz"),
	MEDIUM("123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz"),
	HARD(":}{<>!@#$%.&*(),;^~][-=+123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz");

	private String key;
	
	private Level(String key) {
		setKey(key);
	}
	
	private String getKey() {
		return key;
	}
	
	private void setKey(String key) {
		this.key = key;
	}
	
	@Override
	public String toString() {
		return getKey();
	}
}
