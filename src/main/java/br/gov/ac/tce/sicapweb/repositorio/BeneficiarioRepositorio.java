package br.gov.ac.tce.sicapweb.repositorio;

import java.io.Serializable;
import java.util.Collection;

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;

import com.uaihebert.uaicriteria.UaiCriteria;
import com.uaihebert.uaicriteria.UaiCriteriaFactory;

import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapweb.modelo.pessoa.Beneficiario;

@Stateless
public class BeneficiarioRepositorio implements Serializable {

	private static final long serialVersionUID = 1L;

	@PersistenceContext(unitName = "sicapAnalise")
	private EntityManager entityManager;

	boolean toLowerCase = true;

	@SuppressWarnings("unchecked")
	public Collection<Object> pesquisaTodos(String cpf, String nome) throws RepositorioException {
		Collection<Object> listaBeneficiario = null;
		String sql;

		try {
			sql = "SELECT p.cpf, p.nome, p.sexo FROM auditoria.vw_pessoaPorCPF p ";

			if ((cpf != null) && (!cpf.isEmpty())) {
				sql += "WHERE p.cpf LIKE '%" + cpf + "%' ";
			}

			if ((nome != null) && (!nome.isEmpty())) {
				if ((cpf != null) && (!cpf.isEmpty())) {
					sql += "AND p.nome LIKE '%" + nome + "%' ";
				} else {
					sql += "WHERE p.nome LIKE '%" + nome + "%' ";
				}
			}

			sql += "ORDER BY p.nome";

			Query query = this.entityManager.createNativeQuery(sql);

			listaBeneficiario = query.getResultList();

		} catch (Exception e) {
			throw new RepositorioException("Erro BeneficiarioRepositorio.pesquisaTodos.", e.getCause());
		}
		return listaBeneficiario;
	}

	public Collection<Beneficiario> pesquisaVinculos(String cpf) throws RepositorioException {
		Collection<Beneficiario> listaBeneficiario = null;
		String jpql = "select b from Beneficiario b join fetch b.cadastroUnico cu join fetch cu.entidade where b.registroAtivo = true and cu.cpf = :pCpf";

		try {
//			UaiCriteria<Beneficiario> uaiCriteria = UaiCriteriaFactory.createQueryCriteria(this.entityManager,
//					Beneficiario.class);
//
//			uaiCriteria.innerJoin("cadastroUnico");
//			uaiCriteria.innerJoin("entidade");
//
//			uaiCriteria.andEquals("cadastroUnico.cpf", cpf);
//			listaBeneficiario = uaiCriteria.getResultList();
			
			listaBeneficiario = entityManager.createQuery(jpql, Beneficiario.class).setParameter("pCpf", cpf).getResultList();
		} catch (Exception e) {
			throw new RepositorioException("Erro beneficiarioRepositorio.pesquisaVinculos", e.getCause());
		}

		return listaBeneficiario;
	}
	
//	public Collection<VinculoFuncional> pesquisaVinculosPorCpf(String cpf) throws RepositorioException {
//		Collection<VinculoFuncional> listaVinculoFuncional = null;
//		String jpql = "select vf from VinculoFuncional vf join fetch vf.entidade e join fetch vf.beneficiario b join fetch b.cadastroUnico cu where vf.registroAtivo = true and cu.cpf = :pCpf";
//		try {
//			listaVinculoFuncional = entityManager.createQuery(jpql, VinculoFuncional.class).setParameter("pCpf", cpf).getResultList();
//		} catch (Exception e) {
//			throw new RepositorioException("Erro beneficiarioRepositorio.pesquisaVinculosPorCpf", e.getCause());
//		}
//		return listaVinculoFuncional;
//	}

	public Beneficiario pesquisaVinculoPorId(Long id) throws RepositorioException {
		Beneficiario beneficiario = null;

		try {
			UaiCriteria<Beneficiario> uaiCriteria = UaiCriteriaFactory.createQueryCriteria(this.entityManager,
					Beneficiario.class);

			uaiCriteria.innerJoin("entidade");
			uaiCriteria.innerJoin("cadastroUnico");
			uaiCriteria.innerJoin("cadastroUnico.listaPessoaFisica");
			uaiCriteria.leftJoin("listaVinculosFuncionais");
			uaiCriteria.leftJoin("listaVinculosFuncionais.cargo");
			uaiCriteria.leftJoin("listaVinculosFuncionais.tipoVinculo");

			uaiCriteria.leftJoin("listaPensoes");
			uaiCriteria.leftJoin("listaPensoes.tipoDependencia");
			uaiCriteria.leftJoin("listaPensoes.servidor");
			uaiCriteria.leftJoin("listaPensoes.servidor.cadastroUnico");
			uaiCriteria.leftJoin("listaPensoes.servidor.cadastroUnico.listaPessoaFisica");

			uaiCriteria.leftJoin("listaPensoes.vinculoFuncionalInstituidor");
			uaiCriteria.leftJoin("listaPensoes.vinculoFuncionalInstituidor.cargo");
			uaiCriteria.leftJoin("listaPensoes.vinculoFuncionalInstituidor.tipoVinculo");

			uaiCriteria.andEquals("id", id);

			beneficiario = uaiCriteria.getSingleResult();
		} catch (Exception e) {
			throw new RepositorioException("Erro beneficiarioRepositorio.pesquisaVinculoPorId", e.getCause());
		}

		return beneficiario;
	}
}
