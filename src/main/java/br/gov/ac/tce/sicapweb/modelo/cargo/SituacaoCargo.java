package br.gov.ac.tce.sicapweb.modelo.cargo;

public enum SituacaoCargo {
	VIGENTE(Integer.valueOf(1), "Vigente"), EXTINTO(Integer.valueOf(2), "Extinto");

	private Integer id;
	private String descricao;

	private SituacaoCargo(Integer id, String descricao) {
		this.id = id;
		this.descricao = descricao;
	}

	public Integer getId() {
		return id;
	}

	public String getDescricao() {
		return descricao;
	}

	public static SituacaoCargo parse(Integer id) {
		SituacaoCargo situacaoCargo = null;
		for (SituacaoCargo item : SituacaoCargo.values()) {
			if (item.getId() == id) {
				situacaoCargo = item;
				break;
			}
		}
		return situacaoCargo;
	}
}
