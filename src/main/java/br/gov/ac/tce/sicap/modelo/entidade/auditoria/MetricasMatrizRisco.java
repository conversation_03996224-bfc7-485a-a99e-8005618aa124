package br.gov.ac.tce.sicap.modelo.entidade.auditoria;

import java.io.Serializable;
import java.time.LocalDateTime;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Entity
@Table(schema = "auditoria")
public class MetricasMatrizRisco implements Serializable {

	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;
	@Column(length = 200, nullable = false, unique = true)
	private String nome;
	@ManyToOne
	@JoinColumn(name = "idMatrizRisco", nullable = false)
	private MatrizRisco matrizRisco;
	@Column(precision = 12, scale = 5)
	private Double montanteProventos;
	private Integer quantidadeVinculos;
	@Column(precision = 12, scale = 5)
	private Double cargaHorariaTotal;
	private Integer quantidadeMunicipiosLotacao;
	private Integer agentePolitico;
	private Integer quantidadeVinculosNaoPensao;
	@Column(nullable = false)
	private LocalDateTime dataCadastro;
	@Column(nullable = false)
	private Long idUsuario;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getNome() {
		return nome;
	}

	public void setNome(String nome) {
		this.nome = nome;
	}

	public MatrizRisco getMatrizRisco() {
		return matrizRisco;
	}

	public void setMatrizRisco(MatrizRisco matrizRisco) {
		this.matrizRisco = matrizRisco;
	}

	public Double getMontanteProventos() {
		return montanteProventos;
	}

	public void setMontanteProventos(Double montanteProventos) {
		this.montanteProventos = montanteProventos;
	}

	public Integer getQuantidadeVinculos() {
		return quantidadeVinculos;
	}

	public void setQuantidadeVinculos(Integer quantidadeVinculos) {
		this.quantidadeVinculos = quantidadeVinculos;
	}

	public Double getCargaHorariaTotal() {
		return cargaHorariaTotal;
	}

	public void setCargaHorariaTotal(Double cargaHorariaTotal) {
		this.cargaHorariaTotal = cargaHorariaTotal;
	}

	public Integer getQuantidadeMunicipiosLotacao() {
		return quantidadeMunicipiosLotacao;
	}

	public void setQuantidadeMunicipiosLotacao(Integer quantidadeMunicipiosLotacao) {
		this.quantidadeMunicipiosLotacao = quantidadeMunicipiosLotacao;
	}

	public Integer getAgentePolitico() {
		return agentePolitico;
	}

	public void setAgentePolitico(Integer agentePolitico) {
		this.agentePolitico = agentePolitico;
	}

	public Integer getQuantidadeVinculosNaoPensao() {
		return quantidadeVinculosNaoPensao;
	}

	public void setQuantidadeVinculosNaoPensao(Integer quantidadeVinculosNaoPensao) {
		this.quantidadeVinculosNaoPensao = quantidadeVinculosNaoPensao;
	}

	public LocalDateTime getDataCadastro() {
		return dataCadastro;
	}

	public void setDataCadastro(LocalDateTime dataCadastro) {
		this.dataCadastro = dataCadastro;
	}

	public Long getIdUsuario() {
		return idUsuario;
	}

	public void setIdUsuario(Long idUsuario) {
		this.idUsuario = idUsuario;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((agentePolitico == null) ? 0 : agentePolitico.hashCode());
		result = prime * result + ((cargaHorariaTotal == null) ? 0 : cargaHorariaTotal.hashCode());
		result = prime * result + ((dataCadastro == null) ? 0 : dataCadastro.hashCode());
		result = prime * result + ((idUsuario == null) ? 0 : idUsuario.hashCode());
		result = prime * result + ((matrizRisco == null) ? 0 : matrizRisco.hashCode());
		result = prime * result + ((montanteProventos == null) ? 0 : montanteProventos.hashCode());
		result = prime * result + ((nome == null) ? 0 : nome.hashCode());
		result = prime * result + ((quantidadeMunicipiosLotacao == null) ? 0 : quantidadeMunicipiosLotacao.hashCode());
		result = prime * result + ((quantidadeVinculos == null) ? 0 : quantidadeVinculos.hashCode());
		result = prime * result + ((quantidadeVinculosNaoPensao == null) ? 0 : quantidadeVinculosNaoPensao.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		MetricasMatrizRisco other = (MetricasMatrizRisco) obj;
		if (agentePolitico == null) {
			if (other.agentePolitico != null)
				return false;
		} else if (!agentePolitico.equals(other.agentePolitico))
			return false;
		if (cargaHorariaTotal == null) {
			if (other.cargaHorariaTotal != null)
				return false;
		} else if (!cargaHorariaTotal.equals(other.cargaHorariaTotal))
			return false;
		if (dataCadastro == null) {
			if (other.dataCadastro != null)
				return false;
		} else if (!dataCadastro.equals(other.dataCadastro))
			return false;
		if (idUsuario == null) {
			if (other.idUsuario != null)
				return false;
		} else if (!idUsuario.equals(other.idUsuario))
			return false;
		if (matrizRisco == null) {
			if (other.matrizRisco != null)
				return false;
		} else if (!matrizRisco.equals(other.matrizRisco))
			return false;
		if (montanteProventos == null) {
			if (other.montanteProventos != null)
				return false;
		} else if (!montanteProventos.equals(other.montanteProventos))
			return false;
		if (nome == null) {
			if (other.nome != null)
				return false;
		} else if (!nome.equals(other.nome))
			return false;
		if (quantidadeMunicipiosLotacao == null) {
			if (other.quantidadeMunicipiosLotacao != null)
				return false;
		} else if (!quantidadeMunicipiosLotacao.equals(other.quantidadeMunicipiosLotacao))
			return false;
		if (quantidadeVinculos == null) {
			if (other.quantidadeVinculos != null)
				return false;
		} else if (!quantidadeVinculos.equals(other.quantidadeVinculos))
			return false;
		if (quantidadeVinculosNaoPensao == null) {
			if (other.quantidadeVinculosNaoPensao != null)
				return false;
		} else if (!quantidadeVinculosNaoPensao.equals(other.quantidadeVinculosNaoPensao))
			return false;
		return true;
	}

}
