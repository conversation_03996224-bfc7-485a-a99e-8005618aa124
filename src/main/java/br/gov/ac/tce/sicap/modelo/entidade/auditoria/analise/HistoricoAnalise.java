package br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise;

import java.io.Serializable;
import java.time.LocalDateTime;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.responsavel.Usuario;

@Entity
@Table(schema = "auditoria")
public class HistoricoAnalise implements Serializable {

	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;
	@Column(nullable = false)
	private LocalDateTime data;
//	@Column(nullable = false)
//	private Long idUsuario;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idUsuario", nullable = false)
	private Usuario usuario;
	@Column(nullable = false)
	private Long idUsuarioRecebedor;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idAnalise", nullable = false)
	private Analise analise;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "procedimentoRealizado",nullable = true)
	private TipoProcedimentoAnalise procedimentoRealizado;
	
	@Column(length = 500, nullable = false)
	private String despacho;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public LocalDateTime getData() {
		return data;
	}

	public void setData(LocalDateTime data) {
		this.data = data;
	}

	public Usuario getUsuario() {
		return usuario;
	}

	public void setUsuario(Usuario usuario) {
		this.usuario = usuario;
	}

	public Long getIdUsuarioRecebedor() {
		return idUsuarioRecebedor;
	}

	public void setIdUsuarioRecebedor(Long idUsuarioRecebedor) {
		this.idUsuarioRecebedor = idUsuarioRecebedor;
	}

	public Analise getAnalise() {
		return analise;
	}

	public void setAnalise(Analise analise) {
		this.analise = analise;
	}

	
	public TipoProcedimentoAnalise getProcedimentoRealizado() {
		return procedimentoRealizado;
	}

	public void setProcedimentoRealizado(TipoProcedimentoAnalise procedimentoRealizado) {
		this.procedimentoRealizado = procedimentoRealizado;
	}

	public String getDespacho() {
		return despacho;
	}

	public void setDespacho(String despacho) {
		this.despacho = despacho;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((analise == null) ? 0 : analise.hashCode());
		result = prime * result + ((data == null) ? 0 : data.hashCode());
		result = prime * result + ((despacho == null) ? 0 : despacho.hashCode());
		result = prime * result + ((usuario == null) ? 0 : usuario.hashCode());
		result = prime * result + ((idUsuarioRecebedor == null) ? 0 : idUsuarioRecebedor.hashCode());
		result = prime * result + ((procedimentoRealizado == null) ? 0 : procedimentoRealizado.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		HistoricoAnalise other = (HistoricoAnalise) obj;
		if (analise == null) {
			if (other.analise != null)
				return false;
		} else if (!analise.equals(other.analise))
			return false;
		if (data == null) {
			if (other.data != null)
				return false;
		} else if (!data.equals(other.data))
			return false;
		if (despacho == null) {
			if (other.despacho != null)
				return false;
		} else if (!despacho.equals(other.despacho))
			return false;
		if (usuario == null) {
			if (other.usuario != null)
				return false;
		} else if (!usuario.equals(other.usuario))
			return false;
		if (idUsuarioRecebedor == null) {
			if (other.idUsuarioRecebedor != null)
				return false;
		} else if (!idUsuarioRecebedor.equals(other.idUsuarioRecebedor))
			return false;
		if (procedimentoRealizado == null) {
			if (other.procedimentoRealizado != null)
				return false;
		} else if (!procedimentoRealizado.equals(other.procedimentoRealizado))
			return false;
		return true;
	}

}
