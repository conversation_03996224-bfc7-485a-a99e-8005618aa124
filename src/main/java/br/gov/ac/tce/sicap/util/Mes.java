package br.gov.ac.tce.sicap.util;

public enum Mes {
	JANEIRO(1, "Janeiro"), FEVEREIRO(2, "Fevereiro"), MARCO(3, "<PERSON>ço"), ABRIL(4, "Abril"), MAIO(5, "<PERSON><PERSON>"), JUNHO(6,
			"<PERSON><PERSON>"), JULHO(7, "<PERSON><PERSON>"), AGOSTO(8, "Agosto"), SETEMBRO(9,
					"Setembro"), OUTUBRO(10, "Outubro"), NOVEMBRO(11, "Novembro"), DEZEMBRO(12, "Dezembro");

	private final Integer numero;
	private final String nome;

	private Mes(Integer numero, String nome) {
		this.numero = numero;
		this.nome = nome;
	}

	public Integer getNumero() {
		return numero;
	}

	public String getNome() {
		return nome;
	}

	public static Mes parse(Integer numero) {
		Mes mes = null;
		for (Mes item : Mes.values()) {
			if (item.getNumero() == numero) {
				mes = item;
				break;
			}
		}
		return mes;
	}

}
