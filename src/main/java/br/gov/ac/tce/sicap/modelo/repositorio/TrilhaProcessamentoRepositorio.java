package br.gov.ac.tce.sicap.modelo.repositorio;

import java.util.Collection;

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;

import com.uaihebert.uaicriteria.UaiCriteria;
import com.uaihebert.uaicriteria.UaiCriteriaFactory;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.TipoAuditoria;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.trilha.HistoricoProcessamentoTrilha;

@Stateless
public class TrilhaProcessamentoRepositorio {

	@PersistenceContext(unitName = "sicapAnalise")
	private EntityManager entityManager;

	@SuppressWarnings("unchecked")
	public Collection<HistoricoProcessamentoTrilha> listarTodas2() throws RepositorioException {
		Collection<HistoricoProcessamentoTrilha> listaHistoricoProcessamentoTrilhas = null;
		try {
			String sql = "SELECT hpt.id, hpt.tipo, hpt.mes, hpt.ano, hpt.dataProcessamento, acumulacao.qtdAcumulacao, detalhamento.qtdDetalhamentoAcumulacao "
					+ "FROM auditoria.HistoricoProcessamentoTrilha hpt " + "INNER JOIN "
					+ "(SELECT a.idTrilha, COUNT(1) as qtdAcumulacao FROM auditoria.Acumulacao a GROUP BY a.idTrilha) acumulacao "
					+ "ON acumulacao.idTrilha = hpt.id " + " INNER JOIN "
					+ "(SELECT da.idTrilha, COUNT(1) as qtdDetalhamentoAcumulacao FROM auditoria.DetalhamentoAcumulacao da GROUP BY da.idTrilha) detalhamento "
					+ "ON detalhamento.idTrilha = hpt.id " + "WHERE hpt.tipo = ?"
					+ "GROUP BY hpt.id, hpt.tipo, hpt.mes, hpt.ano, hpt.dataProcessamento, acumulacao.qtdAcumulacao, detalhamento.qtdDetalhamentoAcumulacao";

			Query query = entityManager.createNativeQuery(sql);
			query.setParameter(1, TipoAuditoria.ACUMULACAO.name());

			listaHistoricoProcessamentoTrilhas = query.getResultList();
		} catch (Exception e) {
			e.printStackTrace();
			throw new RepositorioException("Erro ao listar todas as trilhas de processamento.", e.getCause());
		}
		return listaHistoricoProcessamentoTrilhas;
	}

	public Collection<HistoricoProcessamentoTrilha> listarTodas() throws RepositorioException {
		Collection<HistoricoProcessamentoTrilha> listaHistoricoProcessamentoTrilha = null;
		try {
			UaiCriteria<HistoricoProcessamentoTrilha> uaiCriteria = UaiCriteriaFactory
					.createQueryCriteria(this.entityManager, HistoricoProcessamentoTrilha.class);
			listaHistoricoProcessamentoTrilha = uaiCriteria.getResultList();
		} catch (Exception e) {
			throw new RepositorioException("Erro ao listar todas as trilhas de processamento.", e.getCause());
		}
		return listaHistoricoProcessamentoTrilha;
	}
}
