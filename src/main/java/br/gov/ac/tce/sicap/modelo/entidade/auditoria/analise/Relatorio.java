package br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise;

import java.io.Serializable;
import java.time.LocalDateTime;

import javax.persistence.ColumnResult;
import javax.persistence.ConstructorResult;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SqlResultSetMapping;
import javax.persistence.Table;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.responsavel.Usuario;
import br.gov.ac.tce.sicapanalise.auditoria.dto.DocumentoAuditoriaDTO;

/**
 * Entity implementation class for Entity: Relatorio
 *
 */
@Entity
@Table(schema = "auditoria")
public class Relatorio implements Serializable {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idUsuario", nullable = false)
	private Usuario usuario;
	private LocalDateTime data;
	private Boolean ativo;
	private String arquivoCaminho;
	private String arquivoNome;

	@ManyToOne
	@JoinColumn(name = "idTipoDocumento", nullable = false)
	private TipoDocumento tipoDocumento;

	private static final long serialVersionUID = 1L;

	public Relatorio() {
		super();
	}

	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Usuario getUsuario() {
		return this.usuario;
	}

	public void setUsuario(Usuario usuario) {
		this.usuario = usuario;
	}

	public LocalDateTime getData() {
		return this.data;
	}

	public void setData(LocalDateTime data) {
		this.data = data;
	}

	public Boolean getAtivo() {
		return this.ativo;
	}

	public void setAtivo(Boolean ativo) {
		this.ativo = ativo;
	}

	public String getArquivoCaminho() {
		return this.arquivoCaminho;
	}

	public void setArquivoCaminho(String arquivoCaminho) {
		this.arquivoCaminho = arquivoCaminho;
	}

	public String getArquivoNome() {
		return this.arquivoNome;
	}

	public void setArquivoNome(String arquivoNome) {
		this.arquivoNome = arquivoNome;
	}

	public TipoDocumento getTipoDocumento() {
		return tipoDocumento;
	}

	public void setTipoDocumento(TipoDocumento tipoDocumento) {
		this.tipoDocumento = tipoDocumento;
	}

}
