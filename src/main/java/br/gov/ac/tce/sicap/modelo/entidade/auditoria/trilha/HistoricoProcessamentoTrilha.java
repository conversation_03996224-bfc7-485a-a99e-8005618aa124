package br.gov.ac.tce.sicap.modelo.entidade.auditoria.trilha;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Collection;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.TipoAuditoria;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.acumulacao.Acumulacao;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.acumulacao.DetalhamentoAcumulacao;
import br.gov.ac.tce.sicap.util.Mes;

@Entity
@Table(schema = "auditoria")
public class HistoricoProcessamentoTrilha implements Serializable {

	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;
	@Column(nullable = false)
	@Enumerated(EnumType.STRING)
	private TipoAuditoria tipo;
	@Column(nullable = false)
	private LocalDateTime dataProcessamento;
	@Column(nullable = false)
	private Integer mes;
	@Column(nullable = false)
	private Integer ano;
	@OneToMany(mappedBy = "trilha", fetch = FetchType.LAZY)
	private Collection<Acumulacao> listaAcumulacao;
	@OneToMany(mappedBy = "trilha", fetch = FetchType.LAZY)
	private Collection<DetalhamentoAcumulacao> listaDetalhamentoAcumulacao;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public TipoAuditoria getTipo() {
		return tipo;
	}

	public void setTipo(TipoAuditoria tipo) {
		this.tipo = tipo;
	}

	public LocalDateTime getDataProcessamento() {
		return dataProcessamento;
	}

	public void setDataProcessamento(LocalDateTime dataProcessamento) {
		this.dataProcessamento = dataProcessamento;
	}

	public Mes getMes() {
		return Mes.parse(this.mes);
	}

	public void setMes(Mes mes) {
		this.mes = mes.getNumero();
	}

	public Integer getAno() {
		return ano;
	}

	public void setAno(Integer ano) {
		this.ano = ano;
	}

	public Collection<Acumulacao> getListaAcumulacao() {
		return listaAcumulacao;
	}

	public void setListaAcumulacao(Collection<Acumulacao> listaAcumulacao) {
		this.listaAcumulacao = listaAcumulacao;
	}

	public Collection<DetalhamentoAcumulacao> getListaDetalhamentoAcumulacao() {
		return listaDetalhamentoAcumulacao;
	}

	public void setListaDetalhamentoAcumulacao(Collection<DetalhamentoAcumulacao> listaDetalhamentoAcumulacao) {
		this.listaDetalhamentoAcumulacao = listaDetalhamentoAcumulacao;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((ano == null) ? 0 : ano.hashCode());
		result = prime * result + ((dataProcessamento == null) ? 0 : dataProcessamento.hashCode());
		result = prime * result + ((mes == null) ? 0 : mes.hashCode());
		result = prime * result + ((tipo == null) ? 0 : tipo.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		HistoricoProcessamentoTrilha other = (HistoricoProcessamentoTrilha) obj;
		if (ano == null) {
			if (other.ano != null)
				return false;
		} else if (!ano.equals(other.ano))
			return false;
		if (dataProcessamento == null) {
			if (other.dataProcessamento != null)
				return false;
		} else if (!dataProcessamento.equals(other.dataProcessamento))
			return false;
		if (mes == null) {
			if (other.mes != null)
				return false;
		} else if (!mes.equals(other.mes))
			return false;
		if (tipo != other.tipo)
			return false;
		return true;
	}

}
