package br.gov.ac.tce.sicap.modelo.repositorio;

import java.io.Serializable;
import java.util.Collection;

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import com.uaihebert.uaicriteria.UaiCriteria;
import com.uaihebert.uaicriteria.UaiCriteriaFactory;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.MatrizRisco;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.MetricasMatrizRisco;

@Stateless
public class MetricasMatrizRiscoRepositorio implements Serializable {

	private static final long serialVersionUID = 1L;

	@PersistenceContext(unitName = "sicapAnalise")
	private EntityManager entityManager;

	public void salvar(MetricasMatrizRisco metricasMatrizRisco) throws RepositorioException {
		try {
			this.entityManager.persist(metricasMatrizRisco);
		} catch (Exception e) {
			e.printStackTrace();
			throw new RepositorioException("Erro ao salvar métricas para a matriz de risco.", e.getCause());
		}
	}

	public Collection<MetricasMatrizRisco> listarTodasPorMatriz(MatrizRisco matrizRisco) throws RepositorioException {
		Collection<MetricasMatrizRisco> listaMetricasMatrizRisco = null;
		try {
			UaiCriteria<MetricasMatrizRisco> uaiCriteria = UaiCriteriaFactory.createQueryCriteria(this.entityManager,
					MetricasMatrizRisco.class);
			uaiCriteria.andEquals("matrizRisco", matrizRisco);
			listaMetricasMatrizRisco = uaiCriteria.getResultList();
		} catch (Exception e) {
			throw new RepositorioException("Erro ao listar todas as matrizes de risco cadastradas.", e.getCause());
		}
		return listaMetricasMatrizRisco;
	}

}
