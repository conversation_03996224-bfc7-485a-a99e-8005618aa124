package br.gov.ac.tce.sicap.modelo.entidade.notificacao;

import java.time.LocalDateTime;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.responsavel.Usuario;

public class NotificacaoInformacao {

	private int prazoDias = 0;
	private String despacho = "Senhor Gestor, para fins de cumprimento da Resolução TCE-AC Nº 102/2016 e Instrução Normativa TCE-AC Nº 20/2020, segue notificação sobre possíveis acumulações indevidas de remuneração de cargos públicos.";
	private Usuario usuario;
	
	public int getPrazoDias() {
		return prazoDias;
	}

	public void setPrazoDias(int prazoDias) {
		this.prazoDias = prazoDias;
	}

	public String getDespacho() {
		return despacho;
	}

	public void setDespacho(String despacho) {
		this.despacho = despacho;
	}
	
	public LocalDateTime getDataPrazoResposta() {
		return LocalDateTime.now().plusDays((long) this.prazoDias);
	}

	public Usuario getUsuario() {
		return usuario;
	}

	public void setUsuario(Usuario usuario) {
		this.usuario = usuario;
	}
	
	
}
