package br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise;

import java.io.Serializable;
import java.time.LocalDateTime;

import javax.persistence.Column;
import javax.persistence.ColumnResult;
import javax.persistence.ConstructorResult;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SqlResultSetMapping;
import javax.persistence.Table;

import br.gov.ac.tce.sicapanalise.auditoria.dto.DocumentoAuditoriaDTO;

@Entity
@SqlResultSetMapping(name = "DocumentoAuditoriaDTOMapping", classes = {
		@ConstructorResult(targetClass = DocumentoAuditoriaDTO.class, columns = {
				@ColumnResult(name = "idAnalise", type = Long.class),
				@ColumnResult(name = "idNotificacao", type = Long.class),
				@ColumnResult(name = "idEntidadeCjur", type = Long.class),
				@ColumnResult(name = "idtipoDocumento", type = Long.class),
				@ColumnResult(name = "nomeTipoDocumento", type = String.class),
				@ColumnResult(name = "caminhoDocumento", type = String.class),
				@ColumnResult(name = "nomeDocumento", type = String.class)}) })
@Table(schema = "auditoria")
public class DocumentosRol implements Serializable {

	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;
	@ManyToOne
	@JoinColumn(name = "idTipoRolDocumento", nullable = false)
	private TipoRolDocumento tipoRolDocumento;
	@ManyToOne
	@JoinColumn(name = "idTipoDocumento", nullable = false)
	private TipoDocumento tipoDocumento;
	@Column(nullable = false)
	private Boolean obrigatorio;
	@Column(nullable = false, updatable = false)
	private LocalDateTime dataCriacao;
	@Column(nullable = false)
	private Long idUsuario;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public TipoRolDocumento getTipoRolDocumento() {
		return tipoRolDocumento;
	}

	public void setTipoRolDocumento(TipoRolDocumento tipoRolDocumento) {
		this.tipoRolDocumento = tipoRolDocumento;
	}

	public TipoDocumento getTipoDocumento() {
		return tipoDocumento;
	}

	public void setTipoDocumento(TipoDocumento tipoDocumento) {
		this.tipoDocumento = tipoDocumento;
	}

	public Boolean getObrigatorio() {
		return obrigatorio;
	}

	public void setObrigatorio(Boolean obrigatorio) {
		this.obrigatorio = obrigatorio;
	}

	public LocalDateTime getDataCriacao() {
		return dataCriacao;
	}

	public void setDataCriacao(LocalDateTime dataCriacao) {
		this.dataCriacao = dataCriacao;
	}

	public Long getIdUsuario() {
		return idUsuario;
	}

	public void setIdUsuario(Long idUsuario) {
		this.idUsuario = idUsuario;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((dataCriacao == null) ? 0 : dataCriacao.hashCode());
		result = prime * result + ((idUsuario == null) ? 0 : idUsuario.hashCode());
		result = prime * result + ((obrigatorio == null) ? 0 : obrigatorio.hashCode());
		result = prime * result + ((tipoDocumento == null) ? 0 : tipoDocumento.hashCode());
		result = prime * result + ((tipoRolDocumento == null) ? 0 : tipoRolDocumento.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		DocumentosRol other = (DocumentosRol) obj;
		if (dataCriacao == null) {
			if (other.dataCriacao != null)
				return false;
		} else if (!dataCriacao.equals(other.dataCriacao))
			return false;
		if (idUsuario == null) {
			if (other.idUsuario != null)
				return false;
		} else if (!idUsuario.equals(other.idUsuario))
			return false;
		if (obrigatorio == null) {
			if (other.obrigatorio != null)
				return false;
		} else if (!obrigatorio.equals(other.obrigatorio))
			return false;
		if (tipoDocumento == null) {
			if (other.tipoDocumento != null)
				return false;
		} else if (!tipoDocumento.equals(other.tipoDocumento))
			return false;
		if (tipoRolDocumento == null) {
			if (other.tipoRolDocumento != null)
				return false;
		} else if (!tipoRolDocumento.equals(other.tipoRolDocumento))
			return false;
		return true;
	}

}
