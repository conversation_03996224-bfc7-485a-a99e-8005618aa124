package br.gov.ac.tce.sicap.modelo.repositorio;

import java.io.Serializable;
import java.util.Collection;

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import com.uaihebert.uaicriteria.UaiCriteria;
import com.uaihebert.uaicriteria.UaiCriteriaFactory;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.MatrizRisco;

@Stateless
public class MatrizRiscoRepositorio implements Serializable {

	private static final long serialVersionUID = 1L;

	@PersistenceContext(unitName = "sicapAnalise")
	private EntityManager entityManager;

	public void salvar(MatrizRisco matrizRisco) throws RepositorioException {
		try {
			this.entityManager.persist(matrizRisco);
		} catch (Exception e) {
			e.printStackTrace();
			throw new RepositorioException("Erro ao salvar matriz de risco.", e.getCause());
		}
	}

	public Collection<MatrizRisco> listarTodas() throws RepositorioException {
		Collection<MatrizRisco> listaMatrizesRisco = null;
		try {
			UaiCriteria<MatrizRisco> uaiCriteria = UaiCriteriaFactory.createQueryCriteria(this.entityManager,
					MatrizRisco.class);
			listaMatrizesRisco = uaiCriteria.getResultList();
		} catch (Exception e) {
			throw new RepositorioException("Erro ao listar todas as matrizes de risco cadastradas.", e.getCause());
		}
		return listaMatrizesRisco;
	}

	public MatrizRisco pesquisarPorId(Long idMatrizRisco) throws RepositorioException {
		MatrizRisco matrizRisco = null;
		try {
			UaiCriteria<MatrizRisco> uaiCriteria = UaiCriteriaFactory.createQueryCriteria(this.entityManager,
					MatrizRisco.class);
			uaiCriteria.andEquals("id", idMatrizRisco);
			matrizRisco = uaiCriteria.getSingleResult();
		} catch (Exception e) {
			throw new RepositorioException("Erro ao pesquisar matriz de risco cadastrada.", e.getCause());
		}
		return matrizRisco;
	}
}
