package br.gov.ac.tce.sicap.controle.conversor;

import java.time.LocalDate;
import java.util.Date;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

@FacesConverter("dateConverter")
public class DateConverter implements Converter {

	@Override
	public Object getAsObject(FacesContext fc, UIComponent uic, String value) {
		LocalDate localDate = null;
		if (value == null || value.equals("")) {
			return null;
		} else {
			try {
				Date data = new Date(value);
				localDate = LocalDate.of(data.getYear(), data.getMonth(), data.getDay());
			} catch (Exception e) {
			}
			return localDate;
		}
	}

	@Override
	public String getAsString(FacesContext fc, UIComponent uic, Object object) {
		return null;
	}

}
