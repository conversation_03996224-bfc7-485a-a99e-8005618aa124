package br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise;

public enum SituacaoAnalise {

	EM_ANALISE("EA", "Iniciada"), NOTIFICADA("AM", "Notificada"), CONCLUIDA("CO","Concluída");
	

	private String id;
	private String descricao;

	private SituacaoAnalise(String id, String descricao) {
		this.id = id;
		this.descricao = descricao;
	}

	public String getId() {
		return id;
	}

	public String getDescricao() {
		return descricao;
	}

	public static SituacaoAnalise parse(String id) {
		SituacaoAnalise situacaoAnalise = null;
		for (SituacaoAnalise item : SituacaoAnalise.values()) {
			if (item.getId() == id) {
				situacaoAnalise = item;
				break;
			}
		}
		return situacaoAnalise;
	}

}
