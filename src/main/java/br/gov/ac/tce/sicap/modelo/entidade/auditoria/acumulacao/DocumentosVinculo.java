package br.gov.ac.tce.sicap.modelo.entidade.auditoria.acumulacao;

import java.io.Serializable;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise.DetalhamentoSolicitacaoDocumento;

@Entity
@Table(schema = "auditoria")
public class DocumentosVinculo implements Serializable {

	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;
	@ManyToOne
	@JoinColumn(name = "idSolicitacaoDocumentoAcumulacao", nullable = false)
	private SolicitacaoDocumentoAcumulacao solicitacaoDocumentoAcumulacao;
	@ManyToOne
	@JoinColumn(name = "idDetalhamentoSolicitacaoDocumento", nullable = false)
	private DetalhamentoSolicitacaoDocumento detalhamentoSolicitacaoDocumento;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public SolicitacaoDocumentoAcumulacao getSolicitacaoDocumentoAcumulacao() {
		return solicitacaoDocumentoAcumulacao;
	}

	public void setSolicitacaoDocumentoAcumulacao(SolicitacaoDocumentoAcumulacao solicitacaoDocumentoAcumulacao) {
		this.solicitacaoDocumentoAcumulacao = solicitacaoDocumentoAcumulacao;
	}

	public DetalhamentoSolicitacaoDocumento getDetalhamentoSolicitacaoDocumento() {
		return detalhamentoSolicitacaoDocumento;
	}

	public void setDetalhamentoSolicitacaoDocumento(DetalhamentoSolicitacaoDocumento detalhamentoSolicitacaoDocumento) {
		this.detalhamentoSolicitacaoDocumento = detalhamentoSolicitacaoDocumento;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result
				+ ((detalhamentoSolicitacaoDocumento == null) ? 0 : detalhamentoSolicitacaoDocumento.hashCode());
		result = prime * result
				+ ((solicitacaoDocumentoAcumulacao == null) ? 0 : solicitacaoDocumentoAcumulacao.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		DocumentosVinculo other = (DocumentosVinculo) obj;
		if (detalhamentoSolicitacaoDocumento == null) {
			if (other.detalhamentoSolicitacaoDocumento != null)
				return false;
		} else if (!detalhamentoSolicitacaoDocumento.equals(other.detalhamentoSolicitacaoDocumento))
			return false;
		if (solicitacaoDocumentoAcumulacao == null) {
			if (other.solicitacaoDocumentoAcumulacao != null)
				return false;
		} else if (!solicitacaoDocumentoAcumulacao.equals(other.solicitacaoDocumentoAcumulacao))
			return false;
		return true;
	}

}
